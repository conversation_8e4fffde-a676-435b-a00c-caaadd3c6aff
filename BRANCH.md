# 分支管理

> 本项目为一码多端项目，目前支持的渠道有`携程APP`、`携程小程序`、`去哪儿APP`、`去哪儿小程序` ，未来会支持 `携程H5`、`去哪儿H5`。

## 分支命名规范

### 主分支

1. 分支定位：线上生产分支，生产环境发布之后合并至此分支。
2. 新增规则：一般不新增，如需新增master分支，需要找@cczhang确认，并更新此文档。

| 分支名称              | 分支描述                                   |
| --------------------- | ------------------------------------------ |
| master-xtaro          | 携程APP主分支                              |
| master-xtaro-mini     | 小程序主分支                               |
| master-xtaro-qunar    | 去哪儿APP主分支                            |
| master-xtaro-h5       | 携程H5主分支，交易快照与携程H5共用一套代码 |
| master-xtaro-h5-qunar | 去哪儿H5主分支                             |

### 发布分支

1. 主端指携程APP，分端指其他。代码默认在主端开发，分端需要同步主端代码，并进行测试。

#### 主端

| 分支名称    | 分支描述                                                                                                                                           |
| ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| feature/xxx | 项目或CR分支开发的目标分支，合并至此分支的代码具备准发布条件。注意：1. 如果项目改动大，需要分多次MR到此分支 2.有idev的需求，此分支需要以idev号命名 |
| release/xxx | 准发布分支，此分支上的代码必须测试通过，具备直接发布条件，每周二此分支分布生产，发布后合回到master-xtaro                                           |
| fix/xxx     | 线上修复紧急问题，一般不建议使用                                                                                                                   |

#### 分端

| 分支名称   | 分支描述                                     |
| ---------- | -------------------------------------------- |
| client/xxx | 分端准发布分支以此命名，跟随各分端master分支 |

### 开发分支

1. 开发分支在合并至发布分支结束后需要删除。
2. 分支三级名称处需要带idev号，如dev/zhangsan/idev-123456。

| 分支名称         | 描述                                                                                  |
| ---------------- | ------------------------------------------------------------------------------------- |
| dev/zhangsan/xxx | 开发分支，用于开发新功能，zhangsan为开发人员，xxx为开发分支的名称，如dev/zhangsan/xxx |
