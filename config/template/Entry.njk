import { App,lazyRequire } from '@ctrip/crn';
import { PluginRegistry } from '@ctrip/rr-react-native';
const {
  FloatFromBottom,
  FloatFromRight,
  // eslint-disable-next-line import/no-extraneous-dependencies
} = require
('react-native-deprecated-custom-components/src/NavigatorSceneConfigs');

require('./taro/pages/xcar/Preload');

const FloatFromBottomGesturesForbidden = { ...FloatFromBottom, gestures: null };

const rightSceneConfig = {
  ...FloatFromRight,
  defaultTransitionVelocity: 10,
};
let  component = {};
{% for info in pageInfoList %}
component["{{info.rnPageName}}"] = lazyRequire('./pages/{{info.rnPageName}}');
{% endfor%}

const sceneConfigList = {
  bottom:FloatFromBottomGesturesForbidden,
  right:rightSceneConfig,
};

const taroPages = [{% for info in pageInfoList %}
    {
        component: component["{{info.rnPageName}}"],
        name: '{{info.rnPageName}}',
        path: '/{{info.path}}',
        jumpAnimation: '{{ info.jumpAnimation if info.jumpAnimation else 'right' }}'
    }{% if not loop.last %},{% endif %}
{% endfor %}
]
const pages = taroPages.map((p, index) => ({
    component: p.component,
    name: p.path,
    isInitialPage: index === 0 ? true : false,
    __pageName__:/^Page\d/.test(p.name)?undefined:p.name,
    sceneConfig:sceneConfigList[p.jumpAnimation],
}))

const navigationBarConfig = {
    hide: true,
    backgroundColor: 'rgb(9, 159, 222)',
};

export default class extends App {
    constructor(props: any) {
        super(props);
        const initialPage = props.urlQuery.initialPage || props.urlQuery.initial_page;
        if (initialPage) {
            const taroPage = taroPages.find((p) => p.name === initialPage);
            const page = pages.find((p) => p.name === taroPage?.path);
            if (page) {
                page.isInitialPage = true;
                pages[0].isInitialPage = false;
            }
        }
        PluginRegistry.init(
            props, // 透传
            {
                code: 'car_capp', // 申请地址：https://coffeebean.flight.ctripcorp.com/SourceCode
                recordFilter: () => true,
                // 支持UI自动化测试用例录制
                recordAction: true,
            },
            null,
        );
        global.__xtaro_domain = '{{config.storageDomain or ""}}';
        // @ts-ignore
        global.maxDeviceWidthScale = 430; // 最大自适应适配尺寸16 Pro Max (430x932)
        this.init({pages, navigationBarConfig});
    }
    componentWillUnmount() {
      global.isPreSetSomething = false;
  }
}


