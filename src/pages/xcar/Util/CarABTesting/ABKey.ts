/**
 * 新增与下线实验需更新conf http://conf.ctripcorp.com/pages/viewpage.action?pageId=347791611
 */

export type ABValueType = {
  key: string;
  newVersionCode: string[]; // 定义哪个版本为新版, eg: ['B', 'C']
  defaultVersionCode: string; // 当实验号下线后，不返回ExpVersion时，当前值表示该场景下走哪个版本
  isCache: boolean; // 是否存储到localStorage中
};

type ABKeyType = {
  // 海外详情页车图展示标准车图
  showStandardImg: ABValueType;
  // 首页信息流优化
  ISDHomeFlowOptimize: ABValueType;
  // 境外列表页二批覆盖刷新实验
  OSDListAllRefresh: ABValueType;
  // 登陆态校验
  loginAB: ABValueType;
  // 国内详情页返回时调用调研
  ISDProductBack: ABValueType;
  // 出境详情页返回时调用调研
  OSDProductBack: ABValueType;
  // 门店信息反馈入口
  storeInfoSurvey: ABValueType;
  // 海外排序对接AI -> CO实时正反馈
  aiSort2: ABValueType;
  // 国内列表页ipoll
  ISDListIpoll: ABValueType;
  // 国内详情页ipoll
  ISDProductIpoll: ABValueType;
  // 出境列表页ipoll
  OSDListIpoll: ABValueType;
  // 出境详情页ipoll
  OSDProductIpoll: ABValueType;
  // 出境新版订详
  OSDOrderDetail: ABValueType;
  // 货架页二期保险等样式改版
  ISDShelves2New: ABValueType;
  // 货架三期填写页改版
  isISDShelves3: ABValueType;
  // 租车企微项目Capp入口
  ISDHomeQiwei: ABValueType;
  // 超级补贴活动
  superSubsidy: ABValueType;
  // 货架页点评入口实验
  VendorListNoComment: ABValueType;
  // 国内列表卡片
  ISDListCard: ABValueType;
};

export const ABKey: ABKeyType = {
  // 海外详情页车图展示标准车图
  showStandardImg: {
    key: '230821_DSJT_cltpC',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 首页信息流优化
  ISDHomeFlowOptimize: {
    key: '240118_DSJT_xxlyh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 第二批强制刷新和强制重新排序
  OSDListAllRefresh: {
    key: '240716_DSJT_Cref',
    newVersionCode: ['B'],
    defaultVersionCode: 'B',
    isCache: false,
  },
  loginAB: {
    key: '250716_DSJT_coanticr',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 国内详情页返回时调用调研
  ISDProductBack: {
    key: '250214_DSJT_usrpopup',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 出境详情页返回时调用调研
  OSDProductBack: {
    key: '241023_DSJT_usrpopupgj',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 门店信息反馈
  storeInfoSurvey: {
    key: '250409_DSJT_MXFBCtrip1',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 海外排序对接AI -> CO实时正反馈
  aiSort2: {
    key: '250711_DSJT_COfeedback',
    newVersionCode: ['B', 'C'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 国内列表页ipoll
  ISDListIpoll: {
    key: '250424_DSJT_ipolllist',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 国内详情页ipoll
  ISDProductIpoll: {
    key: '250424_DSJT_detailusr',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  OSDListIpoll: {
    key: '250427_DSJT_COXQPOLL',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  OSDProductIpoll: {
    key: '250424_DSJT_COLBXQPOLL',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 出境新版订详
  OSDOrderDetail: {
    key: '250609_DSJT_CtripXBDX',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  ISDShelves2New: {
    key: '250715_DSJT_hjbxbz',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 货架三期填写页改版
  isISDShelves3: {
    key: '250528_DSJT_Fillpage',
    newVersionCode: ['B', 'C'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 租车企微项目Capp入口
  ISDHomeQiwei: {
    key: '250603_DSJT_Wechat',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  superSubsidy: {
    key: '250620_DSJT_price',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 货架页点评入口实验
  VendorListNoComment: {
    key: '250714_DSJT_cxdp',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 国内列表卡片
  ISDListCard: {
    key: '250807_DSJT_carcards',
    newVersionCode: ['B', 'C', 'D', 'E'],
    defaultVersionCode: 'A',
    isCache: false,
  },
};

/**
 * 根据实验号获取实验的配置相关数据
 * @param key 实验号
 * @returns 实验的配置相关数据
 */
export const GetABValueType = key => {
  let abValue = null;
  Object.keys(ABKey).forEach(objKey => {
    if (ABKey[objKey].key === key) {
      abValue = ABKey[objKey];
    }
  });
  return abValue;
};

/**
 * 获取需要缓存的所有Ab实验相关配置数据
 * @returns 需要缓存的所有Ab实验相关配置数据
 */
export const GetAllCacheABValueType = () => {
  const allList = [];
  Object.keys(ABKey).forEach(objKey => {
    if (ABKey[objKey].isCache) {
      allList.push(ABKey[objKey]);
    }
  });
  return allList;
};

export default ABKey;
