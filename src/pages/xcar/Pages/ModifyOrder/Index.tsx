import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import Platform from '@c2x/apis/Platform';
import { IBasePageProps } from '@c2x/components/Page';
import LoadingView from '@c2x/components/LoadingView';
import Event from '@c2x/apis/Event';
import React, { RefObject } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import Header from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import { color, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { IButtonColorType } from '@ctrip/rn_com_car/dist/src/Components/Basic/BookBar';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { XCalendar } from '@ctrip/xtaro-component-calendar';
import CPage, { IStateType } from '../../Components/App/CPage';
import Texts from './Texts';
import ModifyOrderFooter from '../../Components/ModifyOrder/Footer';
import ModifyDriverAndVehicleContainer from '../../Containers/ModifyDriverAndVehicleContainer';
import ModifyDriverInfo from '../../Containers/ModifyOrderDriverInfoContainer';
import ModifyLocationInfo from '../../Containers/ModifyLocationInfoContainer';
import ModifyOrderWarnModal from '../../Containers/ModifyOrderWarnModalContainer';

import ModifyVehicleInfo, { RebookType } from './Components/VehicleInfo';
import Loading from './Components/Loading';
import {
  VehicleInfoType,
  ModifyCancelRule,
} from '../../Types/Dto/OrderDetailRespaonseType';
import ValidatePassenger, {
  IDriversMapType,
} from '../../ComponentBusiness/DriverAddeditModal/src/Validate';
import { IModifyStatus } from '../../State/ModifyOrder/Types';
import ErrorNetWork from '../../Components/Error/ErrorNetWork';
import { OrderModalsVisible } from '../OrderDetail/Types';
import {
  AppContext,
  User,
  Channel,
  CarLog,
  EventHelper,
} from '../../Util/Index';
import { EventName, UITestID } from '../../Constants/Index';
import { FilterPoiType } from '../../Util/AppContext';

const { vh, lazySelector } = BbkUtils;
const styles = StyleSheet.create({
  out: Platform.select({
    web: {
      height: vh(100),
    },
    ios: {},
    android: {},
    // @ts-ignore
    harmony: {},
  }),
  wrapper: {
    backgroundColor: color.grayBg,
  },
});

interface ModifyOrderProps extends IBasePageProps {
  vehicleInfo: VehicleInfoType;
  modifyCancelRule: ModifyCancelRule;
  modifyStatus: IModifyStatus;
  modalsVisible: OrderModalsVisible;
  driversMap: IDriversMapType;
  isLoading: boolean;
  isMaskLoading: boolean;
  modifyOrderSuccess: boolean;
  isFail: boolean;
  orderId: number;
  modifyOrderWarnModalVisible: boolean;
  isEhi?: boolean;
  initData: (orderId: number) => void;
  createModifyOrder: (orderId: number) => void;
  setOrderModalsVisible: (OrderModalsVisible: OrderModalsVisible) => void;
  reBooking: (isInfoFromOrder?: boolean) => void;
  clear: () => void;
  setModifyOrderWarnModalVisible: (visible: boolean) => void;
  disablePickDropInfo: boolean;
  isFromCancelPage?: boolean;
  isCancelOrder?: boolean;
}

interface ModifyOrderState extends IStateType {
  orderId: number;
  isLogin: boolean;
  isShowPageOtherModule: boolean;
}

const rebookClickKeyMap = {
  [RebookType.Vehicle]: '点击_修改订单_修改车型_取消重订',
  [RebookType.DriverVehicle]: '点击_修改订单_修改驾驶员和车型_取消重订',
};
export default class ModifyOrder extends CPage<
  ModifyOrderProps,
  ModifyOrderState
> {
  mobileInputRef = null;

  constructor(props) {
    super(props);
    const orderId = props.orderId || Number(AppContext.UrlQuery.orderId);
    this.state = { orderId, isLogin: false, isShowPageOtherModule: false };
    this.back = this.back.bind(this);
    this.onPressFooterBtn = this.onPressFooterBtn.bind(this);
    this.onPressDriver = this.onPressDriver.bind(this);
    // 屏蔽境外POI
    AppContext.setFilterPoi(FilterPoiType.OSD);
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().ModifyOrder.ID;
  }

  onBackAndroid() {
    const { modifyOrderWarnModalVisible, setModifyOrderWarnModalVisible } =
      this.props;
    if (modifyOrderWarnModalVisible) {
      setModifyOrderWarnModalVisible(false);
    } else {
      this.back();
    }
  }

  back() {
    const { modifyStatus, setOrderModalsVisible } = this.props;
    if (modifyStatus === IModifyStatus.nochange) {
      this.pop();
      return;
    }
    setOrderModalsVisible({
      confirmModal: {
        visible: true,
        data: {
          title: Texts.modifyCancelConfirm,
          btns: [
            {
              name: Texts.thinkTwice,
              onPress: () =>
                setOrderModalsVisible({ confirmModal: { visible: false } }),
            },
            {
              name: Texts.giveUp,
              isPrimary: true,
              onPress: () => {
                setOrderModalsVisible({ confirmModal: { visible: false } });
                this.pop();
              },
            },
          ],
        },
      },
    });
  }

  onPressFooterBtn() {
    const { modifyStatus, driversMap, isFromCancelPage, isCancelOrder } =
      this.props;
    const { orderId } = this.state;
    if (isFromCancelPage) {
      CarLog.LogCode({
        name: '点击_订单详情_取消订单_修改订单/行程_修改订单_去确认',
        info: {
          cancelorder: isCancelOrder,
        },
      });
    } else {
      CarLog.LogCode({ name: '点击_修改订单_确认修改' });
    }

    if (!ValidatePassenger(driversMap)) {
      return;
    }

    if (modifyStatus === IModifyStatus.allchange) {
      this.onPressReBooking();
      return;
    }
    this.props.createModifyOrder(orderId);
  }

  onPressDriver() {
    this.push('DriverList', { fromurl: 'ModifyOrder' });
    CarLog.LogCode({ name: '点击_修改订单_修改驾驶员' });
  }

  onPressReBooking = (
    { isInfoFromOrder, type } = {} as {
      isInfoFromOrder?: boolean;
      type?: RebookType;
    },
  ) => {
    const { reBooking } = this.props;
    reBooking(isInfoFromOrder);
    if (type !== undefined) {
      CarLog.LogCode({
        name: rebookClickKeyMap[type],
      });
    }
  };

  registerEvents() {
    EventHelper.addEventListener(EventName.modifyOrder2Order, () => {
      if (
        AppContext.PageInstance.getPageId() ===
        Channel.getPageId().ModifyOrder.ID
      ) {
        this.pop();
      }
    });
  }

  removeEvents() {
    Event.removeEventListener(EventName.modifyOrder2Order);
  }

  async componentDidMount() {
    super.componentDidMount();
    const { initData } = this.props;
    const { orderId } = this.state;
    const isLogin = await User.isLogin();
    if (!isLogin) {
      const isLoginNow = await User.toLogin();
      if (!isLoginNow) {
        return;
      }
    }
    this.setState({ isLogin: true });
    initData(orderId);
    this.registerEvents();
  }

  componentWillUnmount() {
    const { clear } = this.props;
    clear();
    this.removeEvents();
  }

  componentDidUpdate(prevProps: ModifyOrderProps) {
    const {
      isLoading,
      initData,
      modifyOrderSuccess,
      modifyOrderWarnModalVisible,
      isFromCancelPage,
    } = this.props;
    const { orderId } = this.state;
    if (prevProps.isLoading && !isLoading) {
      initData(orderId);
    }
    if (
      modifyOrderSuccess &&
      !prevProps.modifyOrderSuccess &&
      // 确认页重新获取，不需要 push
      AppContext.PageInstance.getPageId() !==
        Channel.getPageId().ModifyOrderConfirm.ID
    ) {
      this.push(Channel.getPageId().ModifyOrderConfirm.EN, {
        isFromCancelPage,
      });
    }
    if (modifyOrderWarnModalVisible) {
      this.disableDragBack();
    } else {
      this.enableDragBack();
    }
    this.setShowPageOtherModule();
  }

  getInnerTextType = () => {
    const { modifyStatus, modifyCancelRule } = this.props;
    const texts = [
      Texts.noChange,
      modifyCancelRule?.content,
      Texts.eitherChange,
      Texts.eitherChange,
    ];

    let innerTextColor = color.fontPrimary;
    let buttonColorType = IButtonColorType.orange;
    let buttonName = Texts.confirm;
    let tip = '';
    if (modifyStatus === IModifyStatus.allchange) {
      buttonColorType = IButtonColorType.blue;
      buttonName = Texts.cancelToReorder;
      tip = Texts.changedTip;
      if (modifyCancelRule?.color === 1) {
        innerTextColor = color.priceDescIconColor;
      } else {
        innerTextColor = color.redBase;
      }
    }
    return {
      innerText: texts[modifyStatus],
      innerTextColor,
      highLight: modifyCancelRule?.highLight,
      buttonName,
      tip,
      buttonColorType,
    };
  };

  inputRefHandler = (ref: RefObject<any>) => {
    this.mobileInputRef = ref;
  };

  timePressHandler = () => {
    try {
      this.mobileInputRef.blur();
    } catch (e) {
      /* eslint-disable no-empty-pattern */
    }
  };

  warnModalLog = () => {
    CarLog.LogCode({ name: '点击_修改订单_取消重订弹窗' });
  };

  setShowPageOtherModule = () => {
    setTimeout(() => {
      if (this.state.isShowPageOtherModule) {
        return;
      }
      this.setState({
        isShowPageOtherModule: true,
      });
    }, 100);
  };

  renderPage() {
    const {
      vehicleInfo,
      modifyCancelRule,
      isLoading,
      isFail,
      isMaskLoading,
      modifyStatus,
      isEhi,
      disablePickDropInfo,
    } = this.props;
    const { orderId, isLogin, isShowPageOtherModule } = this.state;
    const innerTextProps = this.getInnerTextType();
    if (isLoading || !isLogin) {
      return <LoadingView />;
    }
    if (isFail || !vehicleInfo) {
      return (
        <ErrorNetWork
          title="网络不给力"
          subTitle="请检查网络设置后再试"
          buttonText="再试一次"
          hasError={true}
          buttonTestId={UITestID.car_testid_page_modifyorder_reload}
          operateButtonPress={() => this.props.initData(orderId)}
        />
      );
    }
    return (
      <XCalendar.PageWithCalendar>
        <View style={xMergeStyles([layout.flex1, styles.out])}>
          <Header
            title={Texts.ModifyOrder}
            isBottomBorder={true}
            onPressLeft={this.back}
            leftIconTestID={
              UITestID.car_testid_page_modifyorder_header_lefticon
            }
            contentStyle={layout.flexCenter}
          />

          <KeyboardAwareScrollView
            style={xMergeStyles([layout.flex1, styles.wrapper])}
            onScrollBeginDrag={Keyboard.dismiss}
            scrollEventThrottle={50}
            enableResetScrollToCoords={false}
            keyboardDismissMode="on-drag"
            keyboardShouldPersistTaps="handled"
            automaticallyAdjustContentInsets={false}
            keyboardOpeningTime={100}
            extraHeight={100}
          >
            <ModifyLocationInfo
              timePressHandler={this.timePressHandler}
              rebookType={RebookType.PickDropInfo}
              modifyCancelRule={modifyCancelRule}
              onPressReorder={this.onPressReBooking}
              disabled={disablePickDropInfo}
            />

            {isEhi ? (
              <ModifyDriverAndVehicleContainer
                vehicleInfo={vehicleInfo}
                modifyCancelRule={modifyCancelRule}
                onPressReorder={this.onPressReBooking}
                rebookType={RebookType.DriverVehicle}
                isShowVehicleInfo={isShowPageOtherModule}
              />
            ) : (
              <>
                <ModifyDriverInfo
                  onPressDriver={this.onPressDriver}
                  inputRefHandler={this.inputRefHandler}
                />

                {lazySelector(isShowPageOtherModule, () => (
                  <ModifyVehicleInfo
                    vehicleInfo={vehicleInfo}
                    modifyCancelRule={modifyCancelRule}
                    onPressReorder={this.onPressReBooking}
                    rebookType={RebookType.Vehicle}
                  />
                ))}
              </>
            )}
          </KeyboardAwareScrollView>
          {lazySelector(isShowPageOtherModule, () => (
            <>
              <ModifyOrderFooter
                disabled={modifyStatus === IModifyStatus.nochange}
                onPressBtn={this.onPressFooterBtn}
                tip={innerTextProps.tip}
                highLight={innerTextProps.highLight}
                buttonName={innerTextProps.buttonName}
                innerText={innerTextProps.innerText}
                buttonColorType={innerTextProps.buttonColorType}
              />

              <ModifyOrderWarnModal visibleLog={this.warnModalLog} />
            </>
          ))}
          {isMaskLoading && (
            <Loading justModify={modifyStatus === IModifyStatus.mobilechange} />
          )}
        </View>
      </XCalendar.PageWithCalendar>
    );
  }
}
