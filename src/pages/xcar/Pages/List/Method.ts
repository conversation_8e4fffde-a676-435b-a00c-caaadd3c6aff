import { getGS, getVehicleList } from '../../Global/Cache/ListResSelectors';
import { Utils, CarStorage } from '../../Util/Index';
import { ImageUrl, CommonEnums, StorageKey } from '../../Constants/Index';
import { getVendorListEnterDataNew } from '../../State/List/VehicleListMappers';

const getExposureData = (data, firstVehicleGroup) => {
  const {
    vendorIndex = '',
    vendor = {},
    vendorHeaderProps = {},
    reference = {},
    vehicleCode: ctripVehicleCode = '',
    groupName = '',
    vehicleName = '',
    vehicleLowestPrice,
  } = data || {};
  const {
    priceInfo = {},
    allTags = [],
    distance = '',
    rDistance = '',
    vendorName = '',
    pStoreRouteDesc = '',
    rStoreRouteDesc = '',
  } = vendor;
  const {
    currentDailyPrice = '',
    currentOriginalDailyPrice = '',
    currentTotalPrice = '',
  } = priceInfo;
  const {
    pickWayInfo = '',
    returnWayInfo = '',
    vendorCode = '',
    vehicleCode,
    gsId = 0,
    pStoreCode = '',
    rStoreCode = '',
    bizVendorCode: vendorId = '',
    priceBatchNo,
    klbVersion,
  } = reference;
  const {
    commentDesc = '',
    isEasyLife = false,
    isOptimize = false,
    score = '',
  } = vendorHeaderProps;
  const tags = [];
  allTags.forEach(element => {
    tags.push(element?.labelCode || '');
  });

  const GSInfo = getGS();
  const { gsTypes = [] } = GSInfo;
  const gsType = gsTypes.find(gsTypeItem => gsTypeItem.id === gsId) || {};
  const { title = '' } = gsType;

  if (Utils.isCtripIsd()) {
    return {
      realVehicleCode: vehicleCode,
      vehicleCode: firstVehicleGroup || ctripVehicleCode,
      vendorCode,
      vendorId,
      vendorIndex,
      isEasyLife,
      isOptimize,
      pickWayInfo,
      returnWayInfo,
      distance,
      rDistance,
      commentDesc,
      score,
      currentDailyPrice,
      currentOriginalDailyPrice,
      currentTotalPrice,
      allTags: JSON.stringify(tags),
      gsId,
      gsName: title,
      vendorName,
      groupName,
      vehicleName,
      pStoreCode,
      rStoreCode,
      klbVersion,
    };
  }
  const curVendorRef = vendor?.reference;
  return {
    realVehicleCode: vehicleCode,
    vehicleCode: firstVehicleGroup || ctripVehicleCode,
    vendorCode,
    vendorId,
    vendorIndex,
    isEasyLife,
    isOptimize,
    pickWayInfo: pStoreRouteDesc,
    returnWayInfo: rStoreRouteDesc,
    pDistance: distance,
    rDistance,
    commentDesc,
    score,
    currentDailyPrice,
    minCurrentDailyPrice: vehicleLowestPrice,
    currentOriginalDailyPrice,
    currentTotalPrice,
    allTags: JSON.stringify(tags),
    vendorName,
    groupName,
    vehicleName,
    pStoreCode,
    rStoreCode,
    priceBatchNo,
    klbVersion,
    brandLabelCode: vendor?.vendorTag?.code,
    brandLabelName: vendor?.vendorTag?.title,
    skuId: curVendorRef?.skuId,
    pStoreId: curVendorRef?.pStoreCode,
    rStoreId: curVendorRef?.rStoreCode,
    labelCode: tags,
    packageSellingRuleId: curVendorRef?.pkgRuleId,
    ifPriceAdjusted: !!curVendorRef?.adjustInfo, // 是否有调价
  };
};

export const getShowLikeLabel = ({ activeGroupId, vehicleIndex, hotType }) =>
  (activeGroupId === 'all' && vehicleIndex === 0 && hotType === 4) || false;

export const getSelfServiceLogData = outTags =>
  outTags?.find(item => item?.labelCode === CommonEnums.ILableCode.SelfService)
    ? 1
    : 0;

export const getLogKey = (
  vendor,
  section,
  vendorIndex?: any,
  activeGroupId?: string,
) => {
  const {
    vehicleIndex,
    vehicleCode,
    minDPrice = '',
    minTPrice = '',
    lowestPrice = '',
    hotType,
    productRef,
  } = section || {};
  const showLikeLabel = getShowLikeLabel({
    activeGroupId,
    vehicleIndex,
    hotType,
  });
  const {
    hasOptimize = false,
    hasEasyLife = false,
    hasCreditRent = false,
    rCoup,
  } = getVendorListEnterDataNew(section);
  const newVehicleList = getVehicleList();
  const vehicleMap = newVehicleList?.filter(
    item => item?.vehicleCode === vehicleCode,
  );
  let vehicle = null;
  // 如果筛选车型code出现多辆车且有限行牌照参数，则进行牌照过滤
  if (productRef?.license && vehicleMap?.length > 1) {
    vehicle =
      vehicleMap?.find(item => item.license === productRef?.license) || {};
  } else {
    vehicle = vehicleMap?.[0] || {};
  }
  return {
    isHasCouponVehicle: rCoup || 0, // 是否含有领券订产品
    mediaTypes: vehicle?.mediaTypes,
    vehicleIndex,
    vehicleCode,
    isEasyLife: hasEasyLife,
    isCreditRent: hasCreditRent,
    isOptimize: hasOptimize,
    minDPrice: Utils.isCtripIsd() ? minDPrice : lowestPrice,
    minTPrice: Utils.isCtripIsd() ? minTPrice : '',
    vechicleLable: showLikeLabel ? '你可能喜欢' : '', // 车型标签，记录猜你喜欢字样
    license: productRef?.license,
    realGroupId: vehicle?.groupCode,
    realGroupName: vehicle?.groupName,
    picSource:
      vehicle?.imageList?.[0]?.indexOf(ImageUrl.YiChePictureSuffix) > -1
        ? 1
        : 0,
    isSelfService: getSelfServiceLogData(section?.outTags),
    gearType: vehicle?.transmissionName || '',
    seatCount: vehicle?.passengerNo || '',
    energyType: vehicle?.displacement || '',
    ...(section.extraLog || {}),
  };
};

export default {
  getExposureData,
};

type UserBrowsedType = {
  vehicleCode?: string;
  license?: string;
  userBrowsingHistories: Map<string, object>;
};

// 判断用户是否浏览过该车型
export const getIsUserBrowsed = ({
  vehicleCode,
  license,
  userBrowsingHistories,
}: UserBrowsedType) => {
  return userBrowsingHistories?.has(`${vehicleCode}_${license}`);
};

type UserGroupBrowsedType = {
  priceGroup: Array<{ vehicleCode: string; productRef: { license: string } }>;
} & UserBrowsedType;

// 判断一个Group下的浏览过的车型总数
export const getIsUserGroupBrowsedCount = ({
  userBrowsingHistories,
  priceGroup,
}: UserGroupBrowsedType) => {
  if (!(priceGroup?.length > 0)) return -1;

  return priceGroup
    ?.map(item =>
      getIsUserBrowsed({
        vehicleCode: item?.vehicleCode,
        license: item?.productRef?.license,
        userBrowsingHistories,
      }),
    )
    .filter(item => item)?.length;
};

export const loadOsdUserBrowsed = async setOsdUserBrowsed => {
  let osdBrowsed = {};
  try {
    const res =
      (await CarStorage.loadAsync(StorageKey.CAR_LIST_BROWSING)) || '{}';
    osdBrowsed = JSON.parse(res);
    setOsdUserBrowsed(osdBrowsed);
    // eslint-disable-next-line no-empty
  } catch (e) {}
  return osdBrowsed;
};

export const saveOsdUserBrowsed = osdUserBrowsed => {
  try {
    CarStorage.save(StorageKey.CAR_LIST_BROWSING, osdUserBrowsed, '1d');
    // eslint-disable-next-line no-empty
  } catch (e) {}
};

export const isOsdUserBrowsed = (vehicleKey, osdUserBrowsed) => {
  const curBrowsed = osdUserBrowsed;
  const now = +new Date();
  if (now - (curBrowsed[vehicleKey]?.timeSpan || 0) < 86400000) {
    return true;
  }
  return false;
};

let vehicleCodeBigId = '';

export const getVehicleCodeBigId = () => {
  return vehicleCodeBigId;
};

export const setVehicleCodeBigId = vehicleCode => {
  vehicleCodeBigId = vehicleCode;
};

// 对齐国内浏览过灰底逻辑
export const getIsOsdUserBrowsed = (vehicleKey, osdUserBrowsingHistories) => {
  return osdUserBrowsingHistories?.has(vehicleKey);
};

// 对齐国内浏览过灰底逻辑
export const getIsOsdUserBrowsedByProps = props => {
  const { osdUserBrowsingHistories } = props;
  return getIsOsdUserBrowsed(
    props?.section?.vehicleKey,
    osdUserBrowsingHistories,
  );
};
