import { xMergeStyles, XView as View, XViewExposure } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import VendorTag from '../../../ComponentBusiness/VendorTag';
import { Platform } from '../../../Constants/Index';
import { Utils, CarLog } from '../../../Util/Index';
import UITestId from '../../../Constants/UITestID';
import c2xStyles from './vehiclePriceSection.module.scss';

const { getPixel, isAndroid } = BbkUtils;

const styles = StyleSheet.create({
  container: {
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
  },
  // 日均价格样式
  dayAveragePrice: {
    ...font.F_32_10_regular_TripNumberSemiBold,
    color: color.deepBlueBase,
  },
  // 日均货币符号样式
  dayAverageCurrency: {
    color: color.deepBlueBase,
    ...font.F_24_regular,
  },
  // 划线价格样式
  priceOrigin: {
    ...font.F_26_10_regular,
    color: color.C_888888,
    textDecorationLine: 'line-through',
  },
  dayOriginPriceWrap: {
    marginLeft: getPixel(4),
  },
  // 总价样式
  totalPriceNew: {
    ...font.F_26_10_regular,
    color: color.C_888888,
  },
  totalPriceWrapStyle: {
    // marginBottom: getPixel(4),
  },
  // 已售罄样式
  soldOutContainer: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
  },
  soldOutTipText: {
    ...font.F_28_10_regular,
    color: color.darkGrayBorder,
  },
  marketTagsLabelStyleNew: {
    marginBottom: 0,
    height: getPixel(38),
    minHeight: getPixel(38),
  },
  mbf3: {
    marginBottom: -getPixel(3),
  },
});

export interface PriceType {
  price: number;
  originalTotalPrice?: number;
}

interface IVehiclePriceSectionProps {
  price: number;
  descText: string;
  originPrice?: PriceType;
  minTotalPrice?: number;
  minTotalPriceDesc?: string;
  minTotalPriceOtherDesc?: string;
  totalPricePress?: () => void;
  onTotalPriceLayOut?: (e: LayoutChangeEvent, offsetY?: number) => void;
  marketTags?: Array<VendorTagType>;
  isSoldOut?: boolean;
  style?: CSSProperties;
  containerStyle?: CSSProperties;
}

const VehiclePriceSection = (props: IVehiclePriceSectionProps) => {
  const {
    price,
    descText = '日均',
    originPrice,
    minTotalPrice,
    minTotalPriceDesc,
    minTotalPriceOtherDesc,
    totalPricePress,
    onTotalPriceLayOut = Utils.noop,
    marketTags = [],
    isSoldOut = false,
    style,
    containerStyle,
  } = props;

  if (isSoldOut) {
    return (
      <View style={xMergeStyles([styles.container, containerStyle, style])}>
        <View style={styles.soldOutContainer}>
          <BbkText style={styles.soldOutTipText}>已售罄</BbkText>
        </View>
      </View>
    );
  }

  return (
    <View
      className={c2xStyles.vehiclePriceSectionContainer}
      style={xMergeStyles([styles.container, containerStyle, style])}
    >
      {/* 第一行：日均价格（左）和总价（右） */}
      <View className={c2xStyles.priceRow}>
        {/* 日均价格区域（左侧） */}
        <View className={c2xStyles.dayAveragePriceWrap}>
          <BbkText className={c2xStyles.descText}>{descText}</BbkText>

          <BbkCurrencyFormatter
            price={price}
            currency={Platform.CN_CURRENCY_CODE}
            currencyStyle={styles.dayAverageCurrency}
            priceStyle={styles.dayAveragePrice}
            testID={UITestId.car_testid_comp_vehicle_day_price}
          />
          {originPrice?.price && (
            <BbkCurrencyFormatter
              price={originPrice?.price}
              currency={Platform.CN_CURRENCY_CODE}
              wrapperStyle={styles.dayOriginPriceWrap}
              currencyStyle={styles.priceOrigin}
              priceStyle={styles.priceOrigin}
            />
          )}
        </View>

        {/* 总价区域（右侧） */}
        <View className={c2xStyles.totalPriceContainer}>
          {minTotalPrice >= 0 && (
            <View
              onLayout={onTotalPriceLayOut}
              onPress={totalPricePress}
              testID={UITestId.car_testid_comp_vehicle_price_help}
              className={c2xStyles.totalPriceContent}
            >
              <View className={c2xStyles.totalPriceRow}>
                {!!minTotalPriceDesc && (
                  <BbkText className={c2xStyles.totalPricDesc}>
                    {minTotalPriceDesc}
                  </BbkText>
                )}
                <BbkCurrencyFormatter
                  testID={UITestId.car_testid_comp_vehicle_total_price}
                  price={minTotalPrice}
                  currency={Platform.CN_CURRENCY_CODE}
                  wrapperStyle={styles.totalPriceWrapStyle}
                  currencyStyle={styles.totalPriceNew}
                  priceStyle={styles.totalPriceNew}
                />
                {!!minTotalPriceOtherDesc && (
                  <BbkText className={c2xStyles.otherDesc}>
                    {minTotalPriceOtherDesc}
                  </BbkText>
                )}
                {!!totalPricePress && !!minTotalPrice && (
                  <BbkTouchable
                    onPress={totalPricePress}
                    testID={UITestId.car_testid_pricedesc_help}
                  >
                    <BbkText className={c2xStyles.totalHelpIcon} type="icon">
                      {icon.help}
                    </BbkText>
                  </BbkTouchable>
                )}
              </View>
            </View>
          )}
        </View>
      </View>

      {/* 第二行：营销标签单独一行（左对齐） */}
      {marketTags?.length > 0 && (
        <View className={c2xStyles.marketTagRow}>
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_列表页_车型_营销标签',
              info: { labelCode: marketTags?.[0]?.labelCode },
            })}
            className={c2xStyles.marketTagWrap}
          >
            <VendorTag
              tags={marketTags}
              labelStyle={styles.marketTagsLabelStyleNew}
              textStyle={isAndroid && styles.mbf3}
            />
          </XViewExposure>
        </View>
      )}
    </View>
  );
};

export default VehiclePriceSection;
