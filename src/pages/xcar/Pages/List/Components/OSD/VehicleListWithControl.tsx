import {
  find as lodashFind,
  isEqual as lodashIsEqual,
  get as lodashGet,
  forEach as lodashForEach,
} from 'lodash-es';
import React, { PureComponent } from 'react';
import { XView as View } from '@ctrip/xtaro';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import c2xStyles from './vehicleListWithControlC2xStyles.module.scss';
import VehicleList, { VehicleListProps } from './VehicleList';
import {
  getGroupNameByIndex,
  packageSections,
  getVendorIsCreditRent,
  getVendorListEnterData,
  getMinDPriceData,
} from '../../../../State/List/VehicleListMappers';
import {
  setExposureKey,
  getShowLikeLabel,
} from '../../../../State/List/Mappers';
import { Utils, CarLog } from '../../../../Util/Index';
import Texts from '../../Texts';
import { UITestID } from '../../../../Constants/Index';
import { getVehicleList } from '../../../../Global/Cache/ListResSelectors';

const noop = () => {};
interface VehicleListWithControlProps extends VehicleListProps {
  maxIndex?: number;
  minIndex?: number;
  index?: number;
  showMax?: number;
  progress?: number;
  listData?: any;
  lastNextIndexObj?: any;
  initialNumToRender?: number;
  theme?: any;
  scrollViewHeight?: number;
  locationDatePopVisible: boolean;
  setActiveGroupId: (args: any) => void;
  setVehPopData: (args: any) => void;
  refFn?: (args: any) => void;
  filterNoResult?: boolean;
  vehicleList?: any;
  filters?: any;
  showFilteredProgress?: boolean;
  priceCount?: number;
  vehCount?: number;
  closeTipPop?: () => void;
  isRecommend?: boolean;
  isRefactor?: boolean;
  osdUserBrowsed?: object;
  ipollPosNum?: number;
  ipollGroup?: string;
  sceneid?: string;
  ipollLogData: any;
}

interface VehicleListWithControlState {
  index: number;
}

export const getLogKey = (
  vendor,
  section,
  vendorIndex?: any,
  activeGroupId?: string,
) => {
  const {
    vehicleIndex,
    vehicleCode,
    vehicleKey,
    minDPrice = '',
    minTPrice = '',
    lowestPrice = '',
    hotType,
    vendorPriceList,
    groupLowestPrice,
  } = section || {};
  const showLikeLabel = getShowLikeLabel({
    activeGroupId,
    vehicleIndex,
    hotType,
  });
  const { hasOptimize = false, firstVendor } = getVendorListEnterData(
    section,
    {
      vehicleIndex,
      vehicleCode,
    },
    true,
    undefined,
    true,
  );
  const vehicleList = getVehicleList();
  // 获取车型数据信息
  const curVehInfo =
    lodashFind(vehicleList, {
      vehicleCode,
    }) || {};
  const curVendor = vendor || firstVendor;
  const curVendorRef = curVendor?.reference;
  const isEasyLife = curVendor?.easyLifeInfo?.isEasyLife || false;
  const isCreditRent = getVendorIsCreditRent(curVendor);
  const minDVendorPriceItem = getMinDPriceData(vendorPriceList);
  return {
    vehicleIndex,
    vehicleCode,
    vehicleKey,
    realGroupId: curVehInfo?.groupCode,
    realGroupName: curVehInfo?.groupName,
    vehicleTags: curVehInfo?.vehicleTags,
    isEasyLife,
    isCreditRent,
    isOptimize: hasOptimize,
    minDPrice: Utils.isCtripIsd() ? minDPrice : lowestPrice,
    minTPrice,
    vechicleLable: showLikeLabel ? '你可能喜欢' : '', // 车型标签，记录猜你喜欢字样
    carGroupMinDPrice: groupLowestPrice,
    minDPricePStoreCode: minDVendorPriceItem?.reference?.pStoreCode,
    skuId: curVendorRef?.skuId,
    pStoreId: curVendorRef?.pStoreCode,
    rStoreId: curVendorRef?.rStoreCode,
    vendorId: curVendorRef?.bizVendorCode,
    ifPriceAdjusted: !!curVendorRef.adjustInfo, // 是否有调价
  };
};
export default class VehicleListWithControl extends PureComponent<
  VehicleListWithControlProps,
  VehicleListWithControlState
> {
  static defaultProps = {
    maxIndex: 0,
    minIndex: 0,
    index: 0,
    listData: {},
    initialNumToRender: 12,
    theme: {
      scrollBackgroundColor: color.grayBg,
    },
    showMax: 3,
    lastNextIndexObj: {},
    scrollViewHeight: 0,
    vehicleList: [],
  };

  cacheList = [];

  cacheStyle = [];

  scrollerRef = {};

  vehicleListScrollRef = [];

  isScrolling = false;

  cachePlaceHolder = null;

  animating = false;

  exposureKeyArray = [];

  initGroupIndex: number;

  constructor(props) {
    super(props);
    const { index } = props;
    this.state = {
      index,
    };
    this.initGroupIndex = index;
    props.refFn(this);
  }

  // eslint-disable-next-line
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { index, listData, filters } = this.props;
    if (nextProps.index !== index) {
      this.setState({
        index: nextProps.index,
      });
    }
    if (!lodashIsEqual(nextProps.listData, listData)) {
      this.renderVehicleListDom(nextProps.listData, nextProps.lastNextIndexObj);
    }

    // 可能从筛选无结果变成筛选有结果，当进度条走到100%、车型列表页有资源出现时，toast提醒“已为您找到符合条件的车型”
    const nextSections = packageSections(
      lodashGet(nextProps.listData[index], 'productList'),
    );

    const nowSections = packageSections(
      lodashGet(listData[index], 'productList'),
    );
    if (
      filters?.length > 0 &&
      filters?.length === nextProps.filters?.length &&
      nextSections.length > 0 &&
      nowSections.length === 0
    ) {
      // 已为您找到符合条件的车型
      BbkToast.show(Texts.newVehiclesPush);
    }
  }

  componentDidUpdate() {
    const { showFilteredProgress } = this.props;
    const { index } = this.state;
    if (showFilteredProgress) {
      const { scrollToTop = noop } = this.vehicleListScrollRef[index] || {};
      scrollToTop();
    }
  }

  getVehicleListProps = (index, newLastNextIndexObj) => {
    const {
      minIndex,
      maxIndex,
      lastNextIndexObj,
      initialNumToRender,
      theme,
      showMax,
      scrollUpCallback,
      scrollDownCallback,
      scrollCallback = Utils.noop,
      scrollEndCallback,
      scrollViewHeight,
      showFilteredProgress,
      progress,
      priceCount,
      vehCount,
    } = this.props;

    const $lastNextIndexObj = newLastNextIndexObj || lastNextIndexObj || {};
    const { last, next } = $lastNextIndexObj[index] || {};
    const isTop = index <= minIndex || last < minIndex;
    // const noRefresh = isTop && getSharkValue('listCombine_toTheTop');
    const noRefresh = isTop && ' ';
    const noMoreContent = '到底了';
    const lastGroupName = getGroupNameByIndex(last);
    const nextGroupName = getGroupNameByIndex(next);
    const pullIcon = !isTop ? '\ue0b5' : ' ';
    const noticeContent = `上拉查看${nextGroupName}`;

    const loadingContent = `松开查看${nextGroupName}`;

    const pullStartContent = noRefresh || `下拉查看${lastGroupName}`;
    const pullContinueContent = noRefresh || `松开查看${lastGroupName}`;
    // console.log('noMore ', index, next, maxIndex, $lastNextIndexObj)
    const noMore = index >= maxIndex || next > maxIndex;
    const fixScrollCallback = () => {
      scrollCallback();
    };

    return {
      index,
      key: index,
      stickySectionHeadersEnabled: true,
      showMax,
      initialNumToRender,
      endFillColor: theme.scrollBackgroundColor,
      /**
       * scroll handler props
       */
      isTop,
      refFn: ref => {
        this.scrollerRef[index] = ref;
      },
      scrollUpCallback,
      scrollDownCallback,
      scrollCallback: fixScrollCallback,
      scrollEndCallback,
      /**
       * refresh control props
       */
      pullIcon,
      pullStartContent,
      pullContinueContent,
      refreshingIcon: pullIcon,
      refreshingContent: pullContinueContent,
      /**
       * load control props
       */
      noMore,
      noticeContent,
      loadingContent,
      noMoreContent,
      /**
       * 曝光埋点
       */
      onViewableItemsChanged: this.setExposureData,
      scrollViewHeight,
      isHideLoginItem: true,
      showFilteredProgress,
      getLogKey,
      isShowBottomLoadingText: progress !== 1,
      progress,
      priceCount,
      vehCount,
    };
  };

  setExposureData = ({ changed }) => {
    lodashForEach(changed, ({ item, section: iSection }) => {
      try {
        const section = iSection || item;
        if (item.data) {
          lodashForEach(item.data[0], (vendor, index) => {
            const key = getLogKey(vendor, section, index);
            setExposureKey(key);
          });
        } else {
          const key = getLogKey(item[0], section, 0);
          setExposureKey(key);
        }
      } catch (e) {
        // eslint-disable-next-line
        console.warn('setExposureData error', item.data, item);
      }
    });
  };

  resetCache = () => {
    this.cacheList = [];
  };

  selectFiltersExposure = data => {
    const { activeGroupId, activeGroupName, filters = [] } = this.props;
    const { vehicleLength } = data;
    const exposureData = {
      activeGroupId,
      filters,
    };
    const exposureKey = JSON.stringify(exposureData);
    const isExposeured =
      this.exposureKeyArray.findIndex(e => e === exposureKey) > -1;
    const isHasFilter = filters.length > 0;
    if (!isExposeured && isHasFilter) {
      this.exposureKeyArray.push(exposureKey);

      CarLog.LogCode({
        name: '点击_列表页_选中筛选_曝光',

        data: filters,
        activeGroupId,
        activeGroupName,
        vehicleLength,
      });
    }
  };

  scrollRefFn = (ref, index) => {
    this.vehicleListScrollRef[index] = ref;
  };

  renderVehicleListDom = (newListData?: any, lastNextIndexObj?: any) => {
    const { index } = this.state;
    const {
      minIndex,
      maxIndex,
      activeGroupId,
      activeGroupName,
      filterNoResult,
      listData,
      filters = [],
      setVehPopData,
      pageRef,
      setVendorListModalData,
      scrollViewHeight,
      closeTipPop,
      scrollDownCallback,
      isRecommend,
      isRefactor,
      osdUserBrowsed,
      ipollPosNum,
      ipollGroup,
      sceneid,
      ipollLogData,
    } = this.props;
    if (index < minIndex || index > maxIndex) {
      return null;
    }
    const $listData = newListData || listData;

    if (newListData) {
      this.cacheList = [];
    }

    const cache = this.cacheList[index];
    const isHideFooter = filters.length > 0;
    if (!cache || filterNoResult || isHideFooter !== cache.props.isHideFooter || ipollPosNum) {
      const curProductList = lodashGet($listData[index], 'productList');
      const unUrlProduct = lodashGet($listData[index], 'unUrlProduct');
      this.cacheList[index] = (
        <VehicleList
          // @ts-ignore
          sections={packageSections(curProductList)}
          unUrlProduct={unUrlProduct}
          scrollRefFn={ref => this.scrollRefFn(ref, index)}
          activeGroupId={activeGroupId}
          activeGroupName={activeGroupName}
          isHideFooter={isHideFooter}
          selectFiltersExposure={this.selectFiltersExposure}
          {...this.getVehicleListProps(index, lastNextIndexObj)}
          setVehPopData={setVehPopData}
          pageRef={pageRef}
          resetCache={this.resetCache}
          setVendorListModalData={setVendorListModalData}
          filterNoResult={filterNoResult}
          style={{ height: scrollViewHeight }}
          closeTipPop={closeTipPop}
          scrollDownCallback={scrollDownCallback}
          isRecommend={isRecommend}
          isRefactor={isRefactor}
          osdUserBrowsed={osdUserBrowsed}
          ipollPosNum={ipollPosNum}
          ipollGroup={ipollGroup}
          sceneid={sceneid}
          ipollLogData={ipollLogData}
        />
      );
    }
    return this.cacheList[index];
  };

  render() {
    return (
      <View
        testID={UITestID.car_testid_list_osd_carVehicleList}
        className={
          Utils.isCtripOsd() ? c2xStyles.listWrapNew : c2xStyles.listWrap
        }
      >
        {this.renderVehicleListDom()}
      </View>
    );
  }
}
