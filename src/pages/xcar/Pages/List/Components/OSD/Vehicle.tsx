import { get as lodashGet } from 'lodash-es';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, useState, useMemo } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon, color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BBkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';

import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { SimpleVehicleDesc } from '../../../../ComponentBusiness/CarVehicleDescribe';
import BbkVehicleName, {
  VehicleNameType,
} from '../../../../ComponentBusiness/CarVehicleName';
import BbkCarImage from '../../../../ComponentBusiness/CarImage';
import BbkCarRightIcon from '../../../../ComponentBusiness/RightIcon';
import VendorListEnterOSD from '../../../../ComponentBusiness/VendorListEnter/src/VendorListEnterOSD';
import Vendor from '../../../../Containers/VendorContainer';
import SearchConditionTip from '../../../../Containers/SearchConditionTipContainer';
import OriginalOrderLabel from '../OriginalOrderLabel';
import {
  CREDITRENT_LABELCODE_TYPES,
  ItemLabelCodeType,
} from '../../../../ComponentBusiness/PackageIncludes';
import { VehicleListStyle as style } from '../../Styles';
import Texts from '../../Texts';
import {
  CarLog,
  Utils,
  AppContext,
  CarStorage,
  CarABTesting,
  GetABCache,
} from '../../../../Util/Index';
import { StorageKey, ListEnum, UITestID } from '../../../../Constants/Index';
import {
  getVendorGroupName,
  PropsType as VehiclePricePropsType,
} from '../VehiclePriceGroup';
import UrlFilterNoMatch from '../UrlFilterNoMatch';
import {
  getVendorListData,
  getVehicleItemData,
  getVendorListEnterData,
  getBreakLabel,
  getMinDPriceData,
} from '../../../../State/List/VehicleListMappers';
import {
  getTenancyDays,
  getSelectedFilterString,
  getShowLikeLabel,
  getPDTimeSubStrategyTip,
} from '../../../../State/List/Mappers';
import { Enums } from '../../../../ComponentBusiness/Common';
import { RecommendTip } from '../RecommendVehicleTip';
import {
  getRequestId,
  getBaseResData,
} from '../../../../Global/Cache/ListResSelectors';
import { RecommendType } from '../../../../Constants/ListEnum';
import { ListReqAndResData } from '../../../../Global/Cache/Index';
import {
  getIsOsdUserBrowsed,
  getIsOsdUserBrowsedByProps,
  isOsdUserBrowsed,
} from '../../Method';
import AISortTagsOSD from '../../../../ComponentBusiness/AISortTagsOSD';

const {
  selector,
  htmlDecode,
  getPixel,
  getProcImageUrl,
  ProcImageParamsType,
  useMemoizedFn,
} = BbkUtils;
const { isShowSearchConditionTip } = AppContext.UrlQuery;

interface IVehicleProps extends VehiclePricePropsType {
  locationAndDate?: string;
  expandLocationAndDate?: string;
  age?: number | string;
  activeGroupId?: string;
  vehicleIndex?: number;
  hotType?: any;
  rentCenter?: any;
  storeList?: any;
  setVendorListModalData?: (data) => void;
  setTimeOutPopData?: (data) => void;
  onLayout?: (e) => void;
  theme?: any;
  vehicleList?: any;
  data?: any;
  soldOutList?: any;
  selectedFilters?: any;
  setTotalPriceModalData?: (data) => void;
  setTipPopData?: (data) => void;
  closeTipPop?: () => void;
  fetchListRecommendCallback?: (data) => void;
  unUrlProduct?: boolean;
  progressIsFinish?: boolean;
  priceGroupIndex?: any;
  setVehPopData?: any;
  setNeedSubmitSecondLog?: any;
  progress?: number;
  priceCount?: number;
  vehCount?: number;
  isRecommend?: boolean;
  isDifferentLocation?: boolean;
  recommendType?: string;
  pickupDateTime?: string;
  dropOffDateTime?: string;
  pickupLocationCode?: string;
  dropOffLocationCode?: string;
  population?: number;
  isRefactor?: boolean;
  recommendPickReturnInfo?: any;
  subStrategyType?: number;
  logInfo?: any;
  isNoResultNew?: boolean;
  listVehicleList?: any;
  recommendProductsList?: any;
  listData?: any;
  recommendProductInfo?: any;
  strategyIndex?: any;
  setRecommendType: (data) => void;
  noResultVehicleIndex?: any;
  osdUserBrowsed?: object;
  osdVehicleListIpollData?: {
    sceneid?: string;
    positionNum?: number;
  };
  ipollLogData?: any;
  osdUserBrowsingHistories?: Map<string, object>;
}

const VehicleStyle = StyleSheet.create({
  licenseTagStyle: {
    borderRadius: BbkUtils.getPixel(2),
  },
  vehicleHeaderWrap: {
    borderBottomWidth: 0,
    paddingTop: getPixel(20),
    paddingBottom: getPixel(20),
  },
  labelHeaderWrap: {
    borderBottomWidth: 0,
    paddingTop: getPixel(16),
    paddingBottom: getPixel(20),
  },
  nameTitleTextStyle: {
    ...font.title4BoldStyle,
  },
});

/*eslint-disable */
const isEqualVehicle = (prevProps, nextProps) => {
  const preSection = lodashGet(prevProps, 'section');
  const nextSection = lodashGet(nextProps, 'section');

  const preData = lodashGet(prevProps, 'data');
  const nextData = lodashGet(nextProps, 'data');

  const preUnUrlProduct = lodashGet(prevProps, 'unUrlProduct');
  const nextUnUrlProduct = lodashGet(nextProps, 'unUrlProduct');

  const preSoldOutList = lodashGet(prevProps, 'soldOutList');
  const nextSoldOutList = lodashGet(nextProps, 'soldOutList');

  const preProgressIsFinish = lodashGet(prevProps, 'progressIsFinish');
  const nextProgressIsFinish = lodashGet(nextProps, 'progressIsFinish');

  const preBrowsed = lodashGet(prevProps, 'osdUserBrowsed');
  const nextBrowsed = lodashGet(nextProps, 'osdUserBrowsed');
  const { vehicleKey } = nextSection;
  const preIsBrowsed = isOsdUserBrowsed(vehicleKey, preBrowsed);
  const nextIsBrowsed = isOsdUserBrowsed(vehicleKey, nextBrowsed);

  const preLogInfo = lodashGet(prevProps, 'logInfo');
  const nextLogInfo = lodashGet(nextProps, 'logInfo');

  // 之前的车型报价是否已浏览
  const preIsUserBrowsed = getIsOsdUserBrowsedByProps(prevProps);
  // 之后的车型报价是否已浏览
  const nextIsUserBrowsed = getIsOsdUserBrowsedByProps(nextProps);

  return (
    preSection === nextSection &&
    preData === nextData &&
    preUnUrlProduct === nextUnUrlProduct &&
    preSoldOutList === nextSoldOutList &&
    preProgressIsFinish === nextProgressIsFinish &&
    preIsBrowsed === nextIsBrowsed &&
    preLogInfo === nextLogInfo &&
    preIsUserBrowsed === nextIsUserBrowsed
  );
};

const isEqualVehicleForListPage = (prevProps, nextProps) => {
  const preSection = lodashGet(prevProps, 'section');
  const nextSection = lodashGet(nextProps, 'section');

  const preSectionStr = JSON.stringify(preSection);
  const nextSectionStr = JSON.stringify(nextSection);

  const preData = lodashGet(prevProps, 'data');
  const nextData = lodashGet(nextProps, 'data');

  const preDataStr = JSON.stringify(preData);
  const nextDataStr = JSON.stringify(nextData);

  const preUnUrlProduct = lodashGet(prevProps, 'unUrlProduct');
  const nextUnUrlProduct = lodashGet(nextProps, 'unUrlProduct');

  const preSoldOutList = lodashGet(prevProps, 'soldOutList');
  const nextSoldOutList = lodashGet(nextProps, 'soldOutList');

  return (
    preSectionStr === nextSectionStr &&
    preDataStr === nextDataStr &&
    preUnUrlProduct === nextUnUrlProduct &&
    preSoldOutList === nextSoldOutList
  );
};

export const Vehicle = memo(
  (props: IVehicleProps) => {
    const {
      data,
      section,
      theme,
      onLayout = Utils.noop,
      priceGroupItem,
      item,
      unUrlProduct,
      progressIsFinish,
      vehicleList,
      activeGroupId,
      setVendorListModalData,
      soldOutList,
      selectedFilters,
      setTotalPriceModalData = Utils.noop,
      setTipPopData = Utils.noop,
      closeTipPop = Utils.noop,
      priceCount,
      vehCount: vehicleCount,
      isRecommend,
      isDifferentLocation,
      recommendType,
      pickupDateTime,
      dropOffDateTime,
      pickupLocationCode,
      dropOffLocationCode,
      age,
      population,
      isRefactor,
      recommendPickReturnInfo,
      subStrategyType,
      logInfo,
      isNoResultNew, // 新版出境无结果聚合标志
      listVehicleList = [],
      recommendProductsList = [],
      listData,
      recommendProductInfo,
      strategyIndex,
      fetchListRecommendCallback,
      setRecommendType,
      noResultVehicleIndex,
      osdUserBrowsed,
      osdVehicleListIpollData,
      ipollLogData,
      osdUserBrowsingHistories,
      ...otherData
    } = props;
    const [isShowPriceTip, setIsShowPriceTip] = useState(false);
    const {
      vehicleCode,
      vehicleKey,
      hotType,
      isSpecialized,
      vehicleIndex,
      isGroup,
      modifySameVehicle,
      recommendInfo,
    } = section;
    const { vendorPriceList = [], productTopInfo } = data;
    // 置顶分页兼容处理
    const isBrowseProduct = CarABTesting.isListInPage()
      ? productTopInfo
      : data.isBrowseProduct;
    const isUrlTop = CarABTesting.isListInPage()
      ? productTopInfo === 2
      : data.isUrlTop;
    let vehicleItemData = null;
    if (isNoResultNew) {
      vehicleItemData = getVehicleItemData(
        vehicleCode,
        hotType,
        isSpecialized,
        !isGroup,
        modifySameVehicle,
        undefined,
        listVehicleList,
      );
    } else {
      vehicleItemData = getVehicleItemData(
        vehicleCode,
        hotType,
        isSpecialized,
        !isGroup,
        modifySameVehicle,
      );
    }
    const { vehicleHeader, vehicleDesc } = vehicleItemData;
    const {
      vehicleName,
      groupName,
      groupId,
      isSimilar,
      isHotLabel,
      licenseLabel,
      licenseType,
      vehicleTags,
    } = vehicleHeader;

    const {
      imgUrl,
      vehicleLabelsGroupName,
      vehicleLabelsHorizontal,
      vehicleLabels,
      fuel,
      driveType,
    } = vehicleDesc;
    const vehicleAll = useMemo(() => {
      return [
        ...vehicleLabelsGroupName,
        ...vehicleLabelsHorizontal,
        ...vehicleLabels,
      ];
    }, [vehicleLabelsGroupName, vehicleLabels, vehicleLabelsHorizontal]);

    // 20200303 兼容服务端返回的” “, 后续让服务端优化
    let similarCommentDesc = vehicleDesc.similarCommentDesc || '';
    similarCommentDesc = similarCommentDesc.replace(/[“ ”]/g, '');

    // 组装多年款数据
    const packagePriceGroup = () => {
      if (priceGroupItem && priceGroupItem.length > 0) {
        priceGroupItem.sort((a, b) => a?.groupSort - b?.groupSort);
        const newList = priceGroupItem.map(groupItem => {
          const {
            vehicleCode: itemVehicleCode,
            hotType: itemHotType,
            isSpecialized: itemIsSpecialized,
          } = groupItem;
          const curVehicleItemData = getVehicleItemData(
            itemVehicleCode,
            itemHotType,
            itemIsSpecialized,
            false,
          );
          const vehicleInfo = { vehicleCode, vehicleIndex };
          const vendorListData = getVendorListData(
            groupItem.vendorPriceList,
            vehicleInfo,
          );
          const newGroupItem = {
            ...groupItem,
            ...curVehicleItemData,
            data: [vendorListData],
          };
          return newGroupItem;
        });
        return newList;
      }
      return priceGroupItem;
    };

    const priceGroupList = packagePriceGroup();

    const getGroupDescList = () => {
      const groupDescList = [];
      if (priceGroupList && priceGroupList.length > 0) {
        priceGroupList.forEach(groupItem => {
          groupDescList.push(
            getVendorGroupName({
              displacement: groupItem.displacement,
              style: groupItem.style,
            }),
          );
        });
      }
      return groupDescList;
    };

    const groupDescList = getGroupDescList();

    const moreGroupText = groupDescList.length > 2 ? Texts.moreText : '';

    const showLikeLabel = getShowLikeLabel({
      activeGroupId,
      vehicleIndex,
      hotType,
    });

    const vehicleInfo = { vehicleCode, vehicleIndex };
    // 海外车型页面，获取车型数据时，同时获取供应商报价信息会造成页面卡顿
    // 经过排查代码，确认返回的vendorList数据，仅需要第一个供应商数据
    const vendorListEnterData = getVendorListEnterData(
      data,
      vehicleInfo,
      undefined,
      undefined,
      Utils.isCtripOsd(),
    );
    const hasCreditRent = CREDITRENT_LABELCODE_TYPES.includes(
      lodashGet(vendorListEnterData, 'tagInfo.labelCode'),
    );

    let topVendor = null; // url置顶的车型供应商
    if (isUrlTop) {
      topVendor = (getVendorListData(vendorPriceList, vehicleInfo) || [])[0];
    }

    const VehicleContainer: any = isUrlTop ? View : BbkTouchable;

    const handleNewRecommendToast = useMemoizedFn(
      async (recommendType, recommendPickReturnInfo, subStrategyType) => {
        const requestId = getRequestId();
        let hasToastVehicle = [];
        const result = await CarStorage.loadAsync(StorageKey.RECOMMEND_TOAST);
        if (result) {
          try {
            hasToastVehicle = JSON.parse(result);
          } catch (e) {}
        }

        if (!hasToastVehicle?.includes(`${vehicleCode}-${requestId}`)) {
          let toastTip = '';
          switch (recommendType) {
            case RecommendType.PDTime:
              toastTip = getPDTimeSubStrategyTip({
                recommendPickReturnInfo,
                subStrategyType,
              });
              break;
            case RecommendType.DiffLocation:
              toastTip = Texts.recommendVehicleToastSameArea;
              break;
            case RecommendType.PDArea:
              toastTip = Texts.recommendVehicleToastPDArea;
              break;
            default:
              break;
          }
          if (toastTip) Toast.show(toastTip);
          hasToastVehicle.push(`${vehicleCode}-${requestId}`);
          CarStorage.save(
            StorageKey.RECOMMEND_TOAST,
            JSON.stringify(hasToastVehicle),
          );
        }
      },
    );

    const handleRecommendToast = useMemoizedFn(async () => {
      const requestId = getRequestId();
      let hasToastVehicle = [];
      const result = await CarStorage.loadAsync(StorageKey.RECOMMEND_TOAST);
      if (result) {
        try {
          hasToastVehicle = JSON.parse(result);
        } catch (e) {}
      }

      if (!hasToastVehicle?.includes(`${vehicleCode}-${requestId}`)) {
        let toastTip = '';
        switch (recommendType) {
          case RecommendType.PDTime:
            toastTip = getPDTimeSubStrategyTip({
              recommendPickReturnInfo,
              subStrategyType,
            });
            break;
          case RecommendType.DiffLocation:
            toastTip = Texts.recommendVehicleToastSameArea;
            break;
          case RecommendType.PDArea:
            toastTip = Texts.recommendVehicleToastPDArea;
            break;
          default:
            break;
        }
        if (toastTip) Toast.show(toastTip);
        hasToastVehicle.push(`${vehicleCode}-${requestId}`);
        CarStorage.save(
          StorageKey.RECOMMEND_TOAST,
          JSON.stringify(hasToastVehicle),
        );
      }
    });

    const handleVehiclePress = () => {
      const priceListLen = item.length;
      let positionNum = osdVehicleListIpollData?.positionNum;
      const sceneid = osdVehicleListIpollData?.sceneid;
      if (positionNum === -1) {
        positionNum = priceListLen;
      }
      // 组装ipoll传参数据
      const ipollLogDataAll = { ...ipollLogData, vehicleCode };
      // 组装vendor组件所需数据
      const newItem = getVendorListData(item, vehicleInfo);
      setVendorListModalData({
        visible: true,
        data: {
          item: newItem,
          section: { ...section, ...vehicleItemData },
          priceGroupItem: priceGroupList,
          priceListLen,
          logInfo,
          showLikeLabel,
          sceneid,
          positionNum,
          ipollLogDataAll,
          ...otherData,
        },
      });
      closeTipPop();
      const vendorIds = newItem
        ?.map(items => items?.reference?.vendorCode)
        ?.join(',');
      const { klbVersion } = item?.[0]?.reference || {};
      const minDVendorPriceItem = getMinDPriceData(vendorPriceList);
      let aiRecommendCodeName = [];
      if (isSupportAiSort) {
        aiRecommendCodeName = vehicleTags?.map(
          tagItem =>
            `${tagItem.type}_${tagItem.tag}${tagItem.subTag ? `${tagItem.subTag}` : ''}`,
        );
      }
      CarLog.LogCode({
        name: '点击_列表页_打开报价详情弹层',
        if_viewd: isBrowsed,
        aiRecommendCodeName,
        vehicleCode, // 车型id
        vehicleName,
        vehicleIndex, // 车型位置
        ...logInfo,
        selectedFilters: getSelectedFilterString(selectedFilters), // 筛选项
        isCreditRent: hasCreditRent,
        vechicleLable: showLikeLabel ? '你可能喜欢' : '', // 车型标签，记录猜你喜欢字样
        batchNo: section?.batchNo, // 露出批次
        vendorIds,
        currentTotalPrice: section.minTPrice,
        klbVersion,
        fuelType: fuel,
        isFourDrive: driveType,
        isRefactor: isRefactor ? '1' : '',
        minDPricePStoreCode: minDVendorPriceItem?.reference?.pStoreCode,
        minDPrice: section?.lowestPrice,
        carGroupMindPrice: section?.groupLowestPrice,
        requestId: getRequestId(),
        vehicleGroupId: groupId,
        vehicleGroupName: groupName,
        sortNum: section?.sortNum,
        info: {
          vehicleKey,
          batchCode: getBaseResData()?.baseResponse?.code,
          currency: vendorPriceList?.[0]?.priceInfo?.currentCurrencyCode,
          isZhima: vendorPriceList?.some(i =>
            i?.allTags?.some(
              f => f?.labelCode === ItemLabelCodeType.DEPOSITFREE,
            ),
          )
            ? 1
            : 0,
        },
      });

      AppContext.setVehicleLogInfo({
        batchCode: getBaseResData()?.baseResponse?.code,
        vehicleKey,
      });

      if (isRecommend) {
        if (isNoResultNew) {
          setRecommendType(recommendProductInfo?.recommendType);
          const newRes = listData?.recommendProductsList?.[strategyIndex];
          const firstRecommendInfo = newRes?.recommendProductInfo;
          const firstRecommendation = firstRecommendInfo?.recommendation;
          const tempInfo = {
            isRecommendNoResult: !(newRes?.allVehicleCount > 0),
            recommendAllVehicleCount: newRes?.allVehicleCount,
            recommendAllVendorPriceCount: newRes?.allVendorPriceCount,
            recommendTitle: firstRecommendation?.tilte,
            recommendDesc: firstRecommendation?.subTitle,
            recommendTip: firstRecommendation?.recMessage,
            recommendButtonTitle: firstRecommendation?.recTitle, // 无结果操作按钮文案
            recommendType: firstRecommendInfo?.recommendType, // 车型推荐类型
            subStrategyType: firstRecommendInfo?.subStrategyType, // 车型推荐类型
            recUniqsign: newRes?.recUniqsign, // 推荐接口对应的缓存标识字段
            pickUpAvailableTime: firstRecommendation?.pickUpAvailableTime,
            returnAvailableTime: firstRecommendation?.returnAvailableTime,
            availableLocationCode: firstRecommendInfo?.availableLocationCode,
            availableLocation: firstRecommendInfo?.availableLocation,
            longitude: firstRecommendInfo?.longitude,
            latitude: firstRecommendInfo?.latitude,
            cid: firstRecommendInfo?.cid,
            noResultRecTip: listData?.noResultRecTip, // 无结果聚合顶部文案
          };
          fetchListRecommendCallback?.(tempInfo);
          // 大多数逻辑针对单一数据源，未对数组进行处理，在新版无结果中聚合中由于是一个数组，所以需要将选中车型的数据塞到缓存中
          const activeRecommend = recommendProductsList.find(
            (recommendProductsItem: any) =>
              recommendProductsItem?.recommendProductInfo?.recommendType ===
              recommendProductInfo?.recommendType,
          );
          if (
            listData?.recommendProductInfo?.recommendType !==
            activeRecommend?.recommendProductInfo?.recommendType
          ) {
            if (activeRecommend?.recommendProductInfo) {
              ListReqAndResData.setData(
                ListReqAndResData.keyList.listProductRes,
                {
                  ...listData,
                  ...activeRecommend,
                  extras: listData.extras,
                },
              );
            }
          }
          CarLog.LogCode({
            name: '点击_列表页_补偿车型',
            info: {
              isNoresultRec: isRecommend && isNoResultNew ? '1' : '0',
              recommendType,
              distanceIndex: recommendProductInfo?.recommendation?.recMessage,
              vehicleCode: vehicleHeader.vehicleCode,
              recommendProductInfo,
              pickupDateTime,
              dropOffDateTime,
              pickupLocationCode,
              dropOffLocationCode,
              age,
              requestId: getRequestId(),
              vehicleIndex: noResultVehicleIndex,
            },
          });
          handleNewRecommendToast(
            recommendProductInfo?.recommendType,
            tempInfo,
            firstRecommendInfo?.subStrategyType,
          );
        } else {
          CarLog.LogCode({
            name: '点击_列表页_补偿车型',

            isDifferentLocation,
            recommendType,
            vehicleCode,
            vehicleIndex,
            info: {
              pickupDateTime,
              dropOffDateTime,
              pickupLocationCode,
              dropOffLocationCode,
              age,
              population,
            },
          });
          handleRecommendToast();
        }
      }
    };

    const totalPricePress = useCallback(() => {
      const priceListLen = item.length;
      // 组装vendor组件所需数据
      const newItem = getVendorListData(item, vehicleInfo);
      const totalPriceNote = Texts.totalPriceNote;
      const days = getTenancyDays();
      setTotalPriceModalData({
        visible: true,
        data: {
          type: Enums.TotalPriceModalType.List,
          content: totalPriceNote,
          title: Texts.totalPriceNoteTitle,
          footer: Texts.totalPriceNoteFooter,
          dailyPriceUnit: Texts.dailyPriceUnit(days),
          dailyPriceName: Texts.dailyPriceName(days),
          totalPriceName: Texts.totalPriceName,
          totalPriceContain: Texts.totalPriceContain,
          item: newItem,
          section: { ...section, ...vehicleItemData },
          priceGroupItem: priceGroupList,
          priceListLen,
          showLikeLabel,
          ...otherData,
          dayPrice: section.minDPrice || 0,
          totalPrice: section.minTPrice || 0,
          recommendPriceTitle:
            vendorPriceList.length > 1 ? Texts.recommendPriceTitle : '',
        },
      });
      closeTipPop();
      CarLog.LogCode({ name: '点击_列表页_车型总价提示' });
    }, [item]);

    // 判断该车型下的所有报价是否全部已售罄
    const validateIsSoldOut = () => {
      let isFlag = false;
      if (soldOutList && soldOutList.length > 0) {
        isFlag = true;
        /* eslint-disable */
        for (let i = 0; i < section.vendorPriceList.length; i++) {
          const vendorItem = section.vendorPriceList[i];
          const curKey = Utils.getProductKey(vendorItem?.reference);
          if (!soldOutList.includes(curKey)) {
            isFlag = false;
            break;
          }
        }
      }
      return isFlag;
    };

    const isSoldOut = validateIsSoldOut();
    const newLabel = vehicleLabelsHorizontal.concat(vehicleLabels);
    const { firstLabels = [], secondLabels = [] } = isRefactor
      ? getBreakLabel(newLabel)
      : {};

    let totalPriceTipWidth = 0; // 总价的宽度 用于设置总价提示的X轴坐标
    let vehicleHeight = 0; // 车型的总高度，用于设置总价提示的Y轴坐标
    const showTotalPriceTip = useCallback(async () => {
      const didPop = await CarStorage.loadAsync(StorageKey.TOTAL_PRICE_POP);
      const totalPricePopTip = Texts.totalPricePopTip;
      if (Utils.isCtripIsd() && didPop !== 'true' && totalPricePopTip) {
        if (totalPriceTipWidth > 0 && vehicleHeight > 0) {
          setIsShowPriceTip(true);
        }
        let totalPriceOffsetY = 0; // 价格样式的偏移量， 用于设置总价提示的Y轴坐标
        // 置顶车型的偏移量
        if (!!isUrlTop) {
          const { soldOutLabel, vendor = {} } = topVendor || {};
          const isHasAdvertsOrSoldOutLabel =
            !!isUrlTop && (vendor.adverts > 0 || !!soldOutLabel || !!isSoldOut); // 是否有广告 售罄 或者 剩余车辆数
          totalPriceOffsetY = 74 + (isHasAdvertsOrSoldOutLabel ? 34 : 0);
        } else {
          const { hasOptimize, hasEasyLife, tagInfo } =
            vendorListEnterData || {};
          // 计算标签展示的模式 计算偏移量
          const isSingleShowTag = (hasOptimize || hasEasyLife) && tagInfo;
          totalPriceOffsetY =
            68 +
            (!isSingleShowTag && (hasEasyLife || hasOptimize || tagInfo)
              ? 8
              : 0);
        }

        const top = vehicleHeight - getPixel(totalPriceOffsetY);
        const right = totalPriceTipWidth + getPixel(!!isUrlTop ? 57 : 80);
        let style = {};
        if (vehicleHeight > 0) {
          style['top'] = top;
        }
        if (totalPriceTipWidth > 0) {
          style['right'] = right;
        }
        setTipPopData({
          visible: true,
          data: {
            style,
            content: totalPricePopTip,
            type: ListEnum.TipPopType.TotalPrice,
          },
        });
      }
    }, [item]);
    // 计算价格高度时， 通过标签样式返回Y轴偏移量
    const onTotalPriceLayOut = useCallback(
      e => {
        if (
          isShowSearchConditionTip !== 'true' &&
          e.nativeEvent.layout.width > 0
        ) {
          totalPriceTipWidth = e.nativeEvent.layout.width;
          showTotalPriceTip();
        }
      },
      [item],
    );

    // 计算车型View的总高度, 用来设置总价提示的Y轴坐标
    const onVehicleLayout = useCallback(
      e => {
        if (onLayout) {
          onLayout(e);
        }
        if (
          isShowSearchConditionTip !== 'true' &&
          vehicleIndex === 0 &&
          e.nativeEvent.layout.height > 0
        ) {
          vehicleHeight = e.nativeEvent.layout.height;
          showTotalPriceTip();
        }
      },
      [item],
    );
    const imageUrl = getProcImageUrl(imgUrl, ProcImageParamsType.osdList);
    const imageLoadError = useMemoizedFn(error => {
      CarLog.LogImageLoadFail({
        error,
        imageUrl,
        expPoint: ProcImageParamsType.osdList,
        vehicleCode,
      });
    });
    const onCheckFaild = useMemoizedFn(checkInfo => {
      const { width, height, scale, scaleGap } = checkInfo || {};
      CarLog.LogImageLoadFail({
        error: '',
        imageUrl: imgUrl,
        expPoint: ProcImageParamsType.osdList,
        vehicleCode,
        isOnce: true,
        width,
        height,
        scale,
        scaleGap,
      });
    });
    let nameTitleTextStyle = VehicleStyle.nameTitleTextStyle;
    if (isSoldOut) {
      nameTitleTextStyle = { ...nameTitleTextStyle, ...style.soldOutText };
    }
    const simpleVehicleDescTextStyle = useMemo(
      () => ({
        color: isSoldOut ? color.darkGrayBorder : color.C_555555,
        ...font.caption1LightPlus6Style,
        marginBottom: getPixel(10),
      }),
      [isSoldOut],
    );
    // 取消重定｜推荐车型不展示AI标签
    const isSupportAiSort =
      GetABCache.isAiSort2() && !modifySameVehicle && !isRecommend;
    const isHasVehicleTags = isSupportAiSort && vehicleTags?.length > 0;
    const isBrowsed =
      isSupportAiSort && isOsdUserBrowsed(vehicleKey, osdUserBrowsed);

    // 判断车型卡片是否标记为已浏览 - 基于会话级浏览记录 - 对齐国内浏览过灰底逻辑
    const isCardUserBrowsed =
      GetABCache.isAiSort2() &&
      getIsOsdUserBrowsed(vehicleKey, osdUserBrowsingHistories);

    return (
      <View
        testID={UITestID.car_testid_page_list_vehicle_osd}
        style={xMergeStyles([
          {
            backgroundColor: color.white,
          },
          isBrowseProduct && style.browseBorder,
          isCardUserBrowsed && style.userBrowsed,
        ])}
        onLayout={onVehicleLayout}
      >
        {isHasVehicleTags && (
          <AISortTagsOSD
            aiVehicleTags={vehicleTags}
            isSoldOut={isSoldOut}
            isCardUserBrowsed={isCardUserBrowsed}
          />
        )}
        {!!modifySameVehicle && (
          <OriginalOrderLabel
            wrapStyle={style.vehicleOriginalOrderLabel}
            name={Texts.modifySameVehicle}
            labelStyle={style.modifySameVehicle}
          />
        )}
        {isShowSearchConditionTip !== 'true' &&
          Utils.isCtripIsd() &&
          vehicleIndex === 0 &&
          progressIsFinish &&
          isShowPriceTip && <SearchConditionTip />}
        {isBrowseProduct && (
          <View style={style.browseView}>
            <BBkText style={style.browseText}>{Texts.browseNow}</BBkText>
          </View>
        )}
        {vehicleIndex === 0 && unUrlProduct && progressIsFinish && (
          <UrlFilterNoMatch />
        )}
        <VehicleContainer
          onPress={handleVehiclePress}
          disabled={isSoldOut}
          testID={`${UITestID.car_testid_page_list_vehicle_item}_${vehicleIndex}`}
        >
          {!!recommendInfo && (
            <RecommendTip
              recommendInfo={recommendInfo}
              isNoResultNew={isNoResultNew}
            />
          )}
          <View style={style.wrap}>
            <BbkVehicleName
              name={vehicleName}
              groupName={groupName}
              isSimilar={isSimilar}
              isHotLabel={isHotLabel}
              showLikeLabel={!modifySameVehicle && showLikeLabel}
              licenseTag={licenseLabel}
              licenseType={licenseType}
              licenseTagStyle={VehicleStyle.licenseTagStyle}
              type={
                Utils.isCtripIsd()
                  ? 'none'
                  : isNoResultNew
                    ? VehicleNameType.NoResultNew
                    : VehicleNameType.Default
              }
              style={
                isHasVehicleTags
                  ? VehicleStyle.labelHeaderWrap
                  : VehicleStyle.vehicleHeaderWrap
              }
              titleTextStyle={nameTitleTextStyle}
              isSoldOut={isSoldOut}
            />

            <View style={xMergeStyles([style.flexRow, style.vehicleImgWrap])}>
              <BbkCarImage
                source={{ uri: imageUrl }}
                resizeMode="cover"
                style={xMergeStyles([
                  style.vehicleImageNew,
                  isSoldOut && style.soldOutImg,
                ])}
                onError={imageLoadError}
                checkWidth={3}
                checkHeight={2}
                onCheckFaild={onCheckFaild}
              />

              <View
                style={xMergeStyles([style.vehicleDesc, style.vehicleDescNew])}
              >
                <>
                  <SimpleVehicleDesc
                    data={vehicleLabelsGroupName}
                    textStyle={simpleVehicleDescTextStyle}
                  />

                  {isRefactor ? (
                    <>
                      <SimpleVehicleDesc
                        data={firstLabels}
                        textStyle={simpleVehicleDescTextStyle}
                        showBootBtn={false}
                        splitLineStyle={style.splitLineStyle}
                      />

                      <SimpleVehicleDesc
                        data={secondLabels}
                        textStyle={simpleVehicleDescTextStyle}
                        showBootBtn={false}
                        splitLineStyle={style.splitLineStyle}
                      />
                    </>
                  ) : (
                    <SimpleVehicleDesc
                      data={newLabel}
                      textStyle={simpleVehicleDescTextStyle}
                      showBootBtn={false}
                      lastIsBlock={true}
                      splitLineStyle={style.splitLineStyle}
                    />
                  )}
                </>
              </View>
            </View>

            {!isUrlTop && (
              <VendorListEnterOSD
                groupDescList={groupDescList.slice(0, 2)}
                showBorder={!groupDescList.length}
                priceGroupTitle={Texts.optionalYearTitle}
                moreGroupText={moreGroupText}
                groupName={groupName}
                isSoldOut={isSoldOut}
                {...vendorListEnterData}
                totalPricePress={totalPricePress}
                vehicleIndex={vehicleIndex}
                onTotalPriceLayOut={
                  vehicleIndex === 0 && Utils.isCtripIsd()
                    ? onTotalPriceLayOut
                    : Utils.noop
                }
              />
            )}
          </View>

          {!!isUrlTop && (
            <Vendor
              key={`${vehicleCode}_${vehicleIndex}_${-1}`}
              index={0}
              {...topVendor}
              isHideCommentLabel
              section={section}
              vehicleItemData={vehicleItemData}
              vehicleName={vehicleName}
              isHotLabel={isHotLabel}
              isSoldOut={isSoldOut}
              totalPricePress={totalPricePress}
              onTotalPriceLayOut={
                Utils.isCtripIsd() ? onTotalPriceLayOut : Utils.noop
              }
            />
          )}
        </VehicleContainer>
      </View>
    );
  },
  CarABTesting.isListInPage() ? isEqualVehicleForListPage : isEqualVehicle,
);

export const VehicleFooter = memo(
  withTheme(
    ({
      moreNumber,
      setShowMoreArr,
      // showMoreArr,
      vehicleName,
      vehicleIndex,
      theme: themeProps,
    }) => {
      const theme: any = themeProps || {};
      const moreTextStyle = xMergeStyles([
        style.moreText,
        {
          color: theme.blueBase || color.blueBase,
        },
      ]);

      /* eslint-disable */
      const showMoreHandler = useCallback(() => {
        setShowMoreArr(vehicleIndex);
        CarLog.LogCode({
          name: '点击_列表页_查看更多',

          vehicleIndex,
          vehicleName,
        });
      }, [setShowMoreArr, vehicleIndex, vehicleName]);

      return selector(
        moreNumber,
        <BbkTouchable
          onPress={showMoreHandler}
          style={xMergeStyles([
            style.showMoreWrap,
            style.vehicleMarginBottom,
            {
              backgroundColor: theme.backgroundColor || color.white,
              borderTopColor: theme.grayBorder || color.grayBorder,
            },
          ])}
        >
          <BbkCarRightIcon
            text={Texts.listShowMore(moreNumber)}
            style={style.more}
            textStyle={moreTextStyle}
            iconContent={htmlDecode(icon.circleArrowDown)}
            iconStyle={xMergeStyles([moreTextStyle, style.moreIcon])}
          />
        </BbkTouchable>,
        <View style={style.vehicleMarginBottom} />,
      );
    },
  ),
);
