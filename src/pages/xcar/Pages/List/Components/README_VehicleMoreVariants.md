# VehicleMoreVariants 车型展开组件

## 概述

`VehicleMoreVariants` 是一个用于展示车型变种信息的React组件，提供了展开收起动画、Tab切换和灵活的卡片布局功能。

## 主要特性

### 1. 展开收起交互动画
- ✅ 点击"更多年款"可以展开/收起内容
- ✅ 平滑的展开收起动画效果（300ms过渡时间）
- ✅ 展开时显示箭头向上，收起时显示箭头向下
- ✅ 透明度渐变效果

### 2. Tab组件功能
- ✅ 当有多个tab且tab名称不为空时，自动显示tab切换功能
- ✅ 只有一个分类或tab名称为空时，不显示tab组件
- ✅ Tab可以水平滑动
- ✅ 下划线风格的tab设计，激活状态显示蓝色下划线
- ✅ 点击tab可以切换显示对应分类的卡片

### 3. 卡片布局系统
- ✅ **一张卡片**：通铺整个容器宽度
- ✅ **两张卡片**：平分容器，动态计算宽度适应屏幕，内容超出时换行，高度自适应
- ✅ **大于两张卡片**：可滑动展示，露出后面的卡片，支持水平滚动
- ✅ **展开内容铺满卡片**：使用负margin突破右侧内容区域限制，展开内容与车型图片左侧对齐

## API

### Props

```typescript
interface VehicleMoreVariantsProps {
  tabs: VehicleTab[];                    // 车型分类数据
  onVariantPress?: (variant: VehicleVariant) => void;  // 卡片点击回调
  defaultExpanded?: boolean;             // 是否默认展开
}
```

### 数据结构

```typescript
interface VehicleTab {
  id: string;        // 分类唯一标识
  name: string;      // 分类名称（为空时不显示tab）
  variants: VehicleVariant[];  // 该分类下的车型变种
}

interface VehicleVariant {
  id: string;         // 车型唯一标识
  name: string;       // 车型名称
  yearModel: string;  // 年款信息
  plateType: string;  // 外牌信息
  dailyPrice: number; // 日均价格
  totalPrice: number; // 总价起
  vehicleCode: string; // 车型代码
  category?: string;   // 分类（可选）
}
```

## 使用示例

### 基础用法

```typescript
import VehicleMoreVariants from './VehicleMoreVariants';
import { mockVehicleData } from './mockData';

const handleVariantPress = (variant: VehicleVariant) => {
  console.log('选择的车型：', variant);
  // 处理车型选择逻辑
};

<VehicleMoreVariants
  tabs={mockVehicleData}
  onVariantPress={handleVariantPress}
  defaultExpanded={false}
/>
```

### 不同场景示例

#### 1. 多分类带Tab切换
```typescript
const multiCategoryData = [
  {
    id: 'gl8',
    name: 'GL8',
    variants: [/* GL8车型数据 */]
  },
  {
    id: 'gl8_luxury', 
    name: 'GL8豪华版',
    variants: [/* GL8豪华版车型数据 */]
  }
];
```

#### 2. 单分类不显示Tab
```typescript
const singleCategoryData = [
  {
    id: 'single',
    name: '', // 空名称，不显示tab
    variants: [/* 车型数据 */]
  }
];
```

## 动画效果

- **展开动画**：maxHeight从0变为1000px，opacity从0变为1
- **收起动画**：maxHeight从1000px变为0，opacity从1变为0
- **动画时长**：300毫秒
- **动画类型**：线性插值

## 布局逻辑

### 卡片数量处理
1. **1张卡片**：使用 `styles.cardContainer`，宽度100%
2. **2张卡片**：使用 `styles.multiCardsContainer`，ScrollView水平滚动，固定宽度280px
3. **>2张卡片**：使用 `styles.multiCardsContainer`，ScrollView水平滚动，固定宽度280px

### 样式设计
- 卡片宽度：统一使用固定280px（1张、2张、多张卡片都一致）
- 卡片间距：12px
- 容器边距：左右各16px
- Tab样式：下划线风格，间距24px
- 车型卡片：灰色圆角背景(#f8f8f8)，8px圆角，12px内边距
- 动画容器：使用负margin(-232px)与车型图片左侧对齐，铺满整个卡片宽度

## 依赖组件

- `VehicleVariantDisplay`：单个车型卡片显示组件
- `ScrollView`：来自 `@c2x/components/ScrollView` 的水平滚动组件
- `BbkTouchable`、`BbkText`：基础UI组件

## 注意事项

1. 确保mock数据格式正确，特别是 `VehicleTab` 和 `VehicleVariant` 接口
2. 卡片宽度自动适应屏幕尺寸，两张卡片平分容器宽度
3. Tab名称为空时自动隐藏Tab组件
4. 动画高度设置为1000px，足够容纳大部分内容
5. 支持无限数量的车型变种，通过滚动展示
6. 展开内容使用负margin(-232px)与车型图片左侧对齐，完全铺满卡片宽度

## 测试数据

项目提供了三种测试数据：
- `mockVehicleData`：多分类数据，测试Tab切换功能
- `mockSingleCategoryData`：单分类数据，测试无Tab模式
- `mockVariousCountData`：多卡片数据，测试滚动功能 