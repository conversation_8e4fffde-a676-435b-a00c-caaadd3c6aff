# 车型变体展示组件

此文档描述了新增的两个车型变体展示组件的功能和使用方法。

## 组件概述

### 1. VehicleVariantDisplay
处理只有两个车型时的特殊展示样式。

#### 功能特性
- 上半部分：显示外牌信息，左侧图片及外牌横线位置以上点击区域
- 下半部分：显示年款和价格信息，右侧横线以下点击区域
- 年款单独一行显示（如有）
- 价格和总价区域一行显示
- 右侧箭头上下居中对齐

#### Props
```typescript
interface VehicleVariantDisplayProps {
  yearModel?: string;        // 年款信息，如 "2019款-2022款"
  plateType?: string;        // 牌照类型，默认 "外牌"
  dailyPrice: number;        // 日均价格
  totalPriceStart: number;   // 总价起价
  onPressTop?: () => void;   // 上半部分点击处理
  onPressBottom?: () => void;// 下半部分点击处理
  isSoldOut?: boolean;       // 是否售罄
}
```

### 2. VehicleMoreVariants
处理多个车型时的折叠/展开功能。

#### 功能特性
- 折叠状态：显示"更多年款，共x款"
- 展开状态：显示所有车型变体列表
- 每个车型项显示：年款名称、日均价格、总价起、右侧箭头
- 支持点击切换折叠/展开状态
- 支持点击具体车型项

#### Props
```typescript
interface VehicleVariant {
  name: string;         // 车型名称
  dailyPrice: number;   // 日均价格
  totalPrice: number;   // 总价
  vehicleCode: string;  // 车型代码
}

interface VehicleMoreVariantsProps {
  variants: VehicleVariant[];                    // 车型变体列表
  onVariantPress?: (variant: VehicleVariant) => void; // 车型点击处理
  defaultExpanded?: boolean;                     // 默认是否展开
}
```

## 集成方式

在 `VehicleCard.tsx` 中已经集成了这两个组件，根据车型数量自动选择展示方式：

- 2个车型：使用 `VehicleVariantDisplay`
- 3个或以上车型：使用 `VehicleMoreVariants`
- 1个或0个车型：不显示额外组件

## 测试数据

当前使用Mock数据进行测试，根据 `vehicleIndex` 模拟不同场景：
- `vehicleIndex % 3 === 0`：显示2个变体（测试VehicleVariantDisplay）
- `vehicleIndex % 3 === 1`：显示4个变体（测试VehicleMoreVariants）
- `vehicleIndex % 3 === 2`：不显示变体

## 样式文件

- `vehicleVariantDisplay.module.scss`：VehicleVariantDisplay的样式
- `vehicleMoreVariants.module.scss`：VehicleMoreVariants的样式

## 后续待开发

1. 将Mock数据替换为真实的业务数据
2. 完善点击事件的具体跳转逻辑
3. 根据实际设计稿调整样式细节
4. 添加埋点和日志记录 