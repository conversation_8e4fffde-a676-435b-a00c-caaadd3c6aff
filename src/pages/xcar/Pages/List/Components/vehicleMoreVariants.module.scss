@import '../../../Common/src/Tokens/tokens/color.scss';

.moreVariantsContainer {
  position: relative;
  overflow: visible; // 允许内容突破容器边界
}

.animatedContainer {
  overflow: hidden; // 动画容器内部需要隐藏超出部分
} 

.expandedContent {
  padding: 24px;
  background-color: $C_F0F0F0;
}

.tabItem {
  background: $C_FFF;
  border: 1px solid $C_F0F0F0;
  border-radius: 100px;
  box-shadow: 0px 4px 20px 0px $C_EEF0F5;
  padding: 9px 28px;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-width: 2px;
  border-bottom-color: $transparent;
}

.tabItemActive {
  border-bottom-color: $C_006FF6;
}

.tabText {
  color: $C_111111;
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  line-height: 30px;
}

.tabTextActive {
  color: $C_006FF6;
  font-size: 26px;
  font-weight: bold;
  font-family: PingFangSC-Semibold;
  line-height: 30px;
}