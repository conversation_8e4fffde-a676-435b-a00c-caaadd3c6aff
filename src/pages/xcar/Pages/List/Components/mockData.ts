// Mock数据用于车型展示组件测试
export interface VehicleVariant {
  id: string;
  name: string;
  yearModel: string;
  plateType: string;
  dailyPrice: number;
  totalPrice: number;
  vehicleCode: string;
  category?: string;
}

export interface VehicleTab {
  id: string;
  name: string;
  variants: VehicleVariant[];
}

// 模拟车型数据
export const mockVehicleData: VehicleTab[] = [
  {
    id: 'gl8',
    name: 'GL8',
    variants: [
      {
        id: 'gl8_2023_sh',
        name: '别克GL8',
        yearModel: '2023款',
        plateType: '沪牌',
        dailyPrice: 440,
        totalPrice: 1826,
        vehicleCode: 'GL8_2023_SH',
        category: 'gl8',
      },
      {
        id: 'gl8_2024_sh',
        name: '别克GL8',
        yearModel: '2024款',
        plateType: '沪牌',
        dailyPrice: 372,
        totalPrice: 1923,
        vehicleCode: 'GL8_2024_SH',
        category: 'gl8',
      },
      {
        id: 'gl8_2022_sh',
        name: '别克GL8',
        yearModel: '2022款',
        plateType: '沪牌',
        dailyPrice: 398,
        totalPrice: 1756,
        vehicleCode: 'GL8_2022_SH',
        category: 'gl8',
      },
    ],
  },
  {
    id: 'gl8_luxury',
    name: 'GL8豪华版',
    variants: [
      {
        id: 'gl8_lux_2023_sh',
        name: '别克GL8豪华版',
        yearModel: '2023款',
        plateType: '沪牌',
        dailyPrice: 560,
        totalPrice: 2240,
        vehicleCode: 'GL8_LUX_2023_SH',
        category: 'gl8_luxury',
      },
      {
        id: 'gl8_lux_2024_sh',
        name: '别克GL8豪华版',
        yearModel: '2024款',
        plateType: '沪牌',
        dailyPrice: 612,
        totalPrice: 2448,
        vehicleCode: 'GL8_LUX_2024_SH',
        category: 'gl8_luxury',
      },
    ],
  },
];

// 只有一个分类的数据
export const mockSingleCategoryData: VehicleTab[] = [
  {
    id: 'single',
    name: '',
    variants: [
      {
        id: 'single_1',
        name: '别克GL8',
        yearModel: '2023款',
        plateType: '沪牌',
        dailyPrice: 440,
        totalPrice: 1826,
        vehicleCode: 'SINGLE_1',
      },
    ],
  },
];

// 测试不同数量卡片的数据
export const mockVariousCountData: VehicleTab[] = [
  {
    id: 'test',
    name: '',
    variants: [
      {
        id: 'test_1',
        name: '别克GL8',
        yearModel: '2023款',
        plateType: '沪牌',
        dailyPrice: 440,
        totalPrice: 1826,
        vehicleCode: 'TEST_1',
      },
      {
        id: 'test_2',
        name: '别克GL8豪华版',
        yearModel: '2024款',
        plateType: '沪牌',
        dailyPrice: 612,
        totalPrice: 2448,
        vehicleCode: 'TEST_2',
      },
      {
        id: 'test_3',
        name: '别克GL8尊享版',
        yearModel: '2024款',
        plateType: '沪牌',
        dailyPrice: 720,
        totalPrice: 2880,
        vehicleCode: 'TEST_3',
      },
      {
        id: 'test_4',
        name: '别克GL8旗舰版',
        yearModel: '2024款',
        plateType: '沪牌',
        dailyPrice: 850,
        totalPrice: 3400,
        vehicleCode: 'TEST_4',
      },
    ],
  },
];
