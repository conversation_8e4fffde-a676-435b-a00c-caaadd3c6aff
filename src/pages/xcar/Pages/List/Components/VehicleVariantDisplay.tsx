import { XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { Platform } from '../../../Constants/Index';
import UITestId from '../../../Constants/UITestID';
import vehicleVariantStyles from './vehicleVariantDisplay.module.scss';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  container: {
    paddingTop: getPixel(20),
    paddingBottom: getPixel(14),
    borderTopWidth: getPixel(1),
    borderTopColor: color.blueGrayBg,
  },
  topSection: {
    paddingHorizontal: getPixel(16),
    paddingBottom: getPixel(12),
    borderBottomWidth: getPixel(1),
    borderBottomColor: color.C_E5E5E5,
  },
  bottomSection: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  yearModelWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getPixel(10),
  },
  yearModelText: {
    ...font.F_24_10_regular,
    color: color.C_111111,
  },
  plateTypeText: {
    ...font.F_24_10_regular,
    color: color.C_111111,
    marginLeft: getPixel(8),
  },
  priceAlign: {
    alignItems: 'flex-start',
  },
  priceRow: {
    flexDirection: 'row',
  },
  priceLeft: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  priceRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dayAverageText: {
    ...font.F_24_10_regular,
    color: color.C_111111,
    marginRight: getPixel(2),
  },
  dayAveragePrice: {
    ...font.F_26_10_regular_TripNumberRegular,
    color: color.C_111111,
    lineHeight: getPixel(33),
  },
  totalPriceText: {
    ...font.F_24_10_regular,
    color: color.C_888888,
    marginLeft: getPixel(8),
  },
  totalPrice: {
    ...font.F_26_10_regular_TripNumberRegular,
    color: color.C_888888,
    lineHeight: getPixel(33),
  },
  arrowIcon: {
    marginLeft: getPixel(8),
    ...font.F_26_10_regular,
    color: color.C_888888,
  },
  splitText: {
    opacity: 0.1,
    color: color.C_111111,
    width: getPixel(4),
    marginLeft: getPixel(6),
    marginRight: getPixel(6),
  },
  otherText: {
    ...font.F_24_10_regular,
    color: color.C_888888,
  },
});

interface VehicleVariantDisplayProps {
  // 年款信息
  yearModel?: string;
  // 外牌信息
  plateType?: string;
  // 日均价格
  dailyPrice: number;
  // 总价起
  totalPriceStart: number;
  // 点击处理
  onPressTop?: () => void;
  onPressBottom?: () => void;
  // 是否售罄
  isSoldOut?: boolean;
}

const VehicleVariantDisplay = memo((props: VehicleVariantDisplayProps) => {
  const {
    yearModel,
    plateType = '外牌',
    dailyPrice = 42,
    totalPriceStart = 166,
    onPressTop,
    onPressBottom,
    isSoldOut = false,
  } = props;

  return (
    <View
      className={vehicleVariantStyles.variantContainer}
      style={styles.container}
      testID={UITestId.car_testid_page_list_vehicle_item}
    >
      <BbkTouchable
        onPress={onPressBottom}
        style={styles.bottomSection}
        testID={UITestId.car_testid_page_list_vehicle_item}
      >
        <View style={yearModel ? styles.priceAlign : styles.priceRow}>
          {/* 年款单独一行（如果有） */}

          <View style={yearModel ? styles.yearModelWrap : {}}>
            {yearModel && (
              <BbkText style={styles.yearModelText}>{yearModel}</BbkText>
            )}

            {plateType && (
              <View style={styles.priceRow}>
                <BbkText style={styles.plateTypeText}>{plateType}</BbkText>
                {!yearModel && <BbkText style={styles.splitText}>|</BbkText>}
              </View>
            )}
          </View>

          {/* 价格和总价一行 */}

          {/* 左侧：日均价格+总价起 */}
          <View style={styles.priceLeft}>
            <BbkText style={styles.dayAverageText}>日均</BbkText>
            <BbkCurrencyFormatter
              price={dailyPrice}
              currency={Platform.CN_CURRENCY_CODE}
              currencyStyle={styles.dayAveragePrice}
              priceStyle={styles.dayAveragePrice}
              testID={UITestId.car_testid_comp_vehicle_day_price}
            />
            <BbkText style={styles.totalPriceText}>总价</BbkText>
            <BbkCurrencyFormatter
              price={totalPriceStart}
              currency={Platform.CN_CURRENCY_CODE}
              currencyStyle={styles.totalPrice}
              priceStyle={styles.totalPrice}
              testID={UITestId.car_testid_comp_vehicle_total_price}
            />
            <BbkText style={styles.otherText}>起</BbkText>
          </View>
        </View>
        <View>
          <BbkText style={styles.arrowIcon} type="icon">
            {icon.arrowRight}
          </BbkText>
        </View>
      </BbkTouchable>
    </View>
  );
});

VehicleVariantDisplay.displayName = 'VehicleVariantDisplay';

export default VehicleVariantDisplay;
