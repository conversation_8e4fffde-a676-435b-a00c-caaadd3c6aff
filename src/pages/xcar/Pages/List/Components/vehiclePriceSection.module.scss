@import '../../../Common/src/Tokens/tokens/color.scss';

.vehiclePriceSectionContainer {
  position: relative;
}

.priceRow {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  margin-bottom: 16px;
  gap: 8px; /* 恢复间距 */
}

.dayPriceContainer {
  display: flex;
  flex-direction: row;
  align-items: baseline;
}

.dayAveragePriceWrap {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-shrink: 0; /* 日均价格区域不收缩 */
}

.descText {
  color: $deepBlueBase;
  font-size: 24px;
  line-height: 30px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-right: 4px;
}

.totalPriceContainer {
  display: flex;
  flex-direction: column;
  flex-shrink: 0; /* 总价容器不收缩 */
  min-width: max-content; /* 保持最小内容宽度 */
}

.totalPriceContent {
  display: flex;
  flex-direction: column;
}

.totalPriceRow {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-wrap: nowrap; /* 总价行内不换行 */
  justify-content: flex-end;
  white-space: nowrap; /* 防止文本换行 */
}

.totalPricDesc {
  font-size: 24px;
  font-family: PingFangSC-Regular;
  line-height: 30px;
  color: $C_888888;
}

.totalPriceWrap {
  display: flex;
  flex-direction: row;
  align-items: baseline;
}

.totalHelpIcon {
  font-size: 22px;
  color: $C_888888;
  margin-left: 4px;
}

.otherDesc {
  font-size: 24px;
  font-family: PingFangSC-Regular;
  line-height: 30px;
  color: $C_888888;
}

/* 总价行内元素不收缩 */
.totalPriceRow > * {
  flex-shrink: 0;
}

.marketTagRow {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 8px;
}

.marketTagWrap {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: $C_FFEDE8;
  border-radius: 4px;
  height: 38px;
  min-width: auto;
  width: auto;
} 