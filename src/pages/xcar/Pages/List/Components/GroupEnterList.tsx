import { xMergeStyles, XViewExposure } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { Component, useCallback } from 'react';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color, layout, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './groupEnterListC2xStyles.module.scss';
import { RecommendEventResult } from '../../../Constants/ListEnum';
import {
  getVendorListEnterData,
  getVehicleInfoByCode,
  getTotalPriceModalData,
  validateIsSoldOut,
  getProductEnterExposureData,
  hasFees,
  getTotalPriceModalDataV2,
  getVendorPriceListByVehicleCode,
  getNewPriceGroup,
} from '../../../State/List/VehicleListMappers';
import { VendorListEnterRow } from '../../../ComponentBusiness/VendorListEnter';
import Texts from '../Texts';
import CarLog from '../../../Util/CarLog';
import { CarServerABTesting } from '../../../Util/Index';
import { LogKey } from '../../../Constants/Index';
import UITestId from '../../../Constants/UITestID';
import { getIsUserBrowsed, setVehicleCodeBigId } from '../Method';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  borderTop: {
    borderTopColor: color.grayBorder,
    borderTopWidth: StyleSheet.hairlineWidth,
  },
  buttonWrap: {
    height: getPixel(82),
    borderTopColor: color.grayBorder,
    borderTopWidth: StyleSheet.hairlineWidth,
    ...layout.flexRow,
    justifyContent: 'center',
  },
  userBrowsed: { backgroundColor: color.C_F6F8FB },
});

interface ILogType {
  groupId: string;
  selectedFilters: string;
  vehicleIndex: number;
}

interface IPropsType {
  data: any; // todo-xt 定义类型
  storeList: Array<any>;
  vendorSoldOutList: Array<string>;
  vehicleSoldOutList: Array<string>;
  baseLogInfo: ILogType;
  abVersion: string;
  showBorder: boolean;
  vendorListBasePageParam: {
    tops: Array<{
      vendorId?: string;
      vehicleId?: string;
    }>;
    filters: Array<string>;
    sortType: string;
    uniqSign: string;
  };
  setPriceSummaryModal: (data) => void;
  setTotalPriceModalData: (data) => void;
  handleGotoDetail: (
    vehicleCode: string,
    groupCode: string,
    hasCreditRent: boolean,
    priceListLen?: number,
    productRef?: any,
  ) => void;
  isRecommend?: boolean;
  isRecommendVehicle?: boolean;
  activeGroupId?: string;
  recommendEventResult?: RecommendEventResult;
  cityId: string;
  dropOffCityId: string;
  userBrowsingHistories: Map<string, object>;
  isUserGroupBrowsed: boolean;
  searchKeyWord?: string;
  vehicleName?: string;
  newMergeId?: string;
}
interface IStateType {
  showMore: boolean;
  showNumber: number; // 展示年款个数
}

const defaultShowNumber = 2; // 默认展示年款的个数

// 多年款入口列表
export default class GroupEnterList extends Component<IPropsType, IStateType> {
  exposureVehicleData: any;

  constructor(props) {
    super(props);
    const { group, priceGroup } = props.data || {};
    this.state = {
      showMore: false,
      showNumber:
        getNewPriceGroup(group, priceGroup).showNumber || defaultShowNumber,
    };
  }

  componentDidUpdate(preProps) {
    const { group, priceGroup } = this.props.data || {};
    const { group: perGroup, priceGroup: perPriceGroup } = preProps.data || {};
    const { showNumber: iShowNumber } = getNewPriceGroup(
      perGroup,
      perPriceGroup,
    );
    const { showNumber } = getNewPriceGroup(group, priceGroup);
    if (perGroup === group && iShowNumber !== showNumber) {
      setTimeout(() => {
        this.setState({
          showMore: false,
          showNumber: showNumber || defaultShowNumber,
        });
      });
    }
  }

  handlePressMore = () => {
    const { group, priceGroup, vehicleCode } = this.props.data || {};
    const { showNumber, needFold } = getNewPriceGroup(group, priceGroup);
    if (this.state.showMore) {
      this.setState({
        showMore: false,
        showNumber: showNumber || defaultShowNumber,
      });
    } else {
      this.setState({
        showMore: true,
        showNumber: this.getPriceGroupLen(),
      });
      const { cityId, dropOffCityId } = this.props;
      if (needFold) {
        CarLog.LogCode({
          name: '点击_列表页_外牌默认收起',

          info: {
            vehicleCode,
            pickupCityId: cityId,
            dropOffCityId,
            status: 1,
          },
        });
      } else {
        CarLog.LogCode({ name: '点击_查看更多年款' });
      }
    }
  };

  getPriceGroupLen = () => this.props.data?.priceGroup?.length;

  getGroupEnterListDom = priceGroup => {
    const {
      isRecommend,
      isRecommendVehicle,
      activeGroupId,
      userBrowsingHistories,
    } = this.props;
    this.exposureVehicleData = [];
    const dom = priceGroup.map((item, index) => {
      if (index < this.state.showNumber) {
        this.exposureVehicleData.push(getProductEnterExposureData(item, index));

        const isUserBrowsed = getIsUserBrowsed({
          vehicleCode: item?.vehicleCode,
          license: item?.productRef?.license,
          userBrowsingHistories,
        });

        return (
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          <GroupEnterItem
            isRecommend={isRecommend}
            activeGroupId={activeGroupId}
            isRecommendVehicle={isRecommendVehicle}
            key={`${item.vehicleCode}${item?.productRef?.license}`}
            data={item}
            index={index}
            setPriceSummaryModal={this.props.setPriceSummaryModal}
            setTotalPriceModalData={this.props.setTotalPriceModalData}
            vendorSoldOutList={this.props.vendorSoldOutList}
            vehicleSoldOutList={this.props.vehicleSoldOutList}
            storeList={this.props.storeList}
            handleGotoDetail={this.props.handleGotoDetail}
            vendorListBasePageParam={this.props.vendorListBasePageParam}
            vehicleIndex={this.props.baseLogInfo?.vehicleIndex}
            showBorder={this.props.showBorder || index !== 0}
            vehicleCodeBigId={priceGroup?.[0]?.vehicleCode}
            isUserBrowsed={isUserBrowsed}
          />
        );
      }
      return null;
    });
    return dom;
  };

  getExposureData = () => {
    const { searchKeyWord, baseLogInfo, abVersion, recommendEventResult, vehicleName, newMergeId } =
      this.props;
    return {
      vehicleData: this.exposureVehicleData,
      ...baseLogInfo,
      abVersion,
      recommendEventResult,
      searchKeyWord,
      newMergeId,
      vehicleName,
    };
  };

  getMoreBtnExposureData = needFold => {
    const { showMore } = this.state;
    if (needFold) {
      const { cityId, dropOffCityId } = this.props;
      const { vehicleCode } = this.props.data || {};
      return !showMore
        ? CarLog.LogExposure({
            name: '曝光_列表页_外牌默认收起',

            info: {
              vehicleCode,
              pickupCityId: cityId,
              dropOffCityId,
              status: 0,
            },
          })
        : '';
    }
    return CarLog.LogExposure({ name: '曝光_查看更多年款' });
  };

  render() {
    const { isUserGroupBrowsed } = this.props;
    const { group, priceGroup: iPriceGroup = [] } = this.props.data || {};
    const { priceGroup, needFold, foldBtnContent } =
      getNewPriceGroup(group, iPriceGroup) || {};
    const { showMore } = this.state;
    const priceGroupLen = this.getPriceGroupLen();
    const moreLen = priceGroupLen - defaultShowNumber;
    const showMoreBtn = priceGroupLen > defaultShowNumber || needFold;
    const showMoreText = needFold
      ? foldBtnContent
      : Texts.showMorePriceGroup(moreLen);
    const showText = showMore ? Texts.showLess : showMoreText;
    if (!priceGroupLen) {
      return null;
    }

    const groupEnterListDom = this.getGroupEnterListDom(priceGroup);

    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          key: LogKey.c_car_trace_list_vehicle_exposure_new,
          ...this.getExposureData(),
        })}
      >
        {groupEnterListDom}
        {showMoreBtn && (
          <XViewExposure testID={this.getMoreBtnExposureData(needFold)}>
            <BbkTouchable
              debounce={true}
              onPress={this.handlePressMore}
              debounceTime={500}
              style={xMergeStyles([
                styles.buttonWrap,
                isUserGroupBrowsed && styles.userBrowsed,
              ])}
            >
              <BbkText className={c2xStyles.buttonText}>{showText}</BbkText>
              <BbkText type="icon" className={c2xStyles.buttonIcon}>
                {showMore ? icon.arrowUp : icon.arrowDown}
              </BbkText>
            </BbkTouchable>
          </XViewExposure>
        )}
      </XViewExposure>
    );
  }
}

const GroupEnterItem = ({
  data,
  index,
  setPriceSummaryModal,
  setTotalPriceModalData,
  vendorSoldOutList,
  vehicleSoldOutList,
  storeList,
  handleGotoDetail,
  vendorListBasePageParam,
  vehicleIndex,
  showBorder,
  isRecommend,
  isRecommendVehicle,
  activeGroupId,
  isUserBrowsed,
  vehicleCodeBigId,
}) => {
  const {
    vehicleCode,
    minDPrice,
    minTPrice,
    minDOrinPrice,
    productTopInfo,
    priceSize,
    isCredit,
    productRef,
  } = data;
  const recommendGroupId = isRecommend ? activeGroupId : '';
  // 推荐车型，车型组透传(推荐车型组切换不查询接口)
  const vendorPriceList = getVendorPriceListByVehicleCode(
    vehicleCode,
    productTopInfo,
    recommendGroupId,
    null,
    productRef?.license,
  );
  // 推荐车型，车型组透传(推荐车型组切换不查询接口)
  const vendorListEnterData: any = getVendorListEnterData(data, {
    vehicleCode,
    vehicleIndex: index,
    activeGroupId: recommendGroupId,
  });
  const curVehicleInfo = getVehicleInfoByCode(vehicleCode);
  const { groupCode } = curVehicleInfo || {};

  const totalPricePress = useCallback(() => {
    const showNewTotalPriceModals = hasFees(vendorPriceList);
    // 设置大车型ID, 即曝光里的vehicleData里排第一的vehicleCode
    setVehicleCodeBigId(vehicleCodeBigId);
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const data = showNewTotalPriceModals
      ? getTotalPriceModalData(
          vendorPriceList,
          storeList,
          undefined,
          undefined,
          CarServerABTesting.isISDShelves2B(),
        )
      : getTotalPriceModalDataV2(
          minDPrice,
          minTPrice,
          undefined,
          CarServerABTesting.isISDShelves2B(),
        );

    data.vendorListPageParam = {
      ctripVehicleId: vehicleCode,
      groupCode,
      productRef,
      ...vendorListBasePageParam,
    };
    data.vehicleIndex = vehicleIndex;
    data.priceListLen = priceSize;
    // 如果没有fees节点需打开非价格一致性版本的总价说明
    if (showNewTotalPriceModals) {
      setPriceSummaryModal({ visible: true, data });
    } else {
      setTotalPriceModalData({ visible: true, data });
    }
  }, [setPriceSummaryModal, vendorPriceList, vehicleCodeBigId]);

  const handlePress = useCallback(() => {
    const logData = {
      cardPriceIndex: index,
      yearModel: vendorListEnterData?.vehicleStyle,
      plateType: vendorListEnterData?.productRef?.license,
      currentDailyPrice: minDPrice,
      currentOriginalyPrice: minDOrinPrice,
      minTotalPrice: minTPrice,
    };
    // 设置大车型ID, 即曝光里的vehicleData里排第一的vehicleCode
    setVehicleCodeBigId(vehicleCodeBigId);
    handleGotoDetail(
      vehicleCode,
      groupCode,
      isCredit,
      priceSize,
      productRef,
      logData,
    );
  }, []);

  // 推荐车型，车型组透传(推荐车型组切换不查询接口)
  const isSoldOut = validateIsSoldOut(
    vendorSoldOutList,
    vehicleSoldOutList,
    data,
    isRecommendVehicle,
    recommendGroupId,
  );

  return (
    <BbkTouchable
      testID={UITestId.car_testid_page_list_vehicleList_group_vendoritem}
      debounce={true}
      onPress={handlePress}
      disabled={isSoldOut}
      className={c2xStyles.itemWrap}
      style={showBorder && styles.borderTop}
    >
      <VendorListEnterRow
        isSoldOut={isSoldOut}
        showBorder={false}
        {...vendorListEnterData}
        isRecommend={isRecommend}
        totalPricePress={totalPricePress}
        isUserBrowsed={isUserBrowsed}
        isGroupEnter={true}
      />
    </BbkTouchable>
  );
};
