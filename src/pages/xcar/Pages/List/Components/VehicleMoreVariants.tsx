import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useState, useRef } from 'react';
import { Animated } from 'react-native';
import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import UITestId from '../../../Constants/UITestID';
import vehicleMoreVariantsStyles from './vehicleMoreVariants.module.scss';
import { VehicleTab, VehicleVariant } from './mockData';

const { getPixel } = BbkUtils;

// 卡片固定宽度，确保内容显示完整
const multiCardWidth = getPixel(280); // 统一使用固定宽度

const styles = StyleSheet.create({
  container: {
    paddingTop: getPixel(20),
    paddingBottom: getPixel(14),
    borderTopWidth: getPixel(1),
    borderTopColor: color.blueGrayBg,
  },
  toggleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
  },
  rightContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moreText: {
    ...font.F_24_10_regular,
    color: color.C_111111,
  },
  countText: {
    ...font.F_24_10_regular,
    color: color.C_888888,
    marginLeft: getPixel(8),
  },
  arrowIcon: {
    ...font.F_26_10_regular,
    color: color.C_888888,
    marginLeft: getPixel(8),
  },
  // 动画容器 - 铺满整个卡片宽度
  animatedContainer: {
    // 使用负margin来突破父容器的限制，与车型图片左侧对齐
    // 计算：卡片左边距12px + 车型图片宽度200px + 图片与内容间距20px = 232px
    marginLeft: getPixel(-232), // 与车型图片左侧对齐
    marginRight: getPixel(-16), // 突破右侧内容区域的右边距
  },
  // Tab相关样式 - 修改为下划线风格
  tabContainer: {
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    marginBottom: getPixel(16),
  },
  tabScrollContainer: {
    flexDirection: 'row',
  },
  tabItem: {
    paddingBottom: getPixel(12),
    marginRight: getPixel(24),
    borderBottomWidth: getPixel(2),
    borderBottomColor: color.transparent,
  },
  tabItemActive: {
    borderBottomColor: color.blueBase,
  },
  tabText: {
    ...font.F_28_10_regular,
    color: color.fontSecondary,
  },
  tabTextActive: {
    color: color.C_111111,
    fontWeight: 'bold',
  },
  // 卡片容器样式
  cardContainer: {
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
  },
  // 车型卡片样式 - 修改为灰色圆角边框
  variantCard: {
    backgroundColor: color.grayBgSecondary,
    borderRadius: getPixel(8),
    padding: getPixel(12),
    marginBottom: getPixel(8),
  },
  variantCardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  variantCardLeft: {
    flex: 1,
  },
  variantCardYearModel: {
    ...font.F_28_10_regular,
    color: color.C_111111,
    marginBottom: getPixel(4),
  },
  variantCardPlateType: {
    ...font.F_24_10_regular,
    color: color.C_111111,
  },
  variantCardPriceSection: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginTop: getPixel(8),
  },
  variantCardDailyText: {
    ...font.F_24_10_regular,
    color: color.C_111111,
    marginRight: getPixel(2),
  },
  variantCardDailyPrice: {
    ...font.F_32_10_medium,
    color: color.blueBase,
    marginRight: getPixel(8),
  },
  variantCardTotalText: {
    ...font.F_24_10_regular,
    color: color.C_888888,
    marginRight: getPixel(2),
  },
  variantCardTotalPrice: {
    ...font.F_24_10_regular,
    color: color.C_888888,
  },
  variantCardArrow: {
    ...font.F_26_10_regular,
    color: color.C_888888,
  },

  // 多张卡片：可滑动
  multiCardsContainer: {
    paddingRight: 0,
  },
  multiCardScrollContainer: {
    paddingLeft: getPixel(16),
    paddingRight: getPixel(32), // 增加右边距确保最后一张卡片完整显示
  },
  multiCardItem: {
    width: multiCardWidth,
    marginRight: getPixel(12),
  },
  multiCardItemLast: {
    marginRight: getPixel(16),
  },
});

interface VehicleMoreVariantsProps {
  tabs: VehicleTab[];
  onVariantPress?: (variant: VehicleVariant) => void;
  defaultExpanded?: boolean;
}

const VehicleMoreVariants = memo((props: VehicleMoreVariantsProps) => {
  const { tabs = [], onVariantPress, defaultExpanded = false } = props;

  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [activeTabId, setActiveTabId] = useState(tabs[0]?.id || '');
  const animatedHeight = useRef(new Animated.Value(0)).current;

  // 计算总的变种数量
  const totalVariantCount = tabs.reduce(
    (total, tab) => total + tab.variants.length,
    0,
  );

  // 获取当前激活的tab数据
  const activeTab = tabs.find(tab => tab.id === activeTabId) || tabs[0];
  const currentVariants = activeTab?.variants || [];

  // 是否显示tab（多个tab且有名称）
  const shouldShowTabs = tabs.length > 1 && tabs.some(tab => tab.name);

  const handleToggle = () => {
    const toValue = isExpanded ? 0 : 1;

    setIsExpanded(!isExpanded);

    Animated.timing(animatedHeight, {
      toValue,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const handleTabPress = (tabId: string) => {
    setActiveTabId(tabId);
  };

  const handleVariantPress = (variant: VehicleVariant) => {
    onVariantPress?.(variant);
  };

  // 渲染单个车型卡片
  const renderVariantCard = (variant: VehicleVariant) => {
    return (
      <BbkTouchable
        key={variant.id}
        onPress={() => handleVariantPress(variant)}
        style={styles.variantCard}
        testID={`${UITestId.car_testid_page_list_vehicle_item}_variant_${variant.id}`}
      >
        <View style={styles.variantCardRow}>
          <View style={styles.variantCardLeft}>
            <BbkText style={styles.variantCardYearModel}>
              {variant.yearModel}
            </BbkText>
            <BbkText style={styles.variantCardPlateType}>
              {variant.plateType}
            </BbkText>
            <View style={styles.variantCardPriceSection}>
              <BbkText style={styles.variantCardDailyText}>日均</BbkText>
              <BbkText style={styles.variantCardDailyPrice}>
                ¥{variant.dailyPrice}
              </BbkText>
              <BbkText style={styles.variantCardTotalText}>总价</BbkText>
              <BbkText style={styles.variantCardTotalPrice}>
                ¥{variant.totalPrice}起
              </BbkText>
            </View>
          </View>
          <BbkText style={styles.variantCardArrow} type="icon">
            {icon.arrowRight}
          </BbkText>
        </View>
      </BbkTouchable>
    );
  };

  // 渲染Tab组件
  const renderTabs = () => {
    if (!shouldShowTabs) return null;

    return (
      <View style={styles.tabContainer}>
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabScrollContainer}
        >
          {tabs.map(tab => {
            const isActive = tab.id === activeTabId;
            return (
              <BbkTouchable key={tab.id} onPress={() => handleTabPress(tab.id)}>
                <View
                  className={classNames(
                    vehicleMoreVariantsStyles.tabItem,
                    isActive && vehicleMoreVariantsStyles.tabItemActive,
                  )}
                >
                  <BbkText
                    className={classNames(
                      vehicleMoreVariantsStyles.tabText,
                      isActive && vehicleMoreVariantsStyles.tabTextActive,
                    )}
                  >
                    {tab.name}
                  </BbkText>
                </View>
              </BbkTouchable>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  // 渲染卡片容器
  const renderCards = () => {
    const cardCount = currentVariants.length;

    if (cardCount === 0) return null;

    // 一张卡片：通铺
    if (cardCount === 1) {
      return (
        <View style={styles.cardContainer}>
          {renderVariantCard(currentVariants[0])}
        </View>
      );
    }

    // 两张卡片：使用滚动容器，确保内容显示完整
    if (cardCount === 2) {
      return (
        <View style={styles.multiCardsContainer}>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.multiCardScrollContainer}
          >
            {currentVariants.map((variant, index) => (
              <View
                key={variant.id}
                style={
                  index === cardCount - 1
                    ? { ...styles.multiCardItem, ...styles.multiCardItemLast }
                    : styles.multiCardItem
                }
              >
                {renderVariantCard(variant)}
              </View>
            ))}
          </ScrollView>
        </View>
      );
    }

    // 多张卡片：可滑动
    return (
      <View style={styles.multiCardsContainer}>
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.multiCardScrollContainer}
        >
          {currentVariants.map((variant, index) => (
            <View
              key={variant.id}
              style={
                index === cardCount - 1
                  ? { ...styles.multiCardItem, ...styles.multiCardItemLast }
                  : styles.multiCardItem
              }
            >
              {renderVariantCard(variant)}
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <View
      className={vehicleMoreVariantsStyles.moreVariantsContainer}
      style={styles.container}
      testID={UITestId.car_testid_page_list_vehicle_item}
    >
      {/* 切换行 */}
      <BbkTouchable
        onPress={handleToggle}
        style={styles.toggleRow}
        testID={UITestId.car_testid_page_list_vehicle_item}
      >
        <BbkText style={styles.moreText}>更多年款</BbkText>
        <View style={styles.rightContent}>
          <BbkText style={styles.countText}>共{totalVariantCount}款</BbkText>
          <BbkText style={styles.arrowIcon} type="icon">
            {isExpanded ? icon.arrowUp : icon.arrowDown}
          </BbkText>
        </View>
      </BbkTouchable>

      {/* 展开内容 - 带动画 - 铺满整个卡片宽度 */}
      <Animated.View
        style={[
          styles.animatedContainer,
          {
            maxHeight: animatedHeight.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 1000], // 足够大的高度以容纳内容
            }),
            opacity: animatedHeight.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [0, 0.5, 1],
            }),
          },
        ]}
      >
        <View className={vehicleMoreVariantsStyles.expandedContent}>
          {renderTabs()}
          {renderCards()}
        </View>
      </Animated.View>
    </View>
  );
});

VehicleMoreVariants.displayName = 'VehicleMoreVariants';

export default VehicleMoreVariants;
