import { get as lodashGet } from 'lodash-es';
/* eslint-disable max-lines */
/* eslint-disable complexity */
import StyleSheet from '@c2x/apis/StyleSheet';
import ViewPort from '@c2x/components/ViewPort';
import Page, { IBasePageProps } from '@c2x/components/Page';
import Event from '@c2x/apis/Event';
import Business from '@c2x/apis/Business';
import React from 'react';
import {
  xUser,
  XView as View,
  xShowToast,
  XBoxShadow,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import { FilterType, Sesame } from '@ctrip/rn_com_car/dist/src/Logic';
import {
  getCacheKey,
  MemorizeCache,
} from '@ctrip/rn_com_car/dist/src/CarFetch';

import { selector, vh } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { XCalendar } from '@ctrip/xtaro-component-calendar';
import c2xStyles from './list.module.scss';

import {
  BbkSkeletonLoading,
  PageType,
  NewWarningTipsModal,
  CouponModal,
  BbkUpgradeModal,
  RecommendLoading,
  SearchCarModal,
  SearchBar,
  LoadingAnimateTipStatic,
  SearchBarAnimateGuide,
} from './Components/Index';
import { IPromotion } from '../../ComponentBusiness/CouponEntry/src/Types';
import CPage, { IStateType } from '../../Components/App/CPage';
import { AssistiveTouch } from '../../Components/Index';
import {
  EventName,
  LogKey,
  ApiResCode,
  PageName,
  WarningInfo,
  ListEnum,
  ListConstants,
  StorageKey,
  LogKeyDev,
  UITestID,
  ImageUrl,
} from '../../Constants/Index';
import {
  CarLog,
  Utils,
  Hooks,
  AppContext,
  User,
  CarABTesting,
  CarServerABTesting,
  CarStorage,
  Channel,
  EventHelper,
  GetABCache,
  CarFetch,
  GetAB,
} from '../../Util/Index';
import { LogHistory } from '../../Util/Log/UserBehavior';
import { ListTexts as Texts } from '../../Constants/TextIndex';
import {
  LogListFinalTrace,
  LogListResExposure,
  LogListEachTrace,
} from '../../State/List/UBTLog';
import {
  LoginGuide,
  ListPriceDetailModalContainer as PriceDetailModal,
  ListHeaderContainer as ListHeader,
  ListFilterAndSortModalContainer as FilterAndSortModal,
  VehicleListWithControlContainer as VehicleListWithControlOSD,
  VehicleListNewContainer as VehicleListNew,
  SearchPanelModalContainer as SearchPanelModal,
  NoMatchContainer as ListNoMatch,
  DriverLicensePopContainer as DriverLicensePop,
  TimeOutPopContainer as TimeOutPop,
  ListLimitRulesPopContainer as LimitRulesPop,
  ListVendorListModalContainer as VendorListModal,
  VehicleModalContainer as VehicleModal,
  ListEasyLifeModalContainer as EasyLifeTagListModal,
  SearchConditionTipContainer as SearchConditionTip,
  ListTotalPriceModalContainer as TotalPriceModal,
  ListCacheExpirePopContainer as CacheExpirePop,
  CustomToastContainer as Customtoast,
  TextToastContainer as TextToast,
  AgeModal,
  ListProgress,
  ListFilterProgress,
  ListSearchLessResultTipContainer as SearchLessResultTip,
  ListCalendarModalContainer as ListCalendarModal,
  ListPollContainer as ListPoll,
  LoadingAnimateTipContainer as LoadingAnimateTip,
  GroupChangeToastISD,
  NoResultMultipleContainer,
  ListFilterBarContainer as ListFilterBar,
  ListQuickFilterBarContainer as QuickFilterBar,
  ListTipListContainer as TipList,
  ListVehGroupBarContainer as VehGroupBar,
} from '../../Containers/ListIndex';

import { ListReqAndResData, ListResSelectors } from '../../Global/Cache/Index';
import {
  getListStatusData,
  getListStatusDataByStorage,
  listStatusKeyList,
  removeListStatusData,
  setRegisterPageData,
  getFromPage,
  setListStatusData,
} from '../../Global/Cache/ListReqAndResData';
import {
  getListReqParam,
  getPageCacheKey,
  getExposureData,
  removeExposureData,
} from '../../State/List/Mappers';
import { listPagingAgent } from '../../Util/ListPagingAgent';
import ListPerformanceLog from './ListPerformanceLog';
import { updateFilterItems } from '../Home/Logic/Index';
import { RecommendType, SubStrategyType } from '../../Constants/ListEnum';
import { initializeABListPage } from '../../Util/CarABTesting/InitializeAB';
import { FilterPoiType } from '../../Util/AppContext';
import { loadOsdUserBrowsed } from './Method';
import { IPollConfig } from '../../Types/Dto/getIpollConfigType';
import {
  getIsAiSortTag,
  getIsFromCache,
} from '../../Global/Cache/ListResSelectors';
import { GetUserInfoResponseType } from '../../Types/Dto/GetUserInfoType';

const { DEFAULT_HEADER_HEIGHT, DEFAULT_FILTER_BAR_HEIGHT } = BbkConstants;
const initFilterModalTop =
  DEFAULT_HEADER_HEIGHT +
  BbkUtils.getPixel(DEFAULT_FILTER_BAR_HEIGHT * 2 - 1) +
  BbkUtils.fixOffsetTop();
// 合肥、苏州用户调研问卷配置id
const InstantSurveySenceIdTypes = {
  '278': 488, // 合肥
  '14': 486, // 苏州
};

const { getPixel, isAndroid, isHarmony } = BbkUtils;
const { PageIndexId } = WarningInfo;

enum SceneType {
  isFromHome = 'isFromHome',
  isFromProductNew = 'isFromProductNew',
  isFromMarket = 'isFromMarket',
  isFromList = 'isFromList',
}

interface WarningDto {
  urgencyType?: number;
  urgencyLevel?: number;
  warningTitle?: string;
  warningContent?: string;
  warningRichText?: string;
}

interface ListStateType extends IStateType {
  isShowRentCenterHeader: boolean;
  getCacheFinish: boolean;
  filterAndSortModalCwMarginTop?: number;
  isLogin?: boolean;
  isShowOther: boolean;
  warningTipsModalVisible?: boolean;
  couponModalVisible?: boolean;
  warningTipsModalCotent?: Array<WarningDto>;
  datePickerRefDone?: boolean;
  isShowTipList?: boolean;
  isShowMaskLoading?: boolean;
  isFetchFinish: boolean;
  finishLoadingAnimate?: boolean;
  searchCarModalVisible?: boolean;
  isShowSearchInput?: boolean;
  searchBarFixTop: number;
  searchCarModalFromFilter?: boolean;
}

const PAGESTAGE = {
  INIT: 'INIT',
  SHOW: 'SHOW',
  FAIL: 'FAIL',
};
const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
  headerStyle: {
    elevation: 0,
    borderBottomWidth: 0,
    backgroundColor: color.white,
  },
  osdHeadContentStyle: {
    backgroundColor: color.C_F5F7FA,
  },
  filterBarStyle: {
    elevation: 0,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: color.grayBorder,
  },
  marginTop0: {
    marginTop: 0,
  },
  bottom0: {
    bottom: 0,
  },
  filterAndSortModalStyle: {
    marginTop: initFilterModalTop,
    zIndex: 2,
  },
  vehicleList: {
    backgroundColor: color.grayBg,
  },
  vehicleList_B: {
    backgroundColor: color.noResultBg,
  },
  noMatchStyle: {
    backgroundColor: color.white,
    paddingTop: getPixel(64),
  },
  noMatchStyleTitle: {
    ...font.subTitle1RegularStyle,
    color: color.blueGrayBase,
  },
  noMatchStyleSubTitle: {
    color: color.fontSubDark,
  },
  loadingStyle: {
    alignItems: 'center',
  },
  headerWrap: {
    minHeight: getPixel(180),
  },
  grayPlaceholder: {
    width: getPixel(558),
    height: getPixel(72),
    backgroundColor: color.grayBgSecondary,
    top: getPixel(100),
    left: getPixel(100),
  },
});

interface ICacheType {
  isHitCache?: boolean;
  isHitLimitCache?: boolean;
  type?: any;
  pageNum?: number;
  callbackFun?: () => void;
  uniqSign?: string;
  isWillMount?: boolean;
}

interface IVisible {
  visible: boolean;
}

interface IListPropsType extends IBasePageProps {
  isLoading: boolean;
  isRecommendLoading: boolean;
  isRecommendNoResult: boolean;
  recommendAllVehicleCount: number;
  isFail: boolean;
  isError: boolean;
  indexCallbckData: any;
  datePickerVisible: boolean;
  locationDatePopVisible: boolean;
  agePickerVisible: boolean;
  ageTipPopVisible: boolean;
  sortAndFilterVisible: boolean;
  saleOutList: Array<string>;
  pCityId: number;
  rCityId: number;
  goodsShelfCityIds: Array<string>;
  vehiclePopVisible: boolean;
  isHasRentCenter: boolean;
  selectedFilters: FilterType;
  fetchList: (data?: ICacheType) => void;
  fetchRecommend: (data?: any) => void;
  limitRulePopVisible: boolean;
  vendorListModalVisible: boolean;
  filterNoResult: boolean;
  activeGroupId?: string;
  setLocationInfo: (rentalLocation: any) => void;
  setDatePickerIsShow: ({ visible }: IVisible) => void;
  setLocationAndDatePopIsShow: ({ visible }: IVisible) => void;
  setAgePickerIsShow: ({ visible }: IVisible) => void;
  setAgeTipPopIsShow: ({ visible }: IVisible) => void;
  setFilterModalIsShow: ({ visible }: IVisible) => void;
  setActiveFilterBarCode: (data: string) => void;
  isDebugMode?: boolean;
  setScrollViewHeight: (height: number) => void;
  setSaleOutList: (data: { saleOutList: Array<string> }) => void;
  setVehPopData: ({ visible }: IVisible) => void;
  updateSelectedFilter?: (data: any) => void;
  setLimitRulePopVisible: ({ visible }: IVisible) => void;
  setVendorListModalData: ({ visible }: IVisible) => void;
  easyLifePopVisible?: boolean;
  setEasyLifePopVisible: ({ visible }: IVisible) => void;
  progressIsFinish?: boolean;
  setActiveGroupId?: (data: any) => void;
  setFilterNoResult: (data: any) => void;
  listReqParam: any;
  reset: () => void;
  limitReqParam: any;
  historyLogParam: any;
  rentalLocationAndDate: any;
  fromurl?: string;
  listReqCount: number;
  progress: number;
  getListWarningInfo: (data) => void;
  totalPriceModalVisible?: boolean;
  setTotalPriceModalData?: ({ visible }: IVisible) => void;
  priceSummaryModalVisible?: boolean;
  setPriceSummaryModal?: (visible: boolean, data?: any) => void;
  tipPopVisible?: boolean;
  setTipPopData?: (data) => void;
  tipPopData?: Object;
  updateFetchCacheStatus: (data) => void;
  fetchLimitContent: (data) => void;
  isFilterLoading: boolean;
  isFilterFail: boolean;
  isGroupLoading: boolean;
  isGroupFail: boolean;
  pageCacheKey?: string;
  pageParamStr?: string;
  handleResGroupIdAndFilters: () => void;
  setIsShowToast: (data: boolean) => void;
  promotionData: Array<IPromotion>;
  availablePromotionData: Array<IPromotion>;
  fetchReceivePromotion: (data?: { listFetchSuccess?: boolean }) => void;
  receivePromotion: () => void;
  receiveAllPromotion: () => void;
  couponReset: () => void;
  isListReceiveSuccess?: boolean;
  setIsListReceiveSuccess?: (data: boolean) => void;
  filterItems: Array<any>;
  setFilterItems: (filterItems: Array<any>) => void;
  preFetchCity: () => void;
  isNoPoiData?: boolean;
  isNoRPoiData?: boolean;
  isTimePassed?: boolean;
  urlQuery?: any;
  isNewSearchNoResult?: boolean;
  fromPage?: string;
  activeGroupName?: string;
  isDifferentLocation?: boolean;
  recommendType?: RecommendType;
  subStrategyType?: SubStrategyType;
  setLicenseModalData?: (data) => void;
  country?: string;
  isRentCenterPoint?: boolean;
  isWaitingRecommendResult?: boolean;
  hasSelfService?: boolean;
  isRefactor?: boolean;
  showListTimer?: boolean;
  isRebookOsd?: boolean;
  setOsdUserBrowsed?: (data) => void;
  isFromCache?: boolean;
  ipollConfig?: Array<IPollConfig>;
  queryListIpollConfig: (data?: any) => void;
  setProgressIsFinish: (data: boolean) => void;
  userInfo: Array<GetUserInfoResponseType>;
  getUserInfo: () => void;
}

const removeEvents = () => {
  Event.removeEventListener(EventName.changeRentalLocation);
  Event.removeEventListener(EventName.changeRentalISDLocation);
  Event.removeEventListener(EventName.filterByRentCenter);
  Event.removeEventListener(EventName.filterBySelfService);
  Event.removeEventListener(EventName.SideToolBoxDidOpen);
};

export default class List extends CPage<IListPropsType, ListStateType> {
  static whyDidYouRender = true;

  datePickerRef: any;

  searchPanelModalRef: any;

  hasInitFetch: boolean;

  headerAnimating: boolean;

  lastTranslateYAnim: number;

  listThresholdLayout: number;

  listThreshold: number;

  scrollHeaderThrottle: any;

  vehicleListRef: any;

  isHitCache: boolean;

  isHitCacheAtWillMount: boolean;

  isHitLimitCache: boolean;

  tipListRef: any;

  isSubmitFirstLog: boolean;

  isSubmitSecondLog: boolean;

  scrollDown: boolean;

  submitLog: boolean;

  needSubmitSecondLog: boolean;

  listHeaderHeight: number;

  startTime: Date;

  delayTime: number;

  prePCityId: string;

  preRCityId: string;

  hasUpdateResStatus: boolean;

  setUpdateTime: Date;

  couponModalTimer: any;

  isFetchCouponList?: boolean;

  isLoginChanged?: boolean;

  hasSearchConditionTipShowed?: boolean;

  isNoPoiData?: boolean;

  pageloadSuccessTime?: number = 0;

  hasPrefetchedSelfServiceImage?: boolean;

  listPollRef?: any;

  sceneType?: SceneType; // 是否需要在页面加载完成后发送用户重新搜索埋点

  // 是否是第一次加载列表页
  // 用于处理进度条的显示，首次渲染 && isFromCache=true 时，不显示进度条。非首次（在列表页搜索时）则需要展示进度条
  searchCount: number = 0;

  constructor(props) {
    super(props);
    // 如果是融合首页，设置AppContext.isListCombineSwitch发送事件开关
    if (AppContext.isHomeCombine && !props.searchDiff) {
      AppContext.setIsListCombineEventSwitch(true);
    }
    this.state = {
      isShowRentCenterHeader: true,
      getCacheFinish: false,
      filterAndSortModalCwMarginTop: 0,
      isLogin: xUser.isLogin(),
      isShowOther: false,
      warningTipsModalVisible: false,
      couponModalVisible: false,
      warningTipsModalCotent: [],
      datePickerRefDone: false,
      isShowTipList: true,
      isShowMaskLoading: true,
      isFetchFinish: false,
      finishLoadingAnimate: false,
      searchCarModalVisible: false,
      isShowSearchInput: false,
      searchBarFixTop: 0,
      searchCarModalFromFilter: true,
    };
    this.headerAnimating = false;
    this.lastTranslateYAnim = 0;
    this.listThresholdLayout = 0;
    this.listThreshold = 0;
    this.isSubmitFirstLog = false;
    this.isSubmitSecondLog = false;
    this.needSubmitSecondLog = true;
    this.prePCityId = '';
    this.preRCityId = '';
    this.hasUpdateResStatus = false;
    ListReqAndResData.removeData();
    removeListStatusData();
    ListPerformanceLog.clearAll();
    initializeABListPage();
    // 读取本地缓存必须在发起AB分流之后，否则可能预请求数据与列表页实验版本数据不一致
    this.validateHasCache();
    this.couponModalTimer = null;
    this.isFetchCouponList = true;
    this.isLoginChanged = false;
    this.hasSearchConditionTipShowed = false;
    this.isNoPoiData = props.isNoPoiData || false;
    this.hasPrefetchedSelfServiceImage = false;
    this.searchCount = 0;
  }

  handleLimitCacheData = () => {
    const limitUrl = '18631/translimit';
    const limitCacheKey = getCacheKey({
      url: limitUrl,
      uid: AppContext.UserInfo.userId,
      paramBody: this.props.limitReqParam,
    });
    const limitCacheData =
      Utils.isCtripIsd() &&
      MemorizeCache.getCacheWithResponseMap({
        key: limitCacheKey,
        groupId: limitUrl,
      });

    if (limitCacheData) {
      this.isHitLimitCache = true;
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listLimitRuleRes,
        limitCacheData,
      );
    }
  };

  getListPollRef = ref => {
    this.listPollRef = ref;
  };

  validateHasCache = () => {
    if (CarABTesting.isListInPage()) {
      this.validateHasPageCache();
      return;
    }

    const listReqParam = getListReqParam();
    const listUrl = '18631/queryProducts';
    let cacheData;
    /* eslint-disable no-plusplus */
    for (let i = 0; i < this.props.listReqCount; i++) {
      const curListReqParam = { ...listReqParam, vendorGroup: i };
      const uniqueCacheKey = getCacheKey({
        url: listUrl,
        uid: AppContext.UserInfo.userId,
        paramBody: curListReqParam,
      });
      cacheData = MemorizeCache.getCacheWithResponseMap({
        key: uniqueCacheKey,
        groupId: listUrl,
      });
      if (cacheData) {
        break;
      }
    }
    if (cacheData) {
      this.isHitCache = true;
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listProductRes,
        cacheData,
      );
      this.handleLimitCacheData();
    }
  };

  refreshRecommend = () => {
    const listNeedRecommend = ListReqAndResData.getData(
      ListReqAndResData.keyList.listNeedRecommend,
    );
    if (listNeedRecommend) {
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listNeedRecommend,
        false,
      );
      const resData = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductRes,
      );
      // 请求无少结果推荐接口
      this.props.fetchRecommend(resData);
    }
  };

  validateHasPageCache = () => {
    const { pageCacheKey, pageParamStr } = getPageCacheKey();
    const cacheData = listPagingAgent.loadSnapshot(pageCacheKey);
    if (cacheData && pageParamStr) {
      this.isHitCache = true;
      AppContext.setEasyLifeSwitch(cacheData?.extras?.packageLevelSwitch);
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listProductReq,
        JSON.parse(pageParamStr),
      );
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listProductRes,
        cacheData,
      );
      AppContext.setRouterListLoaderDispatch(true);
      this.handleLimitCacheData();
      this.props.setIsShowToast(true);
      // 请求无少结果推荐接口
      this.props.fetchRecommend(cacheData);
    }
  };

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().List.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    this.startTime = new Date();
    this.isFetchCouponList = false;
    if (this.props.isNoPoiData) {
      xShowToast({
        title: this.props.isNoRPoiData
          ? Texts.noRPoiDataDesc
          : Texts.noPoiDataDesc,
        duration: 3000,
      });
    }
    if (this.props.isTimePassed) {
      CarLog.LogTrace({
        key: LogKey.c_car_trace_module_exposure,
        info: {
          // 2-榜单跳转列表页时间过期
          errorCode: 2,
          name: Texts.errorToastLogName,
        },
      });
      xShowToast({ title: Texts.routeTimePassed, duration: 3000 });
    }
    // 境外修改订单屏蔽国内POI
    if (this.props?.isRebookOsd) {
      AppContext.setFilterPoi(FilterPoiType.ISD);
    }
  }

  handleUpdateStatus = () => {
    if (this.isHitCache) {
      this.setNeedSubmitSecondLog(false);
      // 更新状态 + 请求限行数据
      this.props.updateFetchCacheStatus({
        isLoading: false,
        isFail: false,
        progress: 1,
        progressIsFinish: true,
      });
      if (Utils.isCtripIsd()) {
        this.props.fetchLimitContent(true);
      }
    }
  };

  // 预加载自助取还图片
  prefetchSelfServiceImage = () => {
    const selfServicePrefetchImages = [
      `${ImageUrl.DIMG04_PATH}1tg2q12000cltx46y01BD.png`,
      `${ImageUrl.DIMG04_PATH}1tg1012000csp6dsqE967.png`,
      `${ImageUrl.DIMG04_PATH}1tg6o12000csp6w1nBDEE.png`,
      `${ImageUrl.DIMG04_PATH}1tg5d12000cltx4j0E8D1.png`,
      `${ImageUrl.DIMG04_PATH}1tg0112000cz8q10d3CCC.png`,
    ];

    Utils.imagePrefetch(selfServicePrefetchImages);
  };

  lazyComponentDidMount() {
    // @ts-ignore
    this.delayTime = new Date() - this.startTime;
    this.setUpdateTime = new Date();
    this.handleUpdateStatus();
    this.onLazyLoadLogic();
    this.setUserBehavior();

    // 记录缓存命中埋点
    if (this.isHitCache) {
      const param = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductReq,
      );
      const res = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductRes,
      );
      const requestId = lodashGet(res, 'baseResponse.requestId');
      const searchType = CarABTesting.isListInPage()
        ? ListEnum.FetchListPageType.Search
        : '';
      LogListFinalTrace({ requestId }, res, true, false, searchType);
      LogListEachTrace(false, param, res, null, searchType);
    }

    this.checkIsLogin();

    this.addTimer(
      setTimeout(() => {
        // 预加载自助取还图片
        // 图片预加载埋点
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_list_page_image_prefetch,
          info: {
            hasPrefetchedSelfServiceImage: this.hasPrefetchedSelfServiceImage,
            hasSelfService: this.props.hasSelfService,
          },
        });
        if (!this.hasPrefetchedSelfServiceImage && this.props.hasSelfService) {
          this.hasPrefetchedSelfServiceImage = true;
          this.prefetchSelfServiceImage();
        }
      }, 500),
    );
    // 每次加载列表页清除无结果提示标记
    CarStorage.remove(StorageKey.RECOMMEND_TOAST);
    // 请求ipoll配置
    if (GetABCache.isISDListIpoll() || GetABCache.isOSDListIpoll()) {
      this.props.queryListIpollConfig({ pageType: 1 });
    }
  }

  // 预加载详情页CRN点评模块
  preloadCommentCRNModule = () => {
    // @ts-ignore
    Business.preloadCRNInstanceForBusiness({
      crnUrl: '/rn_vac_comment/_crn_config?CRNModuleName=CtripApp&CRNType=1',
      reuseInstance: false,
    });
    AppContext.setHasPreLoadCommentList(true);
  };

  /* eslint-disable camelcase */
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!this.isSubmitFirstLog && !nextProps.isLoading) {
      this.performanceMonitor.receiveResStart = new Date();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { isLoading, progress } = this.props;
    // 数据加载完成，再发送用户点击重新搜索埋点
    if (this.sceneType && progress === 1 && prevProps?.progress < 1) {
      this.traceUserBehavior(this.sceneType);
      this.sceneType = null;
    }
    if (CarServerABTesting.isRecommend()) {
      this.refreshRecommend();
    }

    if (Utils.isCtripIsd() && this.state.isLogin !== prevState.isLogin) {
      this.isLoginChanged = true;
    }

    const curState = this.getCurStage();
    if (!this.isSubmitFirstLog && curState !== PAGESTAGE.INIT) {
      this.isSubmitFirstLog = true;
      this.onPageReady();
      if (curState === PAGESTAGE.FAIL) {
        this.setNeedSubmitSecondLog(false);
      }
      setTimeout(() => {
        if (this.state.isShowOther) {
          return;
        }
        this.loadOther();
        this.lazyLoadOtherModules();
        this.setState({
          isShowOther: true,
        });
        this.pageloadSuccessTime = new Date().getTime();
      }, 0);
    }

    if (!Utils.isCtripIsd()) {
      this.handleLog();
    }

    // 列表页首次渲染 && 命中缓存 && 进度条未完成，则设置进度条完成
    if (
      this.props.progress === 1 &&
      !this.props.progressIsFinish &&
      this.searchCount === 0 &&
      getIsFromCache() &&
      curState === PAGESTAGE.SHOW &&
      Utils.isCtripOsd()
    ) {
      this.props.setProgressIsFinish(true);
    }

    // 页面加载完成，执行筛选动作
    if (
      !CarABTesting.isListInPage() &&
      this.props.progressIsFinish &&
      AppContext.RouterLoader.List &&
      !AppContext.RouterLoader.List.isDispatch
    ) {
      const { groupCode, filters } = AppContext.RouterLoader.List;
      const {
        selectedFilters,
        updateSelectedFilter,
        setFilterNoResult,
        activeGroupId,
      } = this.props;

      const doFilter = () => {
        let currentActiveGroupId = activeGroupId;
        if (groupCode && ListResSelectors.hasGroupCode(groupCode)) {
          currentActiveGroupId = groupCode;
        }
        if (filters && filters.length > 0) {
          Hooks.updateSelectedFilterByCodeGroup({
            filters: filters.map(m => ({ code: m, isSelected: true })),
            selectedFilters,
            updateSelectedFilter,
            setFilterNoResult,
            activeGroupId: currentActiveGroupId,
          });
        } else if (activeGroupId !== currentActiveGroupId) {
          this.props.setActiveGroupId({ activeGroupId: currentActiveGroupId });
        }
      };
      if (this.isHitCache) {
        doFilter();
      } else {
        setTimeout(doFilter, 500);
      }

      this.closeTipPop();
      AppContext.setRouterListLoaderDispatch(true);
    }

    if (!CarABTesting.isListInPage()) {
      this.logRes(prevProps);
    }

    // 分页命中缓存的情况，需要更新筛选项
    if (
      CarABTesting.isListInPage() &&
      this.isHitCache &&
      !this.hasUpdateResStatus
    ) {
      this.props.handleResGroupIdAndFilters();
      this.hasUpdateResStatus = true;
    }

    if (this.props.isGroupFail || this.props.isFilterFail) {
      this.scrollDownCallback();
    }

    if (this.isNoPoiData && !isLoading) {
      this.isNoPoiData = false;
      setTimeout(() => {
        this.props.setLocationAndDatePopIsShow({ visible: true });
      }, 100);
    }
    // 第一批资源返回渲染
    if (prevProps?.progress === 0 && this.props?.progress >= 0.6) {
      this.setState({
        finishLoadingAnimate: true,
      });
    }
    // 重新搜索
    if (prevProps?.progress === 1 && this.props?.progress === 0) {
      this.clearListPollTimer();
      this.setState({
        isShowMaskLoading: true,
        finishLoadingAnimate: false,
      });
    }

    if (!this.props.isLoading && prevProps.isLoading) {
      if (this.listHeaderHeight) {
        this.handleTipPop();
      }
    }
  }

  clearListPollTimer = () => {
    if (this.listPollRef?.timerRef) {
      clearTimeout(this.listPollRef.timerRef);
    }
  };

  loadOther = () => {
    this.props.fetchReceivePromotion({ listFetchSuccess: true });
    this.props.getListWarningInfo({ pageIndexId: PageIndexId.List });
    // 预请求城市列表
    if (AppContext.isHomeCombine) {
      this.props.preFetchCity();
    }
    if (!AppContext.hasPreLoadCommentList) {
      this.preloadCommentCRNModule();
    }
    // 请求会员等级信息
    this.props.getUserInfo();
  };

  queryList = () => {
    this.props.fetchList({
      isHitCache: this.isHitCache,
      isHitLimitCache: this.isHitLimitCache,
      isWillMount: true,
    });
    setTimeout(() => {
      this.setState({
        isFetchFinish: true,
      });
    }, 0);
  };

  osdRefreshQueryList = () => {
    EventHelper.sendEvent(EventName.removeListCache, {});
    this.props.fetchList();
  };

  /* eslint-disable */
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();

    if (!this.isHitCache) {
      if (this.state.isLogin) {
        this.queryList();
        return;
      }
      // 出境触发AB分流
      if (Utils.isCtripIsd() || GetAB.isLoginAB()) {
        this.login(
          () => {
            this.setState({ isLogin: true });
            this.queryList();
          },
          () => {
            this.pop();
          },
        );
        return;
      }
      this.queryList();
    } else {
      this.setState({
        isFetchFinish: true,
      });
    }

    this.isHitCacheAtWillMount = this.isHitCache;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    // 融合版出列表页会先unload，已重置AppContext
    const { isShowSearchConditionTip, apptype, isHomeCombine } = this.props
      ?.urlQuery?.isHomeCombine
      ? this.props?.urlQuery
      : AppContext.UrlQuery;
    // 离开了页面,如果有总价提示，不再展示总价提示
    const isCtripIsd = Utils.isCtripIsdByType(apptype);
    // 是否命中服务端AI排序算法AB实验
    const isAiSortTag = getIsAiSortTag();
    if (isShowSearchConditionTip !== 'true' && isCtripIsd) {
      CarStorage.save(StorageKey.TOTAL_PRICE_POP, true);
    }
    if (GetABCache.isAiSort2()) {
      // 使前端缓存无效，确保后续请求获取最新数据
      AppContext.setUserFetchCacheId({
        actionType: 'aiUpFeedBackAction',
      });
    }
    if (isAiSortTag) {
      // 使前端缓存无效，确保后续请求获取最新数据
      AppContext.setUserFetchCacheId();
    }
    removeEvents();

    // TODO: 融合版本不需要发送时间回首页，非融合版本待高质量排查
    if (!isHomeCombine) {
      this.sendEvents(isCtripIsd);
    }
    this.props.reset();
    ListReqAndResData.removeData();
    // 清空列表页缓存
    listPagingAgent.clear();
    removeListStatusData();
    this.clearListPollTimer();
    CarLog.LogDevError({
      expPoint: 'valid cache',
      isHitCacheAtWillMount: !!this.isHitCacheAtWillMount,
      isHitCache: !!this.isHitCache,
      isEqual: !!this.isHitCacheAtWillMount === !!this.isHitCache,
      initialPage: AppContext.UrlQuery.initialPage,
      url: AppContext.Url,
    });
    AppContext.setFilterPoi(FilterPoiType.Default);
  }

  shouldComponentUpdate() {
    if (AppContext.PageInstance.getPageId() !== Channel.getPageId().List.ID) {
      return false;
    }
    return true;
  }

  addUrlTopParam = ({ vehicleId, vendorId }) => {
    AppContext.setRouterListLoader({
      groupCode: '',
      filters: [`${ApiResCode.VENDOR_FILTER_PREFIX}${vendorId}`],
      vehicleId,
    });
  };

  toggleAiFeedbackCacheFlag() {
    // 设置AI正反馈的showRemoveCache标识
    const aiUpFeedBack = getListStatusData(listStatusKeyList.aiUpFeedBack);
    if (aiUpFeedBack && aiUpFeedBack.aiUpFeedBackAction) {
      setListStatusData(listStatusKeyList.aiUpFeedBack, {
        ...aiUpFeedBack,
        showRemoveCache: true,
      });
    }
  }

  handleDownGradeCallback = data => {
    this.props.setLocationAndDatePopIsShow({ visible: true });
    this.addUrlTopParam(data);
  };
  backToListFetch = (params?) => {
    const { isRentCenterPoint, fetchList } = this.props;
    if (isRentCenterPoint) {
      const newParams = params
        ? { ...params, filters: [ApiResCode.ITEM_CODE.RentalStoreEnter] }
        : { filters: [ApiResCode.ITEM_CODE.RentalStoreEnter] };
      fetchList(newParams);
    } else {
      fetchList(params);
    }
    this.sceneType = SceneType.isFromProductNew;
  };

  // 发送用户行为埋点
  traceUserBehavior = sceneType => {
    const listRes = ListReqAndResData.getData(
      ListReqAndResData.keyList.listProductRes,
    );
    // 发送用户行为埋点
    CarLog.LogTrace({
      key: Utils.isCtripIsd()
        ? LogKey.c_car_ctrip_isd_search_seed
        : LogKey.c_car_ctrip_osd_list_search_seed,
      info: {
        requestId: listRes?.baseResponse?.requestId,
        scene_type: sceneType,
        eventResult: listRes?.allVehicleCount > 0,
      },
    });
    const info = CarLog.getLogInfo({});
    // 发送用户行为埋点至服务端
    CarFetch.trackPage({
      extendInfos: [
        {
          extendInfo: {
            ...info,
            scene_type: sceneType,
            keyId: Utils.isCtripIsd()
              ? LogKey.c_car_ctrip_isd_search_seed
              : LogKey.c_car_ctrip_osd_list_search_seed,
            key: Utils.isCtripIsd()
              ? 'c_car_ctrip_isd_search_seed'
              : 'c_car_ctrip_osd_list_search_seed',
            eventResult: listRes?.vehicleList?.length > 0 ? '1' : '0',
            requestId: listRes?.baseResponse?.requestId,
          },
        },
      ],
    });
  };

  pageDidAppear = () => {
    super.pageDidAppear();
    if (this.pageAppearCount > 1) {
      // fix: 兼容OpenURL打开VendorList时，后续页面触发列表页刷新的场景
      getListStatusDataByStorage();
      this.checkIsLogin();
    }

    // 判断是否从未登录到登录，且又点击降级调整取车时间
    const loginFromProduct = getListStatusData(
      listStatusKeyList.loginFromProduct,
    );
    const downGradeFromProductInfo = getListStatusData(
      listStatusKeyList.downGradeFromProduct,
    );
    const isDownGradeAndNoLogin = loginFromProduct && downGradeFromProductInfo;

    if (
      getListStatusData(listStatusKeyList.loginFromProduct) ||
      getListStatusData(listStatusKeyList.priceChangeFromBook)
    ) {
      this.resetHitCacheStatus();
      this.backToListFetch({
        callbackFun: isDownGradeAndNoLogin
          ? () => {
              this.handleDownGradeCallback(downGradeFromProductInfo);
            }
          : null,
      });
    }
    if (!loginFromProduct && downGradeFromProductInfo) {
      this.addUrlTopParam(downGradeFromProductInfo);
      this.props.setLocationAndDatePopIsShow({ visible: true });
      setListStatusData(listStatusKeyList.downGradeFromProduct, null);
    }
    if (this.isFetchCouponList) {
      this.props.fetchReceivePromotion({ listFetchSuccess: true });
      this.isFetchCouponList = false;
    }

    // 从新详情页点击时间过期弹层回来时，需要打开时间选择器
    const timeOutFromVendorListPage = getListStatusData(
      listStatusKeyList.timeOutFromVendorListPage,
    );
    if (timeOutFromVendorListPage) {
      this.datePickerRef?.showCalendar();
      this.props.setDatePickerIsShow({ visible: true });
      setListStatusData(listStatusKeyList.timeOutFromVendorListPage, null);
    }

    // 从新详情页回退回来，若新详情页因为登录或领券等重新刷新页面，则列表页也需要重新刷新页面
    if (getListStatusData(listStatusKeyList.refreshFromVendorListPage)) {
      this.resetHitCacheStatus();
      this.backToListFetch();
      setListStatusData(listStatusKeyList.refreshFromVendorListPage, null);
    }

    // 从新详情页回退回来，若新详情页的接口数据是实时请求的，则列表页也需要重新刷新页面
    const cacheExpireFromVendorListPage = getListStatusData(
      listStatusKeyList.cacheExpireFromVendorListPage,
    );
    const cacheToExpireFromVendorListPage = getListStatusData(
      listStatusKeyList.cacheToExpireFromVendorListPage,
    );
    if (cacheExpireFromVendorListPage || cacheToExpireFromVendorListPage) {
      AppContext.setUserFetchCacheId({
        actionType: 'newProductCacheExpireFromVendorListPage',
      });
      this.resetHitCacheStatus();
      this.backToListFetch({ uniqSign: cacheExpireFromVendorListPage || null });
      setListStatusData(listStatusKeyList.cacheExpireFromVendorListPage, null);
      setListStatusData(
        listStatusKeyList.cacheToExpireFromVendorListPage,
        false,
      );
      xShowToast({ title: Texts.reloadTipFromDetail, duration: 3000 });
    }

    // 从新详情页回退回来，若新详情页有进行领券订，则列表页也需要重新刷新页面
    if (getListStatusData(listStatusKeyList.couponBookFromVendorListPage)) {
      AppContext.setUserFetchCacheId({
        actionType: 'couponBookFromVendorListPage',
      });
      this.resetHitCacheStatus();
      this.backToListFetch();
      setListStatusData(listStatusKeyList.couponBookFromVendorListPage, null);
    }

    // 从新详情页回退回来，如果是点击推荐车型进入的详情页，则列表页需要重新刷新页面
    if (getListStatusData(listStatusKeyList.updateLocationAndDateByRecommend)) {
      AppContext.setUserFetchCacheId({
        actionType: 'updateLocationAndDateByRecommend',
      });
      this.resetHitCacheStatus();
      this.backToListFetch();
      setListStatusData(
        listStatusKeyList.updateLocationAndDateByRecommend,
        null,
      );
    }

    // 从境外产品详情页异常场景返回上一页，需要刷新场景
    if (getListStatusData(listStatusKeyList.refreshFromOSDProductPage)) {
      AppContext.setUserFetchCacheId({
        actionType: 'refreshFromOSDProductPage',
      });
      this.resetHitCacheStatus();
      this.props.fetchList();
      setListStatusData(listStatusKeyList.refreshFromOSDProductPage, null);
      this.sceneType = SceneType.isFromProductNew;
    }
  };

  pageDidDisappear = () => {
    super.pageDidDisappear();
    this.logExposureData();
    this.updateSearchFormStatus();
    this.isFetchCouponList = true;
  };

  logRes = prevProps => {
    const { listReqParam, progress } = this.props;
    if (prevProps.progress < 1 && progress === 1) {
      const { isError } = this.props;
      const param =
        ListReqAndResData.getData(ListReqAndResData.keyList.listProductReq) ||
        listReqParam;
      const res = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductRes,
      );
      const fromPageId = getFromPage();
      const pageId = Channel.getPageId();
      let fromPage = '';
      switch (fromPageId) {
        case pageId.List.ID:
          fromPage = Texts.listNoResultExposurePage.List;
          break;
        case pageId.Product.ID:
          fromPage = Texts.listNoResultExposurePage.Product;
          break;
        case pageId.Book.ID:
          fromPage = Texts.listNoResultExposurePage.Book;
          break;
        case pageId.Home.ID:
          fromPage = Texts.listNoResultExposurePage.Home;
          break;
        default:
          fromPage = Texts.listNoResultExposurePage.Default;
      }
      LogListResExposure({
        param,
        res,
        isFromCache: this.isHitCache,
        isError,
        fromPage,
      });
    }
  };

  async checkIsLogin() {
    const isLogin = User.isLoginSync();
    if (this.state.isLogin !== isLogin) {
      this.setState({
        isLogin,
      });
    }
  }

  onLazyLoadLogic = () => {
    this.registerEvents();
    removeExposureData();
    const pageName = `${PageName.List}_${new Date().getTime()}`;

    Page.registerPage(pageName, () => {});
    setRegisterPageData(Channel.getPageId().List.EN, pageName);
  };

  pageWillDisappear() {}

  registerEvents() {
    EventHelper.addEventListener(EventName.changeRentalISDLocation, data => {
      if (Utils.isCtripIsd()) {
        this.props.setLocationInfo({
          ...data,
          fromEvent: 'changeRentalLocation',
        });
      }
    });

    EventHelper.addEventListener(EventName.changeRentalLocation, data => {
      if (Utils.isCtripOsd()) {
        this.props.setLocationInfo({
          ...data,
          fromEvent: 'changeRentalLocation',
        });
      }
    });

    EventHelper.addEventListener(EventName.SideToolBoxDidOpen, () => {
      if (this.props.sortAndFilterVisible) {
        this.props.setActiveFilterBarCode('');
        this.props.setFilterModalIsShow({ visible: false });
      }
    });

    EventHelper.addEventListener(
      Utils.isCtripIsd()
        ? EventName.changeISDSaleOutList
        : EventName.changeSaleOutList,
      data => {
        const { saleOutProductKey } = data;
        // 已售罄产品处理
        const newSaleOutList = Object.assign([], this.props.saleOutList);
        if (saleOutProductKey && !newSaleOutList.includes(saleOutProductKey)) {
          newSaleOutList.push(saleOutProductKey);
          this.props.setSaleOutList({
            saleOutList: newSaleOutList,
          });
        }
      },
    );

    EventHelper.addEventListener(EventName.filterByRentCenter, data => {
      const code = ApiResCode.ITEM_CODE.RentalStoreEnter;
      const { selectedFilters, updateSelectedFilter, activeGroupId } =
        this.props;
      Hooks.updateSelectedFilterByCode({
        code,
        isSelected: true,
        selectedFilters,
        updateSelectedFilter,
        activeGroupId,
      });
      if (!data?.unShowToast) {
        xShowToast({ title: Texts.rentCenterToast, duration: 1000 });
      }
    });

    EventHelper.addEventListener(EventName.filterBySelfService, () => {
      const code = ApiResCode.ITEM_CODE.SelfService;
      const { selectedFilters, updateSelectedFilter, activeGroupId } =
        this.props;
      Hooks.updateSelectedFilterByCode({
        code,
        isSelected: true,
        selectedFilters,
        updateSelectedFilter,
        activeGroupId,
      });
      this.vehicleListRef?.scrollToTop?.();
    });

    EventHelper.addEventListener(EventName.easyLandingPage, () => {
      const nextGroupCode = ApiResCode.EasyLifeCode;
      xShowToast({ title: Texts.carefreeToast, duration: 1000 });
      if (ListResSelectors.hasGroupCode(nextGroupCode)) {
        this.props.setActiveGroupId({ activeGroupId: nextGroupCode });
      }
    });

    EventHelper.addEventListener(EventName.osdEasyLandingPage, () => {
      const nextGroupCode = ApiResCode.EasyLifeCode;
      if (ListResSelectors.hasGroupCode(nextGroupCode)) {
        this.props.setActiveGroupId({ activeGroupId: nextGroupCode });
      }
    });
  }

  sendEvents(isCtripIsd) {
    EventHelper.sendEvent(
      isCtripIsd
        ? EventName.changeRentalISDLocation
        : EventName.changeRentalDate,
      this.props.indexCallbckData,
    );
  }

  logExposureData() {
    const data = getExposureData();
    CarLog.LogTrace({
      key: LogKey.c_car_trace_list_exposure,
      info: { data },
    });
  }

  updateSearchFormStatus = () => {
    const { selectedFilters, progressIsFinish, filterItems, setFilterItems } =
      this.props;
    const { homeSearchFormRefEvent } = AppContext.UrlQuery || {};

    const isSelect = selectedFilters.bitsFilter.includes(
      ApiResCode.FILTER_CODE.StoreService_PickupOnDoor,
    );
    // 站内取还，列表页首页联动
    const isSelectStationPR = selectedFilters.bitsFilter.includes(
      ApiResCode.FILTER_CODE.PickReturn_StationPR,
    );
    const { SesameCode } = Sesame.SesameConstant;
    const nextCheck =
      selectedFilters.bitsFilter.includes(
        SesameCode['SpecialService_Free&Deposit'],
      ) ||
      selectedFilters.bitsFilter.includes(
        SesameCode.SpecialService_FreeCarDeposit,
      ) ||
      selectedFilters.bitsFilter.includes(
        SesameCode.SpecialService_FreePreAuthorization,
      ) ||
      selectedFilters.bitsFilter.includes(
        SesameCode.SpecialService_FreePreAuthorizationAfterVerify,
      );

    const filterCodeSelectsData = progressIsFinish && {
      filterCodeSelects: [
        {
          code: ApiResCode.FILTER_CODE.StoreService_PickupOnDoor,
          isSelected: isSelect,
        },
        {
          code: ApiResCode.FILTER_CODE.PickReturn_StationPR,
          isSelected: isSelectStationPR,
        },
      ],
    };
    if (AppContext.HomeSearchFormRef) {
      if (AppContext.HomeSearchFormRef.updateFilterItems && progressIsFinish) {
        const isSelect = selectedFilters.bitsFilter.includes(
          ApiResCode.FILTER_CODE.StoreService_PickupOnDoor,
        );
        AppContext.HomeSearchFormRef.updateFilterItems(
          ApiResCode.FILTER_CODE.StoreService_PickupOnDoor,
          isSelect,
        );
        // 站内取还，列表页首页联动
        const isSelectStationPR = selectedFilters.bitsFilter.includes(
          ApiResCode.FILTER_CODE.PickReturn_StationPR,
        );
        AppContext.HomeSearchFormRef.updateFilterItems(
          ApiResCode.FILTER_CODE.PickReturn_StationPR,
          isSelectStationPR,
        );
      }
    }

    if (homeSearchFormRefEvent) {
      EventHelper.sendEvent(homeSearchFormRefEvent, {
        updateFilterItemsArgs: {
          ...filterCodeSelectsData,
          sesameChecked: nextCheck,
        },
      });
    }

    if (progressIsFinish) {
      updateFilterItems({
        filterItems,
        setFilterItems,
        ...filterCodeSelectsData,
        sesameChecked: nextCheck,
      });
    }
  };

  pageGoBack = async () => {
    if (this.props.sortAndFilterVisible) {
      this.props.setActiveFilterBarCode('');
      this.props.setFilterModalIsShow({ visible: false });
      this.props.setVendorListModalData({ visible: false });
    } else {
      const { rentalLocationAndDate } = this.props;
      const { cid } = rentalLocationAndDate?.rentalLocation?.pickUp || {};
      if (
        new Date().getTime() - this.pageloadSuccessTime >= 2000 &&
        InstantSurveySenceIdTypes[cid]
      ) {
        this.pop();
        CarLog.LogCode({ name: '点击_列表页_后退' });
      } else {
        this.pop();
        CarLog.LogCode({ name: '点击_列表页_后退' });
      }
    }
  };

  getCurStage() {
    let curStage;
    const { isFail, isLoading, isRecommendLoading, isRecommendNoResult } =
      this.props;
    const isRecommend = CarServerABTesting.isRecommend();
    // 列表页搜索无结果失败情况下
    if (isFail) {
      // 若列表页的无少结果推荐服务端实验为B版
      if (isRecommend) {
        // 加载推荐接口
        if (isRecommendLoading) {
          curStage = PAGESTAGE.INIT;
        } else {
          // 加载完成推荐信息后，判断是否是推荐无结果
          if (isRecommendNoResult) {
            curStage = PAGESTAGE.FAIL;
          } else {
            curStage = PAGESTAGE.SHOW;
          }
        }
      } else {
        curStage = PAGESTAGE.FAIL;
      }
    } else if (!isLoading) {
      curStage = PAGESTAGE.SHOW;
    } else {
      curStage = PAGESTAGE.INIT;
    }
    return curStage;
  }

  secondPerformanceLog = () => {
    const responseMap = ListResSelectors.getBaseResponseMap();
    const interactiveStart = new Date();
    const pageReady =
      +interactiveStart - +this.performanceMonitor.loadStart || 0;
    const {
      networkCost = 0,
      isFromCache = null,
      isCacheValid = null,
      isSuccess = null,
      hasRetry = null,
      hasResult = null,
      cacheFrom = '',
    } = responseMap || {};
    let renderCost = +pageReady - +networkCost;
    if (!renderCost || renderCost < 0) {
      renderCost = 0;
    }
    const logInfo = {
      key: LogKey.c_car_trace_list_second_page_interactive_time,
      info: {
        pageReady,
        networkCost,
        renderCost,
        isFromCache,
        isCacheValid,
        cacheFrom,
        hasRetry,
        isSuccess,
        hasResult,
        pageId: this.getPageId(),
      },
    };

    CarLog.LogTrace(logInfo);
  };

  // 加一个在报价layout时的性能埋点
  performanceLog = () => {
    if (!this.submitLog) {
      this.performanceMonitor.responseMap =
        ListResSelectors.getBaseResponseMap();
      const interactiveStart = new Date();

      const domReady =
        +this.performanceMonitor.firstRenderEnd -
          +this.performanceMonitor.firstRenderStart || 0;
      const pageReady =
        +interactiveStart - +this.performanceMonitor.loadStart || 0;
      const {
        networkCost = 0,
        isFromCache = null,
        isCacheValid = null,
      } = this.performanceMonitor.responseMap || {};
      let renderCost = +pageReady - +networkCost;
      if (!renderCost || renderCost < 0) {
        renderCost = 0;
      }
      const logInfo = {
        key: LogKeyDev.c_car_trace_list_interactive_time,
        info: {
          domReady,
          pageReady,
          networkCost,
          renderCost,
          isFromCache,
          isCacheValid,
          pageId: this.getPageId(),
        },
      };

      CarLog.LogTraceDev(logInfo);
      this.submitLog = true;
    }
  };

  initialOsdUserBrowsed = async () => {
    try {
      const { setOsdUserBrowsed } = this.props;
      loadOsdUserBrowsed(setOsdUserBrowsed);
      // eslint-disable-next-line no-empty
    } catch (e) {}
  };

  onPageReady = () => {
    const { homeSearchFormRefEvent } = AppContext.UrlQuery || {};
    this.traceUserBehavior(
      homeSearchFormRefEvent ? SceneType.isFromHome : SceneType.isFromMarket,
    );
    try {
      this.performanceMonitor.responseMap =
        ListResSelectors.getBaseResponseMap();
      if (this.isHitCache) {
        this.performanceMonitor.responseMap['hasRetry'] = false;
        this.performanceMonitor.responseMap['isSuccess'] = true;
        this.performanceMonitor.responseMap['hasResult'] = true;
        this.performanceMonitor.delayTime = this.delayTime;
      }
      this.performanceMonitor.contentShowStart =
        this.performanceMonitor.interactiveStart = new Date();
      this.performanceMonitor.fromurl =
        this.props.fromurl || AppContext.UrlQuery.fromurl;
      this.performanceMonitor.packageDataTime =
        ListPerformanceLog.getAllPackageTime();
      this.performanceMonitor.isHitCache = this.isHitCache;
      this.performanceMonitor.isFromSearch = ListResSelectors.getIsFromSearch();

      // 各个阶段
      if (this.isHitCache) {
        const delayReady =
          // @ts-ignore
          this.performanceMonitor.receiveResStart - this.setUpdateTime;
        this.performanceMonitor.delayReady = delayReady;
        this.performanceMonitor.beforeFetchReady = 0;
        this.performanceMonitor.afterFetchReady = 0;
      }
      this.performanceMonitor.submit();
      this.performanceMonitor.submitTTI();
    } catch (e) {}
    // 加载出境浏览记录
    if (GetABCache.isAiSort2()) {
      this.initialOsdUserBrowsed();
    }
  };

  setVehicleListThreshold = ({ nativeEvent }) => {
    const { height } = nativeEvent.layout;
    const filterBarIsShow = getListStatusData(
      listStatusKeyList.filterBarIsShow,
    );
    const quickFilterBarIsShow = getListStatusData(
      listStatusKeyList.quickFilterBarIsShow,
    );
    if (!this.headerAnimating && (filterBarIsShow || quickFilterBarIsShow)) {
      this.listThresholdLayout = height;
    }
  };

  setListHeaderHeight = ({ nativeEvent }) => {
    const { height } = nativeEvent.layout;
    this.listHeaderHeight = height;
    if (!this.props.isLoading) {
      this.handleTipPop();
    }
  };

  handleDatePickerRef = ref => {
    this.datePickerRef = ref;
    this.setState({
      datePickerRefDone: true,
    });
  };

  handleSearchPanelModalRef = ref => {
    this.searchPanelModalRef = ref;
  };

  onBackAndroid() {
    const {
      datePickerVisible,
      locationDatePopVisible,
      agePickerVisible,
      ageTipPopVisible,
      sortAndFilterVisible,
      vehiclePopVisible,
      limitRulePopVisible,
      vendorListModalVisible,
      easyLifePopVisible,
      totalPriceModalVisible,
      priceSummaryModalVisible,
    } = this.props;
    const {
      warningTipsModalVisible,
      couponModalVisible,
      searchCarModalVisible,
    } = this.state;
    if (datePickerVisible) {
      this.props.setDatePickerIsShow({ visible: false });
    } else if (priceSummaryModalVisible) {
      this.props.setPriceSummaryModal(false);
    } else if (locationDatePopVisible) {
      this.searchPanelModalRef?.onCancel?.();
      this.props.setLocationAndDatePopIsShow({ visible: false });
      AppContext.setListPageIsEffect(true);
    } else if (agePickerVisible) {
      this.props.setAgePickerIsShow({ visible: false });
    } else if (ageTipPopVisible) {
      this.props.setAgeTipPopIsShow({ visible: false });
    } else if (sortAndFilterVisible) {
      this.props.setActiveFilterBarCode('');
      this.props.setFilterModalIsShow({ visible: false });
    } else if (vehiclePopVisible) {
      this.props.setVehPopData({ visible: false });
    } else if (limitRulePopVisible) {
      this.props.setLimitRulePopVisible({ visible: false });
    } else if (totalPriceModalVisible) {
      this.props.setTotalPriceModalData({ visible: false });
    } else if (vendorListModalVisible) {
      this.props.setVendorListModalData({ visible: false });
    } else if (easyLifePopVisible) {
      this.props.setEasyLifePopVisible({ visible: false });
    } else if (warningTipsModalVisible) {
      this.setState({ warningTipsModalVisible: false });
    } else if (couponModalVisible) {
      this.setState({ couponModalVisible: false });
    } else if (searchCarModalVisible) {
      this.setState({ searchCarModalVisible: false });
    } else {
      this.pageGoBack();
    }
  }

  handlePressHeader = () => {
    const {
      isDifferentLocation,
      recommendType,
      subStrategyType,
      pCityId,
      activeGroupName,
    } = this.props;
    const recommendEventResult = ListResSelectors.getRecommendEventResult();

    this.props.setLocationAndDatePopIsShow({ visible: true });
    CarLog.LogCode({
      name: '点击_列表页_头部_修改取还车信息',

      recommendEventResult,
      isDifferentLocation,
      recommendType,
      recommendTimeType: subStrategyType,
      pickupCityId: pCityId,
      groupName: activeGroupName,
    });
  };

  scrollUpCallback = () => {
    this.scrollDown = false;
    const { isShowTipList } = this.state;
    if (this.tipListRef && this.tipListRef.validateHasTip()) {
      this.tipListRef.scrollUpCallback();
      if (isShowTipList) {
        this.setState({
          isShowTipList: false,
        });
      }
    }
  };

  scrollDownCallback = () => {
    this.scrollDown = true;
  };

  scrollEndCallback = event => {
    const { nativeEvent } = event;
    const { y } = nativeEvent.contentOffset;
    const { isShowTipList } = this.state;
    if (
      this.scrollDown &&
      this.tipListRef &&
      this.tipListRef.validateHasTip() &&
      !y
    ) {
      this.tipListRef.scrollEndCallback(event);
      if (!isShowTipList) {
        this.setState({
          isShowTipList: true,
        });
      }
    }
  };

  scrollContainerToTop = () => {
    this.vehicleListRef?.scrollToTop();
    this.tipListRef.scrollEndCallback();
    this.setState({
      isShowTipList: true,
    });
    CarLog.LogCode({ name: '点击_列表页_快速回到顶部' });
  };

  goMap = limitScope => {
    this.push(Channel.getPageId().LimitMap.EN, { limitScope });
  };

  setListThreshold = value => {
    this.listThreshold = value;
    const newValue = { marginTop: initFilterModalTop + this.listThreshold };
    styles.filterAndSortModalStyle = {
      ...styles.filterAndSortModalStyle,
      ...newValue,
    };
  };

  handleRefFn = ref => {
    this.vehicleListRef = ref;
  };

  resetHitCacheStatus = () => {
    this.isHitCache = false;
    this.isHitLimitCache = false;
    this.props.getListWarningInfo({ pageIndexId: PageIndexId.List });
  };

  pressSearchCallback = () => {
    this.resetHitCacheStatus();
    this.sceneType = SceneType.isFromList; // 设置用户重新搜索后需要发送的行为埋点标识
    this.searchCount += 1;
    if (GetABCache.isAiSort2()) {
      this.toggleAiFeedbackCacheFlag();
    }
  };

  handleTipRef = ref => {
    this.tipListRef = ref;
  };

  // 添加浏览历史埋点
  setUserBehavior = () => {
    const { historyLogParam, rentalLocationAndDate } = this.props;
    LogHistory({
      type: 'list',
      ...historyLogParam,
      params: rentalLocationAndDate,
    });
  };

  handleLog = () => {
    if (
      this.needSubmitSecondLog &&
      this.isSubmitFirstLog &&
      !this.isSubmitSecondLog &&
      this.props.progress === 1
    ) {
      this.secondPerformanceLog();
      this.isSubmitSecondLog = true;
    }
  };

  handleTipPop = () => {
    const { isShowSearchConditionTip } = AppContext.UrlQuery;
    const { setTipPopData = Utils.noop } = this.props;
    if (
      isShowSearchConditionTip === 'true' &&
      !this.hasSearchConditionTipShowed
    ) {
      setTipPopData({
        visible: true,
        data: {
          style: {
            top: this.listHeaderHeight,
            justifyContent: isAndroid ? 'flex-start' : 'center',
          },
          content: Texts.searchConditionTip,
          type: ListEnum.TipPopType.Search,
        },
      });
      setTimeout(() => {
        setTipPopData({
          visible: false,
        });
      }, ListConstants.tipPopTimeOut);
      this.hasSearchConditionTipShowed = true;
    }
  };

  // 总价气泡——去除功能，保留位置
  closeTipPop = () => {};

  getSearchConditionTipProps = () => {
    const {
      tipPopVisible,
      setTipPopData = Utils.noop,
      tipPopData = {} as any,
    } = this.props;
    return {
      visible: tipPopVisible,
      setTipPopData,
      isHitCache: this.isHitCache,
      ...tipPopData,
    };
  };

  // 设置是否需要进行第二批用户可交互时长埋点
  setNeedSubmitSecondLog = flag => {
    this.needSubmitSecondLog = flag;
  };

  showWarningTipsModal = content => {
    this.setState({
      warningTipsModalVisible: true,
      warningTipsModalCotent: content,
    });
  };

  closeWarningTipsModal = () => {
    this.setState({
      warningTipsModalVisible: false,
    });
  };

  showSearchModal = (searchCarModalFromFilter = true) => {
    const { searchBarFixTop } = this.state;
    if (!searchBarFixTop) {
      this.setState({
        searchCarModalVisible: true,
        searchCarModalFromFilter: false,
        searchBarFixTop: vh(10),
      });
    } else {
      this.setState({
        searchCarModalVisible: true,
        searchCarModalFromFilter,
      });
    }
    CarStorage.save(
      StorageKey.CAR_LIST_LOADED_SEARCH_BAR_GUIDE,
      true,
      '100000M',
    );
  };

  getSearchCarModalHeight = top => {
    this.setState({
      searchBarFixTop: top,
    });
  };

  onCloseSearchCarModal = () => {
    this.setState({
      searchCarModalVisible: false,
      searchCarModalFromFilter: true,
      isShowSearchInput: false,
    });
  };

  showSearchInput = visible => {
    this.setState({
      isShowSearchInput: visible,
    });
  };

  showCouponModal = () => {
    this.setState({
      couponModalVisible: true,
    });
  };

  closeCouponModal = () => {
    this.setState({
      couponModalVisible: false,
    });
    const {
      isListReceiveSuccess,
      setIsListReceiveSuccess,
      fetchList,
      couponReset,
    } = this.props;

    if (isListReceiveSuccess || this.isLoginChanged) {
      // 清空列表页缓存
      AppContext.setUserFetchCacheId({
        actionType: 'listCloseCouponModal',
      });
      this.resetHitCacheStatus();
      clearTimeout(this.couponModalTimer);
      this.couponModalTimer = setTimeout(() => {
        fetchList();
        couponReset();
        if (isListReceiveSuccess) {
          xShowToast({
            title: '已自动计算优惠后价格',
            duration: 3000,
          });
          setIsListReceiveSuccess(false);
        }
        if (this.isLoginChanged) {
          this.isLoginChanged = false;
        }
      }, 300);
    }
  };

  onPressGoTrip = () => {
    const { setLicenseModalData } = this.props;
    CarLog.LogCode({ name: '点击_列表页_去trip看看' });
    this.push(Channel.getPageId().Middle.EN);
    setTimeout(() => {
      setLicenseModalData({
        visible: false,
        data: '',
        title: '',
      });
    }, 500);
  };

  isShowLoadingModule = curStage => {
    const { isFilterLoading } = this.props;
    const showCondition = curStage === PAGESTAGE.INIT;
    if (this.getIsNotLogin()) {
      return false;
    }
    if (CarABTesting.isListInPage()) {
      return showCondition || isFilterLoading;
    }
    return showCondition;
  };

  isShowLoadingImg = () => {
    const isListInPage = CarABTesting.isListInPage();
    if (!isListInPage && this.isHitCache) {
      return false;
    } else if (isListInPage && this.isHitCache && !this.props.isFilterLoading) {
      return false;
    }

    return true;
  };

  isShowFailModule = curStage => {
    const { isFilterFail } = this.props;
    const showCondition = curStage === PAGESTAGE.FAIL;
    if (CarABTesting.isListInPage()) {
      return showCondition || isFilterFail;
    }
    return showCondition;
  };

  isShowPriceModule = curStage => {
    const { isFilterLoading, isFilterFail } = this.props;
    if (CarABTesting.isListInPage()) {
      return curStage === PAGESTAGE.SHOW && !isFilterLoading && !isFilterFail;
    }

    return curStage === PAGESTAGE.SHOW;
  };

  isShowVehilceListModule = curStage => {
    const { isFilterLoading, isFilterFail, isGroupLoading, isGroupFail } =
      this.props;
    return (
      CarABTesting.isListInPage() &&
      curStage === PAGESTAGE.SHOW &&
      !isFilterLoading &&
      !isFilterFail &&
      !isGroupLoading &&
      !isGroupFail
    );
  };

  isShowGroupLoading = () => {
    return CarABTesting.isListInPage() && this.props.isGroupLoading;
  };

  getLoadingPageName = () => {
    const { isFilterLoading, isGroupLoading } = this.props;
    if (!CarABTesting.isListInPage()) return PageType.List;

    if (isFilterLoading) {
      return PageType.Batch_List_Most;
    } else if (isGroupLoading) {
      return PageType.Batch_List_Half;
    }
    return PageType.Batch_List_Full;
  };

  getIsNotLogin = () => {
    return !this.state.isLogin && (Utils.isCtripIsd() || GetAB.isLoginAB());
  };

  getIsShowLoadingTip = () => {
    const loadingPageName = this.getLoadingPageName();
    if (this.getIsNotLogin()) {
      return false;
    }
    if (Utils.isCtripOsd() && loadingPageName === PageType.List) {
      return true;
    }
    return false;
  };

  isShowGroupFail = () => {
    return CarABTesting.isListInPage() && this.props.isGroupFail;
  };

  onLoadingFinished = () => {
    this.setState({
      isShowMaskLoading: false,
    });
  };

  renderPage() {
    const {
      warningTipsModalVisible,
      couponModalVisible,
      warningTipsModalCotent,
      datePickerRefDone,
      isShowMaskLoading,
      finishLoadingAnimate,
      searchCarModalVisible,
      searchBarFixTop,
      searchCarModalFromFilter,
      isFetchFinish,
      isShowSearchInput,
    } = this.state;
    const curStage = this.getCurStage();
    const {
      filterNoResult,
      promotionData,
      availablePromotionData,
      receivePromotion,
      receiveAllPromotion,
      isNewSearchNoResult,
      isRecommendLoading,
      isRecommendNoResult,
      recommendAllVehicleCount,
      isFail,
      fromPage,
      country,
      isRefactor,
      showListTimer,
      sortAndFilterVisible,
      ipollConfig,
      progressIsFinish,
    } = this.props;
    const { isShowSearchConditionTip, debug } = AppContext.UrlQuery;
    const groupFailNoMatchStyle = {
      style: styles.noMatchStyle,
    };
    const failModuleNoMatchStyle = {
      titleStyle: styles.noMatchStyleTitle,
      subtitleStyle: styles.noMatchStyleSubTitle,
    };

    const isRecommend = CarServerABTesting.isRecommend();
    // 是否是新版无结果推荐
    const isNoResultNew = CarServerABTesting.isNoResult();
    // 是否是列表页无结果推荐且推荐数据返回
    const isNoResultRecommend = isFail && isRecommend && !isRecommendLoading;
    // 列表页头部可用逻辑：命中缓存 ||
    const listHeaderEnable =
      this.isHitCache ||
      (Utils.isCtripOsd()
        ? curStage !== PAGESTAGE.INIT
        : this.props.progress === 1);

    // 置灰筛选项
    const isDisabledListFilterBar =
      Utils.isCtripOsd() && !(this.isHitCache || this.props.progress === 1);
    // 是否是首屏渲染组件
    const isFirstShow = this.isHitCache || curStage === PAGESTAGE.SHOW;
    const showShadow =
      (Utils.isCtripOsd() && !isRecommend) || !this.state.isShowTipList;
    const ipollPosNum = ipollConfig?.find(
      item => item.pageType === 1,
    )?.positionNum;
    const ipollGroup = ipollConfig?.find(item => item.pageType === 1)?.group;
    const sceneid = ipollConfig?.find(item => item.pageType === 1)?.sceneid;
    const isFromCache = getIsFromCache();
    const hasLoadedSearchBarGuide =
      !debug &&
      CarStorage.loadSync(StorageKey.CAR_LIST_LOADED_SEARCH_BAR_GUIDE);
    return (
      // @ts-ignore 升级072
      <XCalendar.PageWithCalendar>
        <ViewPort style={styles.page}>
          <React.Fragment>
            {/* 境外Loading弹层 */}
            {Utils.isCtripOsd() && isShowMaskLoading && !isFromCache && (
              <View
                className={c2xStyles.loadingMask}
                style={{ height: BbkUtils.vh(100) }}
              >
                {!this.isShowLoadingModule(curStage) && (
                  <View
                    className={c2xStyles.animateModal}
                    style={{ height: BbkUtils.vh(100) }}
                  />
                )}
                {this.getIsShowLoadingTip() &&
                  (isFetchFinish ? (
                    <LoadingAnimateTip
                      finishAnimate={finishLoadingAnimate}
                      onFinish={this.onLoadingFinished}
                    />
                  ) : (
                    <LoadingAnimateTipStatic />
                  ))}
              </View>
            )}

            <XBoxShadow
              className={classNames(c2xStyles.wrapper)}
              onLayout={this.setVehicleListThreshold}
              coordinate={{ x: 0, y: 2 }}
              color={
                showShadow
                  ? setOpacity(color.black, 0.05)
                  : setOpacity(color.black, 0)
              }
              opacity={1}
              blurRadius={2}
              elevation={showShadow ? 4 : 0}
            >
              {isShowSearchConditionTip === 'true' && <SearchConditionTip />}
              <View
                style={styles.headerWrap}
                onLayout={this.setListHeaderHeight}
              >
                {!isFetchFinish && !this.getIsNotLogin() && (
                  <View style={styles.grayPlaceholder} />
                )}
                {isFetchFinish && (
                  <ListHeader
                    // @ts-ignore
                    enableChange={listHeaderEnable}
                    handleBackPress={this.pageGoBack}
                    showSearchSelectorWrap={this.handlePressHeader}
                    style={styles.headerStyle}
                    sideLeftStyle={styles.marginTop0}
                    headContentStyle={
                      Utils.isCtripOsd() ? styles.osdHeadContentStyle : null
                    }
                    // @ts-ignore
                    testID={UITestID.car_testid_page_list_header}
                    leftIconTestID={UITestID.car_testid_page_list_header_goback}
                    centerTestID={UITestID.car_testid_page_list_header_modify}
                  />
                )}
              </View>
              {isFirstShow && !isNewSearchNoResult && (
                <View>
                  {!isRecommend && (
                    <ListFilterBar
                      disable={isDisabledListFilterBar}
                      style={styles.filterBarStyle}
                      isHitCache={this.isHitCache}
                      clickSearchBar={this.showSearchModal}
                      getSearchCarModalHeight={this.getSearchCarModalHeight}
                      showSearchBar={
                        !(searchCarModalVisible && searchCarModalFromFilter)
                      }
                      searchBarClickAble={this.isShowVehilceListModule(
                        curStage,
                      )}
                    />
                  )}
                  {!isRecommend && (
                    <QuickFilterBar
                      isHitCache={this.isHitCache}
                      isListInPage={CarABTesting.isListInPage()}
                    />
                  )}
                  {!Utils.isCtripOsd() &&
                    !CarABTesting.isListInPage() &&
                    !this.isHitCache && <ListProgress />}
                  {!CarABTesting.isListInPage() && this.state.isShowOther && (
                    <ListFilterProgress />
                  )}
                </View>
              )}
            </XBoxShadow>
            {/* fix: ListProgress 消失后抖动，添加 position absolute  */}
            <View
              className={
                Utils.isCtripOsd() && !progressIsFinish
                  ? c2xStyles.tipWrap
                  : null
              }
            >
              {/* harmony下需要加state的判断才能控制tip的消失 */}
              {selector(
                isHarmony,
                isFirstShow && this.state.isShowTipList && (
                  <TipList
                    refFn={this.handleTipRef}
                    showWarningTipsModal={this.showWarningTipsModal}
                    showCouponModal={this.showCouponModal}
                    isNoResultRecommend={isNoResultRecommend}
                    isNoResultNew={isNoResultNew}
                  />
                ),

                isFirstShow && (
                  <TipList
                    refFn={this.handleTipRef}
                    showWarningTipsModal={this.showWarningTipsModal}
                    showCouponModal={this.showCouponModal}
                    isNoResultRecommend={isNoResultRecommend}
                    isNoResultNew={isNoResultNew}
                  />
                ),
              )}
              <View className={c2xStyles.progressWrap}>
                {Utils.isCtripOsd() &&
                  curStage === PAGESTAGE.SHOW &&
                  isFetchFinish &&
                  !CarABTesting.isListInPage() &&
                  !isNoResultRecommend &&
                  // 首次渲染 && isFromCache=true 时，不显示进度条
                  !(this.searchCount === 0 && isFromCache) &&
                  !this.isHitCache && <ListProgress />}
              </View>
            </View>
            {this.isShowLoadingModule(curStage) && (
              <View className={c2xStyles.loadingWrap}>
                <BbkSkeletonLoading
                  visible
                  pageName={this.getLoadingPageName()}
                  isSide={true}
                  showImg={this.isShowLoadingImg()}
                  isShowBreathAnimation={this.isShowLoadingImg()}
                />
              </View>
            )}

            {this.isShowFailModule(curStage) && !isNewSearchNoResult && (
              <ListNoMatch
                datePickerRef={this.datePickerRef}
                pageRef={this}
                style={styles.vehicleList}
                noMatchStyle={failModuleNoMatchStyle}
                datePickerRefDone={datePickerRefDone}
              />
            )}
            {/** 搜索无结果且有推荐推荐展示且不为新版无结果推荐聚合 */}
            {isNoResultRecommend && !isRecommendNoResult && !isNoResultNew && (
              <SearchLessResultTip />
            )}

            {/** 供应商报价 */}
            {isFirstShow && (
              <View
                testID={UITestID.car_testid_page_list_vehicleList}
                className={c2xStyles.vehicleWrap}
                onLayout={this.performanceLog}
              >
                {!isNewSearchNoResult && (
                  <VehGroupBar isRefactor={isRefactor} />
                )}
                {this.isShowPriceModule(curStage) && (
                  <>
                    {!CarABTesting.isListInPage() &&
                      curStage === PAGESTAGE.SHOW &&
                      (this.state.isShowOther ? (
                        isNoResultNew ? (
                          <NoResultMultipleContainer
                            scrollUpCallback={this.scrollUpCallback}
                            scrollDownCallback={this.scrollDownCallback}
                            scrollEndCallback={this.scrollEndCallback}
                            isNoResultNew={isNoResultNew}
                            isRecommend={isRecommend}
                          />
                        ) : (
                          <VehicleListWithControlOSD
                            scrollUpCallback={this.scrollUpCallback}
                            scrollDownCallback={this.scrollDownCallback}
                            scrollEndCallback={this.scrollEndCallback}
                            closeTipPop={this.closeTipPop}
                            refFn={this.handleRefFn}
                            pageRef={this}
                            // @ts-ignore
                            filterNoResult={filterNoResult}
                            // 解决 initialNumToRender=6时，无法展示全部
                            initialNumToRender={10}
                            isRecommend={isRecommend}
                            isRefactor={isRefactor}
                            ipollPosNum={ipollPosNum}
                            ipollGroup={ipollGroup}
                            sceneid={sceneid}
                          />
                        )
                      ) : (
                        <BbkSkeletonLoading
                          visible
                          pageName={PageType.Batch_List_Half}
                        />
                      ))}
                    {this.isShowVehilceListModule(curStage) &&
                      (this.state.isShowOther ? (
                        <VehicleListNew
                          scrollUpCallback={this.scrollUpCallback}
                          scrollDownCallback={this.scrollDownCallback}
                          scrollEndCallback={this.scrollEndCallback}
                          closeTipPop={this.closeTipPop}
                          refFn={this.handleRefFn}
                          pageRef={this}
                          // @ts-ignore
                          filterNoResult={filterNoResult}
                          initialNumToRender={12}
                          isRecommend={isRecommend}
                          fromPage={fromPage}
                          clickSearchBar={this.showSearchModal}
                          ipollPosNum={ipollPosNum}
                          ipollGroup={ipollGroup}
                          sceneid={sceneid}
                        />
                      ) : (
                        <BbkSkeletonLoading
                          visible
                          pageName={PageType.Batch_List_Half}
                        />
                      ))}
                    {this.isShowGroupLoading() && (
                      <BbkSkeletonLoading
                        visible
                        pageName={this.getLoadingPageName()}
                      />
                    )}

                    {this.isShowGroupFail() && (
                      <ListNoMatch
                        datePickerRef={this.datePickerRef}
                        pageRef={this}
                        noMatchStyle={groupFailNoMatchStyle}
                      />
                    )}
                  </>
                )}
              </View>
            )}
            {CarABTesting.isListInPage() && <Customtoast />}
            {CarABTesting.isListInPage() && <TextToast />}
            {Utils.isCtripIsd() && <GroupChangeToastISD />}
          </React.Fragment>
          {this.state.isShowOther && (
            <React.Fragment>
              <CouponModal
                visible={couponModalVisible}
                renderData={promotionData}
                availableData={availablePromotionData}
                onCancel={this.closeCouponModal}
                onReceive={receivePromotion}
                onReceiveAll={receiveAllPromotion}
                isList
              />

              <FilterAndSortModal
                listThreshold={initFilterModalTop}
                style={styles.filterAndSortModalStyle}
                // @ts-ignore
                filterNoResult={filterNoResult}
              />

              <VendorListModal
                pageRef={this}
                setNeedSubmitSecondLog={this.setNeedSubmitSecondLog}
                isRefactor={isRefactor}
              />
              <SearchPanelModal
                searchPanelModalRefFn={this.handleSearchPanelModalRef}
                currentPageId={this.getPageId()}
                pressSearchCallback={this.pressSearchCallback}
                backPageName={PageName.List}
              />

              <DriverLicensePop />
              <TimeOutPop
                datePickerRef={this.datePickerRef}
                scrollDownCallback={this.scrollDownCallback}
              />

              <LimitRulesPop goMap={this.goMap} />

              <AgeModal />
              {this.props.isDebugMode && (
                <AssistiveTouch
                  onPress={() => {
                    this.push('Debug');
                  }}
                />
              )}
              {!Utils.isCtripIsd() && <VehicleModal />}
              <EasyLifeTagListModal />
              <TotalPriceModal pageRef={this} />

              <PriceDetailModal pageRef={this} />

              <NewWarningTipsModal
                visible={warningTipsModalVisible}
                onClose={this.closeWarningTipsModal}
                title={Texts.warningTipDetailText}
                content={warningTipsModalCotent}
              />

              <CacheExpirePop
                scrollDownCallback={this.scrollDownCallback}
                pressSearchCallback={this.resetHitCacheStatus}
              />

              <BbkUpgradeModal />
              {!!searchCarModalVisible && (
                <SearchBar
                  isFixed={true}
                  visible={searchCarModalVisible && searchCarModalFromFilter}
                  top={searchBarFixTop}
                  showSearchInput={this.showSearchInput}
                />
              )}
              <SearchBar
                isFixed={true}
                visible={false}
                isLargeStyle={true}
                top={searchBarFixTop}
                showSearchInput={this.showSearchInput}
              />
              {!hasLoadedSearchBarGuide && !searchCarModalVisible && (
                <SearchBarAnimateGuide
                  clickSearchBar={this.showSearchModal}
                  isFixed={true}
                  top={searchBarFixTop}
                />
              )}

              <SearchCarModal
                visible={searchCarModalVisible}
                onClose={this.onCloseSearchCarModal}
                fixTop={searchBarFixTop}
                animation={searchCarModalFromFilter}
                isShowSearchInput={isShowSearchInput}
              />
              <ListCalendarModal
                handleDatePickerRef={this.handleDatePickerRef}
                calendarModalConfirmCallback={this.resetHitCacheStatus}
              />

              {!!showListTimer &&
                Utils.isCtripOsd() &&
                curStage === PAGESTAGE.SHOW && (
                  <ListPoll
                    overTimePopConfirmFn={this.osdRefreshQueryList}
                    getRef={this.getListPollRef}
                  />
                )}
            </React.Fragment>
          )}
          {!Utils.isCtripIsd() && this.state.isShowOther && (
            <LoginGuide isLogin={this.state.isLogin} style={styles.bottom0} />
          )}
          {isRecommend && isFail && (
            <RecommendLoading
              isRecommendLoading={isRecommendLoading}
              recommendAllVehicleCount={recommendAllVehicleCount}
              isRecommendNoResult={isRecommendNoResult}
            />
          )}
        </ViewPort>
      </XCalendar.PageWithCalendar>
    );
  }
}
