import StyleSheet from '@c2x/apis/StyleSheet';

import {
  border,
  space,
  font,
  icon,
  color,
  layout,
  label,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

const { getPixel, isAndroid } = BbkUtils;

export const VehicleListStyle = StyleSheet.create({
  wrap: {
    paddingLeft: getPixel(20),
    paddingRight: getPixel(20),
  },
  logoImgStyleExtra: {
    marginRight: getPixel(4),
  },
  wrap_B: {
    paddingLeft: space.spaceXL,
    paddingRight: space.spaceL,
  },
  wrap_padding: {
    paddingLeft: space.spaceXL,
    paddingRight: space.spaceL,
  },
  userBrowsed: {
    backgroundColor: color.C_F6F8FB,
  },
  wrap_SecretBox: {
    backgroundColor: color.white,
    marginLeft: space.spaceS,
    marginRight: space.spaceS,
    marginBottom: space.spaceS,
    borderRadius: getPixel(16),
    paddingLeft: space.spaceL,
    paddingRight: space.spaceS,
  },
  browseBorder: {
    borderWidth: getPixel(2),
    borderStyle: 'solid',
    borderColor: color.orangeBase,
  },
  browseView: {
    height: getPixel(48),
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    backgroundColor: color.orangeBg,
    justifyContent: 'center',
  },
  browseImg: {
    height: getPixel(44),
  },
  secretBoxImg: {
    height: getPixel(68),
  },
  browseText: {
    ...font.labelLLightStyle,
    color: color.orangeBase,
  },
  urlFilterNoResult: {
    height: getPixel(288),
  },
  vehicleImgWrapNew: {
    paddingTop: getPixel(16),
    marginBottom: getPixel(16),
  },
  vehicleImgWrap: {
    marginTop: border.borderSizeXsm,
  },
  vehicleImgWrap_B: {
    marginTop: border.borderSizeXsm,
    paddingTop: getPixel(16),
  },
  vehicleImgWrapPaddingBottom_B: {
    paddingBottom: getPixel(12),
  },
  vehicleHotLikeWrap: {
    paddingTop: getPixel(38),
  },
  vehicleLikeWrap: {
    paddingTop: getPixel(30),
  },
  labelFlexLeft: {
    justifyContent: 'flex-start',
  },
  labelStyle: {
    height: getPixel(40),
    paddingLeft: space.spaceL,
    borderWidth: 0,
    marginTop: space.spaceXL,
  },
  vendor: {
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
  },
  vendor_B: {
    paddingLeft: space.spaceXL,
    paddingRight: space.spaceXL,
  },
  vendorWrap: {
    borderBottomWidth: border.borderSizeXsm,
    marginTop: space.spaceXL,
    paddingBottom: getPixel(24),
    marginBottom: StyleSheet.hairlineWidth,
  },
  vehicleOriginalOrderLabel: {
    marginBottom: getPixel(-21),
    justifyContent: 'flex-end',
  },
  modifySameVehicle: {
    paddingTop: getPixel(2),
    paddingBottom: getPixel(2),
  },
  vendorOriginalOrderLabel: {
    justifyContent: 'flex-end',
    marginBottom: getPixel(-2),
    marginTop: getPixel(-16),
    marginRight: getPixel(-32),
  },
  soldOutPlace: {
    height: BbkUtils.getPixel(40),
    width: BbkUtils.getPixel(20),
  },
  soldOutText: {
    color: color.darkGrayBorder,
  },
  soldOutImg: {
    opacity: 0.3,
  },
  flexRow: {
    flexDirection: 'row',
  },
  flexWrap: {
    flexWrap: 'wrap',
  },
  bookButton: {
    paddingLeft: getPixel(20),
    paddingRight: getPixel(20),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    minWidth: 0,
  },
  bookButtonText: {
    ...font.title2MediumStyle,
    lineHeight: BbkUtils.getLineHeight(40),
  },
  priceTagsWrap: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  priceWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '35%',
  },
  tagWrap: {
    width: '65%',
    paddingRight: getPixel(12),
  },
  priceAndLabelWrap: {
    justifyContent: 'flex-end',
    flexDirection: 'column',
  },
  discountLabels: {
    marginRight: getPixel(26),
    marginBottom: getPixel(2),
  },
  marketLabelsWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: getPixel(44),
  },
  mLeft2: {
    marginLeft: getPixel(2),
  },
  verndorLabel: {
    marginBottom: space.spaceL,
    alignItems: 'flex-start',
  },
  vehicleMarginBottom: {
    marginBottom: space.spaceL,
  },
  vehicleMarginBottom_B: {
    marginBottom: space.spaceM,
  },
  recommendFilter: {
    marginBottom: getPixel(12),
  },
  more: {
    paddingTop: space.spaceXXL,
    paddingBottom: space.spaceXXL,
    // borderBottomWidth: border.borderSizeXsm,
  },
  moreText: {
    ...font.body2LightStyle,
  },
  moreIcon: {
    marginLeft: space.spaceS,
    fontFamily: icon.getIconFamily(),
  },
  vehicleImage: {
    width: getPixel(335),
    height: getPixel(223),
  },
  vehicleImageNew: {
    width: getPixel(258),
    height: getPixel(172),
  },
  vehicleImageNew_A: {
    width: getPixel(200),
    height: getPixel(134),
  },
  vehicleImageNew_B: {
    width: getPixel(180),
    height: getPixel(120),
  },
  secretBoxImage: {
    resizeMode: 'cover',
    height: getPixel(200),
  },
  secretBoxImageWrap: {
    height: getPixel(176),
    width: getPixel(200),
    overflow: 'hidden',
  },
  vehicleDesc: {
    marginLeft: space.spaceL,
    flex: 1,
    justifyContent: 'center',
    marginBottom: -space.spaceL,
  },
  vehicleDescNew2: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: getPixel(20),
  },
  vehicleDesc_B: {
    flex: 1,
    marginLeft: getPixel(20),
  },
  vehicleDescTop: {
    marginTop: getPixel(16),
  },
  vehicleDescJustifyCenter_B: {
    justifyContent: 'center',
  },
  vehicleDescJustifyCenterFlexStart: {
    justifyContent: 'flex-start',
  },
  vehicleDescFix: {
    marginLeft: getPixel(10),
    marginTop: getPixel(14),
  },
  vehicleDescNew: {
    marginLeft: 0,
    left: getPixel(13),
    marginTop: -getPixel(16),
  },
  secretBoxDesc: {
    marginTop: getPixel(22),
    marginBottom: getPixel(6),
    marginLeft: getPixel(32),
  },
  spaceDesc: {
    color: color.fontPrimary,
    paddingTop: getPixel(4),
    paddingBottom: getPixel(4),
    ...font.body3LightStyle,
  },
  verdorHeader: {
    minHeight: getPixel(54),
    alignItems: 'center',
  },
  scoreLabel: {
    marginLeft: space.spaceM,
    marginRight: space.spaceS,
  },
  comment: {
    marginLeft: space.spaceS,
  },
  vendorPriceWrap: {
    paddingRight: isAndroid ? space.spaceXXL : 0,
  },
  vehicleHeaderWrap: {
    borderBottomWidth: 0,
  },
  showMoreWrap: {
    borderTopWidth: border.borderSizeXsm,
  },
  showNearLike: {
    paddingTop: space.spaceXL,
    marginBottom: -space.spaceL,
  },
  rentSqu: {
    ...layout.alignHorizontal,
    width: getPixel(32),
    height: getPixel(32),
    marginRight: getPixel(8),
    borderRadius: getPixel(4),
    backgroundColor: color.blueBase,
  },
  capSupTex: {
    color: color.blueBase,
    marginRight: getPixel(4),
    ...font.caption1LightStyle,
  },
  rentPic: {
    width: getPixel(24),
    height: getPixel(16),
  },
  distanceText: {
    color: color.fontPrimary,
    marginBottom: space.spaceS,
    ...font.caption1LightStyle,
  },
  distanceTextNew: {
    color: color.fontPrimary,
    marginBottom: getPixel(12),
    marginTop: getPixel(12),
    ...font.F_26_10_regular,
  },
  addrIcon: {
    width: label.baseLabelLHeight,
    height: label.baseLabelLHeight,
    ...font.labelLStyle,
  },
  easyLifeTagPriceWrap: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  rightIcon: {
    width: getPixel(36),
    height: getPixel(36),
    marginTop: getPixel(10),
    marginLeft: getPixel(8),
  },
  mTop8: {
    marginTop: getPixel(8),
  },
  browsingView: {
    width: getPixel(214),
    height: getPixel(32),
    alignSelf: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: getPixel(32),
  },
  browsingText: {
    color: color.browsingBlue,
    ...font.labelSLightStyle,
  },
  borderTopWidth0: {
    borderTopWidth: 0,
  },
  splitLineStyle: {
    top: getPixel(9),
  },
  splitLineStyleNew: {
    top: getPixel(8),
  },
  wrapStyleNew: {
    marginRight: getPixel(14),
  },
  mt4: {
    marginTop: getPixel(4),
  },
  pb8: {
    marginTop: border.borderSizeXsm,
    paddingBottom: getPixel(8),
  },
  pb14: {
    marginTop: border.borderSizeXsm,
    paddingBottom: getPixel(14),
  },
  pt12: {
    paddingTop: getPixel(12),
  },
  absLeft: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  newVehicleDescWrap: {
    marginLeft: getPixel(210),
    paddingTop: getPixel(24),
  },
  pl14: {
    paddingLeft: getPixel(14),
  },
  lessImg: {
    width: getPixel(104),
    height: getPixel(23),
    marginLeft: getPixel(10),
  },
  badgeView: {
    width: getPixel(350),
    height: getPixel(34),
    ...layout.flexRow,
    overflow: 'hidden',
  },
  negaMt12: {
    marginTop: getPixel(-12),
  },
  badgeWrapper: {
    position: 'relative',
    zIndex: 10,
    flexWrap: 'nowrap',
  },
  jCflexBetween: {
    ...layout.betweenEnd,
  },
  jCflexEnd: {
    justifyContent: 'flex-end',
  },
  negaTop4: {
    top: getPixel(-4),
  },
  carImageWrap: {
    position: 'relative',
  },
  lessImgNew: {
    width: getPixel(26),
    height: getPixel(26),
    position: 'absolute',
    top: getPixel(26),
    right: getPixel(8),
  },
  // 列表卡片样式
  vehicleCardDescWrap: {
    marginLeft: getPixel(208),
    paddingTop: getPixel(30),
  },
});

export const placeHolder = null;
