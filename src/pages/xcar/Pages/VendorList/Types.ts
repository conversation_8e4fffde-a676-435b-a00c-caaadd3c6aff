import { ReactNode, CSSProperties } from 'react';

import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import { ImgType } from '../../ComponentBusiness/EmptyComponent/src/EmptyComponent';
import { TotalPriceModalType } from '../../ComponentBusiness/Common/src/Enums';
import { IMarketThemeVendorList } from '../../Types/Dto/QueryThemeConfigResponseType';
import { VehicleListType } from '../../Types/Dto/ListDtoType';
import {
  ExtraInfosType,
  MediaGroupInfo,
  MediaInfo,
  Floor,
  SimpleObject,
  FloorPackage,
  CtqSimplePriceInfo,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { PageParamType } from '../../Types/Dto/QueryVehicleDetailListRequestType';

export interface IBbkVehicleNameProps {
  name: string;
  isSimilar: boolean;
  isHotLabel: boolean;
  licenseTag: string;
  licenseType: string;
}

export interface IVehicleImage {
  isSmallImage?: boolean;
  smallImage?: ISmallImage;
  isProductLoading?: boolean;
  firstImageUrl?: string;
  imageList?: Array<ISmallImage>;
  video?: string;
  defaultImage?: string;
  animateStyle?: any;
  tipVisiable?: boolean;
  videoRefCallBack?: (data?: any) => void;
  getTestId?: () => void;
  vehicleCode?: string;
  videoStateChangedCallBack?: (videoPlayerState: string) => void;
}

export interface IBouncesImage {
  vehicleCode?: string;
  totalPhotos?: Array<MediaInfo>;
  tabNameMap?: any;
  tabNameMapIndex?: any;
  mediaGroup?: Array<MediaGroupInfo>;
  note?: string;
}

export interface ISmallImage extends IImageLabel {
  url?: string;
}

export interface IImageLabel {
  type?: number;
  sourceName?: string;
}

export enum IImageType {
  StorePicture = 1,
  DefaultPicture = 2,
}

export interface ILimitTipInfoType {
  isShowTip: boolean; // 是否展示限行提示模块
  isLimit: boolean; // 车牌是否限行
  tipText: string; // 提示文案
  onPressTip?: () => void;
}

export interface IVehicleAndLimit extends ILimitTipInfoType {
  isHotLabel?: boolean;
  isSimilar?: boolean;
  licenseTag?: string;
  licenseType?: string;
  name?: string;
  vehicleCode?: string;
  onPressVehicleName?: () => void;

  descList?: Array<any>;
  wrapStyle?: Array<CSSProperties>;
  marketingAtmosphere?: number;
  hasLaborDayLabel?: boolean;
  isShowMarketTheme?: boolean;
  isNewEnergy?: boolean;
  minTotalPrice?: number;
  showFullImmerse?: boolean;
  marketTheme?: {
    skinUrl?: string;
    jumpUrl?: string;
  };
  onPressShowLessModal?: () => void;
  onPressShowNoLimitModal?: (content) => void;
}

export interface ITipCenter {
  isLimit?: boolean;
  tipText?: string;
}
export enum EnterPosition {
  topScreen = 1, // 首屏入口
  secondScreen = 2,
} // 二屏置顶入口

export interface ILocationAndDateMonitor {
  ptime: any;
  rtime: any;
  pickupLocation: {
    locationType: string;
    locationCode: string;
    locationName: string;
  };
  returnLocation: {
    locationType: string;
    locationCode: string;
    locationName: string;
  };
  style?: CSSProperties;
  testID?: string;
  clickWithLog?: () => void;
  datePickerRef?: any;
  enterPosition?: EnterPosition;
  isShelvesServer?: boolean;
  floorName?: string[];
  clickLogData?: any;
}

export enum IMarketingAtmosphere {
  RestAssured = 1,
  Spring = 2,
  YunNan = 3,
}
export interface IMarketingBanner {
  isShowRestAssured?: boolean;
  wrapStyle?: CSSProperties;
  marketingAtmosphere?: IMarketingAtmosphere;
  hasLaborDayLabel?: boolean;
  marketTheme?: IMarketThemeVendorList;
  isShowMarketTheme?: boolean;
  yunnanBannerInfo?: ExtraInfosType;
}

export interface IPriceItem {
  price: number;
  currency: string;
  originalTotalPrice?: number;
}

export interface IPriceDescProps {
  dayPrice: number;
  totalPrice: number;
  dayText: string;
  totalText: string;
  currentCurrencyCode: string;
  originDailyPrice?: number;
}

export interface ITextIncludeSoldOut {
  style: CSSProperties;
  text: string;
  theme?: any;
  testID?: string;
}

export interface IVendorHeader {
  vendorName: string;
  isOptimize: boolean;
  score: string;
  commentDesc: string;
  onPressReview: () => void;
  showRightIcon?: boolean; // 供应商名称后面是否要展示右箭头
  arrowRightStyle?: CSSProperties;
  nationalChainTagTitle?: string;
  theme?: any;
  rightBtn?: string;
  onPressRightBtn?: () => void;
  wrapStyle?: CSSProperties;
  vendorContainerStyle?: CSSProperties;
  vendorContentStyle?: CSSProperties;
  vendorNameStyle?: CSSProperties;
  optimizationStrengthenImgStyle?: CSSProperties;
  optimizationStrengthenDotStyle?: CSSProperties;
  optimizationStrengthenVendorNameStyle?: CSSProperties;
  isShowOptimizationStrengthenRightArrow?: boolean;
  vendorNameMaxByte?: number;
  onPressVendor: () => void;
  isHasBookBtn?: boolean;
}

export interface IPickUpDropOffDesc {
  pickUpDesc: string;
  dropOffDesc?: string;
  isPickUpRentCenter?: boolean;
  isDropOffRentCenter?: boolean;
  pickUpRentCenterName?: string;
  dropOffRentCenterName?: string;
  theme?: any;
}

export interface IVendor extends IVendorHeader {
  isEasyLife?: boolean;
  isSoldOut?: boolean;
  almostSoldOutLabel?: string;
  isShowAtTop?: boolean;
  wrapStyle?: CSSProperties;
  isShowBtnIcon?: boolean; // 底部是展示“icon”还是展示“订”字

  pickUpDesc: string;
  dropOffDesc?: string;
  isPickUpRentCenter?: boolean;
  isDropOffRentCenter?: boolean;

  vehicleTags?: Array<VendorTagType>;
  serviceTags?: Array<VendorTagType>;
  marketTags?: Array<VendorTagType>;
  allLabels?: Array<VendorTagType>;
  selfServiceLabel?: VendorTagType;
  belongTab?: string;

  hint?: string;
  newCar?: boolean;
  priceDescProps: IPriceDescProps;

  onPressVendor: (uniqueCode?: string, skuId?: string) => void;
  onPressBooking: (type: TotalPriceModalType, uniqueCode: string) => void;
  onPressQuestion: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressReview: (uniqueCode?: string) => void;

  uniqueCode?: string; // 供应商报价的唯一code

  vendorPriceKey?: string; // 售罄标识
  clickLogData?: any; // 点击埋点数据
  exposureLogData?: any; // 曝光埋点数据
  hasFees?: boolean; // 是否有fees节点
  isCouponBook?: boolean; // 是否是领券订产品
  pickUpRentCenterName?: string;
  dropOffRentCenterName?: string;
  isUrlTop?: boolean; // 是否是车型置顶
  skuId?: string; // 车型skuid（仅卡拉比车型有）
  sortType?: number;
  licenseTag?: string;
  licenseType?: string;
  isSelfService?: boolean;
  showTopBorder?: boolean;
  vendorNameMaxByte?: number;
  isHasBookBtn?: boolean;
  lowestPrice?: number;
  vendorListLength?: number;
  index?: number; // 供应商在列表中的索引
}

export interface IVehicle {
  floorId?: string;
  lowestPrice?: number;
  floorName?: string[];
  isSelect?: boolean;
  pickWayInfo?: string;
  returnWayInfo?: string;
  alltags?: SimpleObject[];
  index?: number;
  packageList?: FloorPackage[];
  isExpend?: boolean;
  exposureLogData?: any; // 曝光埋点数据
  clickLogData?: any; // 点击埋点数据
  isSelfService?: boolean;
  saleOutList?: string[];
  needVenderNameLabel?: boolean;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  onPressQuestion?: (
    type: string,
    floorId?: string,
    packageCode?: string,
  ) => void;
  onPressBooking?: (
    type: TotalPriceModalType,
    floorId?: string,
    packageCode?: string,
    index?: number, // 车型所在的楼层索引
  ) => void;
  onPressVendor?: (
    floorId?: string,
    packageCode?: string,
    skuId?: string,
  ) => void;
  onScrollToFloor?: (index: number) => void;
}

export interface ISectionHeader {
  isFit?: boolean;
  isShowGradient?: boolean;

  locationDate?: ILocationAndDateMonitor;
  isLocationDateFixed?: boolean;
  isShowFitTitle?: boolean;
  isShowFitBottomGradientLine?: boolean;
  vehicleCode?: string;

  isShowRightArrow?: boolean;
  onPressRightArrow?: () => void;
  testID?: string;
  clickWithLog?: () => void;
}

export interface ISectionFooter {
  moreNum?: number;
  onPressMore?: () => void;
  isShowBackToListPage?: boolean;
  onPressBackToListPage?: () => void;
  isShowGradient?: boolean;
  hasMoreBtn?: boolean;
  isFit?: boolean;
  vehicleCode?: string;
  showLoading?: boolean;
  isShelves2?: boolean;
}

export interface ISection {
  isLoading?: boolean;
  sectionHeader?: ISectionHeader;
  vendorList?: Array<IVendor>;
  sectionFooter?: ISectionFooter;
  pTime?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  fetchVehicleDetailList?: () => void;
  errorImgType?: ImgType;
  saleOutList?: Array<string>;
  isHasSpecificButSoldOut?: boolean;
  isNoSpecificButHasFiltered?: boolean;
  priceListLen?: number;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  datePickerRef?: any;
  isSpecificList?: boolean;
  showAmount?: number;
  vehicleCode?: string;
  showSearchSelectorWrapWithLog?: () => void;
  sectionHeaderTestID?: string;
  handleshowFit?: () => void;
  loadNextPageVendorList?: boolean;
}

export enum IPicSource {
  SmallImage = 20,
}

export enum IAtmosphere {
  RestAssured = 1, // 安心行
  WarmSpring = 2, // 2022 3-23 暖春租车节
  Yunnan = 3,
} // 云南文旅
export interface IVendorPrice extends IPriceDescProps {
  isSoldOut?: boolean;
  vendorPriceKey?: string; // 供应商报价key,用来判断是否售罄
  onPressQuestion: () => void;
  handlePressBooking?: () => void;
  isShowBtnIcon?: boolean; // 底部是展示“icon”还是展示“订”字
  isStorageNumTip?: boolean;
  almostSoldOutLabel?: any;
  vendorButtonColors?: any;
  clickLogData?: any; // 点击埋点数据
  exposureLogData?: any; // 曝光埋点数据
  wrapStyle?: CSSProperties;
  priceStyle?: CSSProperties;
  renderMarket?: () => void;
  bottomRightContentLayout?: (e) => void;
  hasMarketTags?: boolean;
  hasMarketTagsUpgrade?: boolean;
  isCouponBook?: boolean;
  singlePriceWrapStyle?: CSSProperties;
  totalPriceWrapStyle?: CSSProperties;
}

export interface IVehicleModal {
  visible: boolean;
  onClose: () => void;
  data: any;
  footChildren?: ReactNode;
  onPressHelp?: (fuelType) => void;
  onPressShowLessModal?: () => void;
}

export enum IClickVendorArea {
  Review = 1,
  Question = 2,
  Booking = 3,
  Vendor = 4,
  CouponBook = 5,
  Expend = 6,
} // 领券订

export interface IVendorListFirstScreenParamType {
  vehicleInfo: VehicleListType;
  isUsed?: boolean;
}

/**
 * 注意:当有多个弹层可以同时打开时,如果想通过同一方法进行关闭,则需将先关闭的弹层配置在前面
 * 例如:费用明细弹层可以在信息确认弹层上打开，则需将费用明细的type配置在信息确认弹层之前
 * */
export enum IVendorListModalVisibleType {
  timeOutPopVisible = 'timeOutPopVisible',
  priceDetailModalVisible = 'priceDetailModalVisible',
  totalPriceModalVisible = 'totalPriceModalVisible',
  isEasyLifeModalVisible = 'isEasyLifeModalVisible',
  vehicleModalVisible = 'vehicleModalVisible',
  limitRulePopVisible = 'limitRulePopVisible',
  couponModalVisible = 'couponModalVisible',
  isVirtualNumberDialogVisible = 'isVirtualNumberDialogVisible',
  virtualNumberStoreModalVisible = 'virtualNumberStoreModalVisible',
  productConfirmModalVisible = 'productConfirmModalVisible',
}

export enum PriceDetailModalType {
  isFromRecommendList = 1,
  isFromProductConfirm = 2,
}

export interface ILocationCenterView {
  locationDate?: ILocationAndDateMonitor;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    locationDatePopType?: number;
    [propsKey: string]: any;
  }) => void;
  testID: string;
  datePickerRef?: any;
  clickWithLog?: () => void;
  enterPosition?: EnterPosition;
  initMaxWidth?: number;
  floorName?: string[];
  clickLogData?: any;
}

export interface ILocationCenterView2 {
  locationDate?: ILocationAndDateMonitor;
  floorName?: string[];
  clickLogData?: any; // 点击埋点数据
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    locationDatePopType?: number;
    [propsKey: string]: any;
  }) => void;
  testID: string;
  datePickerRef?: any;
  clickWithLog?: () => void;
  enterPosition?: EnterPosition;
  initMaxWidth?: number;
  onPressVendor?: (
    floorId?: string,
    packageCode?: string,
    skuId?: string,
  ) => void;
}

export interface IVendorListCurrentPageParams {
  pageParam?: PageParamType;
  vehicleIndex?: number;
  vendorListFirstScreenParam: IVendorListFirstScreenParamType;
  priceListLen?: number;
}

export interface IArrow {
  showFullImmerse?: boolean;
}

export interface INewCarLabel {
  text: string;
  isSoldOut: boolean;
  isOverRow?: boolean; // 是否换行
}
export interface DetailPage {
  title?: string;
  lowestDailyPrice?: number;
  vendorList?: Array<IVendor>;
  filteredVendorList?: Array<IVendor>;
}

export interface IShelvesTabBgItem {
  imageUrl?: string;
  isShow?: boolean; // 是否展示
  isVisible?: boolean; // 是否可见，透明度为1
}

export interface IShelves {
  isLoading?: boolean;
  isNoMatch?: boolean;
  sectionHeader?: ISectionHeader;
  detailPage?: Array<DetailPage>;
  detailPageIndex?: number;
  vendorList?: Array<IVendor>;
  sectionFooter?: ISectionFooter;
  pTime?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  errorImgType?: ImgType;
  saleOutList?: Array<string>;
  isHasSpecificButSoldOut?: boolean;
  isNoSpecificButHasFiltered?: boolean;
  priceListLen?: number;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  showAmount?: number;
  showFilterAmount?: number;
  vehicleCode?: string;
  showSearchSelectorWrapWithLog?: () => void;
  sectionHeaderTestID?: string;
  handleshowFit?: () => void;
  loadNextPageVendorList?: boolean;
  filterLabelsStr?: string;
  onSpecificLayout?: (e) => void;
  onFilteredLayout?: (e) => void;
}

interface IpollLodData {
  queryVid?: string;
  vehicleCode?: string;
  pCityId?: string;
  rCityId?: string;
  pickupLocation?: string;
  returnLocation?: string;
  ptime?: string;
  rtime?: string;
  gradename?: string;
}

export interface IShelves2 {
  isLoading?: boolean;
  isCouponEntry?: boolean;
  isNoMatch?: boolean;
  sectionHeader?: ISectionHeader;
  shelvesFloor?: Array<Floor>;
  sectionHeaderTestID?: string;
  filterLabelsStr?: string;
  onPressQuestion?: (
    type: string,
    floorId?: string,
    packageCode?: string,
  ) => void;
  onFloorLayout?: (height: number, index: number) => void;
  onPressBooking?: (
    type: TotalPriceModalType,
    floorId?: string,
    packageCode?: string,
  ) => void;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  showSearchSelectorWrapWithLog?: () => void;
  onScrollToFloor?: (index: number) => void;
  onSpecificLayout?: (e) => void;
  couponRenderData?: Array<string>;
  isReceiveAble?: boolean;
  onPress?: () => void;
  vehicleCode?: string;
  handleExpand?: () => void;
  ipollSceneid?: string;
  ipollPositionNum?: number;
  ipollLogData?: IpollLodData;
}
export interface IShelvesTab {
  title?: string;
  price?: number;
  index?: number;
  checked?: boolean;
  hasVendorList?: boolean;
  onPress?: (index) => void;
}

export interface IShelvesPage {
  data?: DetailPage;
  isLoading?: boolean;
  sectionHeader?: ISectionHeader;
  sectionFooter?: ISectionFooter;
  pTime?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  fetchVehicleDetailList?: () => void;
  errorImgType?: ImgType;
  saleOutList?: Array<string>;
  isHasSpecificButSoldOut?: boolean;
  isNoSpecificButHasFiltered?: boolean;
  priceListLen?: number;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  datePickerRef?: any;
  isSpecificList?: boolean;
  showAmount?: number;
  showFilterAmount?: number;
  vehicleCode?: string;
  handleshowFit?: () => void;
  loadNextPageVendorList?: boolean;
  onSpecificLayout?: (e) => void;
  onFilteredLayout?: (e) => void;
}

export interface IShelvesPage2 {
  shelvesFloor?: Array<Floor>;
  isLoading?: boolean;
  saleOutList?: Array<string>;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  showCarServiceDetail?: (reference: any, code: string) => void;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  onFloorLayout?: (height: number, index: number) => void;
  onSpecificLayout?: (e) => void;
}
export interface IShelvesItem {
  isLoading?: boolean;
  sectionHeader?: ISectionHeader;
  vendorList?: Array<IVendor>;
  sectionFooter?: ISectionFooter;
  pTime?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  fetchVehicleDetailList?: () => void;
  errorImgType?: ImgType;
  saleOutList?: Array<string>;
  isHasSpecificButSoldOut?: boolean;
  isNoSpecificButHasFiltered?: boolean;
  priceListLen?: number;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  datePickerRef?: any;
  showAmount?: number;
  vehicleCode?: string;
  handleshowFit?: () => void;
  loadNextPageVendorList?: boolean;
  isFit?: boolean;
}

export interface IShelvesFloors {
  isLoading?: boolean;
  shelvesFloor?: Array<Floor>;
  onSpecificLayout?: (e) => void;
  onFloorLayout?: (height: number, index: number) => void;
  onPressQuestion?: (
    type: string,
    floorId?: string,
    packageCode?: string,
  ) => void;
  onPressBooking?: (
    type: TotalPriceModalType,
    floorId?: string,
    packageCode?: string,
  ) => void;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  onScrollToFloor?: (index: number) => void;
  vehicleCode?: string;
  handleExpand?: () => void;
  ipollSceneid?: string;
  ipollPositionNum?: number;
  ipollLogData?: IpollLodData;
}

export enum ShelvesType {
  Fit = 1,
  NoFit = 2,
  Recommend = 3,
}

export interface IShelvesItemHeader {
  shelvesType?: ShelvesType;
  filterLabelsStr?: string;
  hasLineTop?: boolean;
}

export interface IVendorListNoMatch {
  isNomatch?: boolean;
  buttonTestId?: string;
  operateButtonPress?: () => void;
}

export interface IVehicleName {
  vehicleName?: string; // 车型名称
  licenseTag?: string; // 沪牌
  licenseType?: string; // 牌照样式
  isHotLabel?: boolean; // 是否是热销
  isNewEnergy?: boolean; // 是否新能源车型
  isDetail?: boolean; // 是否有详情按钮
  isShowDetailBtn?: boolean;
  style?: CSSProperties;
  lessStyle?: CSSProperties; // less标识样式
  isShowTip?: boolean;
  isLimit?: boolean;
  onTipsPress?: () => void;
  isShowNewEnergyWidth?: boolean;
}

export interface ILimitationText {
  isLimit?: boolean;
}

export interface IVendorIsdLabel2 {
  title?: string;
  testID?: string;
  isSelect?: boolean;
  labelCode?: string;
  isSoldOut?: boolean;
  noShowYouXuanIcon?: boolean;
}

export interface IVehiclePrice2 {
  style?: CSSProperties;
  isSoldOut?: boolean;
  currentDailyPrice?: number;
  curOriginDPrice?: number;
  currentCurrencyCode?: string;
  currentTotalPrice?: number;
  marketTag?: SimpleObject;
  floorId?: string;
  packageCode?: string;
  priceCount?: number;
  clickLogData?: any;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
    clickLogData?: any,
  ) => void;
  isCurrentExpend?: boolean;
}

export interface IVendorPrice2 {
  isSoldOut?: boolean;
  currentDailyPrice?: number;
  currentCurrencyCode?: string;
  currentTotalPrice?: number;
  curOriginDPrice?: number;
  marketTag?: SimpleObject;
  code?: string;
  singleDesc?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  floorId?: string;
  reference?: any;
  allPackageLevel?: string[];
  style?: CSSProperties;
}

export interface IVendorIsd2 {
  floorId?: string;
  isFirst?: boolean;
  isSoldOut?: boolean;
  code?: string;
  name?: string;
  insuranceDesc?: string[];
  marketTag?: SimpleObject;
  priceInfo?: CtqSimplePriceInfo;
  onPressQuestion?: (
    type: string,
    floorId?: string,
    packageCode?: string,
    clickLogData?: any,
  ) => void;
  onPressBooking?: (
    floorId: string,
    packageCode: string,
    clickLogData: any,
  ) => void;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  reference?: any;
  allPackageLevel?: string[];
  clickLogData?: any;
}

export interface IRenderVehicle {
  item: Floor;
  index: number;
}
