import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { useCallback, memo, useMemo } from 'react';
import {
  XView as View,
  xMergeStyles,
  XLinearGradient as LinearGradient,
  XViewExposure,
  xClassNames as classNames,
  xClassNames,
} from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { CarLog } from '../../../Util/Index';
import { UITestID, ImageUrl } from '../../../Constants/Index';
import c2xStyles from './vendorIsd2C2xStyles.module.scss';
import { MarketLabel } from '../../../ComponentBusiness/VendorTag';
import { IVendorIsd2, IVendorPrice2 } from '../Types';
import Texts from '../Texts';
import { ShelvesSafeguardType } from '../../../Constants/CommonEnums';

const { getPixel, vw, isIos, isAndroid } = BbkUtils;

const styles = StyleSheet.create({
  headerBg: {
    width: vw(100) - getPixel(48),
    height: getPixel(78),
    position: 'absolute',
    top: -12,
  },
  originDayPrice: {
    ...font.F_28_10_regular_TripNumberRegular,
    top: getPixel(isIos ? -0.5 : 4),
    color: color.C_888888,
  },
  throughPrice: {
    ...font.F_28_10_regular_TripNumberRegular,
    top: getPixel(isIos ? 1.5 : 4),
    color: color.C_888888,
  },
  originTotalPrice: {
    ...font.F_28_10_regular_TripNumberRegular,
    top: getPixel(isIos ? -1 : 2),
    color: color.C_888888,
  },
  priceThroughStyle: {
    textDecorationLine: 'line-through',
  },
  dayPriceCurrencyStyle: {
    ...font.F_28_10_regular,
    color: color.deepBlueBase,
    top: getPixel(isIos ? 2 : 3),
  },
  safeguardDayPriceCurrencyStyle: {
    ...font.F_28_10_regular,
    color: color.deepBlueBase,
    top: getPixel(isIos ? 1 : 3),
  },
  addUnitStyle: {
    top: getPixel(isIos ? 0 : 1),
  },
  dayPriceStyle: {
    color: color.deepBlueBase,
    ...font.F_38_10_regular_TripNumberSemiBold,
    top: getPixel(isIos ? 2 : 4),
    lineHeight: getPixel(38),
  },
  safeguardDayPriceStyle: {
    color: color.deepBlueBase,
    ...font.F_38_10_regular_TripNumberSemiBold,
    lineHeight: getPixel(40),
    top: getPixel(isIos ? -2 : 3),
    fontSize: getPixel(32),
  },
  soldOutStyle: {
    color: color.C_ccc,
  },
  totalText: {
    top: getPixel(-1),
  },
  totalIcon: {
    top: getPixel(isIos ? 0 : 1),
  },
  dayPriceStyleAndroid: {
    marginTop: getPixel(-3),
  },
});

const soldOutBtnLinearColors = [color.C_eee, color.C_eee];

export const SingleDesc: React.FC<IVendorPrice2> = memo(
  ({
    isSoldOut,
    singleDesc,
    showCarServiceDetail,
    reference,
    code,
    allPackageLevel,
  }: IVendorPrice2) => {
    const handleShowCarServiceDetail = useCallback(() => {
      showCarServiceDetail(reference, code, allPackageLevel);
    }, [showCarServiceDetail, reference, code, allPackageLevel]);
    return (
      !!singleDesc && (
        <BbkTouchable
          debounce={true}
          disabled={isSoldOut}
          onPress={handleShowCarServiceDetail}
          className={c2xStyles.singleDescWrap}
        >
          <BbkText
            className={classNames(
              c2xStyles.singleDescText,
              isSoldOut && c2xStyles.soldOutStyle,
            )}
          >
            {singleDesc}
          </BbkText>
        </BbkTouchable>
      )
    );
  },
);

export const VendorPrice: React.FC<IVendorPrice2> = memo(
  ({
    isSoldOut,
    singleDesc,
    currentDailyPrice,
    currentTotalPrice,
    curOriginDPrice,
    currentCurrencyCode,
    marketTag,
    onPressQuestion,
    showCarServiceDetail,
    reference,
    code,
    allPackageLevel,
  }: IVendorPrice2) => {
    const vendorButtonColors = useMemo(() => {
      let buttonColors = [color.C_00a7fa, color.deepBlueBase];
      if (isSoldOut) {
        buttonColors = soldOutBtnLinearColors;
      }
      return buttonColors;
    }, [isSoldOut]);
    return (
      <View className={c2xStyles.priceWrap}>
        <View className={c2xStyles.leftWrap}>
          <View
            className={classNames(
              c2xStyles.priceRow,
              !singleDesc && c2xStyles.singlePriceRow,
            )}
          >
            {!!singleDesc && (
              <SingleDesc
                isSoldOut={isSoldOut}
                showCarServiceDetail={showCarServiceDetail}
                singleDesc={singleDesc}
                code={code}
                reference={reference}
                allPackageLevel={allPackageLevel}
              />
            )}
            <View className={c2xStyles.dayPriceWrap}>
              {curOriginDPrice >= 0 &&
                curOriginDPrice !== currentDailyPrice && (
                  <BbkCurrencyFormatter
                    price={curOriginDPrice}
                    currency={currentCurrencyCode}
                    currencyStyle={xMergeStyles([
                      styles.throughPrice,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    priceStyle={xMergeStyles([
                      styles.throughPrice,
                      styles.priceThroughStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    isNew={true}
                  />
                )}
              <BbkText
                className={classNames(
                  c2xStyles.dayUnit,
                  isSoldOut && c2xStyles.soldOutStyle,
                )}
              >
                日均
              </BbkText>
              <BbkCurrencyFormatter
                price={currentDailyPrice}
                currency={currentCurrencyCode}
                currencyStyle={xMergeStyles([
                  styles.dayPriceCurrencyStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.dayPriceStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                isNew={true}
              />
            </View>
          </View>
          <View className={c2xStyles.marketLabelWrap}>
            {marketTag && (
              <XViewExposure
                testID={CarLog.LogExposure({
                  name: '曝光_详情页_供应商_营销标签',
                  info: { labelCode: marketTag?.labelCode },
                })}
              >
                <MarketLabel
                  title={marketTag?.title}
                  desc={marketTag?.amountTitle}
                  isSoldOut={isSoldOut}
                  height={42}
                  isBg={true}
                />
              </XViewExposure>
            )}
            {!!onPressQuestion && currentTotalPrice >= 0 && (
              <BbkTouchable
                debounce={true}
                className={c2xStyles.helpIcon}
                disabled={isSoldOut}
                testID={UITestID.car_testid_comp_vendor_price_help}
                onPress={onPressQuestion}
              >
                <BbkText
                  className={classNames(
                    c2xStyles.totalIcon,
                    isSoldOut && c2xStyles.soldOutStyle,
                  )}
                  style={styles.totalIcon}
                  type="icon"
                >
                  {icon.help}
                </BbkText>
              </BbkTouchable>
            )}
            <View>
              <BbkText
                className={classNames(
                  c2xStyles.totalUnit,
                  isSoldOut && c2xStyles.soldOutStyle,
                )}
                style={styles.totalText}
              >
                总价
              </BbkText>
            </View>
            <BbkCurrencyFormatter
              price={currentTotalPrice}
              currency={currentCurrencyCode}
              currencyStyle={xMergeStyles([
                styles.originTotalPrice,
                isSoldOut && styles.soldOutStyle,
              ])}
              priceStyle={xMergeStyles([
                styles.originTotalPrice,
                isSoldOut && styles.soldOutStyle,
              ])}
              isNew={true}
            />
          </View>
        </View>
        {isSoldOut && (
          <View className={c2xStyles.storageNumWrap}>
            <BbkText className={c2xStyles.storageNumStyle}>
              {Texts.vendorListSoldOutText}
            </BbkText>
          </View>
        )}
        <View
          testID={UITestID.car_testid_comp_vendor_list_book}
          className={c2xStyles.bookBtn}
        >
          <LinearGradient
            className={c2xStyles.bookBtnWrapNew}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 1.0 }}
            locations={[0, 1]}
            colors={vendorButtonColors}
          >
            <BbkText
              className={classNames(
                c2xStyles.bookBtnText,
                isSoldOut && c2xStyles.soldOutBtnStyle,
              )}
              fontWeight="medium"
            >
              {Texts.book}
            </BbkText>
          </LinearGradient>
        </View>
      </View>
    );
  },
);

interface GapPriceAndBookProps {
  isSoldOut?: boolean;
  gapPrice?: number;
  currentCurrencyCode?: string;
}

export const GapPriceAndBook: React.FC<GapPriceAndBookProps> = memo(
  ({ isSoldOut, gapPrice, currentCurrencyCode }: GapPriceAndBookProps) => {
    const vendorButtonUri = useMemo(() => {
      if (isSoldOut) {
        return `${ImageUrl.DIMG04_PATH}1tg1t12000k79lpy87D84.png`;
      }
      return `${ImageUrl.DIMG04_PATH}1tg4m12000k79kwdk8403.png`;
    }, [isSoldOut]);
    return (
      <View
        testID={UITestID.car_testid_comp_vendor_list_book}
        className={c2xStyles.gapPriceAndBookWrap}
      >
        <View className={c2xStyles.safeguardAddPriceWrap}>
          <BbkText
            style={styles.addUnitStyle}
            className={classNames(
              c2xStyles.addUnit,
              isSoldOut && c2xStyles.soldOutStyle,
            )}
          >
            +
          </BbkText>

          <BbkCurrencyFormatter
            price={gapPrice}
            currency={currentCurrencyCode}
            currencyStyle={xMergeStyles([
              styles.safeguardDayPriceCurrencyStyle,
              isSoldOut && styles.soldOutStyle,
              isAndroid && styles.dayPriceStyleAndroid,
            ])}
            priceStyle={xMergeStyles([
              styles.safeguardDayPriceStyle,
              isSoldOut && styles.soldOutStyle,
              isAndroid && styles.dayPriceStyleAndroid,
            ])}
            isNew={true}
          />

          <BbkText
            className={classNames(
              c2xStyles.safeguardDayUnit,
              isSoldOut && c2xStyles.soldOutStyle,
            )}
          >
            /天
          </BbkText>
        </View>
        <Image
          className={c2xStyles.vBtn}
          source={{
            uri: vendorButtonUri,
          }}
        />
      </View>
    );
  },
);

const VendorIsd2: React.FC<IVendorIsd2> = memo(
  ({
    floorId,
    isFirst,
    isSoldOut,
    code,
    name,
    insuranceDesc,
    priceInfo,
    marketTag,
    onPressQuestion,
    onPressBooking,
    showCarServiceDetail,
    reference,
    allPackageLevel,
    clickLogData,
  }: IVendorIsd2) => {
    const isPrep = code !== ShelvesSafeguardType.Basic;
    const headerIcon = isPrep
      ? `${ImageUrl.DIMG04_PATH}1tg2w12000m8yxwkuC373.png`
      : `${ImageUrl.DIMG04_PATH}1tg6s12000m9g47yxEDB9.png`;
    const iconUrl = isSoldOut
      ? `${ImageUrl.DIMG04_PATH}1tg4v12000ja6bg70673D.png`
      : headerIcon;
    const { descRows, singleDesc } = useMemo(() => {
      const rows = [];
      let secondeDesc = '';
      let left = '';
      let right = '';
      insuranceDesc?.forEach((desc, index) => {
        if (index === insuranceDesc.length - 1 && index % 2 === 0) {
          secondeDesc = desc;
        } else {
          if (index % 2 === 0) {
            left = desc;
          } else {
            right = desc;
          }
          if (left && right) {
            rows.push({
              left,
              right,
            });
            left = '';
            right = '';
          }
        }
      });
      return { descRows: rows, singleDesc: secondeDesc };
    }, [insuranceDesc]);
    const handlePressQuestion = useCallback(() => {
      onPressQuestion(floorId, code, clickLogData);
    }, [code, floorId, clickLogData, onPressQuestion]);
    const handlePressBooking = useCallback(() => {
      onPressBooking(floorId, code, clickLogData);
    }, [code, floorId, clickLogData, onPressBooking]);
    const handleShowCarServiceDetail = useCallback(() => {
      showCarServiceDetail(reference, code, allPackageLevel);
    }, [showCarServiceDetail, reference, code, allPackageLevel]);
    const gapPrice = priceInfo?.gapPrice;
    const isAddPriceStyle = gapPrice !== undefined;
    return (
      <BbkTouchable
        className={classNames(
          c2xStyles.vendorWrap,
          isFirst && c2xStyles.safeguardFirstWrap,
        )}
        debounce={true}
        onPress={handlePressBooking}
        disabled={isSoldOut}
      >
        {isFirst ? (
          <Image
            style={styles.headerBg}
            source={{
              uri: `${ImageUrl.DIMG04_PATH}1tg4p12000m8z23n208B5.png`,
            }}
            mode="aspectFit"
          />
        ) : (
          <View className={c2xStyles.borderView} />
        )}
        <View className={isAddPriceStyle && c2xStyles.safeguardGapPriceWrap}>
          <View className={c2xStyles.safeguardWrap}>
            <View
              className={
                isPrep ? c2xStyles.prepTitleWrap : c2xStyles.safeguardTitleWrap
              }
            >
              <BbkTouchable
                debounce={true}
                disabled={isSoldOut}
                onPress={handleShowCarServiceDetail}
                className={c2xStyles.safeguardTitleClickView}
              >
                <Image
                  className={c2xStyles.safeguardTitleIcon}
                  source={{
                    uri: iconUrl,
                  }}
                />
                <BbkText
                  className={classNames(
                    c2xStyles.safeguardTitleText,
                    isSoldOut && c2xStyles.soldOutStyle,
                  )}
                >
                  {name}
                </BbkText>
              </BbkTouchable>
            </View>
            <View className={c2xStyles.safeguardText}>
              {descRows?.length > 0 && (
                <View>
                  {insuranceDesc?.map((desc, index) => {
                    return (
                      <View
                        className={c2xStyles.descColumn}
                        key={`${floorId}_${code}_${String(index)}`}
                      >
                        <BbkTouchable
                          className={c2xStyles.descClickArea}
                          disabled={isSoldOut}
                          debounce={true}
                          onPress={handleShowCarServiceDetail}
                        >
                          <BbkText
                            className={classNames(
                              c2xStyles.descText,
                              isSoldOut && c2xStyles.soldOutStyle,
                            )}
                          >
                            {desc}
                          </BbkText>
                        </BbkTouchable>
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
          </View>
          {!!isAddPriceStyle && (
            <GapPriceAndBook
              isSoldOut={isSoldOut}
              gapPrice={gapPrice}
              currentCurrencyCode={priceInfo?.currentCurrencyCode}
            />
          )}
        </View>

        {!isAddPriceStyle && !!priceInfo && (
          <VendorPrice
            isSoldOut={isSoldOut}
            currentDailyPrice={priceInfo?.currentDailyPrice}
            currentCurrencyCode={priceInfo?.currentCurrencyCode}
            currentTotalPrice={priceInfo?.currentTotalPrice}
            curOriginDPrice={priceInfo?.curOriginDPrice}
            singleDesc={singleDesc}
            marketTag={marketTag}
            onPressQuestion={handlePressQuestion}
            showCarServiceDetail={showCarServiceDetail}
            floorId={floorId}
            code={code}
            reference={reference}
            allPackageLevel={allPackageLevel}
          />
        )}
      </BbkTouchable>
    );
  },
);

export default VendorIsd2;
