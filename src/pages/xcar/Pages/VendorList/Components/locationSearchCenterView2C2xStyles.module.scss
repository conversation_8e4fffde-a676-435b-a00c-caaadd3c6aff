@import '../../../Common/src/Tokens/tokens/color.scss';

.bgWrapTab {
  width: 750px;
  height: 132px;
  position: absolute;
  top: 0px;
}
.bgWrapNormal {
  width: 750px;
  height: 200px;
  position: absolute;
  top: 0px;
}
.textWrap {
  width: 86%;
}
.addressWrap {
  flex-direction: row;
  align-items: center;
}
.pAddressWrap {
  flex: 1;
}
.rAddressWrap {
  flex: 1;
}
.timeWrapTab {
  flex-direction: row;
  margin-top: 12px;
  margin-bottom: 26px;
  align-items: center;
}
.diffTime {
  border-width: 1px;
  padding-left: 12px;
  padding-right: 12px;
  border-color: rgba($C_0040AC, 0.19);
  border-style: solid;
  border-radius: 17px;
  margin-left: 8px;
  margin-right: 8px;
  height: 34px;
}
.diffTimeAndroid {
  top: -1px;
}
.titleView {
  border-top: 1px;
  border-top-color: $C_eef1f6;
  border-style: solid;
  flex-direction: row;
  align-items: center;
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 24px;
  padding-bottom: 24px;
}
.titleWrap {
  flex-direction: row;
  align-items: center;
}
.arrowRightList {
  font-size: 26px;
  margin-left: 16px;
}
.splitLine {
  width: 2px;
  height: 22px;
  margin-left: 12px;
  margin-right: 12px;
  background-color: $C_111111;
}
.wrap {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.editIconWrap {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  left: -15px;
  top: -15px;
}
.editIcon {
  width: 38px;
  height: 38px;
}