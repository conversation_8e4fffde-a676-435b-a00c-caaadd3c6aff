@import '../../../Common/src/Tokens/tokens/color.scss';

.wrap {
  flex-direction: row;
  padding-left: 10px;
  padding-right: 10px;
  height: 44px;
  background-color: $C_F5F8FA;
  border-radius: 4px;
  align-items: center;
}
.androidText {
  top: -0.5px;
}
.youxuanBg {
  background-color: $C_FFF6EA;
}
.soldWrapBg {
  background-color: $C_eee;
}
.limitIcon {
  width: 16px;
  height: 32px;
  top: -1px;
}
.titleText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_333333;
}
.youxuanText {
  color: $C_602A12;
}
.youxuanIcon {
  width: 28px;
  height: 28px;
}
.youxuanIconAndroid {
  top: -0.5px;
}
.freeText {
  color: $C_3565B9;
}
.soldText {
  color: $C_999;
}

