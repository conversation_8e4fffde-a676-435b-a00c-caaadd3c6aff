import React, { memo, useCallback, useMemo, useState } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import * as ImageUrl from '../../../Constants/ImageUrl';
import Utils from '../../../Util/Utils';
import NumberSplitText from '../../../ComponentBusiness/NumberSplitText';
import c2xStyles from './vehicleIsd2C2xStyles.module.scss';
import { GetAB, AppContext, CarLog } from '../../../Util/Index';
import { LogKey, ApiResCode } from '../../../Constants/Index';
import { MarketLabel } from '../../../ComponentBusiness/VendorTag';
import VendorIsdLabel2 from './VendorIsdLabel2';
import { IVehicle, IVehiclePrice2, IClickVendorArea } from '../Types';
import { Enums } from '../../../ComponentBusiness/Common';
import VendorIsd2 from './VendorIsd2';
import VendorIsd2NewAB from './VendorIsd2NewAB';
import UITestId from '../../../Constants/UITestID';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';
import LowestPriceLabel from '../../../ComponentBusiness/AISortTags/LowestPriceLabel';
import { getIsAiSortTag } from '../../../Global/Cache/ListResSelectors';

const { getPixel, isIos, vw } = BbkUtils;

const LimitWidth = getPixel(560);
const RowLimitWidth =
  vw(100) -
  getPixel(48) -
  getPixel(22) -
  getPixel(32) -
  getPixel(42) -
  getPixel(30);

const styles = StyleSheet.create({
  wrap: {
    marginLeft: getPixel(24),
    paddingRight: getPixel(24),
  },
  originWrap: {
    marginRight: getPixel(8),
  },
  priceThroughStyle: {
    textDecorationLine: 'line-through',
  },
  originDayPrice: {
    ...font.F_28_10_regular_TripNumberRegular,
    top: getPixel(isIos ? -0.5 : 4),
    color: color.C_888888,
  },
  dayPriceCurrencyStyle: {
    ...font.F_28_10_regular,
    top: getPixel(isIos ? 0 : 2),
    color: color.deepBlueBase,
  },
  dayPriceStyle: {
    color: color.deepBlueBase,
    ...font.F_38_10_regular_TripNumberSemiBold,
    top: getPixel(isIos ? 0 : 3),
    lineHeight: getPixel(38),
  },
  totalIcon: {
    top: getPixel(isIos ? 0 : 1),
  },
  soldOutStyle: {
    color: color.C_ccc,
  },
});

export const VehiclePrice: React.FC<IVehiclePrice2> = memo(
  ({
    style,
    isSoldOut,
    currentDailyPrice,
    curOriginDPrice,
    currentTotalPrice,
    currentCurrencyCode,
    marketTag,
    floorId,
    packageCode,
    priceCount,
    clickLogData,
    onPressQuestion,
    isCurrentExpend,
  }: IVehiclePrice2) => {
    const handlePressQuestion = useCallback(() => {
      onPressQuestion(floorId, packageCode, clickLogData);
    }, [floorId, onPressQuestion, packageCode, clickLogData]);
    return (
      <View className={c2xStyles.priceDetail} style={style}>
        <View className={c2xStyles.priceWrap}>
          <View className={c2xStyles.dayPriceWrap}>
            {curOriginDPrice >= 0 && curOriginDPrice !== currentDailyPrice && (
              <BbkCurrencyFormatter
                wrapperStyle={styles.originWrap}
                price={curOriginDPrice}
                currency={currentCurrencyCode}
                currencyStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.originDayPrice,
                  styles.priceThroughStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                isNew={true}
              />
            )}
            <BbkText
              className={classNames(
                c2xStyles.dayUnit,
                isSoldOut && c2xStyles.soldOutStyle,
              )}
            >
              日均
            </BbkText>
            <BbkCurrencyFormatter
              price={currentDailyPrice}
              currency={currentCurrencyCode}
              currencyStyle={xMergeStyles([
                styles.dayPriceCurrencyStyle,
                isSoldOut && styles.soldOutStyle,
              ])}
              priceStyle={xMergeStyles([
                styles.dayPriceStyle,
                isSoldOut && styles.soldOutStyle,
              ])}
              isNew={true}
            />
          </View>
          <View
            className={classNames(c2xStyles.dayPriceWrap, c2xStyles.priceGap)}
          >
            {currentTotalPrice >= 0 && (
              <BbkTouchable
                className={c2xStyles.helpIcon}
                debounce={true}
                disabled={isSoldOut}
                testID={UITestId.car_testid_comp_vendor_price_help}
                onPress={handlePressQuestion}
              >
                <BbkText
                  className={classNames(
                    c2xStyles.totalIcon,
                    isSoldOut && c2xStyles.soldOutStyle,
                  )}
                  style={styles.totalIcon}
                  type="icon"
                >
                  {icon.help}
                </BbkText>
              </BbkTouchable>
            )}
            <BbkText
              className={classNames(
                c2xStyles.totalUnit,
                isSoldOut && c2xStyles.soldOutStyle,
              )}
            >
              总价
            </BbkText>
            <BbkCurrencyFormatter
              price={currentTotalPrice}
              currency={currentCurrencyCode}
              currencyStyle={xMergeStyles([
                styles.originDayPrice,
                isSoldOut && styles.soldOutStyle,
              ])}
              priceStyle={xMergeStyles([
                styles.originDayPrice,
                isSoldOut && styles.soldOutStyle,
              ])}
              isNew={true}
            />
            {priceCount > 1 && (
              <BbkText
                className={classNames(
                  c2xStyles.totalUnit,
                  isSoldOut && c2xStyles.soldOutStyle,
                )}
              >
                起
              </BbkText>
            )}
          </View>
          <View
            className={classNames(c2xStyles.dayPriceWrap, c2xStyles.marketGap)}
          >
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_详情页_供应商_营销标签',
                info: { labelCode: marketTag?.labelCode },
              })}
            >
              <MarketLabel
                title={marketTag?.title}
                desc={marketTag?.amountTitle}
                isSoldOut={isSoldOut}
                height={42}
                isBg={true}
              />
            </XViewExposure>
          </View>
        </View>
        <View className={c2xStyles.arrowWrap}>
          <Image
            className={c2xStyles.expendIcon}
            source={{
              uri: isCurrentExpend
                ? `${ImageUrl.DIMG04_PATH}1tg0e12000j7dnokw86E6.png`
                : `${ImageUrl.DIMG04_PATH}1tg2712000j7dnkzz9390.png`,
            }}
          />
        </View>
      </View>
    );
  },
);

const VehicleIsd: React.FC<IVehicle> = memo(
  ({
    floorId,
    lowestPrice,
    floorName,
    isSelect,
    pickWayInfo,
    returnWayInfo,
    alltags,
    packageList,
    index,
    isExpend,
    exposureLogData,
    clickLogData,
    isSelfService,
    saleOutList,
    showCarServiceDetail,
    onPressQuestion,
    onPressBooking,
    onPressVendor,
    onScrollToFloor,
    needVenderNameLabel,
  }: IVehicle) => {
    const [isCurrentExpend, setIsCurrentExpend] = useState(isExpend);
    const curPackage = packageList?.[0];
    const unSoldPackage = packageList?.find(packItem => {
      return !Utils.validateIsSaleOut(saleOutList, packItem);
    });
    const isSoldOut = !unSoldPackage;
    const vehiclePriceInfo = curPackage?.priceInfo;
    const {
      currentDailyPrice,
      curOriginDPrice,
      currentCurrencyCode,
      currentTotalPrice,
    } = vehiclePriceInfo || {};
    const marketTags = packageList?.[0]?.marketingTags;

    const clickLog = useCallback(
      (buttonValue: IClickVendorArea, logData: any) => {
        CarLog.LogCode({
          name: '点击_产品详情页_供应商报价',
          button: buttonValue,
          data: logData,
          info: {
            isSelfService,
            totalPrice: logData?.totalPrice,
            dailyPrice: logData?.dailyPrice,
            isLowestPrice: lowestPrice,
            carAge: logData?.carAge,
            skuId: logData?.skuId,
            productIndex: logData?.productIndex,
            isSelectedVendorTop1:
              AppContext.vehicleLogInfo?.isSelectedVendorTop1,
            topSelectedVendorCarAge: logData?.carAge,
            isSelectedVendor: isSelect ? 1 : 0,
            quoteRank: index,
            filterInfo: AppContext?.vehicleLogInfo?.filterInfo,
          },
        });
        CarLog.LogTrace({
          key: LogKey.cm_car_app_click_airanking,
          info: {
            name: '点击_产品详情页_供应商报价',
            newMergeId: AppContext.currentNewMergeId,
            vehicleId: logData?.vehicleCode,
            serverRequestId: getServerRequestId(),
            requestId: getListRequestId(),
            vid: AppContext.MarketInfo.vid,
          },
        });
      },
      [isSelfService, lowestPrice, index, isSelect],
    );

    const onExpend = useCallback(() => {
      setIsCurrentExpend(!isCurrentExpend);
      clickLog(IClickVendorArea.Expend, clickLogData);
      if (!isCurrentExpend) {
        // 需要展开收起渲染完成后在执行锚点定位
        setTimeout(() => {
          onScrollToFloor(index);
        }, 500);
      }
    }, [clickLog, index, clickLogData, isCurrentExpend, onScrollToFloor]);

    const getVendorExposureData = () => ({
      key: LogKey.c_car_exposure_product_quote_10650068646,
      ...exposureLogData,
      info: {
        ...exposureLogData?.info,
        ...AppContext?.vehicleLogInfo,
        quoteRank: index, // 车型楼层
      },
    });

    const handlePressVendor = useCallback(() => {
      onPressVendor(floorId, curPackage?.code, clickLogData?.skuId);
      clickLog(IClickVendorArea.Vendor, clickLogData);
    }, [clickLog, curPackage?.code, floorId, clickLogData, onPressVendor]);

    const handlePressBooking = useCallback(
      (curFloorId, curPackageCode, logData) => {
        onPressBooking(
          Enums.TotalPriceModalType.Vendor,
          curFloorId,
          curPackageCode,
          index, // 车型楼层
        );
        clickLog(IClickVendorArea.Booking, logData);
      },
      [clickLog, onPressBooking, index],
    );

    const handlePressQuestion = useCallback(
      (curFloorId, curPackageCode, logData) => {
        onPressQuestion(
          Enums.TotalPriceModalType.Vendor,
          curFloorId,
          curPackageCode,
        );
        clickLog(IClickVendorArea.Question, logData);
      },
      [clickLog, onPressQuestion],
    );
    const pickUpDescText = returnWayInfo ? `取-${pickWayInfo}` : pickWayInfo;
    const dropOffDescText = `还-${returnWayInfo}`;
    const { vendorName, vendorCode } = clickLogData;
    const showVendorNameLabel =
      !!needVenderNameLabel &&
      !![ApiResCode.ydfVendorCode, ApiResCode.ehaiVendorCode].includes(
        vendorCode,
      );
    const priceStyle = React.useMemo(() => {
      // market标签宽度 3
      let labelLength = 0;
      alltags?.forEach(tag => {
        if (tag?.title) {
          labelLength += getPixel(BbkUtils.getCharLength(tag?.title) * 12 + 28);
        }
      });
      if (showVendorNameLabel) {
        labelLength += getPixel(
          BbkUtils.getCharLength(clickLogData?.vendorName) * 12 + 28,
        );
      }
      // 日均前面的划线价
      let originDailyPriceWidth = 0;
      if (curOriginDPrice >= 0 && curOriginDPrice !== currentDailyPrice) {
        originDailyPriceWidth =
          getPixel(18 + 8) + BbkUtils.getCharLength(`${curOriginDPrice}`) * 17;
      }
      // 日均宽度 4
      const dailyPriceWidth =
        getPixel(52) +
        getPixel(18) +
        getPixel(8) +
        getPixel(19 * BbkUtils.getCharLength(`${currentDailyPrice}`)) +
        originDailyPriceWidth;

      // 取车地点宽度1
      const pickWayWidth = getPixel(
        13 * BbkUtils.getCharLength(`${pickUpDescText}`),
      );
      // 还车地点宽度2
      const returnWayWidth = returnWayInfo
        ? getPixel(14 * BbkUtils.getCharLength(`${dropOffDescText}`))
        : 0;
      // 优惠券标签宽度6
      let marketLabelWidth = 0;
      if (marketTags?.[0]?.title && marketTags?.[0]?.amountTitle) {
        marketLabelWidth += getPixel(
          (BbkUtils.getCharLength(marketTags?.[0]?.title) +
            BbkUtils.getCharLength(marketTags?.[0]?.amountTitle)) *
            12 +
            12 +
            16,
        );
      }
      // 总价宽度5
      const totalPriceWidth = getPixel(
        25 +
          13 +
          52 +
          18 +
          BbkUtils.getCharLength(`${currentTotalPrice}`) * 17 +
          26,
      );

      if (returnWayWidth) {
        if (
          pickWayWidth + dailyPriceWidth < RowLimitWidth &&
          returnWayWidth + totalPriceWidth < RowLimitWidth &&
          labelLength + marketLabelWidth < RowLimitWidth
        ) {
          if (marketLabelWidth === 0) {
            return { marginTop: getPixel(-86) };
          }
          return { marginTop: getPixel(-140) };
        }
        if (
          dailyPriceWidth + returnWayWidth < RowLimitWidth &&
          labelLength + totalPriceWidth < RowLimitWidth
        ) {
          return { marginTop: getPixel(-78) };
        }
        if (dailyPriceWidth + labelLength < RowLimitWidth) {
          return { marginTop: getPixel(-42) };
        }
      } else {
        if (
          pickWayWidth + dailyPriceWidth < RowLimitWidth &&
          labelLength + totalPriceWidth < RowLimitWidth
        ) {
          return { marginTop: getPixel(-86) };
        }
        if (labelLength + dailyPriceWidth < RowLimitWidth) {
          return { marginTop: getPixel(-42) };
        }
      }
      return {};
    }, [
      alltags,
      curOriginDPrice,
      currentDailyPrice,
      currentTotalPrice,
      isSelect,
      marketTags,
      returnWayInfo,
      pickWayInfo,
      needVenderNameLabel,
      clickLogData,
      showVendorNameLabel,
    ]);

    const allPackageLevel = useMemo(() => {
      return packageList
        ?.map(item => item?.reference?.packageLevel)
        ?.filter(v => v);
    }, [packageList]);
    const isLowestPrice = lowestPrice === 1;
    const isAddPriceStyle = !!packageList?.find(
      packageItem => packageItem?.priceInfo?.gapPrice !== undefined,
    );
    const VendorIsd2NewABComponent = GetAB.isISDShelves2New()
      ? VendorIsd2NewAB
      : VendorIsd2;
    // 是否命中服务端AI排序算法AB实验
    const isAiSortTag = getIsAiSortTag();
    return (
      <XViewExposure
        style={styles.wrap}
        className={classNames(index > 0 && c2xStyles.mt16)}
        testID={CarLog.LogExposure(getVendorExposureData())}
      >
        <BbkTouchable
          debounce={true}
          onPress={onExpend}
          className={classNames(c2xStyles.wrap)}
          style={{
            paddingBottom: getPixel(isCurrentExpend ? 0 : 30),
          }}
          testID={UITestId.car_testid_page_vendor_card}
        >
          <View
            style={{
              paddingTop: getPixel(lowestPrice ? 32 : 30),
            }}
          >
            {isLowestPrice && (
              <View
                className={classNames(
                  c2xStyles.headerTop,
                  !isLowestPrice && c2xStyles.headerNoLowestPrice,
                )}
              >
                {isLowestPrice && (
                  <View>
                    {/* isAiSortTag 表示是否命中服务端AI排序算法AB实验 */}
                    {isAiSortTag ? (
                      <LowestPriceLabel
                        isSoldOut={isSoldOut}
                        labelName="总价最低"
                        isFromVendorList={true}
                      />
                    ) : (
                      <View
                        className={classNames(
                          c2xStyles.topLabel,
                          isSoldOut && c2xStyles.soldOutLabelStyle,
                        )}
                      >
                        <BbkText
                          className={classNames(
                            c2xStyles.topLabelText,
                            !isIos && c2xStyles.topLabelTextAndroid,
                            isSoldOut && c2xStyles.soldOutLabelTextStyle,
                          )}
                        >
                          总价最低
                        </BbkText>
                      </View>
                    )}
                  </View>
                )}
              </View>
            )}
            <BbkTouchable
              onPress={handlePressVendor}
              debounce={true}
              className={c2xStyles.headWrap}
              style={{
                marginTop: getPixel(isLowestPrice ? 16 : 0),
              }}
            >
              <View className={c2xStyles.titleView}>
                {isSelect && (
                  <Image
                    className={c2xStyles.youxuanBg}
                    source={{
                      uri: isSoldOut
                        ? `${ImageUrl.DIMG04_PATH}1tg1u12000k79lfa03853.png`
                        : `${ImageUrl.DIMG04_PATH}1tg6312000k6uj4za170C.png`,
                    }}
                  />
                )}
                {floorName?.map((item, fIndex) => {
                  return (
                    <View
                      className={c2xStyles.titleWrap}
                      key={`${floorId}_${item}`}
                    >
                      {fIndex > 0 && (
                        <View
                          className={classNames(
                            c2xStyles.splitLine,
                            isSoldOut && c2xStyles.soldOutLabelStyle,
                          )}
                        />
                      )}
                      <NumberSplitText
                        text={item}
                        fontSize={32}
                        textStyle={isSoldOut && styles.soldOutStyle}
                      />
                    </View>
                  );
                })}
              </View>
              <View className={c2xStyles.detailWrap}>
                <BbkText
                  className={classNames(
                    c2xStyles.pickWayText,
                    isSoldOut && c2xStyles.soldOutStyle,
                  )}
                >
                  详情
                </BbkText>
                <BbkText
                  type="icon"
                  className={classNames(
                    c2xStyles.arrowRightList,
                    isSoldOut && c2xStyles.soldOutStyle,
                  )}
                >
                  {icon.arrowRight}
                </BbkText>
              </View>
            </BbkTouchable>
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光-货架页-门店位置',
                info: {
                  pickupway: pickUpDescText,
                  ...(!!returnWayInfo && { returnway: dropOffDescText }),
                },
              })}
            >
              {!!pickWayInfo && (
                <View className={c2xStyles.pickWrayWrap}>
                  <BbkText
                    className={classNames(
                      c2xStyles.pickWayText,
                      isSoldOut && c2xStyles.soldOutStyle,
                    )}
                  >
                    {pickUpDescText}
                  </BbkText>
                </View>
              )}
              {!!returnWayInfo && (
                <View className={c2xStyles.pickWrayWrap}>
                  <BbkText
                    className={classNames(
                      c2xStyles.pickWayText,
                      isSoldOut && c2xStyles.soldOutStyle,
                    )}
                  >
                    {dropOffDescText}
                  </BbkText>
                </View>
              )}
            </XViewExposure>
            {(alltags?.length > 0 || isSelect) && (
              <View className={c2xStyles.labelWrap}>
                {showVendorNameLabel && (
                  <View className={c2xStyles.labelStyle}>
                    <VendorIsdLabel2
                      title={vendorName}
                      isSelect={true}
                      isSoldOut={isSoldOut}
                      noShowYouXuanIcon={true}
                    />
                  </View>
                )}
                {alltags?.map(item => {
                  return (
                    <View className={c2xStyles.labelStyle}>
                      <VendorIsdLabel2
                        title={item.title}
                        labelCode={item.labelCode}
                        isSoldOut={isSoldOut}
                      />
                    </View>
                  );
                })}
              </View>
            )}
          </View>
          {vehiclePriceInfo && (!isCurrentExpend || isAddPriceStyle) && (
            <VehiclePrice
              style={priceStyle}
              isSoldOut={isSoldOut}
              currentDailyPrice={currentDailyPrice}
              curOriginDPrice={curOriginDPrice}
              currentCurrencyCode={currentCurrencyCode}
              currentTotalPrice={currentTotalPrice}
              floorId={floorId}
              packageCode={curPackage?.code}
              marketTag={marketTags?.[0]}
              clickLogData={clickLogData}
              onPressQuestion={handlePressQuestion}
              priceCount={packageList?.length}
              isCurrentExpend={isCurrentExpend}
            />
          )}
          {isCurrentExpend &&
            packageList?.map((packageItem, pIndex) => {
              const {
                code,
                name,
                insuranceDesc,
                priceInfo,
                marketingTags,
                reference,
              } = packageItem;
              const isVendorSoldOut = Utils.validateIsSaleOut(
                saleOutList,
                packageItem,
              );
              // 报价的埋点信息，需覆盖默认取第一个报价的埋点数据
              const logData = {
                ...clickLogData,
                isdropoffSource: Utils.getLogStrValue(reference?.rRc),
                ispickupSource: Utils.getLogStrValue(reference?.pRc),
                pStoreCode: reference?.pStoreCode,
                pickWayInfo: reference?.pickWayInfo,
                rStoreCode: reference?.rStoreCode,
                returnWayInfo: reference?.returnWayInfo,
                currentDailyPrice: priceInfo?.currentDailyPrice,
                currentOriginalDailyPrice: priceInfo?.currentOriginalDailyPrice,
                currentTotalPrice: priceInfo?.currentTotalPrice,
                productName: name,
                skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
                packageCode: code,
                totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
                dailyPrice: priceInfo?.currentDailyPrice, // 日均价
                recommendType: reference?.gsDesc || '', // 货架推荐类型
              };
              return (
                <VendorIsd2NewABComponent
                  isFirst={pIndex === 0}
                  key={`${floorId}_package_${String(pIndex)}`}
                  floorId={floorId}
                  code={code}
                  name={name}
                  isSoldOut={isVendorSoldOut}
                  insuranceDesc={insuranceDesc}
                  priceInfo={priceInfo}
                  reference={reference}
                  marketTag={marketingTags?.[0]}
                  clickLogData={logData}
                  onPressQuestion={handlePressQuestion}
                  onPressBooking={handlePressBooking}
                  showCarServiceDetail={showCarServiceDetail}
                  allPackageLevel={allPackageLevel}
                />
              );
            })}
        </BbkTouchable>
      </XViewExposure>
    );
  },
);

export default VehicleIsd;
