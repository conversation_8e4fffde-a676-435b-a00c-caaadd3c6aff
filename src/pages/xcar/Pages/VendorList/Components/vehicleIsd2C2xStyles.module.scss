@import '../../../Common/src/Tokens/tokens/color.scss';

.container {
  margin-left: 24px;
  margin-right: 24px;
}
.mt16 {
  margin-top: 16px;
}
.wrap {
  background-color: $white;
  border-radius: 12px;
  z-index: 2;
}
.titleView {
  flex-direction: row;
  align-items: center;
}
.headerTop {
  flex-direction: row;
  padding-left: 32px;
  padding-right: 32px;
  justify-content: space-between;
}
.headerNoLowestPrice {
  justify-content: flex-end;
}
.topLabel {
  height: 40px;
  justify-content: center;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 4px;
  background-color: $orangePrice;
}
.topLabelText {
  font-size: 24px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  color: $white;
}
.topLabelTextAndroid {
  top: -0.5px;
}
.headWrap {
  margin-left: 32px;
  margin-right: 32px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.titleWrap {
  flex-direction: row;
  align-items: center;
}
.splitLine {
  width: 2px;
  height: 22px;
  margin-left: 12px;
  margin-right: 12px;
  background-color: $C_111111;
}
.arrowRightList {
  font-size: 28px;
  margin-left: 16px;
  color: $C_111111;
}
.expendIcon {
  width: 40px;
  height: 40px;
  position: relative;
  top: -4px;
}
.pickWrayWrap {
  margin-top: 12px;
  margin-left: 32px;
  margin-right: 32px;
}
.pickWayText {
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
  margin-right: 0;
}
.labelWrap {
  margin-top: 4px;
  margin-left: 22px;
  margin-right: 22px;
  flex-direction: row;
  flex-wrap: wrap;
  width: 560px;
}
.priceWrap {
  margin-right: 12px;
  flex: 1;
}
.dayPriceWrap {
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}
.dayUnit {
  font-size: 26px;
  font-weight: medium;
  margin-right: 4px;
  font-family: PingFangSC-Medium;
  color: $C_006FF6;
}
.totalUnit {
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_888888;
}
.helpIcon {
  margin-left: 9px;
  margin-right: 4px;
  height: 38px;
  justify-content: center;
  padding-right: 4px;
}
.totalIcon {
  font-size: 24px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Medium;
  color: $C_888888;
}
.vendorPriceWrap {
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 4px;
}
.bookingHasBookBtnWrap {
  flex-direction: row;
  align-items: flex-end;
  padding-bottom: 24px;
  padding-right: 32px;
}
.dayPriceSubWrap {
  flex-direction: row;
  align-items: flex-end;
  flex-wrap: nowrap;
}
.totalPriceWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.marketLabelAndPriceWrap {
  height: 38px;
  flex-direction: row;
}
.soldOutStyle {
  color: $C_ccc;
}
.soldOutLabelStyle {
  background-color: $C_eee;
}
.soldOutLabelTextStyle {
  color: $C_999;
}
.totalTextStyle {
  color: $C_888888;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Medium;
}
.storageNumStyle {
  color: $redBase;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Medium;
}
.bookBtn {
  position: relative;
}
.lowestPriceIcon {
  width: 52px;
  height: 38px;
  position: absolute;
  top: -22px;
  right: -8px;
  z-index: 1;
}
.couponBtnText {
  width: 70px;
  height: 30px;
}
.bookBtnText {
  color: $white;
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
}
.soldOutBtnStyle {
  color: $C_999;
}
.bookingNoBookBtnWrap {
  flex-direction: row;
  align-items: flex-end;
  padding-bottom: 24px;
  padding-right: 16px;
}
.marketTagsWrap {
  z-index: 10;
  margin-right: 8px;
}
.showAtTopWrap {
  padding-top: 74px;
}
.showAtTopOnlyWrap {
  padding-top: 66px;
}
.topTip {
  justify-content: space-between;
  flex-direction: row;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.topTipImage {
  height: 60px;
}
.tipText {
  color: $C_bbb;
  font-size: 24px;
  margin-right: 30px;
  margin-top: 8px;
  text-align: right;
  flex: 1;
}
.contentWrap {
  padding-left: 32px;
  padding-right: 32px;
}
.labelStyle {
  margin-top: 8px;
  margin-right: 8px;
}
.bottomWrap {
  flex-direction: row;
  align-items: center;
  padding-left: 32px;
  margin-top: 4px;
}
.bottomContentWrap {
  flex: 1;
  flex-direction: row;
}
.rightWrap {
  flex: 1;
}
.PickUpDropOffWrap {
  margin-top: 2px;
}
.detailWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 4px;
}
.youxuanBg {
 height: 40px;
 width: 167px;
 margin-right: 17px;
}
.priceGap {
  margin-top: 6px;
}
.marketGap {
  margin-top: 8px;
}
.priceDetail{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 28px;
}
.arrowWrap {
  display: flex;
  align-items: center;
}