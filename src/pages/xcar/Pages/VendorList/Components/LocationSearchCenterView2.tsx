import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { memo, useCallback } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { GetAB, CarLog, AppContext } from '../../../Util/Index';
import NumberSplitText from '../../../ComponentBusiness/NumberSplitText/index';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './locationSearchCenterView2C2xStyles.module.scss';
import {
  ILocationCenterView2,
  EnterPosition,
  IClickVendorArea,
} from '../Types';
import { UITestID, LogKey } from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';

const { vw, getPixel, htmlDecode, isIos } = BbkUtils;
const PH32 = 32;
const SplitWidth = 20;
const SplitMH = 8;
const ArrowDownWidth = 26;
const ArrowDownML = 12;
const styles = StyleSheet.create({
  wrap: {
    paddingTop: getPixel(16),
    paddingLeft: getPixel(PH32),
    paddingRight: getPixel(PH32),
  },
  wrapBorder: {
    borderTopWidth: getPixel(1),
    borderTopColor: color.C_eef1f6,
  },
  addressText: {
    ...font.F_30_10_medium,
    color: color.C_111111,
  },
  smallAddressText: {
    ...font.F_28_10_medium,
    color: color.C_111111,
  },
  fixAddressText: {
    ...font.F_26_10_regular,
    color: color.C_111111,
  },
  splitIcon: {
    fontSize: getPixel(SplitWidth),
    marginLeft: getPixel(SplitMH),
    marginRight: getPixel(SplitMH),
  },
  arrowDownIcon: {
    width: getPixel(ArrowDownWidth),
    height: getPixel(26),
    marginLeft: getPixel(ArrowDownML),
    marginTop: getPixel(1),
  },
  timeText: {
    color: color.C_111111,
  },
  diffTimeText: {
    color: color.C_111111,
    ...font.F_24_10_regular,
    top: getPixel(-1),
  },
});

const LocationSearchCenter2: React.FC<ILocationCenterView2> = memo(
  ({
    locationDate,
    testID,
    clickLogData,
    setLocationAndDatePopIsShow,
    clickWithLog,
    enterPosition,
    floorName,
    onPressVendor,
  }: ILocationCenterView2) => {
    const isLocationDateFixed = enterPosition === EnterPosition.topScreen;
    const showSearchSelectorWrap = useCallback(() => {
      setLocationAndDatePopIsShow({
        visible: true,
        locationDatePopType: 3,
        enterPosition,
      });
      BbkUtils.ensureFunctionCall(clickWithLog);
    }, [setLocationAndDatePopIsShow, clickWithLog, enterPosition]);
    const handlePressVendor = useCallback(() => {
      onPressVendor(
        clickLogData?.floorId,
        clickLogData?.packageCode,
        clickLogData?.skuId,
      );
      CarLog.LogCode({
        name: '点击_产品详情页_供应商报价',
        button: IClickVendorArea.Vendor,
        data: clickLogData,
        info: {
          isSelfService: clickLogData?.isSelfService,
          totalPrice: clickLogData?.totalPrice,
          dailyPrice: clickLogData?.dailyPrice,
          isLowestPrice: clickLogData?.lowestPrice,
          carAge: clickLogData?.carAge,
          skuId: clickLogData?.skuId,
          productIndex: clickLogData?.productIndex,
        },
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_产品详情页_供应商报价',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: clickLogData?.vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
          vid: AppContext.MarketInfo.vid,
        },
      });
    }, [clickLogData, onPressVendor]);
    const limitWidth =
      (vw(100) -
        getPixel(
          PH32 * 2 + SplitWidth + SplitMH * 2 + ArrowDownWidth + ArrowDownML,
        )) /
      2;

    const pAddress = locationDate?.pickupLocation?.locationName;
    const rAddress = locationDate?.returnLocation?.locationName;
    const plen = BbkUtils.getCharLength(pAddress);
    const rlen = BbkUtils.getCharLength(rAddress);
    const addressLimitLength = isLocationDateFixed ? 26 : 20;

    const isPOver = plen > addressLimitLength;
    const isROver = rlen > addressLimitLength;
    const isSameAddress = pAddress === rAddress;
    const isAllOver = plen + rlen > addressLimitLength * 2;
    const isPAddressDynamics =
      !isSameAddress && isPOver && !isROver && isAllOver;
    const isOverLimitAddress = !isSameAddress && isPOver && isROver;
    const addressTextStyle = styles.fixAddressText;
    const ptime = locationDate?.ptime;
    const rtime = locationDate?.rtime;
    const { pickUpDateStr, dropOffDateStr } =
      DateFormatter.pickUpAndDropOffDateFormat(ptime, rtime);
    const diff = BbkUtils.isd_dhm(ptime, rtime);
    const isISDShelves2NewABTest = GetAB.isISDShelves2New();
    return (
      <View testID={testID}>
        <BbkTouchable
          debounce={true}
          onPress={showSearchSelectorWrap}
          style={xMergeStyles([
            styles.wrap,
            isLocationDateFixed && styles.wrapBorder,
          ])}
          testID={UITestID.car_testid_page_vendorlist_header_location}
          className={isISDShelves2NewABTest ? c2xStyles.wrap : ''}
        >
          <View className={isISDShelves2NewABTest ? c2xStyles.textWrap : ''}>
            <View className={c2xStyles.addressWrap}>
              {!!pAddress && (
                <View
                  className={isPAddressDynamics && c2xStyles.pAddressWrap}
                  style={
                    isOverLimitAddress && {
                      maxWidth: limitWidth,
                    }
                  }
                >
                  <Text numberOfLines={1} style={addressTextStyle}>
                    {pAddress}
                  </Text>
                </View>
              )}
              {!isSameAddress && (
                <Text type="icon" style={styles.splitIcon}>
                  {htmlDecode(icon.oneWay)}
                </Text>
              )}
              {!isSameAddress && !!rAddress && (
                <View
                  className={
                    !isPAddressDynamics &&
                    (isISDShelves2NewABTest || isAllOver) &&
                    c2xStyles.rAddressWrap
                  }
                >
                  <Text numberOfLines={1} style={addressTextStyle}>
                    {rAddress}
                  </Text>
                </View>
              )}
              {!isISDShelves2NewABTest && (
                <Image
                  style={styles.arrowDownIcon}
                  source={{
                    uri: `${ImageUrl.DIMG04_PATH}1tg3612000knwjid1E5DD.png`,
                  }}
                />
              )}
            </View>
            <View className={c2xStyles.timeWrapTab}>
              {!!pickUpDateStr && (
                <NumberSplitText
                  textStyle={styles.timeText}
                  text={pickUpDateStr}
                  fontSize={26}
                />
              )}
              {!!diff && (
                <View
                  className={classNames(
                    c2xStyles.diffTime,
                    !isIos && c2xStyles.diffTimeAndroid,
                  )}
                >
                  <Text style={styles.diffTimeText}>{diff}</Text>
                </View>
              )}
              {!!dropOffDateStr && (
                <NumberSplitText
                  textStyle={styles.timeText}
                  text={dropOffDateStr}
                  fontSize={26}
                />
              )}
            </View>
          </View>
          {isISDShelves2NewABTest && (
            <View className={c2xStyles.editIconWrap}>
              <Image
                source={{
                  uri: `${ImageUrl.DIMG04_PATH}1tg0p12000m8yxppb0BC5.png`,
                }}
                className={c2xStyles.editIcon}
              />
            </View>
          )}
        </BbkTouchable>
        {isLocationDateFixed && floorName?.length > 0 && (
          <BbkTouchable
            onPress={handlePressVendor}
            debounce={true}
            className={c2xStyles.titleView}
          >
            {floorName?.map((item, fIndex) => {
              return (
                <View
                  className={c2xStyles.titleWrap}
                  key={`top_header_${item}`}
                >
                  {fIndex > 0 && (
                    <View className={classNames(c2xStyles.splitLine)} />
                  )}
                  <NumberSplitText text={item} fontSize={32} />
                </View>
              );
            })}
            <Text type="icon" className={classNames(c2xStyles.arrowRightList)}>
              {icon.arrowRightList}
            </Text>
          </BbkTouchable>
        )}
      </View>
    );
  },
);

export default LocationSearchCenter2;
