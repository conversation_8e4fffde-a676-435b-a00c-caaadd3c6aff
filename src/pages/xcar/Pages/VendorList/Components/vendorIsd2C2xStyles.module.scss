@import '../../../Common/src/Tokens/tokens/color.scss';

.vendorWrap {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 24px;
  padding-bottom: 24px;
}
.borderView {
  position: absolute;
  height: 1px;
  width: 638px;
  left: 32px;
  background-color: $C_eef1f6;
}
.gapPriceWrap {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.safeguardGapPriceWrap {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 138px;
  margin: 1px 0;
}
.gapPriceAndBookWrap {
  flex-direction: row;
  align-items: center;
}
.firstWrap {
  margin-top: 30px;
  padding-top: 32px;
}
.safeguardFirstWrap {
  margin-top: 24px;
  padding-top: 32px;
}
.safeguardWrap {
  height: 138px;
  display: flex;
  flex-direction: row;
}
.titleWrap {
  flex-direction: row;
  left: -4px;
}
.safeguardTitleWrap {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 138px;
  width: 144px;
  border-radius: 8px 8px 8px 8px;
  background-color: $C_F5F8FA;
}
.prepTitleWrap {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 138px;
  width: 144px;
  border-radius: 8px 8px 8px 8px;
  background-color: $C_F3F9FE;
}
.safeguardText {
  margin: 1px 0 1px 24px;
}
.titleClickView {
  flex-direction: row;
  align-items: center;
  padding-bottom: 12px;
}
.titleIcon {
  width: 36px;
  height: 36px;
  margin-right: 8px;
}
.safeguardTitleClickView {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}
.safeguardTitleIcon {
  width: 52px;
  height: 52px;
}
.titleText {
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  color: $C_111111;
}
.safeguardTitleText {
  font-size: 26px;
  margin-top: 2px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  color: $C_111111;
}
.prepText {
  color: $C_ff6600;
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
}
.descWrap {
  margin-top: 8px;
}
.descColumn {
  margin-bottom: 12px;
}
.descRow {
  flex-direction: row;
}
.descRowPT {
  padding-top: 12px;
}
.descClickArea {
  flex-direction: row;
  align-items: center;
}
.descText {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
}
.splitLine {
  width: 2px;
  height: 22px;
  background-color: $C_ccc;
  margin-left: 12px;
  margin-right: 12px;
}
.splitLineAndroid {
  top: 1px;
}
.priceWrap {
  flex-direction: row;
}
.leftWrap {
  flex: 1;
  justify-content: center;
}
.priceRow {
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: flex-end;
}
.singlePriceRow {
  justify-content: flex-end;
}
.rowCenter {
  align-items: center;
}
.singleDescWrap {
  flex: 1;
}
.singleDescText {
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
}
.dayPriceWrap {
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-end;
  flex-wrap: wrap;
}
.helpIcon {
  margin-left: 9px;
  margin-right: 4px;
  height: 38px;
  justify-content: center;
  padding-right: 4px;
}
.totalIcon {
  font-size: 24px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Medium;
  color: $C_888888;
}
.totalUnit {
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_888888;
}
.dayUnit {
  margin-left: 4px;
  font-size: 26px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  margin-right: 4px;
  color: $C_006FF6;
}
.safeguardDayUnit {
  margin-left: 6px;
  font-size: 26px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  margin-right: 8px;
  color: $C_006FF6;
}
.addUnit {
  font-size: 26px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
  color: $C_006FF6;
  margin-right: 4px;
}
.marketLabelWrap {
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 4px;
}
.storageNumWrap {
  position: absolute;
  right: 0;
  width: 70px;
  align-items: center;
  bottom: 82px;
}
.storageNumStyle {
  font-size: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_F63B2E;
}
.bookBtn {
  flex-direction: row;
  align-items: flex-end;
  top: -2px;
}
.bookBtnWrapNew {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  margin-left: 16px;
  margin-bottom: 6px;
}
.mb0 {
  margin-bottom: 0;
}
.bookBtnText {
  color: $white;
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Medium;
}
.soldOutBtnStyle {
  color: $C_999;
}
.soldOutStyle {
  color: $C_ccc;
}
.soldOutLabelStyle {
  background-color: $C_eee;
}
.priceTotalWrap {
  flex-direction: row;
}
.addPriceWrap {
  flex-direction: row;
}
.safeguardAddPriceWrap {
  flex-direction: row;
  top: 2px;
}
.vBtn {
  width: 70px;
  height: 70px;
  margin-left: 8px;
}