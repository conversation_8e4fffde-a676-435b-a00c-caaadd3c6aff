import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { useMemo } from 'react';
import { XView as View, XViewExposure, xClassNames } from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './vehicleAndLimit2C2xStyles.module.scss';
import { Utils, CarLog, AppContext, GetAB } from '../../../Util/Index';
import { ImageUrl, UITestID, LogKey } from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';
import { IVehicleAndLimit, ITipCenter } from '../Types';
import UITestId from '../../../Constants/UITestID';
import NewVehicleNameIsd from './NewVehicleNameIsd';
import VehicleDescNew2 from './VehicleDescNew2';
import { openURL } from '../../../Helpers/WebHelper';

const { getPixel, isIos, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  nameStyle: {
    paddingTop: getPixel(40),
    paddingBottom: getPixel(isIos ? 16 : 12),
    marginLeft: 0,
  },
  wrap: {
    backgroundColor: color.white,
    borderTopLeftRadius: getPixel(16),
    borderTopRightRadius: getPixel(16),
    borderBottomLeftRadius: getPixel(12),
    borderBottomRightRadius: getPixel(12),
  },
});

function splitNumberAndText(inputString) {
  if (!inputString) return {};
  // 使用正则表达式匹配数字（包括小数）和字母
  const numberMatch = inputString.match(/[\d.]+/);
  const textMatch = inputString.match(/[^\d.]+/);

  // 提取匹配到的数字和文字部分
  const number = numberMatch ? numberMatch[0] : '';
  const text = textMatch ? textMatch.join('').trim() : '';

  return { number, text };
}

export const TipCenter: React.FC<ITipCenter> = ({ isLimit, tipText }) => {
  if (isLimit === undefined) return null;
  if (isLimit)
    return (
      <>
        <View className={c2xStyles.limitTipCenterWrap}>
          <Image
            className={c2xStyles.limitTipPic}
            source={{
              uri: `${ImageUrl.DIMG04_PATH}0AS0b12000adu17613D0D.png_.webp`,
            }}
            resizeMode="contain"
          />

          {!!tipText && (
            <BbkText className={c2xStyles.tipText} numberOfLines={1}>
              {tipText}
            </BbkText>
          )}
        </View>
        <BbkText type="icon" className={c2xStyles.tipDetailText}>
          {icon.arrowForward}
        </BbkText>
      </>
    );

  return (
    <View className={c2xStyles.unLimitTipCenterWrap}>
      <Image
        className={c2xStyles.unLimitTipPic}
        source={{
          uri: `${ImageUrl.DIMG04_PATH}0AS1012000adu18x73A24.png_.webp`,
        }}
        resizeMode="contain"
      />

      <BbkText className={c2xStyles.tipText}>{` ${tipText}`}</BbkText>
    </View>
  );
};

export type IVehicleAndLimitBType = IVehicleAndLimit & {
  baseInfoDescList: {
    code: string;
    text: any;
    iconUrl: string;
  }[];
  configInfoDescList: {
    code: string;
    text: any;
    iconUrl: string;
  }[];
  renderData: any;
  commentAggregation: any;
  isShelvesCommentSummaryLoading?: boolean;
};

const VehicleAndLimitB: React.FC<IVehicleAndLimitBType> = ({
  isHotLabel,
  licenseTag,
  name,
  vehicleCode,
  onPressVehicleName,
  baseInfoDescList,
  configInfoDescList,

  isShowTip,
  // 车牌是否限行
  isLimit,
  tipText,
  onPressTip = Utils.noop,
  onPressShowNoLimitModal = Utils.noop,
  wrapStyle = [],
  marketingAtmosphere,
  isShowMarketTheme,
  isNewEnergy,
  minTotalPrice,
  showFullImmerse,
  marketTheme,
  renderData,
  commentAggregation = {},
  isShelvesCommentSummaryLoading,
}) => {
  const onPressVehicleNameFn = (tagName: string, tagCode: string) => {
    onPressVehicleName();
    if (tagName && tagCode) {
      CarLog.LogCode({
        name: `点击_产品详情页_${tagName}`,
        info: {
          tagCode,
          tagName,
          vehicleCode,
        },
      });
    } else {
      CarLog.LogCode({
        name: `点击_产品详情页`,
        info: {
          vehicleCode,
        },
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_产品详情页',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
          vid: AppContext.MarketInfo.vid,
        },
      });
    }
  };
  const onPressTipFn = () => {
    if (isLimit) {
      onPressTip();
    } else {
      onPressShowNoLimitModal(tipText);
    }

    CarLog.LogCode({ name: '点击_详情页_打开限行弹层' });
  };

  const marketMT = useMemo(() => {
    if (isShowMarketTheme && marketTheme?.skinUrl) {
      return -118;
    }
    let mt = 0;
    switch (marketingAtmosphere) {
      case 1:
        mt = -90;
        break;
      default:
        break;
    }
    return mt;
  }, [marketingAtmosphere, isShowMarketTheme, marketTheme]);

  const handlePressCommon = () => {
    if (commentAggregation?.commentUrl) {
      openURL(commentAggregation?.commentUrl);
      CarLog.LogCode({
        name: '点击_产品详情页_评论模块',
        info: {
          score: commentAggregation?.scoreAvg,
          totalCount: commentAggregation?.totalCount,
          commentDesc: commentAggregation.totalCountDesc,
          commentAggregation,
          vehicleCode,
        },
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_产品详情页_评论模块',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
          vid: AppContext.MarketInfo.vid,
        },
      });
    }
  };

  const totalCountDesc = useMemo(() => {
    return splitNumberAndText(commentAggregation?.totalCountDesc);
  }, [commentAggregation]);

  // isISDShelves2
  const isHasCouponEntry = renderData?.length > 0;
  const widthStyle = useWindowSizeChanged();

  if (!name && !isShowTip) return null;
  return (
    <View
      style={Utils.getPackageStyle([
        styles.wrap,
        {
          marginTop: getPixel(marketMT),
          paddingBottom: getPixel(isHasCouponEntry ? 0 : 8),
        },
        ...wrapStyle,
      ])}
      testID={UITestID.car_testid_page_vendorList_carInfo}
    >
      <View className={c2xStyles.arrowWrap} style={widthStyle}>
        <Image
          className={c2xStyles.iconImage}
          source={{
            uri: showFullImmerse
              ? `${ImageUrl.DIMG04_PATH}1tg4o12000cm7i1zq218F.png`
              : `${ImageUrl.DIMG04_PATH}1tg1712000cm7hdm2DE28.png`,
          }}
          resizeMode="cover"
        />
      </View>
      <BbkTouchable
        onPress={!!minTotalPrice && onPressVehicleNameFn}
        testID={UITestId.car_testid_page_vendor_detail_btn}
      >
        <View className={c2xStyles.vehicleNameContainer}>
          <NewVehicleNameIsd
            vehicleName={name}
            isHotLabel={isHotLabel}
            licenseTag={licenseTag}
            isNewEnergy={isNewEnergy}
            isShowTip={isShowTip}
            isShowNewEnergyWidth={false}
            isLimit={isLimit}
            style={styles.nameStyle}
            onTipsPress={onPressTipFn}
          />
        </View>
      </BbkTouchable>
      <VehicleDescNew2
        rows={[baseInfoDescList, configInfoDescList]}
        iconColor={color.fontSecondary}
        onPressVehicleNameFn={onPressVehicleNameFn}
        vehicleCode={vehicleCode}
      />
      {!GetAB.isVendorListNoComment() &&
        (isShelvesCommentSummaryLoading ? (
          <View className={c2xStyles.loadingContainer}>
            <View className={c2xStyles.loadingScore} />
            <View className={c2xStyles.loadingContent} />
            <View className={c2xStyles.loadingComment} />
          </View>
        ) : (
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_产品详情页_评论模块',
              info: {
                score: commentAggregation?.scoreAvg,
                totalCount: commentAggregation?.totalCount,
                commentDesc: commentAggregation.totalCountDesc,
                commentAggregation,
                vehicleCode,
              },
            })}
          >
            <BbkTouchable
              className={c2xStyles.commentContainer}
              onPress={handlePressCommon}
            >
              {commentAggregation?.content && commentAggregation?.scoreAvg ? (
                <>
                  <View className={c2xStyles.evaluateAndScoreContainer}>
                    <View className={c2xStyles.scoreContainer}>
                      <BbkText
                        className={xClassNames(
                          c2xStyles.scoreText,
                          isAndroid && c2xStyles.androidScoreText,
                        )}
                      >
                        {commentAggregation?.scoreAvg}
                      </BbkText>
                      <BbkText className={c2xStyles.scoreTextMessage}>
                        分
                      </BbkText>
                    </View>
                    <View className={c2xStyles.evaluateContainer}>
                      <BbkText
                        className={xClassNames(
                          c2xStyles.evaluateText,
                          isIos && c2xStyles.iosEvaluateText,
                        )}
                      >
                        {commentAggregation?.desc}
                      </BbkText>
                    </View>
                  </View>
                  <View className={c2xStyles.messageContainer}>
                    <BbkText numberOfLines={1}>
                      {commentAggregation?.content}
                    </BbkText>
                    <Image
                      source={{
                        uri: `${ImageUrl.DIMG04_PATH}1tg4e12000j7srtuxDDBA.png`,
                      }}
                      resizeMode="contain"
                      className={c2xStyles.lessLowCarbonBg}
                    />
                  </View>
                </>
              ) : (
                <View className={c2xStyles.noComment}>
                  <BbkText numberOfLines={1}>暂无评价</BbkText>
                </View>
              )}
              {commentAggregation?.commentUrl && (
                <>
                  <View
                    className={xClassNames(
                      c2xStyles.messageCount,
                      isAndroid && c2xStyles.androidMessageCount,
                    )}
                  >
                    <BbkText className={c2xStyles.fontNumberComment}>
                      {totalCountDesc?.number}
                      <BbkText className={c2xStyles.fontTextComment}>
                        {totalCountDesc?.text}
                      </BbkText>
                    </BbkText>
                  </View>
                  <View className={c2xStyles.iconContainer}>
                    <BbkText className={c2xStyles.iconStyle} type="icon">
                      {icon.arrowRight}
                    </BbkText>
                  </View>
                </>
              )}
            </BbkTouchable>
          </XViewExposure>
        ))}
    </View>
  );
};
export default VehicleAndLimitB;
