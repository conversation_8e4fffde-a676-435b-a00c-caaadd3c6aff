import Image from '@c2x/components/Image';
import React, { memo, useCallback, useMemo } from 'react';
import { XView as View, XLinearGradient as LinearGradient } from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { ImageUrl } from '../../../Constants/Index';
import c2xStyles from './shelves2C2xStyles.module.scss';
import ShelvesFloors from './ShelvesFloors';
import ShelvesHeader2 from './ShelvesHeader2';
import { IShelves2, EnterPosition } from '../Types';
import CouponShelves2 from './CouponShelves2';
import CarLog from '../../../Util/CarLog';
import { GetAB } from '../../../Util/Index';

const { useMemoizedFn } = BbkUtils;

const Shelves: React.FC<IShelves2> = memo(
  ({
    isLoading,
    isCouponEntry,
    isNoMatch,
    sectionHeader,
    shelvesFloor,
    sectionHeaderTestID,
    filterLabelsStr,
    onPressQuestion,
    onFloorLayout,
    onPressBooking,
    setLocationAndDatePopIsShow,
    showCarServiceDetail,
    showSearchSelectorWrapWithLog,
    onSpecificLayout,
    couponRenderData,
    isReceiveAble,
    onPress,
    onScrollToFloor,
    vehicleCode,
    handleExpand,
    ipollSceneid,
    ipollPositionNum,
    ipollLogData,
  }: IShelves2) => {
    const showSearchSelectorWrap = useCallback(() => {
      setLocationAndDatePopIsShow({
        visible: true,
        locationDatePopType: 3,
        enterPosition: EnterPosition.secondScreen,
      });
    }, [setLocationAndDatePopIsShow]);
    const showSearchSelectorWrapWithLogFn = useMemoizedFn(() => {
      showSearchSelectorWrap();
      BbkUtils.ensureFunctionCall(showSearchSelectorWrapWithLog);
    });
    const isShowFilter =
      !isLoading && sectionHeader?.isShowFitTitle && !isNoMatch;
    const filterLabelsString = useMemo(() => {
      if (!filterLabelsStr) return '';
      const hasUnwantedContent = /undefined|null|NaN|\s/.test(filterLabelsStr);
      if (hasUnwantedContent || !filterLabelsStr) {
        CarLog.LogDevError({
          expPoint: 'Shelves2-filterLabelsStr',
          eventResult: `filterLabelsStr: ${filterLabelsStr}`,
        });
      }
      // 字符串中如果包含undefined、null、NaN或者空字符则replace
      return filterLabelsStr.replace(/undefined|null|NaN|\s/g, '');
    }, [filterLabelsStr]);
    const isISDShelves2NewABTest = GetAB.isISDShelves2New();
    return (
      <View className={isCouponEntry ? c2xStyles.couponWrap : c2xStyles.wrap}>
        {/* 优惠券组件 */}
        {isCouponEntry && (
          <CouponShelves2
            couponRenderData={couponRenderData}
            onPress={onPress}
            isReceiveAble={isReceiveAble}
            vehicleCode={vehicleCode}
          />
        )}
        <Image
          className={
            isCouponEntry ? c2xStyles.shelvesTopBg : c2xStyles.shelvesBg
          }
          source={{
            uri: isISDShelves2NewABTest
              ? `${ImageUrl.DIMG04_PATH}1tg6812000m4kwthm69D3.png`
              : `${ImageUrl.DIMG04_PATH}1tg3712000j6usx14BB94.png`,
          }}
          resizeMode="cover"
        />
        <LinearGradient
          className={
            isCouponEntry ? c2xStyles.headerTopLiner : c2xStyles.headerLiner
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={[color.C_F0F6FA, color.C_EFF4FB]}
          locations={[0, 1]}
        />

        {/** 取还车地址 */}
        <ShelvesHeader2
          isFit={sectionHeader?.isFit}
          isShowGradient={sectionHeader?.isShowGradient}
          locationDate={sectionHeader?.locationDate}
          isLocationDateFixed={sectionHeader?.isLocationDateFixed}
          isShowFitTitle={sectionHeader?.isShowFitTitle}
          testID={sectionHeaderTestID}
          clickWithLog={showSearchSelectorWrapWithLogFn}
        />
        <View className={c2xStyles.wrapper}>
          {/** 列表页筛选回显示 */}
          {isShowFilter && (
            <View className={c2xStyles.filterWrap}>
              <BbkText
                className={c2xStyles.filterText}
              >{`符合${filterLabelsString}的报价`}</BbkText>
            </View>
          )}
          {/** 货架列表 */}
          <ShelvesFloors
            isLoading={isLoading}
            shelvesFloor={shelvesFloor}
            onPressQuestion={onPressQuestion}
            onPressBooking={onPressBooking}
            onFloorLayout={onFloorLayout}
            setLocationAndDatePopIsShow={setLocationAndDatePopIsShow}
            onSpecificLayout={onSpecificLayout}
            showCarServiceDetail={showCarServiceDetail}
            onScrollToFloor={onScrollToFloor}
            vehicleCode={vehicleCode}
            handleExpand={handleExpand}
            ipollSceneid={ipollSceneid}
            ipollPositionNum={ipollPositionNum}
            ipollLogData={ipollLogData}
          />
        </View>
      </View>
    );
  },
);

export default Shelves;
