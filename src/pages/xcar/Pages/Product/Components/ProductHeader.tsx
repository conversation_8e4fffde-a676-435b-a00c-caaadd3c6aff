import {
  get as lodashGet,
  merge as lodashMerge,
  omitBy as lodashOmitBy,
  findIndex as lodashFindIndex,
  isObject as lodashIsObject,
} from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import Loading from '@c2x/apis/Loading';
import Share from '@c2x/apis/Share';
import Event from '@c2x/apis/Event';
import React, { Component } from 'react';
import {
  xApplication as Application,
  xShowToast,
  xClassNames as classNames,
  XBoxShadow,
  xRouter,
  XView as View,
} from '@ctrip/xtaro';

import qs from 'qs';
import {
  BbkUtils,
  BbkConstants,
  DateFormatter,
} from '@ctrip/rn_com_car/dist/src/Utils';
import { getFormattedPrice } from '@ctrip/rn_com_car/dist/src/Shark/src/Index';
import BbkHorizontalNav, {
  BbkHorizontalNavItem,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/HorizontalNav';
import { color, font, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './productHeaderC2xStyles.module.scss';
import BbkCarHeader, { HeaderType } from '../../../ComponentBusiness/CarHeader';
import PackageTabs from './PackageTabs';

import {
  AppContext,
  CarLog,
  Utils,
  CarFetch,
  Channel,
  GetABCache,
} from '../../../Util/Index';
import {
  Platform,
  EventName,
  UITestID,
  Url,
  LogKey,
} from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
  setListStatusData,
  listStatusKeyList,
} from '../../../Global/Cache/ListReqAndResData';
import {
  getBbkImageHeaderProps,
  getBbkCommentProps,
  getInnerBookProps,
} from '../../../State/Product/BbkMapper';
import {
  ProductSelectors,
  ListReqAndResData,
} from '../../../Global/Cache/Index';
import texts from '../Texts';
import { ShareType, ShareResult } from '../../../Constants/ShareEnums';
import { ProductHeaderProps } from '../Types';

const tabHeight = 78;
const DefaultType = [HeaderType.Float];
const noop = () => {};
const styles = StyleSheet.create({
  header: {
    borderBottomWidth: 0,
  },
  navWrap: {
    borderBottomWidth: 0,
  },
  tabItem: {
    height: BbkUtils.getPixel(tabHeight),
    paddingLeft: BbkUtils.getPixel(20),
    paddingRight: BbkUtils.getPixel(20),
    paddingBottom: BbkUtils.getPixel(5),
  },
  selectTabItem: {
    color: color.blueBase,
  },
  newSelectTabItem: {
    color: color.easylifeBlue,
    ...font.F_30_16_medium,
  },
  containderStyle: {
    backgroundColor: color.white,
  },
  tabItemText: {
    ...font.F_30_16_medium,
    color: color.C_111111,
  },
});

interface ProductHeaderStates {
  isShowHeaderBottom?: boolean;
  selectedId?: string;
  isShowAnimation?: boolean;
  posterUrl?: string;
}

export const headerConfigParams = {
  showRate: 0.3,
  startY: 0,
  endY: BbkUtils.getPixel(
    350 - tabHeight - BbkUtils.fixOffsetTop(BbkConstants.DEFAULT_HEADER_HEIGHT),
  ),
};

enum CtripShareType {
  Copy = 'Copy',
  Email = 'Email',
  SMS = 'SMS',
  WeixinFriend = 'WeixinFriend',
  WeixinCircle = 'WeixinCircle',
  SinaWeibo = 'SinaWeibo',
  QQ = 'QQ',
  QQZone = 'QQZone',
}

enum TripShareType {
  Facebook = 'facebook',
  Twitter = 'twitter',
  Wechat = 'wechat',
  Wechatmoment = 'wechatmoment',
  Copy = 'copy',
  Email = 'email',
  Message = 'message',
}

const shareChannelId = {
  trip: {
    [TripShareType.Facebook]: 17260,
    [TripShareType.Twitter]: 17261,
    [TripShareType.Wechat]: 17262,
    [TripShareType.Wechatmoment]: 17263,
    WhatsApp: 17264,
    KakaoStory: 17265,
    KaKaoTalk: 17266,
    [TripShareType.Copy]: 17267,
    [TripShareType.Email]: 17268,
    [TripShareType.Message]: 17269,
  },
  ctrip: {
    [CtripShareType.Copy]: 17270,
    [CtripShareType.Email]: 17271,
    [CtripShareType.SMS]: 17272,
    [CtripShareType.WeixinFriend]: 17273,
    [CtripShareType.WeixinCircle]: 17274,
    [CtripShareType.SinaWeibo]: 17275,
    [CtripShareType.QQZone]: 17276,
    [CtripShareType.QQ]: 17277,
  },
};

const getShortUrl = async url => {
  // http://conf.ctripcorp.com/pages/viewpage.action?pageId=26083724
  // http://conf.ctripcorp.com/pages/viewpage.action?pageId=26084035
  try {
    const res = await CarFetch.longToShortLink({
      longUrl: url,
    });
    const { shortUrl } = res;
    if (!shortUrl) {
      return url;
    }
    const env = await CarFetch.getEnvType();
    const domain = Platform.SHORT_URL_DOMAIN[env];
    return `https://${domain}/${shortUrl}`;
  } catch (e) {
    return url;
  }
};

const getChannelUrl = async (linkUrl, type) => {
  const channelidConf = shareChannelId.ctrip || {};
  const url = `${linkUrl}&channelid=${channelidConf[type] || ''}`;
  const sortUrl = await getShortUrl(url);
  return sortUrl;
};

export default class ProductHeader extends Component<
  ProductHeaderProps,
  ProductHeaderStates
> {
  shareEnabled = true;

  constructor(props) {
    super(props);
    const { selectedId, refFn = noop } = props;
    refFn(this);
    this.state = {
      isShowHeaderBottom: false,
      selectedId,
      isShowAnimation: false,
      posterUrl: '',
    };
  }

  componentDidMount() {
    const { vehicleCode } = this.props;
    Event.addEventListener(EventName.ShareClickEvent, info => {
      if (info && info.shareType && info.shareType === 'CustomList') {
        CarLog.LogCode({
          name: '点击_国内_产品详情页_分享模块',

          info: {
            vehicleCode,
            shareType: '海报',
          },
        });
        const { posterUrl } = this.state;
        if (!posterUrl) {
          xShowToast({ title: '海报生成失败, 请稍后重试', duration: 3000 });
        }
        const url = `${Url.rNXTaroCarOrderBaseUrl}&initialPage=posterReview&posterUrl=${posterUrl}`;
        xRouter.navigateTo({ url });
      }
      if (info && info.shareType && info.shareType === 'WeixinFriend') {
        CarLog.LogCode({
          name: '点击_国内_产品详情页_分享模块',

          info: {
            vehicleCode,
            shareType: '卡片',
          },
        });
      }
    });
  }

  shouldComponentUpdate() {
    return AppContext.PageInstance.getPageId() !== Channel.getPageId().Book.ID;
  }

  componentWillUnmount() {
    Event.removeEventListener(EventName.ShareClickEvent);
  }

  setShowHeaderBottom = showHeaderBottom => {
    const { isShowHeaderBottom } = this.state;
    if (showHeaderBottom !== isShowHeaderBottom) {
      this.setState({
        isShowHeaderBottom: showHeaderBottom,
      });
    }
  };

  setSelectedId = tabSelectedId => {
    const { selectedId } = this.state;
    if (tabSelectedId !== selectedId) {
      this.setState({
        selectedId: tabSelectedId,
      });
    }
  };

  getBbkCarHeaderProps = () => {
    const {
      scrollY,
      isFail,
      data,
      currency,
      bizType,
      toolBoxCustomerJumpUrl,
      name,
      isSimilar,
      isHotLabel,
      licenseTag,
      licenseType,
      channelType,
      handleBackPress,
      headerContentShowAtCenter,
      vrUrl,
      vehicleCode,
    } = this.props;

    const smallImage = false;
    return {
      currency,
      enableChange: true,
      data,
      page: AppContext.PageInstance,
      scrollY,
      types: isFail || smallImage ? null : DefaultType,
      renderBottom: this.renderTab(),
      enablePress: false,
      bizType,
      toolBoxCustomerJumpUrl,
      style: styles.header,
      isRenderLeft: true,
      name: isFail ? '产品详情' : name,
      isSimilar: isFail ? false : isSimilar,
      isHotLabel,
      licenseTag,
      licenseType,
      channelType,
      handleBackPress,
      isFail,
      headerContentShowAtCenter,
      vrUrl,
      vehicleCode,
      shareExposureData: {
        name: '曝光_国内_产品详情页_分享模块',
        info: { vehicleCode },
      },
      // onPressAddress: () => {},
      // onPressDate: () => {},
      // onPressCurrency: () => {},
      // showSearchSelectorWrap: null,
    };
  };

  renderTab = () => {
    const {
      tabSections,
      isFail,
      scrollHandler,
      curInsPackageId,
      selectPackage,
      isShowPackageTabs,
    } = this.props;
    if (!tabSections?.length) return null;
    const { selectedId } = this.state;
    // 小图模式一开始隐藏
    if (isFail) {
      return null;
    }
    return (
      <>
        <BbkHorizontalNav
          style={styles.navWrap}
          animateIndicator={false}
          indicatorWidth={BbkUtils.getPixel(60)}
          indicatorHeight={BbkUtils.getPixel(6)}
          selectedId={selectedId}
          spaceAround={true}
          isAutoSlide={false}
          indicatorColor={Utils.isCtripOsd() && color.easylifeBlue}
        >
          {tabSections.map(item => (
            <BbkHorizontalNavItem
              id={item}
              title={item}
              key={item}
              style={styles.tabItem}
              textStyle={styles.tabItemText}
              textSelectedStyle={
                Utils.isCtripOsd()
                  ? styles.newSelectTabItem
                  : styles.selectTabItem
              }
              testID={`${UITestID.car_testid_page_product_header_section_tab_item}_${item}`}
              onPress={() => scrollHandler(item)}
              isLinearIndicator={true}
            />
          ))}
        </BbkHorizontalNav>
        <View style={{ display: isShowPackageTabs ? 'flex' : 'none' }}>
          <PackageTabs
            curInsPackageId={curInsPackageId}
            selectPackage={selectPackage}
          />
        </View>
      </>
    );
  };

  getShareConfig = async () => {
    const { isShowDropOff, data, curInsPackageId } = this.props;
    const { pickupLocation, returnLocation, ptime, rtime } = data;
    const { pickUpDateStr, dropOffDateStr } =
      DateFormatter.pickUpDropOffDateFormatCT({
        ptime,
        rtime,
        isShowHM: false,
        mode: 'short',
      });

    const { imageList } = getBbkImageHeaderProps({});
    let imageUrl = lodashGet(imageList, '[0].url');
    imageUrl = BbkUtils.autoProtocol(imageUrl);

    const { totalPrice } = getInnerBookProps();
    const price = await getFormattedPrice(
      totalPrice.displayCurrency,
      totalPrice.displayPrice,
    );

    const { vehicleName } = getBbkCommentProps(curInsPackageId);
    const wechatTitle = `${vehicleName} ${
      isShowDropOff
        ? `${pickupLocation.locationName}-${returnLocation.locationName}`
        : pickupLocation.locationName
    } ${pickUpDateStr}-${dropOffDateStr}`;

    const titleConfig = {
      wechat: wechatTitle,
      wechatmoment: `${texts.ctripCar}-${wechatTitle}`,
      default: '',
    };
    const contentConfig = {
      twitter: texts.shareTwitterContent(vehicleName),
      // 产品定义，存在优惠券价格不一致的情况
      wechat: texts.shareWechatContent(price),
      default: '',
    };
    return {
      titleConfig,
      contentConfig,
      imageUrl,
    };
  };

  getCtripShareConfig = async (url, data, isdData?: Object) => {
    const { titleConfig, contentConfig, imageUrl } =
      await this.getShareConfig();
    const domain = await this.getDomain();
    const domainUrl = `https://${domain}${url}`;
    const linkUrl = `${domainUrl}${Utils.toParams(data, true, true)}${
      isdData ? qs.stringify(isdData) : ''
    }`;

    const decodeLinkUrl = `${domainUrl}${Utils.toParams(data, true)}${
      isdData
        ? qs.stringify(isdData, {
            encode: false,
          })
        : ''
    }`;

    const bt825 = BbkUtils.versionCompare(Application.version, '8.25') > -1;
    const res = [
      {
        shareType: CtripShareType.Copy,
        imageUrl,
        title: titleConfig.default,
        text: contentConfig.twitter,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.Copy),
      },
      {
        shareType: CtripShareType.SMS,
        imageUrl,
        title: titleConfig.default,
        text: contentConfig.twitter,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.SMS),
      },
      {
        shareType: CtripShareType.WeixinFriend,
        imageUrl,
        title: titleConfig.wechat,
        text: contentConfig.wechat,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.WeixinFriend),
      },
      {
        shareType: CtripShareType.WeixinCircle,
        imageUrl,
        title: titleConfig.wechatmoment,
        text: contentConfig.default,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.WeixinCircle),
      },
      {
        shareType: CtripShareType.SinaWeibo,
        imageUrl,
        title: titleConfig.default,
        text: contentConfig.twitter,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.SinaWeibo),
      },
      {
        shareType: CtripShareType.QQ,
        imageUrl,
        title: titleConfig.wechat,
        text: contentConfig.wechat,
        linkUrl: await getChannelUrl(
          bt825 ? linkUrl : decodeLinkUrl,
          CtripShareType.QQ,
        ),
      },
    ];

    if (bt825) {
      res.push({
        shareType: CtripShareType.QQZone,
        imageUrl,
        title: titleConfig.wechatmoment,
        text: contentConfig.wechat,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.QQZone),
      });
    }

    // 安卓邮件屏蔽（outlook闪退，框架未找到原因）
    if (BbkUtils.isIos) {
      res.push({
        shareType: CtripShareType.Email,
        imageUrl,
        title: titleConfig.default,
        text: contentConfig.twitter,
        linkUrl: await getChannelUrl(linkUrl, CtripShareType.Email),
      });
    }

    return res.map(item => ({ ...item, desc: item.text }));
  };

  getDomain = async () => {
    const env = await CarFetch.getEnvType();
    return CarFetch.getDomainURL(env);
  };

  getMiniBookParam = () => {
    const { reference = {} } = ProductSelectors.getProductReq();
    const { dropOffOnDoor, pickUpOnDoor, payMode } = reference;
    // @ts-ignore
    const { isdParam = {} } = reference;
    const {
      pricetype,
      ptime,
      rtime,
      poiinfo,
      rpoiinfo,
      atype,
      pcid,
      rcid,
      pid,
      psid,
      rsid,
      vpid,
      vendorid,
      issend,
      isgranted,
      comPriceCode,
      hottype,
      ctripvendortype,
      sfname,
      sfot,
      iType = 1,
      idtype = 1,
      cinfo = {},
    } = isdParam;

    const IsPickupOndoor = pickUpOnDoor;
    const IsPickoffOndoor = dropOffOnDoor;
    const areatype = poiinfo.type || 0;

    // 拼接小程序需要参数
    const bookParam = [
      IsPickoffOndoor,
      IsPickupOndoor,
      areatype,
      atype,
      cinfo,
      comPriceCode,
      ctripvendortype,
      hottype,
      iType,
      idtype,
      0,
      isgranted,
      issend,
      payMode,
      pcid,
      pid,
      poiinfo,
      pricetype,
      psid,
      ptime,
      rcid,
      rpoiinfo,
      rsid,
      rtime,
      sfname,
      sfot,
      vendorid,
      vpid,
    ];

    const miniBookParam = encodeURIComponent(JSON.stringify(bookParam));
    return miniBookParam;
  };

  getOsdMiniParam = () => {
    const {
      reference = {},
      pickupPointInfo = {},
      returnPointInfo = {},
    } = ProductSelectors.getProductReq();
    const {
      bizVendorCode,
      decoratorVendorType,
      isEasyLife,
      packageId,
      pStoreCode,
      rStoreCode,
      vcExtendRequest,
      vehicleCode,
      packageType,
      payMode,
      bomCode,
      priceVersion,
      pCityId,
      rCityId,
    } = reference;
    // @ts-ignore

    // 拼接海外小程序旧版详情页入参
    const osdProuctparam = {
      request: {
        reference: {
          bizVendorCode,
          decoratorVendorType,
          isEasyLife,
          // eslint-disable-next-line no-bitwise
          packageId: ~~packageId,
          pStoreCode,
          rStoreCode,
          vcExtendRequest,
          vehicleCode,
          packageType,
          payMode,
          bomCode,
          priceVersion,
          pCityId,
          rCityId,
        },
        pickupPointInfo: {
          dateArgs: pickupPointInfo.date,
          cityId: pickupPointInfo.cityId,
          landMark: {
            locationId: pickupPointInfo.locationCode || '',
            locationType: pickupPointInfo.locationType || 1,
            locationName: pickupPointInfo.locationName || '',
          },
          radius: 0.0,
          poiInfo: {
            latitude: pickupPointInfo.poi.latitude,
            longitude: pickupPointInfo.poi.longitude,
            type: 0,
          },
        },
        returnPointInfo: {
          dateArgs: returnPointInfo.date,
          cityId: returnPointInfo.cityId,
          landMark: {
            locationId: returnPointInfo.locationCode || '',
            locationType: returnPointInfo.locationType || 1,
            locationName: returnPointInfo.locationName || '',
          },
          radius: 0.0,
          poiInfo: {
            latitude: returnPointInfo.poi.latitude,
            longitude: returnPointInfo.poi.longitude,
            type: 0,
          },
        },
      },
    };
    const osdMiniParam = encodeURIComponent(JSON.stringify(osdProuctparam));
    return osdMiniParam;
  };

  osdShare = async () => {
    const { titleConfig, imageUrl } = await this.getShareConfig();
    const { expandLocationAndDate } = this.props;
    const { reference = {} } = ProductSelectors.getProductReq();
    const { vcExtendRequest = {} } = reference;
    const osdH5Data = lodashMerge(
      {
        landingto: 'booking',
        fromurl: 'ser_com',
        easyLifeUpgradePackageId: 0,
      },
      expandLocationAndDate,
      lodashOmitBy(reference, lodashIsObject),
      vcExtendRequest,
    );
    const osdMiniParam = this.getOsdMiniParam();
    // todo-dyy: dev
    // linkUrl = `http://*************:9000/webapp/cw/car/hwzc/market.html?${linkUrl}`;
    const linkUrl = '/webapp/cw/car/hwzc/market.html?';
    const weixinFriendConfig: any = {
      shareType: CtripShareType.WeixinFriend,
      imageUrl,
      title: titleConfig.wechatmoment,
      linkUrl,
      miniProgramPath: `pages/isd/indexNew/index?landingto=osd&osdMiniParam=${osdMiniParam}`,
      miniProgramID: 'gh_5761189821f5',
    };

    const dataList = await this.getCtripShareConfig(linkUrl, osdH5Data);
    const WeixinFriendIndex = lodashFindIndex(dataList, {
      shareType: CtripShareType.WeixinFriend,
    });
    dataList[WeixinFriendIndex] = weixinFriendConfig;
    Share.customShare(
      {
        // @ts-ignore
        dataList,
        businessCode: 'car-osd',
      },
      status => {
        if (!!status && !!status.shareResult && status.shareResult === 1) {
          // 分享成功，返回携程的埋点
          try {
            CarLog.LogCode({
              name: '点击_详情页_分享成功',

              status,
              dataList,
            });
          } catch (error) {
            CarLog.LogCode({
              name: '点击_详情页_分享失败',

              error,
              dataList,
            });
          }
        }
      },
    );
  };

  getCRNShareType = shareType => ShareType[shareType];

  fetchIsdShareDataList = async () => {
    const { vehicleDetailListReq, shareVehicleInfo } = this.props;
    // 构造请求参数
    const basicParam = {
      shareTypes: [1],
      shareChannelId: '235489',
      shareVehicleInfo,
      content: JSON.stringify(vehicleDetailListReq),
    };
    const pcitype0Param = {
      picType: 0, // 0 图片 1 海报
      ...basicParam,
    };
    const pcitype1Param = {
      picType: 1, // 0 图片 1 海报
      ...basicParam,
    };
    // 发起请求
    let resErr = false;
    let toastMsg = '';
    const res = await Promise.all([
      CarFetch.queryShareInfo(pcitype0Param),
      CarFetch.queryShareInfo(pcitype1Param),
    ]).catch(() => {
      // 接口报错
      resErr = true;
      toastMsg = '分享失败';
    });

    if (resErr) {
      if (toastMsg) {
        xShowToast({ title: toastMsg, duration: 3000 });
      }
      return [];
    }

    // 接口响应成功
    const { shareInfo = [] } = res?.[0] || {};

    // 处理数据
    let dataList;
    if (!resErr && shareInfo.length) {
      const { miniprogramType } = AppContext.UrlQuery;
      dataList = [
        {
          shareType: 'WeixinFriend',
          imageUrl: shareInfo?.[0]?.picUrl,
          title: shareInfo?.[0]?.title,
          text: shareInfo?.[0]?.title,
          linkUrl:
            'https://m.ctrip.com/webapp/cw/car/isd/List.html?apptype=ISD_C_CW_MAIN',
          miniprogramType: miniprogramType || '0',
          miniProgramID: 'gh_5761189821f5',
          miniProgramPath: shareInfo?.[0]?.longUrl,
        },
      ];

      // 海报生成失败 则不展示分享海报入口
      if (res?.[1]?.shareInfo?.[0]?.picUrl) {
        dataList.push({
          shareType: 'CustomList',
          customType: '991',
          customTitle: '分享海报',
          customIcon: `${ImageUrl.DIMG04_PATH}1tg5c12000gv070xmDC8A.png`,
        });
        this.setState({
          posterUrl: res?.[1]?.shareInfo?.[0]?.picUrl,
        });
      }
    }
    return dataList;
  };

  getIsdShareDataList = async () => {
    const resIsdShareDataList = await this.fetchIsdShareDataList();
    return resIsdShareDataList;
  };

  isdShare = async () => {
    const { vehicleCode } = this.props;
    const dataList = await this.getIsdShareDataList();
    if (dataList?.length) {
      Share.customShare(
        {
          // @ts-ignore
          dataList,
        },
        result => {
          let toastMsg = '';
          const { shareType, shareResult } = result || {};
          if (shareResult === ShareResult.Success) {
            toastMsg = texts.shareSuccess;
          }
          if (
            shareResult === ShareResult.Failure ||
            shareResult === ShareResult.DataFormatErr ||
            shareResult === ShareResult.CannotBeRealized
          ) {
            toastMsg = texts.shareFailure;
          }
          if (shareResult === ShareResult.Canceled) {
            toastMsg = texts.shareCanceled;
          }
          if (toastMsg && shareType === 'WeixinFriend') {
            xShowToast({ title: toastMsg, duration: 3000 });
          }
          // 分享回调结果埋点
          CarLog.LogCode({
            name: '点击_产品详情页_分享_分享渠道回调结果',

            info: {
              shareType,
              shareResult,
            },
          });
        },
      );
    }
    // 点击分享入口埋点
    CarLog.LogCode({
      name: '点击_国内_产品详情页_分享按钮',

      info: { vehicleCode },
    });
    CarLog.LogTrace({
      key: LogKey.cm_car_app_click_airanking,
      info: {
        name: '点击_国内_产品详情页_分享按钮',
        newMergeId: AppContext.currentNewMergeId,
        vehicleId: vehicleCode,
        serverRequestId: getServerRequestId(),
        requestId: getListRequestId(),
        vid: AppContext.MarketInfo.vid,
      },
    });
  };

  goShare = async () => {
    // 避免多次点击
    if (!this.shareEnabled) {
      return;
    }
    try {
      this.shareEnabled = false;
      CarLog.LogCode({ name: '点击_详情页_分享' });

      Loading.showMaskLoading({
        cancelable: false,
      });

      if (Utils.isCtripIsd()) {
        await this.isdShare();
      } else if (Utils.isCtripOsd()) {
        await this.osdShare();
      }

      Loading.hideMaskLoading();

      this.shareEnabled = true;
    } catch (e) {
      this.shareEnabled = true;
    }
  };

  // 2022-9-8 根据220901_DSJT_gsyxh实验控制是否展示客服入口, 新版不展示，旧版展示
  isService = () => !Utils.isCtripIsd();

  goService = () => {
    const { toolBoxCustomerJumpUrl, vehicleCode } = this.props;
    Utils.openUrlWithTicket(toolBoxCustomerJumpUrl);
    CarLog.LogCode({
      name: '点击_详情页_头部_客服',

      url: toolBoxCustomerJumpUrl,
    });
    const listRes = ListReqAndResData.getData(
      ListReqAndResData.keyList.listProductRes,
    );
    // 发送出境AI正向反馈埋点
    const { reference = {} } = ProductSelectors.getProductReq();
    CarLog.LogTrace({
      key: LogKey.co_car_app_click_airanking,
      info: {
        name: '点击_详情页_头部_客服',
        vehicleKey: reference?.vehicleKey,
        vehicleId: reference?.vehicleCode,
        requestid: listRes?.baseResponse?.requestId,
        batchcode: listRes?.baseResponse?.code,
      },
    });
    // CO实时正反馈记录
    if (GetABCache.isAiSort2()) {
      setListStatusData(listStatusKeyList.aiUpFeedBack, {
        aiUpFeedBackAction: true,
        showRemoveCache: false,
      });
    }
  };

  setShowAnimation = isShow => {
    const { isShowAnimation } = this.state;
    if (isShowAnimation !== isShow) {
      this.setState({
        isShowAnimation: isShow,
      });
    }
  };

  render() {
    const {
      isFail,
      opacityAnimation,
      fixOpacityAnimation,
      isProductLoading,
      isHideShare,
      leftIconTestID,
      serviceTestID,
      isHideLinear,
    } = this.props;
    const { isShowHeaderBottom, isShowAnimation } = this.state;
    const toolBoxStyleType = !isFail && isShowHeaderBottom ? 1 : 5;
    const isShowIsdShare = Utils.isCtripIsd() && !isProductLoading;
    return (
      <XBoxShadow
        coordinate={{ x: 0, y: 2 }}
        color={
          toolBoxStyleType === 1
            ? setOpacity(color.black, 0.08)
            : setOpacity(color.black, 0)
        }
        opacity={1}
        blurRadius={4}
        elevation={toolBoxStyleType === 1 ? 4 : 0}
        className={classNames(
          c2xStyles.headerWrap,
          toolBoxStyleType === 1 && c2xStyles.headerShadow,
        )}
        testID={UITestID.car_testid_page_product_header_osd}
        onLayout={e => this.props.onHeaderLayout(e)}
      >
        <BbkCarHeader
          // @ts-ignore
          isShowProductName={true}
          opacityAnimation={opacityAnimation}
          fixOpacityAnimation={fixOpacityAnimation}
          isShowAnimation={isShowAnimation}
          isHideAnimation={!isFail}
          containderStyle={styles.containderStyle}
          toolBoxStyleType={toolBoxStyleType}
          isHideHeader={isShowHeaderBottom}
          isSmallImage={false}
          isShare={!isHideShare && isShowIsdShare}
          isHideRight={isFail}
          goShare={this.goShare}
          isService={this.isService()}
          goService={this.goService}
          configParams={headerConfigParams}
          leftIconTestID={leftIconTestID}
          serviceTestID={serviceTestID}
          isHideLinear={isHideLinear}
          {...this.getBbkCarHeaderProps()}
        />
      </XBoxShadow>
    );
  }
}
