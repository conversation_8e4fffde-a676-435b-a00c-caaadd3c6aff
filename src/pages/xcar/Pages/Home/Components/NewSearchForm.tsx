import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { Component } from 'react';
import { XBoxShadow } from '@ctrip/xtaro';
import { color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

import c2xStyles from './newSearchFormC2xStyles.module.scss';
import { composeCityArea2RentalLocation } from '../../../State/LocationAndDate/Mappers';
import {
  NewSearchPanel,
  ILocation,
} from '../../../ComponentBusiness/NewSearchPanel/index';
import { getLocalDayJs } from '../../../Components/Calendar/Method';
import { PageName, ImageUrl } from '../../../Constants/Index';
import {
  Utils,
  CarABTesting,
  AppContext,
  Channel,
  CarLog,
} from '../../../Util/Index';
import { saveIsdPickUpOnDoor } from '../Logic/Filter';
import { setFromPage } from '../../../Global/Cache/Index';
import { ItravelItems } from './ItineraryCard/Type';
import { Enums } from '../../../ComponentBusiness/Common/index';
import { CancelReasonCode } from '../../../Constants/OrderDetail';

const { cloneDeep, isIos, getPixel } = BbkUtils;
const styles = StyleSheet.create({
  searchPanel: {
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
});

interface INewSearchForm {
  ptime: Date;
  rtime: Date;
  pcity: ILocation;
  rcity: ILocation;
  rentalLocation: any;
  pickType: any;
  isShowDropOff: boolean;
  adultSelectNum: number;
  childSelectNum: number;
  age: string;
  filterItems: Array<any>;
  isLazyLoad: boolean;
  airPortTransferTip: { isShow: boolean };
  isShowItineraryCard: boolean;
  hasLocationPermission: boolean;
  selectedItineraryCard: ItravelItems;
  travelItems: ItravelItems[];
  searchBtnLottieJson: string;
  searchBtnBg: string;
  isPickupStation: boolean;
  isDropOffStation: boolean;
  searchButtonText?: string;
  searchBtnTextIcon?: string;
  searchBtnTextIconStyle?: any;
  searchBtnStyle?: any;
  isShowBusinessLicense?: boolean;
  isCrossCRNContainer?: boolean;
  isShowShadow?: boolean;
  isShowLocationBar?: boolean;
  fromurl?: string;
  style?: any;
  timeLineStyle?: any;
  timeWarning: string;
  insufficientTimeWarning: string;
  setDateInfo: (data: any) => void;
  setLocationInfo: (data: any) => void;
  setPickType: (data: any) => void;
  setFilterItems: (data: any) => void;
  preListPageFetch: (data) => void;
  showAirPortTransferTip: () => void;
  onPressPosition?: () => void;
  openList?: (homeSelectedFilters: any) => void;
  onPressSearch?: () => void;
  setAge: (data: { age: number }) => void;
  setAgeAdultAndChildNum: (data: any) => void;
  updateSelectedItineraryCard: (data: any) => void;
  fetchItineraryCardInfo: () => void;
  cancelOrderCode?: string;
  cancelOrder?: string;
  orderStatus?: string;
  orderId?: string;
}

interface SearchFormStateType {
  shadowHeight: number;
}

export default class NewSearchForm extends Component<
  INewSearchForm,
  SearchFormStateType
> {
  pageInstance: any;

  constructor(props) {
    super(props);
    this.state = {
      shadowHeight: 0,
    };
    this.pageInstance = AppContext.PageInstance;
    AppContext.setHomeSearchFormRef(this);
  }

  componentDidMount() {
    const delayTime = isIos ? 100 : 200;
    setTimeout(() => {
      this.lazyDidMount();
    }, delayTime);
  }

  lazyDidMount() {
    const { fetchItineraryCardInfo } = this.props;
    fetchItineraryCardInfo();
  }

  // 取车时间过期
  handleTimepassed = (ptime, rtime) => {
    const { setDateInfo } = this.props;
    const curMinutes = Math.ceil(parseInt(dayjs().format('mm'), 10) / 15) * 15;
    const timeDiff =
      Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
    const newRentalDate = {
      pickup: dayjs().add(4, 'hours').minute(curMinutes),
      dropoff: dayjs()
        .add(4, 'hours')
        .minute(curMinutes + timeDiff),
    };
    setDateInfo(newRentalDate);
    Toast?.show('取车时间已过当前时间，已为您修改取车时间', 2);
  };

  // 时间更新回调
  onTimeChange = data => {
    const { ptime } = data;
    const { rtime } = data;
    const { setDateInfo } = this.props;
    const newRentalDate = { pickup: ptime, dropoff: rtime };
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else {
      setDateInfo(newRentalDate);
    }
  };

  handleAreaPress = data => {
    const { setLocationInfo, pickType } = this.props;
    const rentalLocation = composeCityArea2RentalLocation(data);
    const locationInfo = {
      [pickType]: rentalLocation,
    };
    setTimeout(() => {
      setLocationInfo(locationInfo);
    });
  };

  // 异地还车按钮回调
  onIsShowDropOffChange = isShowDropOff => {
    const { rentalLocation, setLocationInfo } = this.props;
    setLocationInfo({
      isShowDropOff,
      dropOff: !isShowDropOff ? rentalLocation.pickUp : rentalLocation.dropOff,
      notDisPatchPreFetch: true,
    });
    CarLog.LogCode({
      name: '点击_首页_修改取还车信息_异地还车按钮',

      isShowDropOff,
    });
  };

  onPressCity = (isPickUp: boolean) => {
    CarLog.LogCode({
      name: isPickUp
        ? '点击_首页_修改取还车信息_修改取车城市信息'
        : '点击_首页_修改取还车信息_修改还车城市信息',
    });
  };

  onPressLocation = (isPickUp: boolean) => {
    CarLog.LogCode({
      name: isPickUp
        ? '点击_首页_修改取还车信息_修改取车地点信息'
        : '点击_首页_修改取还车信息_修改还车地点信息',
    });
  };

  // 点击取车时间
  onPressPickUpDate = () => {
    CarLog.LogCode({ name: '点击_首页_首页首屏查询框_取车时间' });
  };

  // 点击还车时间
  onPressDropOffDate = () => {
    CarLog.LogCode({ name: '点击_首页_首页首屏查询框_还车时间' });
  };

  // 点击租期
  onPressRentalTime = () => {
    if (Utils.isCtripIsd()) {
      CarLog.LogCode({ name: '点击_国内首页_首页首屏查询框_租期' });
    } else {
      CarLog.LogCode({ name: '点击_出境首页_首页首屏查询框_租期' });
    }
  };

  onPressFilter = item => {
    const { filterItems, setFilterItems, preListPageFetch } = this.props;
    const newFilterItems = cloneDeep(filterItems);
    const index = filterItems?.findIndex(m => m?.code === item.code);
    newFilterItems[index].isSelected = !item.isSelected;
    if (
      newFilterItems &&
      newFilterItems[index] &&
      Utils.isObject(newFilterItems[index])
    ) {
      newFilterItems[index].isSelected = !item?.isSelected;
    }

    setFilterItems({ filterItems: newFilterItems });
    saveIsdPickUpOnDoor(item.code, newFilterItems[index].isSelected);

    // 预请求列表页
    if (CarABTesting.isListInPage()) {
      const filters = [].concat(
        newFilterItems.filter(newItem => newItem.isSelected).map(v => v.code),
      );
      preListPageFetch({ filters });
    }
  };

  getHomeSelectedFilters = () => {
    const { filterItems } = this.props;
    return filterItems.filter(item => item.isSelected).map(v => v.code);
  };

  goList = () => {
    const { fromurl } = this.props;

    AppContext.setRouterListLoader({
      groupCode: '',
      filters: this.getHomeSelectedFilters(),
    });

    setFromPage(this.pageInstance.getPageId());
    this.pageInstance.push(Channel.getPageId().List.EN, {
      fromurl: fromurl || PageName.Home,
    });
  };

  onPressSearch = () => {
    const {
      ptime,
      rtime,
      isLazyLoad,
      isPickupStation,
      isDropOffStation,
      openList,
      airPortTransferTip,
      showAirPortTransferTip,
      adultSelectNum,
      childSelectNum,
      age,
      onPressSearch,
      cancelOrderCode,
      cancelOrder,
      orderStatus,
      orderId,
    } = this.props;
    let logName = '点击_首页_修改取还车信息_重新搜索';
    switch (Number(cancelOrderCode)) {
      case CancelReasonCode.TripCancellation:
        logName = '点击_订单详情_取消订单_行程取消_重新预订_查询';
        break;
      case CancelReasonCode.StoreNoCar:
        logName = '点击_订单详情_取消订单_门店告知无车_重新预订_查询';
        break;
      default:
        break;
    }
    CarLog.LogCode({
      name: logName,
      info: {
        ispickupStation: isPickupStation,
        isdropoffStation: isDropOffStation,
        adultsNumber: adultSelectNum,
        childrenNumber: childSelectNum,
        driverAge: age,
        cancelOrder,
        orderStatus,
        orderId,
      },
    });
    if (onPressSearch) {
      onPressSearch();
      return;
    }
    AppContext.setListCacheInterval({
      homeReady: new Date(),
      listCacheBuild: new Date(),
    });
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else if (airPortTransferTip?.isShow) {
      showAirPortTransferTip();
    } else if (openList) {
      openList(this.getHomeSelectedFilters());
    } else if (isLazyLoad) {
      this.goList();
    }
  };

  onAgeChange = data => {
    const { setAge } = this.props;
    setAge({ age: data });
    CarLog.LogCode({ name: '点击_首页_修改取还车信息_修改年龄_确认' });
  };

  onAgeCancel = () => {
    CarLog.LogCode({ name: '点击_首页_修改取还车信息_修改年龄_取消' });
  };

  onNumberConfirm = data => {
    const { setAgeAdultAndChildNum } = this.props;
    setAgeAdultAndChildNum(data);
    CarLog.LogCode({
      name: '点击_选择出行人数弹层_确定',

      info: {
        adultsNumber: data?.adultSelectNum,
        childrenNumber: data?.childSelectNum,
      },
    });
  };

  onNumberCancel = () => {
    CarLog.LogCode({ name: '点击_选择出行人数弹层_取消' });
  };

  onPressNumberSelect = () => {
    CarLog.LogCode({ name: '点击_首页_搜索框_人数' });
  };

  onPressAgeSelect = () => {
    CarLog.LogCode({ name: '点击_首页_修改取还车信息_修改年龄' });
  };

  onPressAgeTip = () => {
    CarLog.LogCode({ name: '点击_首页_打开年龄说明弹层' });
  };

  onAgeTipClose = () => {
    CarLog.LogCode({ name: '点击_首页_关闭年龄说明弹层' });
  };

  handleLayout = e => {
    const curHeight = e.nativeEvent.layout.height;
    this.setState({
      shadowHeight: curHeight - getPixel(16),
    });
  };

  render() {
    const {
      ptime,
      rtime,
      pcity,
      rcity,
      isShowDropOff,
      adultSelectNum,
      childSelectNum,
      age,
      timeWarning,
      insufficientTimeWarning,
      filterItems,
      hasLocationPermission,
      isShowItineraryCard,
      selectedItineraryCard,
      travelItems,
      searchBtnLottieJson,
      searchBtnBg,
      onPressPosition,
      updateSelectedItineraryCard,
      setDateInfo,
      setLocationInfo,
      setPickType,
      isShowBusinessLicense = Utils.isCtripIsd(),
      isShowLocationBar = Utils.isCtripIsd(),
      isCrossCRNContainer = true,
      isShowShadow = true,
      searchButtonText,
      searchBtnTextIcon,
      searchBtnTextIconStyle,
      style,
      timeLineStyle,
      searchBtnStyle,
    } = this.props;
    const { shadowHeight } = this.state;
    return (
      <XBoxShadow
        className={isIos ? c2xStyles.formWrap : c2xStyles.androidFormWrapper}
        coordinate={{ x: 0, y: getPixel(8) }}
        color={
          isIos
            ? setOpacity(color.darkGrayBorder, 0.08)
            : setOpacity(color.darkGrayBorder, 0)
        }
        opacity={1}
        blurRadius={isIos ? 20 : 0}
        elevation={isIos ? 12 : 0}
        style={style}
        onLayout={this.handleLayout}
      >
        <NewSearchPanel
          style={!isIos && styles.searchPanel}
          ptime={ptime}
          rtime={rtime}
          pcity={pcity}
          rcity={rcity}
          showDropoff={isShowDropOff}
          adultSelectNum={adultSelectNum}
          childSelectNum={childSelectNum}
          age={age}
          searchButtonText={searchButtonText}
          searchBtnTextIcon={searchBtnTextIcon}
          searchBtnTextIconStyle={searchBtnTextIconStyle}
          searchBtnStyle={searchBtnStyle}
          timeLineStyle={timeLineStyle}
          filterItems={filterItems}
          isShowLocationBar={isShowLocationBar}
          hasLocationPermission={hasLocationPermission}
          searchBtnLottieJson={searchBtnLottieJson}
          searchBtnBg={searchBtnBg}
          isShowItineraryCard={isShowItineraryCard}
          selectedItineraryCard={selectedItineraryCard}
          travelItems={travelItems}
          searchPanelButtonType={Enums.SearchPanelButtonEnum.search}
          isCrossCRNContainer={isCrossCRNContainer}
          timeWarning={timeWarning}
          insufficientTimeWarning={insufficientTimeWarning}
          isShowAdvantage={Utils.isCtripOsd()}
          onTimeChange={this.onTimeChange}
          onIsShowDropOffChange={this.onIsShowDropOffChange}
          onPressCity={this.onPressCity}
          onPressLocation={this.onPressLocation}
          onPressFilter={this.onPressFilter}
          onPressSearch={this.onPressSearch}
          onPressPosition={onPressPosition}
          onAgeChange={this.onAgeChange}
          onAgeCancel={this.onAgeCancel}
          onNumberConfirm={this.onNumberConfirm}
          onNumberCancel={this.onNumberCancel}
          updateSelectedItineraryCard={updateSelectedItineraryCard}
          setDateInfo={setDateInfo}
          setLocationInfo={setLocationInfo}
          onPressPickUpDate={this.onPressPickUpDate}
          onPressDropOffDate={this.onPressDropOffDate}
          onPressRentalTime={this.onPressRentalTime}
          onPressNumberSelect={this.onPressNumberSelect}
          onPressAgeSelect={this.onPressAgeSelect}
          onPressAgeTip={this.onPressAgeTip}
          onAgeTipClose={this.onAgeTipClose}
          setPickType={setPickType}
          handleAreaPress={this.handleAreaPress}
          isShowBusinessLicense={isShowBusinessLicense}
        />

        {!isIos && isShowShadow && (
          <>
            <Image
              className={c2xStyles.leftImg}
              style={{ height: shadowHeight }}
              src={`${ImageUrl.componentImagePath}Home/homeShadowLeft.png`}
            />

            <Image
              className={c2xStyles.rightImg}
              style={{ height: shadowHeight }}
              src={`${ImageUrl.componentImagePath}Home/homeShadowRight.png`}
            />

            <Image
              className={c2xStyles.bottomImg}
              src={`${ImageUrl.componentImagePath}Home/homeShadowBottom.png`}
            />
          </>
        )}
      </XBoxShadow>
    );
  }
}
