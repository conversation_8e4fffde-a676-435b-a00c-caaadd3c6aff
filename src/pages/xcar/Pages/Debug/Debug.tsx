import Clipboard from '@c2x/apis/Clipboard';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import TouchableOpacity from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import ViewPort from '@c2x/components/ViewPort';
import { IBasePageProps } from '@c2x/components/Page';
import Device from '@c2x/apis/Device';
import React from 'react';
import Trace from '@ctrip/bbk-trace';
import { XView as View, xShowToast } from '@ctrip/xtaro';

import HeaderView from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import withForwardPage from '../../withForwardPage';
import { AppContext, Channel, Utils, CarStorage } from '../../Util/Index';
import { StorageKey } from '../../Constants/Index';

import CPage, { IStateType } from '../../Components/App/CPage';

const MainTrace = [
  {
    apiName: '详情页接口',
    apiUrl: Utils.isCtripIsd() ? 'queryVehicleDetailList' : 'queryProductInfo',
  },
  { apiName: '填写页下单接口', apiUrl: 'createOrder' },
  { apiName: '订单详情页接口', apiUrl: Utils.isCtripIsd() ? 'queryMainOrder' : 'OSDQueryOrder' },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingLeft: 5,
    paddingRight: 5,
  },
  copyText: {
    flex: 1,
    padding: 10,
    marginTop: 10,
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: 5,
    borderColor: color.grayBase,
    ...layout.flexRow,
  },
});

interface PropsType extends IBasePageProps {}

interface StateType extends IStateType {}

const CopyText = ({ title, text }: any) => {
  const copyText = () => {
    Clipboard.setString(text);
    xShowToast({ title: '已复制', duration: 3000 });
  };
  return (
    <View style={styles.copyText}>
      <Text style={font.F_26_10_medium} fontWeight="medium">
        {title}{' '}
      </Text>
      <TouchableOpacity onLongPress={copyText}>
        <Text style={{ color: color.deepBlueBase }}>{text}</Text>
      </TouchableOpacity>
    </View>
  );
};
class Debug extends CPage<PropsType, StateType> {
  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Debug.ID;
  }

  componentDidMount() {
    // 出境浏览记录清除
    CarStorage.remove(StorageKey.CAR_LIST_BROWSING);
  }

  render() {
    return (
      <ViewPort>
        <HeaderView title={this.getPageId()} onPressLeft={this.pop} />
        <ScrollView style={styles.container}>
          <CopyText title="MCD版本名称" text={AppContext.CarEnv.buildTime} />
          <CopyText title="CID" text={Device?.deviceInfo?.clientID} />
          <CopyText title="UID" text={AppContext.UserInfo.userId} />
          <Trace mainTrace={MainTrace} allPageList={Channel.getPageId()} />
        </ScrollView>
      </ViewPort>
    );
  }
}

export default withForwardPage(Debug);
