/* eslint-disable react/jsx-props-no-spreading */
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import Page, { IBasePageProps } from '@c2x/components/Page';
import ViewPort from '@c2x/components/ViewPort';
import Event from '@c2x/apis/Event';
import React from 'react';
import { XView as View, XBoxShadow } from '@ctrip/xtaro';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './rebookHome.module.scss';
import CPage, { IStateType } from '../../Components/App/CPage';
import { MarketingFooter } from '../Home/Components/Index';
import * as homeConfig from '../Home/Logic/config';
import SearchForm from '../../Containers/HomeNewSearchFormContainer';
import { EventName, PageName, UITestID } from '../../Constants/Index';
import Tip from '../ModifyOrderConfirm/Components/Tip';
import { Utils, EventHelper, GetAB } from '../../Util/Index';
import { setRegisterPageData } from '../../Global/Cache/ListReqAndResData';
import Channel from '../../Util/Channel';
import Texts from './Texts';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
  headerStyle: {
    backgroundColor: color.white,
  },
  titleStyle: {
    ...font.title4MediumStyle,
  },
  tipWrap: {
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    backgroundColor: color.orangeBg,
    borderTopColor: color.fontSubLight,
  },
  searchForm: { marginLeft: 0, marginRight: 0, marginTop: getPixel(-18) },
});

interface IRebookHomePropsType extends IBasePageProps {
  setLocationInfo: (data: any) => void;
  urlQuery?: any;
}

interface RebookHomeStateType extends IStateType {
  forceUpdateForm: Date;
}

export default class RebookHome extends CPage<
  IRebookHomePropsType,
  RebookHomeStateType
> {
  // pageName 当前页面的名称，用于选择区域的回退操作
  pageName?: string;

  constructor(props) {
    super(props);
    GetAB.isISDListIpoll();
    GetAB.isOSDListIpoll();
    GetAB.isSuperSubsidy();
    GetAB.isISDListCard();
    this.state = {
      forceUpdateForm: new Date(),
    };
    this.pageName = this.createPageName(PageName.RebookHome);
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().RebookHome.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    this.registerPage();
    this.registerEvents();
  }

  pageDidAppear() {
    super.pageDidAppear();
    this.registerPage();
    this.registerEvents();
  }

  onBackPress = () => {
    this.pop();
  };

  getConfig = () => {
    const config = homeConfig;
    const { marktingFooterData: footDataAlias } = config;
    const marktingFooterData = footDataAlias.isd;
    return { marktingFooterData };
  };

  registerPage() {
    Page.registerPage(this.pageName, () => {});
    setRegisterPageData(Channel.getPageId().RebookHome.EN, this.pageName);
  }

  registerEvents() {
    EventHelper.addEventListener(EventName.changeRentalISDLocation, data => {
      if (Utils.isCtripIsd()) {
        this.props.setLocationInfo({
          ...data,
          fromEvent: 'changeRentalLocation',
        });
      }
    });
  }

  removeEvents() {
    Event.removeEventListener(EventName.changeRentalISDLocation);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.removeEvents();
  }

  renderPage() {
    const { marktingFooterData } = this.getConfig();
    const { urlQuery } = this.props;
    const { cancelCode, cancelOrder, orderStatus, ctripOrderId } =
      urlQuery || {};
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.page}>
        <View className={c2xStyles.wrap}>
          <BbkHeader
            onPressLeft={this.onBackPress}
            title={Texts.header}
            titleStyle={styles.titleStyle}
            style={styles.headerStyle}
            contentStyle={layout.flexCenter}
            leftIconTestID={UITestID.car_testid_page_rebookhome_header_lefticon}
            isBottomBorder={false}
          />

          <ScrollView bounces={false}>
            <Tip content={Texts.tip} wrapStyle={styles.tipWrap} />
            <XBoxShadow
              className={c2xStyles.searchFormContainer}
              coordinate={{ x: 0, y: getPixel(6) }}
              color="rgba(0, 0, 0, 0.11)"
              opacity={1}
              blurRadius={getPixel(20)}
              elevation={20}
            >
              <View className={c2xStyles.searchFormHeader} />
              <SearchForm
                isShowLocationBar={false}
                isCrossCRNContainer={false}
                fromurl={PageName.RebookHome}
                hasLocationPermission={true}
                isShowBusinessLicense={false}
                isLazyLoad={true}
                style={styles.searchForm}
                cancelOrderCode={cancelCode}
                cancelOrder={cancelOrder}
                orderStatus={orderStatus}
                orderId={ctripOrderId}
              />
            </XBoxShadow>
            <MarketingFooter {...marktingFooterData} />
          </ScrollView>
        </View>
      </ViewPort>
    );
  }
}
