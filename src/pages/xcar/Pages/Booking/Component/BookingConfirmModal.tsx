import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useMemo } from 'react';
import {
  XLinearGradient as LinearGradient,
  XView as View,
  xMergeStyles,
  XViewExposure,
} from '@ctrip/xtaro';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Modal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import KeywordHighlightText from '@ctrip/rn_com_car/dist/src/Components/Basic/KeywordHighlightText';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import color from '@ctrip/rn_com_car/dist/src/Tokens/tokens/color';
import c2xStyles from './bookingConfirmModalC2xStyles.module.scss';
import Texts from '../Texts';
import { UITestID } from '../../../Constants/Index';
import { CarLog, GetAB } from '../../../Util/Index';

const { getPixel, autoProtocol, useMemoizedFn, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  title: {
    ...font.title4MediumStyle,
    color: color.fontPrimary,
  },
  sub: {
    ...font.title3LightStyle,
    color: color.fontPrimary,
    marginTop: getPixel(16),
    marginBottom: getPixel(48),
  },
  highLightTitle: {
    textDecorationLine: 'none',
    ...font.title4MediumStyle,
  },
  highLightSub: {
    textDecorationLine: 'none',
    ...font.title3MediumStyle,
  },
  btnCancel: {
    height: getPixel(72),
    lineHeight: getLineHeight(72),
    borderRadius: getPixel(8),
    backgroundColor: color.white,
    borderWidth: getPixel(1),
    borderColor: color.orangePrice,
    flex: 1,
    ...layout.flexCenter,
  },
  btnCancelNew: {
    height: getPixel(72),
    lineHeight: getLineHeight(72),
    borderRadius: getPixel(8),
    backgroundColor: color.white,
    borderWidth: getPixel(1),
    borderColor: color.deepBlueBase,
    flex: 1,
    ...layout.flexCenter,
    paddingLeft: getPixel(0),
    paddingRight: getPixel(0),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
  },
  mr24: {
    marginRight: getPixel(24),
  },
  btnCancelTxt: {
    ...font.subTitle1RegularStyle,
    color: color.orangePrice,
  },
  btnCancelTxtNew: {
    ...font.subTitle1RegularStyle,
    color: color.deepBlueBase,
  },
});

interface IBookingConfirmModal {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  data: any;
}

const getComposedData = data => {
  const text = data?.stringObjs?.map(m => m?.content)?.join('');
  const textHighLight = data?.stringObjs?.find(m => m?.style)?.content;
  const textHighLightColor = data?.stringObjs?.find(m => m?.style)?.style;
  return {
    text,
    textHighLight,
    textHighLightColor,
  };
};

const colors = [color.payButtonGradientOrange, color.orangePrice];
const deepBlueColors = [color.deepBlueBase, color.deepBlueBase];
const BookingConfirmModal: React.FC<IBookingConfirmModal> = memo(
  ({ visible, onConfirm, onCancel, data }) => {
    const { title, sub } = useMemo(() => {
      if (data) {
        return {
          title: getComposedData(data.titleObject),
          sub: getComposedData(data.subTitleObject),
        };
      }
      return {};
    }, [data]);
    const onPressConfirm = useMemoizedFn(() => {
      onConfirm();
      CarLog.LogCode({
        name: '点击_填写页_挽留弹窗',

        info: {
          retentionType: data.subType,
          operateContent: Texts.continueBook,
        },
      });
    });
    const onPressCancel = useMemoizedFn(() => {
      onCancel();
      CarLog.LogCode({
        name: '点击_填写页_挽留弹窗',

        info: {
          retentionType: data.subType,
          operateContent: Texts.cancelBook,
        },
      });
    });
    if (!data) return null;
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    return (
      <Modal
        closeModalBtnTestID={
          UITestID.car_testid_page_booking_confirm_modal_closemask
        }
        modalVisible={visible}
        useModal={false}
        location="center"
      >
        <XViewExposure
          testID={CarLog.LogExposure({
            name: '曝光_填写页_挽留弹窗',

            info: {
              retentionType: data.subType,
            },
          })}
          className={c2xStyles.container}
        >
          <Image
            src={autoProtocol(data.backGroundUrl)}
            className={c2xStyles.image}
          />

          <View className={c2xStyles.content}>
            <View style={layout.verticalItem}>
              <KeywordHighlightText
                keywords={title.textHighLight}
                text={title.text}
                highLightStyle={{
                  ...styles.highLightTitle,
                  color: title.textHighLightColor,
                }}
                textStyle={styles.title}
              />

              <KeywordHighlightText
                text={sub.text}
                keywords={sub.textHighLight}
                highLightStyle={{
                  ...styles.highLightSub,
                  color: sub.textHighLightColor,
                }}
                textStyle={styles.sub}
              />
            </View>
            <View style={layout.betweenHorizontal}>
              <View style={xMergeStyles([layout.flex1, styles.mr24])}>
                <Button
                  text={Texts.cancelBook}
                  buttonStyle={
                    isISDInterestPoints ? styles.btnCancelNew : styles.btnCancel
                  }
                  textStyle={
                    isISDInterestPoints
                      ? styles.btnCancelTxtNew
                      : styles.btnCancelTxt
                  }
                  testID={UITestID.car_testid_page_booking_confirm_modal_cancel}
                  onPress={onPressCancel}
                />
              </View>

              <Touchable
                debounce={true}
                onPress={onPressConfirm}
                testID={UITestID.car_testid_page_booking_confirm_modal_confirm}
                style={layout.flex1}
              >
                <LinearGradient
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 1.0 }}
                  locations={[0, 1]}
                  colors={isISDInterestPoints ? deepBlueColors : colors}
                  className={c2xStyles.btnContinue}
                >
                  <Text className={c2xStyles.btnContinueTxt}>
                    {Texts.continueBook}
                  </Text>
                </LinearGradient>
              </Touchable>
            </View>
          </View>
        </XViewExposure>
      </Modal>
    );
  },
);

export default BookingConfirmModal;
