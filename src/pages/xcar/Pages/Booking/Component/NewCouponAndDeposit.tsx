import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  xClassNames as classNames,
  XView as View,
  XImageBackground as ImageBackground,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, useMemo } from 'react';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  layout,
  font,
  icon,
  setOpacity,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './newCouponAndDepositC2xStyles.module.scss';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import Texts from '../Texts';
import {
  CouponListType,
  ActivityDetailType,
  DepositPayInfosType,
  TrackInfoType,
} from '../../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import DepositBox, {
  IDepositInfo,
} from '../../../ComponentBusiness/DepositBox';
import { Enums } from '../../../ComponentBusiness/Common';
import SkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import { Utils } from '../../../Util/Index';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  mainWrap: {
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    borderRadius: getPixel(16),
    backgroundColor: color.white,
    marginTop: getPixel(24),
    borderWidth: getPixel(2),
    borderColor: color.white,
    overflow: 'hidden',
  },
  couponBgImg: { width: getPixel(718), height: getPixel(100) },
  mergeTitleText: {
    color: color.recommendBg,
    marginLeft: getPixel(32),
    marginBottom: getPixel(24),
  },
  ml90: { marginLeft: getPixel(90) },
  disableText: { color: color.darkGrayBorder },
  priceStyle: { ...font.body4Style, color: color.recommendBg },
  depositBox: { padding: 0, backgroundColor: color.transparent },
  depositBoxTable: {
    backgroundColor: color.couponMergeBg,
    borderRadius: getPixel(8),
  },
  textDecorationLine: { textDecorationLine: 'line-through' },
  depositWrap: {
    paddingTop: getPixel(20),
    paddingBottom: getPixel(16),
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.couponSplit,
  },
  loadingBg: {
    backgroundColor: color.white,
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    marginTop: getPixel(24),
    borderRadius: getPixel(16),
    overflow: 'hidden',
  },
  pb8: { paddingBottom: getPixel(8) },
  mb16: { marginBottom: getPixel(16) },
  bookingOptimizationMainWrap: {
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
  },
  bookingOptimizationTitleText: {
    ...font.title4BoldStyle,
  },
});

interface PropsType extends IDepositType {
  currency: string;
  couponList: CouponListType;
  activityDetail: ActivityDetailType;
  couponTip: string;
  couponTestID: string;
  activityTestID: string;
  isMergeDeposit: boolean;

  onPressCoupon: () => void;
  onPressActivity: () => void;
  isShowLoading?: boolean;
  isZero?: boolean;
}

interface ICheckBoxWithImg {
  depositPayType: number;
  isSelected: boolean;
  onPress: (depositPayType: number, checked: boolean) => void;
}

const CheckBoxWithImg: React.FC<ICheckBoxWithImg> = ({
  depositPayType,
  isSelected,
  onPress,
}) => {
  const [isCheck, setIsCheck] = useState(isSelected);
  const switchCheckStatus = useMemoizedFn(() => {
    const checked = !isCheck;
    setIsCheck(checked);
    onPress(depositPayType, checked);
  });
  return (
    <BbkTouchable
      testID={UITestID.car_testid_page_booking_deposit_check_give_up}
      debounce={true}
      onPress={switchCheckStatus}
      style={layout.flexRow}
    >
      <Image
        src={
          isCheck
            ? `${ImageUrl.componentImagePath}CouponAndDeposit/select.png`
            : `${ImageUrl.componentImagePath}CouponAndDeposit/unSelect.png`
        }
        mode="aspectFill"
        className={c2xStyles.checkImg}
      />

      <BbkText className={c2xStyles.checkText}>
        {Texts.giveUpFreeDeposit}
      </BbkText>
    </BbkTouchable>
  );
};

interface IDepositType {
  trackInfo: TrackInfoType;
  depositTitle: string;
  depositDescs: Array<{ stringObjs: Array<{ content: string }> }>;
  opeButtonInfo: DepositPayInfosType;
  depositInfo: IDepositInfo;
  fundStringObjs: Array<{ content: string; url?: string }>; // 补足资金提示
  selectPassengerObj: { content: string }; // 选择驾驶员提示
  depositRuleObj: Array<{ content: string; url?: string }>; // 减免规则
  isZero?: boolean; // 是否是0元订单
  changePayMode: (data: any) => void;
  showEhiFreeDepositModalVisible: () => void;
  toSelectPassenger: () => void;
}

const Deposit: React.FC<IDepositType> = ({
  trackInfo,
  depositTitle,
  opeButtonInfo,
  depositInfo,
  fundStringObjs,
  selectPassengerObj,
  depositRuleObj,
  depositDescs,
  isZero,
  changePayMode,
  showEhiFreeDepositModalVisible,
  toSelectPassenger,
}) => {
  const changeDepositPayType = useMemoizedFn((depositPayType, checked) => {
    const newDepositPayType = checked ? depositPayType : 0;
    changePayMode({
      depositPayType: newDepositPayType,
      nextDepositPayType: newDepositPayType,
    });
  });

  let isMerge = false;
  // 是否为程信分T，和程信分N+芝麻通过
  const isShowCredit =
    trackInfo?.riskFinal === Enums.HomeUserCreditStatus.T ||
    (trackInfo?.riskFinal === Enums.HomeUserCreditStatus.N &&
      trackInfo?.zhimaResult === '1');
  // 且没有选择放弃免押 且不是0元订单
  isMerge = !isZero && isShowCredit && !opeButtonInfo?.isCheck;
  return (
    <View style={styles.depositWrap}>
      <View style={layout.rowStart}>
        <View className={c2xStyles.depositTitleWrap}>
          <Image
            src={
              isShowCredit
                ? `${ImageUrl.componentImagePath}CouponAndDeposit/creditRentLogo.png`
                : `${ImageUrl.componentImagePath}CouponAndDeposit/sesameLogo.png`
            }
            className={c2xStyles.logoImg}
          />

          <BbkText className={c2xStyles.itemTitle}>
            {Texts.freeDepositTitle}
          </BbkText>
        </View>

        <View style={layout.flex1}>
          {!!depositTitle && (
            <View className={c2xStyles.depositSubTitleWrap}>
              <View testID={UITestID.car_testid_page_booking_deposit_title}>
                <BbkText className={c2xStyles.depositTitle}>
                  {depositTitle}
                </BbkText>
              </View>
              {!!opeButtonInfo && (
                <CheckBoxWithImg
                  isSelected={opeButtonInfo.isCheck}
                  depositPayType={opeButtonInfo.depositPayType}
                  onPress={changeDepositPayType}
                />
              )}
            </View>
          )}
          {!!isMerge && (
            <View style={layout.flexRow}>
              {depositInfo.items.map((item, index) => {
                return (
                  <>
                    {index !== 0 && <View className={c2xStyles.splitLine} />}
                    <BbkText>{item.title}</BbkText>
                    <BbkCurrencyFormatter
                      currency={item.currencyCode}
                      price={item.currentTotalPrice}
                      currencyStyle={xMergeStyles([
                        item.showFree && styles.textDecorationLine,
                      ])}
                      priceStyle={xMergeStyles([
                        item.showFree && styles.textDecorationLine,
                      ])}
                    />

                    <BbkText>{Texts.showFree}</BbkText>
                  </>
                );
              })}
            </View>
          )}
          {fundStringObjs?.length > 0 && (
            <BbkText className={c2xStyles.mt8}>
              {fundStringObjs.map((item, index) => (
                <>
                  <BbkText>{item?.content}</BbkText>
                  {!!item.url && (
                    <BbkTouchable
                      testID={`${UITestID.car_testid_page_booking_deposit_help}_${index}`}
                      onPress={showEhiFreeDepositModalVisible}
                    >
                      <BbkText type="icon" className={c2xStyles.helpIcon}>
                        {icon.help}
                      </BbkText>
                    </BbkTouchable>
                  )}
                </>
              ))}
            </BbkText>
          )}
          {!!selectPassengerObj?.content && (
            <BbkTouchable
              testID={UITestID.car_testid_page_booking_deposit_select_driver}
              onPress={toSelectPassenger}
              className={c2xStyles.selectPassengerWrap}
            >
              <BbkText className={c2xStyles.selectPassengerText}>
                {Texts.selectPassengerTitle}
              </BbkText>
              <BbkText type="icon" className={c2xStyles.rightIcon}>
                {icon.arrowRight}
              </BbkText>
            </BbkTouchable>
          )}
        </View>
      </View>

      {depositRuleObj?.length > 0 && (
        <BbkText className={c2xStyles.depositDescWrap}>
          {depositRuleObj.map(item => (
            <BbkText
              key={item?.content}
              disabled={!item?.url}
              onPress={showEhiFreeDepositModalVisible}
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,
                item?.url && c2xStyles.depositRuleText,
              )}
            >
              {item?.content}
            </BbkText>
          ))}
        </BbkText>
      )}

      {depositDescs?.length > 0 && (
        <View
          testID={UITestID.car_testid_page_booking_deposit_subtitle}
          className={c2xStyles.depositDescWrap}
        >
          {depositDescs?.map(descItem => {
            return <BbkText>{descItem?.stringObjs?.[0]?.content}</BbkText>;
          })}
        </View>
      )}

      {!isMerge && !!depositInfo && !!Object.keys(depositInfo).length && (
        <View className={c2xStyles.depositBoxWrap}>
          <DepositBox
            depositInfo={depositInfo}
            style={styles.depositBox}
            tableStyle={styles.depositBoxTable}
            isIsd={true}
            isDepositAtOrder={true}
            richNoticePress={showEhiFreeDepositModalVisible}
          />
        </View>
      )}
    </View>
  );
};

interface ICouponType {
  currency: string;
  couponList: CouponListType;
  couponTestID: string;
  onPressCoupon: () => void;
}

const Coupon: React.FC<ICouponType> = ({
  couponTestID,
  currency,
  couponList,
  onPressCoupon,
}) => {
  const hasSelectedCoupon = couponList?.selectedCoupon?.couponName;
  const couponTitle =
    couponList?.selectedCoupon?.couponName || couponList?.title;
  const hasCoupon = couponList?.usableCoupons?.length > 0;
  return (
    <View testID={couponTestID} className={c2xStyles.contentItemWrap}>
      <View className={c2xStyles.detailTitleWrap}>
        <BbkText className={c2xStyles.detailTitle}>
          {Texts.couponPromoCode}
        </BbkText>
      </View>
      <BbkTouchable
        onPress={onPressCoupon}
        testID={UITestID.car_testid_comp_booking_NewCouponAndDeposit_coupon}
        style={xMergeStyles([layout.betweenHorizontal, layout.flex1])}
      >
        {!!couponTitle && (
          <BbkText
            className={classNames(c2xStyles.detailText, c2xStyles.detailName)}
            style={!hasCoupon && styles.disableText}
            numberOfLines={1}
          >
            {couponTitle}
          </BbkText>
        )}

        <View style={layout.flexRow}>
          {hasSelectedCoupon && (
            <>
              <BbkText className={c2xStyles.priceStyle}>-</BbkText>
              <BbkCurrencyFormatter
                currency={currency}
                price={couponList?.selectedCoupon?.deductionAmount}
                currencyStyle={styles.priceStyle}
                priceStyle={styles.priceStyle}
              />
            </>
          )}
          <BbkText type="icon" className={c2xStyles.rightIcon}>
            {icon.arrowRight}
          </BbkText>
        </View>
      </BbkTouchable>
    </View>
  );
};

interface IPromotionItemType {
  disableActivityText?: string;
  activityTitle?: string;
  description?: string;
  deductionAmount?: number;
  currency?: string;
  code?: string;
  onPressActivity: (code: string) => void;
}
const PromotionItem: React.FC<IPromotionItemType> = ({
  disableActivityText,
  activityTitle,
  description,
  deductionAmount,
  currency,
  onPressActivity,
  code,
}) => {
  return (
    <View className={c2xStyles.contentItemWrap}>
      <View className={c2xStyles.detailTitleWrap}>
        <BbkText className={c2xStyles.detailTitle}>
          {Texts.couponActivity}
        </BbkText>
      </View>
      <BbkTouchable
        testID={UITestID.car_testid_page_booking_discount_activity}
        onPress={() => {
          onPressActivity(code);
        }}
        disabled={!!disableActivityText}
        style={xMergeStyles([layout.betweenStart, layout.flex1])}
      >
        <View style={layout.flexRow}>
          {!!activityTitle && (
            <BbkText className={c2xStyles.detailText}>{activityTitle}</BbkText>
          )}
          {!!description && (
            <BbkText type="icon" className={c2xStyles.circleQuestionIcon}>
              {icon.circleQuestion}
            </BbkText>
          )}
          {!!disableActivityText && (
            <BbkText className={c2xStyles.detailText}>
              {disableActivityText}
            </BbkText>
          )}
        </View>
        {!!deductionAmount && (
          <View style={layout.flexRow}>
            <BbkText className={c2xStyles.priceStyle}>-</BbkText>
            <BbkCurrencyFormatter
              currency={currency}
              price={deductionAmount}
              currencyStyle={styles.priceStyle}
              priceStyle={styles.priceStyle}
            />
          </View>
        )}
      </BbkTouchable>
    </View>
  );
};
interface IActivityType {
  currency: string;
  activityDetail: ActivityDetailType;
  activityTestID: string;
  onPressActivity: (code: string) => void;
}
const Activity: React.FC<IActivityType> = ({
  activityTestID,
  activityDetail,
  currency,
  onPressActivity,
}) => {
  const { promotions, status } = activityDetail || {};

  const activities = useMemo(() => {
    if (status === 0 || !promotions?.length) {
      return [
        {
          activityTitle: '',
          disableActivityText: Texts.activityDisableMessage,
          deductionAmount: 0,
          code: '',
          description: '',
        },
      ];
    }
    return promotions.map(item => ({
      activityTitle: item.title,
      ...item,
    }));
  }, [promotions, status]);
  return (
    <View testID={activityTestID}>
      {activities.map(item => (
        <PromotionItem
          key={item.code}
          activityTitle={item.activityTitle}
          description={item.description}
          deductionAmount={item.deductionAmount}
          code={item.code}
          currency={currency}
          onPressActivity={onPressActivity}
        />
      ))}
    </View>
  );
};
const NewCouponAndDeposit = ({
  currency,
  couponList,
  activityDetail,
  couponTestID,
  activityTestID,
  couponTip,
  isMergeDeposit,
  depositTitle,
  depositInfo,
  depositDescs,
  opeButtonInfo,
  trackInfo,
  fundStringObjs,
  selectPassengerObj,
  depositRuleObj,
  isZero,
  onPressCoupon,
  onPressActivity,
  changePayMode,
  showEhiFreeDepositModalVisible,
  toSelectPassenger,
  isShowLoading,
}: PropsType) => {
  const [showExtra, setExtra] = useState(false);

  const onToggleExtra = useMemoizedFn(() => {
    setExtra(x => !x);
  });

  if (Utils.isCtripIsd() && isShowLoading) {
    return (
      <SkeletonLoading
        visible={true}
        style={styles.loadingBg}
        pageName={PageType.BookingCoupon}
      />
    );
  }

  return (
    <ImageBackground
      imageStyle={styles.couponBgImg}
      style={xMergeStyles([
        styles.mainWrap,
        Utils.isCtripIsd() && styles.bookingOptimizationMainWrap,
        isMergeDeposit && styles.pb8,
      ])}
      source={{
        uri: isMergeDeposit
          ? `${ImageUrl.componentImagePath}CouponAndDeposit/bg.png`
          : `${ImageUrl.componentImagePath}CouponAndDeposit/bg3.png`,
      }}
      resizeMode="contain"
    >
      {isMergeDeposit && (
        <BbkText
          className={c2xStyles.titleText}
          style={xMergeStyles([
            isMergeDeposit && styles.mergeTitleText,
            Utils.isCtripIsd() && styles.bookingOptimizationTitleText,
          ])}
        >
          {Texts.couponAndDepositTitle}
        </BbkText>
      )}

      {/** 优惠模块 */}
      <View testID={UITestID.car_testid_comp_booking_NewCouponAndDeposit}>
        <BbkTouchable
          activeOpacity={1}
          testID={UITestID.car_testid_page_booking_discount_toggle}
          onPress={onToggleExtra}
          className={c2xStyles.couponWrap}
          style={isMergeDeposit && styles.mb16}
        >
          {!isMergeDeposit && (
            <BbkText
              className={classNames(c2xStyles.titleText, c2xStyles.mr28)}
            >
              {Texts.preferentialTitle}
            </BbkText>
          )}

          {isMergeDeposit && (
            <>
              <Image
                src={`${ImageUrl.componentImagePath}CouponAndDeposit/couponLogo.png`}
                className={c2xStyles.logoImg}
              />

              <BbkText className={c2xStyles.itemTitle}>
                {Texts.preferentialTitle}
              </BbkText>
            </>
          )}
          {!!couponTip && (
            <LinearGradient
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 1.0 }}
              locations={[0, 0.15, 1]}
              colors={[
                setOpacity(color.white, 0),
                setOpacity(color.orangePrice, 0.06),
                setOpacity(color.orangePrice, 0.1),
              ]}
              className={c2xStyles.couponLabel}
            >
              <BbkText className={c2xStyles.couponLabelText}>
                {couponTip}
              </BbkText>
            </LinearGradient>
          )}
          {!couponTip && (
            <BbkText className={c2xStyles.disableCouponTipText}>
              {Texts.disableCouponTip}
            </BbkText>
          )}
          <BbkText type="icon" className={c2xStyles.couponIcon}>
            {showExtra ? icon.arrowUp : icon.arrowDown}
          </BbkText>
        </BbkTouchable>
        {showExtra && (
          <View
            testID={
              UITestID.car_testid_comp_booking_NewCouponAndDeposit_activity
            }
            className={c2xStyles.contentWrap}
            style={isMergeDeposit && styles.ml90}
          >
            {/** 优惠劵模块 */}
            <Coupon
              couponTestID={couponTestID}
              couponList={couponList}
              currency={currency}
              onPressCoupon={onPressCoupon}
            />

            {/** 活动模块 */}
            <Activity
              activityTestID={activityTestID}
              activityDetail={activityDetail}
              currency={currency}
              onPressActivity={onPressActivity}
            />
          </View>
        )}
      </View>

      {/** 押金模块 */}
      {isMergeDeposit && (
        <Deposit
          trackInfo={trackInfo}
          depositTitle={depositTitle}
          opeButtonInfo={opeButtonInfo}
          depositInfo={depositInfo}
          fundStringObjs={fundStringObjs}
          selectPassengerObj={selectPassengerObj}
          depositRuleObj={depositRuleObj}
          depositDescs={depositDescs}
          isZero={isZero}
          changePayMode={changePayMode}
          showEhiFreeDepositModalVisible={showEhiFreeDepositModalVisible}
          toSelectPassenger={toSelectPassenger}
        />
      )}
    </ImageBackground>
  );
};

export default NewCouponAndDeposit;
