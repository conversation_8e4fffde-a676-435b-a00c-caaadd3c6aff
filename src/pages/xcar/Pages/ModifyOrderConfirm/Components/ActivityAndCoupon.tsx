import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './activityAndCouponC2xStyles.module.scss';
import Discount from '../../../ComponentBusiness/DiscountBlock';
import Tip from './Tip';
import { modifyOrderCouponText } from '../Texts';
import {
  CouponListType,
  ActivityDetailType,
} from '../../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';

const { getPixel, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  tipstyle: { marginLeft: getPixel(32), marginRight: getPixel(32) },
  titleStyle: {
    ...font.title2MediumStyle,
  },
  discountItemStyle: {
    ...font.rcFont,
  },
  priceExtraStyle: {
    ...font.title4MediumStyle,
    lineHeight: getLineHeight(42),
  },
  marginStyle: {
    marginBottom: getPixel(14),
  },
  activityStyle: {
    paddingTop: getPixel(44),
  },
  couponStyle: {
    paddingTop: getPixel(36),
    paddingBottom: 0,
  },
  couponTipStyle: {
    ...font.body3LightStyle,
  },
});

export const Activity = memo(
  ({ isLogin, activityDetails, currency, onPressActivity }: any) => {
    if (!isLogin || !activityDetails?.length) {
      return null;
    }

    const discountItems = [];
    activityDetails.forEach(item => {
      const { promotion = {} } = item || {};
      const {
        couponName,
        longTag,
        title: titleInner,
        couponDesc,
        longDesc,
        deductionAmount,
      } = promotion || {};

      if (couponName || longTag || titleInner) {
        discountItems.push({
          title: couponName || longTag || titleInner,
          desc: couponDesc || longDesc,
          currency,
          price: deductionAmount,
        });
      }
    });

    return (
      <Discount
        isLogin={isLogin}
        isActivity={true}
        showIcon={false}
        discountItems={discountItems}
        disableText={!discountItems.length && activityDetails[0]?.title}
        onPressActivity={onPressActivity}
        style={styles.activityStyle}
        titleStyle={styles.titleStyle}
        couponTipStyle={styles.couponTipStyle}
        priceExtraStyle={styles.priceExtraStyle}
        marginStyle={styles.marginStyle}
        discountItemStyle={styles.discountItemStyle}
      />
    );
  },
);

export const Coupon = memo(
  ({
    isLogin,
    onPressCoupon = true,
    couponList,
    currency,
    enablePress,
  }: any) => {
    if (!couponList) {
      return null;
    }
    const discountItems = [];
    const { selectedCoupon = {}, title } = couponList;
    const { couponName, deductionAmount } = selectedCoupon;
    if (couponName) {
      discountItems.push({
        title: couponName,
        currency,
        price: deductionAmount,
      });
    }
    return (
      <Discount
        isLogin={isLogin}
        isActivity={false}
        showIcon={false}
        discountItems={discountItems}
        disableText={!discountItems.length && title}
        onPressCoupon={onPressCoupon}
        enablePress={enablePress}
        style={styles.couponStyle}
        titleStyle={styles.titleStyle}
        couponTipStyle={styles.couponTipStyle}
        discountItemStyle={styles.discountItemStyle}
        priceExtraStyle={styles.priceExtraStyle}
        marginStyle={styles.marginStyle}
      />
    );
  },
);

interface IActivityAndCoupon {
  isLogin: boolean;
  explain: string;
  couponList: CouponListType;
  activityDetails: ActivityDetailType[];
  enablePressCoupon?: boolean;
  onPressCoupon: () => void;
  onLayoutCoupon: (e) => void;
}

const ActivityAndCoupon: React.FC<IActivityAndCoupon> = ({
  isLogin,
  explain,
  couponList,
  activityDetails,
  enablePressCoupon = true,
  onPressCoupon,
  onLayoutCoupon,
}) => {
  if (!couponList && !activityDetails) {
    return null;
  }
  return (
    <View className={c2xStyles.couponWrap} onLayout={onLayoutCoupon}>
      {!!explain && (
        <Tip
          content={explain || modifyOrderCouponText.modifyOrderCouponTip}
          wrapStyle={styles.tipstyle}
        />
      )}

      <Coupon
        isLogin={isLogin}
        couponList={couponList}
        currency="CNY"
        enablePress={enablePressCoupon}
        onPressCoupon={onPressCoupon}
      />

      {!!activityDetails && (
        <Activity
          isLogin={isLogin}
          activityDetails={activityDetails}
          currency="CNY"
          onPressActivity={null}
        />
      )}
    </View>
  );
};

export default ActivityAndCoupon;
