import { get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import { IBasePageProps } from '@c2x/components/Page';
import ViewPort from '@c2x/components/ViewPort';
import Loading from '@c2x/apis/Loading';
import Event from '@c2x/apis/Event';
import LoadingView from '@c2x/components/LoadingView';
import React from 'react';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { ResultInfoType } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/CreateOrderResponseType';
import CPage, { IStateType } from '../../Components/App/CPage';
import Channel from '../../Util/Channel';
import { AppContext, User, CarLog, Utils, EventHelper } from '../../Util/Index';
import Texts, { pollingPriceText } from './Texts';
import CreateInsModal from '../../ComponentBusiness/CreateInsModal/index';
import InsFailedModal from '../../ComponentBusiness/InsFailedModal';
import Header from './Components/Header';
import { Content } from './Components/Content';
import ModifyInfo from '../../Containers/ModifyOrderConfirmModifyInfoContainer';
import Insurance from '../../Containers/ModifyOrderConfirmInsuranceContainer';
import CancelPolicy from '../../Containers/ModifyOrderConfirmCancelPolicyContainer';
import ActivityAndCoupon from '../../Containers/ModifyOrderConfirmActivityAndCouponContainer';
import Footer from '../../Containers/ModifyOrderConfirmFooterContainer';
import FeeDetailModal from './Components/FeeDetailModal';
import ConfirmModal from '../../Containers/ModifyOrderConfirmModalContainer';
import OrderConfirmModal, {
  ModifyOrderModal,
  TitleHightlightType,
} from '../../ComponentBusiness/OrderConfirmModal/Index';
import ModifyOrderWarnModal from '../../Containers/ModifyOrderWarnModalContainer';
import {
  FeeInfoType,
  PageInfoType,
} from '../../Types/Dto/ModifyOrderResponseType';
import { InsMsg } from '../../Constants/PromptMessage';
import { EventName } from '../../Constants/Index';
import { InsCallStatus } from '../OrderDetail/Types';
import {
  IModifyOrderModalPropsType,
  IModifyType,
} from '../../State/ModifyOrderConfirm/Types';
import { CalendarPriceModalContextProvider } from '../../ComponentBusiness/CalendarPrice/CalendarPriceContext';
import CalendarPriceModal from '../../ComponentBusiness/CalendarPrice/CalendarPriceModal';
import { ButtonAction } from '../../Components/CouponPreValidationModals/Index';
import CouponPreValidationFailedModalsContainer from '../../Containers/CouponPreValidationFailedModalsContainer';
import { ModifiedPopStorage as ModifiedStorage } from '../../State/ModifyOrder/ModifiedStorage';
import ModifyOrderConfirmInterceptionModal from '../../Containers/ModifyOrderConfirmInterceptionContainer';

const { vh, fixOffsetTop } = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;

const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
});

interface IModifyOrderConfirmPropsType extends IBasePageProps {
  orderId: number;
  feeInfo: FeeInfoType;
  createInsModalVisible: boolean;
  insFailedModalVisible: boolean;
  pageInfo: PageInfoType;
  createModifyOrder: (orderId: number) => void;
  onPressSubmit: (data: any) => void;
  setCreateInsModalVisible: (visible: boolean) => void;
  setInsFailedModalVisible: (visible: boolean) => void;
  modifyOrderToPay: (orderId: number) => void;
  selectedInsuranceId: Array<string>;
  modifyOrderModalProps: IModifyOrderModalPropsType;
  isSubmitting: boolean;
  isLoading?: boolean;
  isModifyToPay?: boolean;
  resetConfirmData: () => void;
  payTimeOutModalVisible: boolean;
  setPayTimeOutModalVisible: (visible: boolean) => void;
  setCouponPreValidationModalVisible: (
    visible: boolean,
    content: ResultInfoType,
  ) => void;
  isCancelOrder?: boolean;
  isFromCancelPage?: boolean;
}

interface ModifyOrderConfirmStateType extends IStateType {
  feeDetailVisible: boolean;
  pollingPriceVisible: boolean;
  isLogin: boolean;
}

export default class ModifyOrderConfirm extends CPage<
  IModifyOrderConfirmPropsType,
  ModifyOrderConfirmStateType
> {
  pollingPriceTimer = null;

  pollingPriceDuration = 5 * 60 * 1000;

  scrollView: any;

  couponY: number;

  couponHeight: number;

  constructor(props) {
    super(props);
    this.state = {
      feeDetailVisible: false,
      pollingPriceVisible: false,
      isLogin: false,
    };
    this.scrollView = React.createRef();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().ModifyOrderConfirm.ID;
  }

  async componentDidMount() {
    super.componentDidMount();
    const isLogin = await User.isLogin();
    if (!isLogin) {
      const isLoginNow = await User.toLogin();
      if (!isLoginNow) {
        return;
      }
    }
    this.setState({ isLogin: true });
    const { isModifyToPay } = this.props;
    if (!isModifyToPay) {
      this.registerEvents();
    }
  }

  pageDidAppear() {
    super.pageDidAppear();
    if (!this.props?.isModifyToPay) {
      this.pollingPriceTimerFn();
      this.registerEvents();
    }
  }

  pollingPriceTimerFn = () => {
    const payAmount = this.props?.feeInfo?.payAmount || 0;
    if (payAmount === 0) return;
    clearTimeout(this.pollingPriceTimer);
    this.pollingPriceTimer = setTimeout(() => {
      this.setState({
        pollingPriceVisible: true,
      });
    }, this.pollingPriceDuration);
  };

  clearPollingPriceTimerFn = () => {
    if (!this.pollingPriceTimer) {
      clearTimeout(this.pollingPriceTimer);
    }
  };

  registerEvents = () => {
    EventHelper.addEventListener(
      EventName.insConfirmBackToModifyOrderConfirm,
      data => {
        const { selectedInsuranceId, isCancelOrder } = this.props;
        const status = lodashGet(data, 'status');
        if (
          (status === InsCallStatus.submit ||
            status === InsCallStatus.cancel) &&
          selectedInsuranceId.length > 0
        ) {
          CarLog.LogCode({
            name: '点击_订单详情_取消订单_修改订单/行程_修改订单_确认信息_去支付',
            info: {
              cancelorder: isCancelOrder,
            },
          });
          this.props.onPressSubmit(data);
        }
      },
    );
  };

  removeEventListener = () => {
    Event.removeEventListener(EventName.insConfirmBackToModifyOrderConfirm);
  };

  onBackPress = () => {
    this.pop();
  };

  getHeaderData = () => ({
    title: Texts.pageTitle,
    onPressLeft: this.onBackPress,
  });

  getFooterData = () => ({
    onPressBtn: () => {
      const { isFromCancelPage, isCancelOrder } = this.props;
      if (isFromCancelPage) {
        CarLog.LogCode({
          name: '点击_订单详情_取消订单_修改订单/行程_修改订单_确认信息_确认修改',
          info: {
            cancelorder: isCancelOrder,
          },
        });
      } else {
        CarLog.LogCode({ name: '点击_修改订单_修改确认页_提交修改' });
      }
      this.clearPollingPriceTimerFn();
    },
    onPressFeeDetail: () => {
      this.setState({
        feeDetailVisible: true,
      });
    },
    isModifyToPay: this.props?.isModifyToPay,
  });

  onCloseFeeDetail = () => {
    this.setState({
      feeDetailVisible: false,
    });
  };

  reSearch = () => {
    const { orderId } = this.props;
    this.props.createModifyOrder(orderId);
  };

  getPollingPriceModalData = () => ({
    title: pollingPriceText.title,
    btns: [
      {
        name: pollingPriceText.btn,
        isPrimary: true,
        onPress: () => {
          this.setState({
            pollingPriceVisible: false,
          });
          this.reSearch();
        },
      },
    ],
  });

  async handleLogin() {
    const res = (await User.toLogin()) as boolean;
    if (res) {
      this.setState({ isLogin: res });
    }
  }

  onPressCoupon = () => {
    CarLog.LogCode({ name: '点击_修改订单_修改确认页_优惠券' });

    if (!this.state.isLogin) {
      this.handleLogin();
      return;
    }

    this.push(Channel.getPageId().ModifyCoupon.EN);
  };

  getPayTimeOutModalData = () => ({
    title: Texts.modifyFailSimple,
    contentText: Texts.payTimeOutTipInfo,
    btns: [
      {
        name: Texts.gotit,
        isPrimary: true,
        onPress: () => {
          const { orderId } = this.props;
          this.props.setPayTimeOutModalVisible(false);
          ModifiedStorage.save(orderId);
          AppContext.PageInstance.pop();
        },
      },
    ],

    titleHeightlightStyle: TitleHightlightType.Warning,
  });

  componentDidUpdate(prevProps) {
    const { isSubmitting } = this.props;
    if (!prevProps.isSubmitting && isSubmitting) {
      Loading.showMaskLoading({
        cancelable: false,
      });
    } else if (!isSubmitting) {
      Loading.hideMaskLoading();
    }
  }

  componentWillUnmount() {
    Loading.hideMaskLoading();
    super.componentWillUnmount();
    if (!this.props?.isModifyToPay) {
      this.clearPollingPriceTimerFn();
      this.removeEventListener();
      this.props.resetConfirmData();
    }
  }

  handlePressCouponModalButton = type => {
    const { setCouponPreValidationModalVisible } = this.props;

    switch (type) {
      case ButtonAction.scrollToCoupon:
        setCouponPreValidationModalVisible(false, null);
        this.reSearch();
        if (this.scrollView) {
          // 优惠券模块滚动到屏幕正中间位置计算（优惠券模块置顶滚动距离 - （屏幕一半高度 - 优惠券模块一半高度）- 头部修正高度）
          const couponY =
            this.couponY -
            (vh(50) -
              this.couponHeight / 2 -
              (DEFAULT_HEADER_HEIGHT + fixOffsetTop()));
          this.scrollView.scrollTo({ x: 0, y: couponY, animated: true });
        }
        break;
      case ButtonAction.closeModal:
      default:
        setCouponPreValidationModalVisible(false, null);
        break;
    }
  };

  onLayoutCoupon = e => {
    this.couponY = e.nativeEvent.layout.y;
    this.couponHeight = e.nativeEvent.layout.height;
  };

  renderPage() {
    const {
      createInsModalVisible,
      insFailedModalVisible,
      setCreateInsModalVisible,
      setInsFailedModalVisible,
      modifyOrderModalProps,
      isModifyToPay,
      isLoading,
      payTimeOutModalVisible,
      pageInfo,
    } = this.props;
    const { feeDetailVisible, pollingPriceVisible, isLogin } = this.state;
    if (isLoading || !isLogin) {
      return <LoadingView />;
    }
    const isModifyDriver = pageInfo?.type === IModifyType.modifyDriver;
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.page}>
        <Header {...this.getHeaderData()} isModifyDriver={isModifyDriver} />
        <Content
          scrollViewRef={ref => {
            this.scrollView = ref;
          }}
          isModifyDriver={isModifyDriver}
        >
          <ModifyInfo />
          <Insurance isModifyToPay={isModifyToPay} />
          {/** 2022-9-8 修改驾驶员信息的场景下，移除取消政策 和 优惠模块 */}
          {!isModifyDriver && (
            <>
              <ActivityAndCoupon
                isLogin={true}
                onPressCoupon={this.onPressCoupon}
                onLayoutCoupon={this.onLayoutCoupon}
              />

              <CancelPolicy />
            </>
          )}
        </Content>
        <Footer {...this.getFooterData()} />
        <>
          <CalendarPriceModalContextProvider isOrderPolicy={true}>
            <FeeDetailModal
              visible={feeDetailVisible}
              isModifyToPay={isModifyToPay}
              onCancel={this.onCloseFeeDetail}
            />

            <CalendarPriceModal />
          </CalendarPriceModalContextProvider>
          <ModifyOrderModal {...modifyOrderModalProps} />
          {!isModifyToPay && (
            <OrderConfirmModal
              {...this.getPollingPriceModalData()}
              visible={pollingPriceVisible}
            />
          )}
          {isModifyToPay && (
            <OrderConfirmModal
              {...this.getPayTimeOutModalData()}
              visible={payTimeOutModalVisible}
            />
          )}
          <ConfirmModal reSearch={this.reSearch} />
          <ModifyOrderWarnModal />
          <CreateInsModal
            visible={createInsModalVisible}
            onClose={() => {
              setCreateInsModalVisible(false);
            }}
            content={InsMsg.enterBeforeInsurancePlat}
          />

          <InsFailedModal
            visible={insFailedModalVisible}
            onClose={() => {
              setInsFailedModalVisible(false);
            }}
            onPay={() => {
              setInsFailedModalVisible(false);
              EventHelper.sendEvent(
                EventName.insConfirmBackToModifyOrderConfirm,
                {
                  status: 2,
                },
              );
            }}
            onBack={() => {
              setInsFailedModalVisible(false);
            }}
            title={InsMsg.insFailModalTitle}
            content={InsMsg.insFailModalContent}
            backBtnText={InsMsg.backBtnText}
            payBtnText={InsMsg.payBtnText}
          />

          <CouponPreValidationFailedModalsContainer
            modalPageId={Channel.getPageId().ModifyOrderConfirm.ID}
            onPressButton={this.handlePressCouponModalButton}
          />

          {Utils.isCtripIsd() && <ModifyOrderConfirmInterceptionModal />}
        </>
      </ViewPort>
    );
  }
}
