import React, { useCallback, useState, memo } from 'react';
import { XView as View, xClassNames, XImage } from '@ctrip/xtaro';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { isIos } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { ImageUrl } from '../../../Constants/Index';
import styles from './ctripInsurancesStyles.module.scss';

type CtripInsuranceT = {
  ctripInsurance: any;
  selectedInsuranceId: any[];
  updateCarGuarantee: (data) => void;
  isPriceLoading: boolean;
  gotoInsurance: () => void;
};

const CtripInsurance = ({
  ctripInsurance,
  selectedInsuranceId,
  updateCarGuarantee,
  isPriceLoading,
  gotoInsurance,
}: CtripInsuranceT) => {
  const handleSwitchStatus = useCallback(() => {
    if (isPriceLoading) {
      return;
    }
    const insuranceId = ctripInsurance.uniqueCode;
    updateCarGuarantee({ insuranceId });
  }, [isPriceLoading, updateCarGuarantee, ctripInsurance]);
  if (!ctripInsurance) return null;
  return (
    <View className={styles.cInsuItem}>
      <View onClick={handleSwitchStatus} className={styles.cInsuItemTop}>
        <Text className={styles.cInsuItemName} fontWeight="medium">
          {ctripInsurance?.name}
        </Text>
        <View className={styles.cInsuItemTopR}>
          <View className={styles.priceWrap} fontWeight="medium">
            <Text
              className={xClassNames(styles.priceText, isIos && styles.mt5)}
              fontWeight="medium"
            >
              +
            </Text>
            <Text className={styles.priceText}>￥</Text>
            <Text className={styles.price} fontWeight="bold">
              {ctripInsurance?.currentDaliyPriceFixed}
            </Text>
            <Text
              className={xClassNames(styles.priceText, styles.perDay)}
              fontWeight="medium"
            >
              /天
            </Text>
          </View>
          <XImage
            src={`${ImageUrl.DIMG04_PATH}${selectedInsuranceId?.indexOf(ctripInsurance?.uniqueCode) > -1 ? '1tg2z12000jbuo29tDE0B' : '1tg2412000jbvqow04C7F'}.png`}
            className={styles.upgradeGou}
            mode="scaleToFill"
          />
        </View>
      </View>
      <View className={styles.cInsuDescWrap}>
        {ctripInsurance?.descriptionSimple?.map?.((item, idx) => {
          return (
            <View className={styles.cInsuDescTextWrap} key={item}>
              <Text className={styles.cInsuDescText}>{item}</Text>
              {idx !== ctripInsurance.description.length - 1 && (
                <View className={styles.serviceItemSplit} />
              )}
            </View>
          );
        })}
      </View>
      <View onClick={gotoInsurance} className={styles.cInsuTipsWrap}>
        <Text className={styles.cInsuTipsText}>投保须知</Text>
        <Text className={styles.moreIcon} type="icon">
          {icon.arrowRight}
        </Text>
      </View>
    </View>
  );
};

export default memo(CtripInsurance);
