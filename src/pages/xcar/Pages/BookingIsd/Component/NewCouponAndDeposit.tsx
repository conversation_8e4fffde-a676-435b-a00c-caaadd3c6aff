/* eslint-disable no-nested-ternary */
import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  xClassNames as classNames,
  XView as View,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { useSelector } from 'react-redux';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, layout, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './newCouponAndDepositC2xStyles.module.scss';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import Texts from '../Texts';
import {
  CouponListType,
  ActivityDetailType,
  DepositPayInfosType,
  TrackInfoType,
} from '../../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import { IDepositInfo } from '../../../ComponentBusiness/DepositBox';
import DepositSimpleDesc from '../../../ComponentBusiness/DepositSimpleDesc/src/DepositSimpleDesc';
import { Enums } from '../../../ComponentBusiness/Common';
import SkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import { CarLog, Utils } from '../../../Util/Index';
import ActivityDiscount from './ActivityDiscount';
import { getPriceLoading } from '../../../State/Booking/Selectors';

const { getPixel, isIos } = BbkUtils;
const styles = StyleSheet.create({
  loadingBg: {
    backgroundColor: color.white,
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    marginTop: getPixel(16),
    borderRadius: getPixel(16),
    overflow: 'hidden',
  },
  originCurrency: {
    ...font.F_34_10_regular_TripNumberBold,
    color: color.orangePrice,
    marginRight: getPixel(0),
  },
  originDayPrice: {
    ...font.F_34_10_regular_TripNumberBold,
    color: color.orangePrice,
    marginLeft: getPixel(-4),
  },
  top4: {
    top: getPixel(-4),
  },

  simpleWrap: {
    marginBottom: getPixel(32),
  },
});

interface PropsType extends IDepositType {
  currency: string;
  couponList: CouponListType;
  activityDetail: ActivityDetailType;
  isMergeDeposit: boolean;
  preferentialTips: any;
  onPressCoupon: () => void;
  onPressActivity: (data?: any) => void;
  isShowLoading?: boolean;
  isZero?: boolean;
  depositTitleDesc?: any;
  depositSimpleDescs?: any;
  onReceive: (data?: any) => void;
  isNewUser: boolean;
  queryPriceInfo: () => void;
  setHasReceivedCouponAtBooking: (data: boolean) => void;
  couponTestID?: string;
  activityTestID?: string;
}

interface ICheckBoxWithImg {
  isSelected: boolean;
}

const CheckBoxWithImg: React.FC<ICheckBoxWithImg> = ({ isSelected }) => {
  return (
    <View
      testID={UITestID.car_testid_page_booking_deposit_check_give_up}
      style={layout.flexRow}
    >
      <Image
        src={
          isSelected
            ? `${ImageUrl.DIMG04_PATH}1tg2412000jbvqow04C7F.png`
            : `${ImageUrl.DIMG04_PATH}1tg5412000kt9p6ca6597.png`
        }
        mode="aspectFill"
        className={c2xStyles.checkImg}
      />
    </View>
  );
};

interface IDepositType {
  trackInfo: TrackInfoType;
  depositTitle: string;
  depositDescs: Array<{ stringObjs: Array<{ content: string }> }>;
  opeButtonInfo: DepositPayInfosType;
  depositInfo: IDepositInfo;
  fundStringObjs: Array<{ content: string; url?: string }>; // 补足资金提示
  selectPassengerObj: { content: string }; // 选择驾驶员提示
  // eslint-disable-next-line react/no-unused-prop-types
  depositRuleObj: Array<{ content: string; url?: string }>; // 减免规则
  isZero?: boolean; // 是否是0元订单
  changePayMode: (data: any) => void;
  showEhiFreeDepositModalVisible: () => void;
  toSelectPassenger: () => void;
  depositTitleDesc?: any;
  depositSimpleDescs?: any;
}

const Deposit: React.FC<IDepositType> = ({
  trackInfo,
  depositTitle,
  opeButtonInfo,
  depositInfo,
  fundStringObjs,
  selectPassengerObj,
  changePayMode,
  showEhiFreeDepositModalVisible,
  toSelectPassenger,
  depositTitleDesc,
  depositSimpleDescs,
}) => {
  const isPriceLoading = useSelector(getPriceLoading);
  const changeDepositPayType = useMemoizedFn((depositPayType, checked) => {
    if (isPriceLoading) return;
    const newDepositPayType = checked ? depositPayType : 0;
    changePayMode({
      depositPayType: newDepositPayType,
      nextDepositPayType: newDepositPayType,
    });
  });

  // 是否为程信分T，和程信分N+芝麻通过
  const isShowCredit =
    trackInfo?.riskFinal === Enums.HomeUserCreditStatus.T ||
    (trackInfo?.riskFinal === Enums.HomeUserCreditStatus.N &&
      trackInfo?.zhimaResult === '1');
  // 且没有选择放弃免押 且不是0元订单
  // isMerge = !isZero && isShowCredit && !opeButtonInfo?.isCheck;
  const title = isShowCredit ? depositTitle : depositTitleDesc;
  const ViewWrap = opeButtonInfo ? BbkTouchable : View;
  const onChangeDepositPayType = useMemoizedFn(() => {
    const { isCheck, depositPayType } = opeButtonInfo || {};
    changeDepositPayType(depositPayType, !isCheck);
  });
  return (
    <>
      <ViewWrap
        className={c2xStyles.itemWrap}
        onPress={!!opeButtonInfo && onChangeDepositPayType}
      >
        <View className={c2xStyles.leftItem}>
          <Image
            src={
              selectPassengerObj?.content
                ? `${ImageUrl.DIMG04_PATH}${isIos ? '1tg1c12000lghb4df1D23' : '1tg0612000lghbfve9FF3'}.png`
                : !isShowCredit
                  ? `${ImageUrl.DIMG04_PATH}${isIos ? '1tg3p12000lghbxweF5FC' : '1tg6w12000lghbu0yC25E'}.png`
                  : `${ImageUrl.DIMG04_PATH}${isIos ? '1tg5w12000lghb4gf4451' : '1tg4212000lghauajF925'}.png`
            }
            className={c2xStyles.logoImg}
          />

          <BbkText className={c2xStyles.itemTitle} fontWeight="bold">
            免押
          </BbkText>
          {/* </View> */}
        </View>
        <View className={c2xStyles.rightItem}>
          {!!title && !selectPassengerObj?.content && (
            <View className={c2xStyles.depositSubTitleWrap}>
              <View testID={UITestID.car_testid_page_booking_deposit_title}>
                <BbkText className={c2xStyles.itemTxt}>{title}</BbkText>
              </View>
              {!!opeButtonInfo && (
                <CheckBoxWithImg isSelected={opeButtonInfo.isCheck} />
              )}
            </View>
          )}
          {fundStringObjs?.length > 0 && (
            <BbkText className={c2xStyles.mt8}>
              {fundStringObjs.map((item, index) => (
                <>
                  <BbkText>{item?.content}</BbkText>
                  {!!item.url && (
                    <BbkTouchable
                      testID={`${UITestID.car_testid_page_booking_deposit_help}_${index}`}
                      onPress={showEhiFreeDepositModalVisible}
                    >
                      <BbkText type="icon" className={c2xStyles.helpIcon}>
                        {icon.help}
                      </BbkText>
                    </BbkTouchable>
                  )}
                </>
              ))}
            </BbkText>
          )}
          {/* 没有驾驶员情况 */}
          {!!selectPassengerObj?.content && (
            <BbkTouchable
              testID={UITestID.car_testid_page_booking_deposit_select_driver}
              onPress={toSelectPassenger}
              className={c2xStyles.selectPassengerWrap}
            >
              <BbkText className={c2xStyles.selectPassengerText}>
                {Texts.selectPassengerTitle}
              </BbkText>
              <BbkText type="icon" className={c2xStyles.rightIcon}>
                {icon.arrowRight}
              </BbkText>
            </BbkTouchable>
          )}
          {/* </View> */}
        </View>
      </ViewWrap>
      {depositSimpleDescs?.length > 0 && (
        <DepositSimpleDesc
          depositSimpleDescs={depositSimpleDescs}
          depositInfo={depositInfo}
          showExtra={true}
          showEhiFreeDepositModalVisible={showEhiFreeDepositModalVisible}
          isShowCredit={isShowCredit}
          style={styles.simpleWrap}
        />
      )}
    </>
  );
};

const NewCouponAndDeposit = ({
  currency,
  couponList,
  activityDetail,
  isMergeDeposit,
  depositTitle,
  depositInfo,
  depositDescs,
  opeButtonInfo,
  trackInfo,
  fundStringObjs,
  selectPassengerObj,
  depositRuleObj,
  isZero,
  onPressCoupon,
  onPressActivity,
  changePayMode,
  showEhiFreeDepositModalVisible,
  toSelectPassenger,
  isShowLoading,
  preferentialTips,
  depositTitleDesc,
  depositSimpleDescs,
  onReceive,
  isNewUser,
  queryPriceInfo,
  setHasReceivedCouponAtBooking,
}: PropsType) => {
  if (Utils.isCtripIsd() && isShowLoading) {
    return (
      <SkeletonLoading
        visible={true}
        imageStyle={styles.loadingBg}
        pageName={PageType.BookingCouponNew}
      />
    );
  }
  const preferentialTipArr = preferentialTips?.description?.split(/(\￥)/);
  const { selectedCoupon, receiveCoupons, title } = couponList || {};
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const onReceiveCoupon = useMemoizedFn(() => {
    const promotionSecretIds = receiveCoupons?.map(
      item => item.promotionSecretId,
    );
    const promotionIds = receiveCoupons?.map(item => item.promotionId);
    onReceive({
      promotionSecretIds,
      promotionIds,
      callback: () => {
        queryPriceInfo();
        onPressCoupon();
        setHasReceivedCouponAtBooking(true);
      },
      isNewBooking: true,
    });
    CarLog.LogCode({
      name: '点击_填写页_本单可享_领取优惠券',
    });
  });
  return (
    <View className={c2xStyles.mainWrap}>
      <View className={c2xStyles.couponHead}>
        <View className={c2xStyles.headImg}>
          <Image
            src={
              !isNewUser
                ? `${ImageUrl.DIMG04_PATH}1tg1n12000l65hwuu1004.png`
                : `${ImageUrl.DIMG04_PATH}1tg7212000kt9oiy0E78D.png`
            }
            className={classNames(
              isNewUser ? c2xStyles.newImg : c2xStyles.oldImg,
            )}
          />
        </View>
        {!!preferentialTipArr?.length && (
          <View
            className={classNames(c2xStyles.headTitle, isIos && c2xStyles.top5)}
          >
            <BbkText className={c2xStyles.font32} fontWeight="bold">
              {preferentialTipArr?.[0]}
            </BbkText>
            {preferentialTipArr?.[1] && (
              <BbkCurrencyFormatter
                price={+preferentialTipArr[2]}
                currency={preferentialTipArr[1]}
                currencyStyle={xMergeStyles([
                  styles.originCurrency,
                  isIos && styles.top4,
                ])}
                priceStyle={xMergeStyles([
                  styles.originDayPrice,
                  isIos && styles.top4,
                ])}
                isNew={true}
              />
            )}
          </View>
        )}
      </View>
      <View className={c2xStyles.couponBg} />
      <View
        testID={UITestID.car_testid_comp_booking_NewCouponAndDeposit}
        className={c2xStyles.newContentWrap}
      >
        <ActivityDiscount
          activityDetail={activityDetail}
          currency={currency}
          onPress={onPressActivity}
        />
        <BbkTouchable
          className={classNames(c2xStyles.itemWrap)}
          debounce={true}
          onPress={receiveCoupons?.length ? onReceiveCoupon : onPressCoupon}
        >
          <View className={c2xStyles.leftItem}>
            <Image
              src={`${ImageUrl.DIMG04_PATH}${isIos ? '1tg5w12000lghbenfAEED' : '1tg2v12000lghc0jc7D22'}.png`}
              className={c2xStyles.logoImg}
            />
            <BbkText className={c2xStyles.itemTitle} fontWeight="bold">
              优惠券
            </BbkText>
          </View>
          <View className={c2xStyles.rightItem}>
            {selectedCoupon?.couponName ? (
              <>
                <BbkText className={c2xStyles.itemTxt}>
                  {couponList?.selectedCoupon?.couponName}
                </BbkText>
                <BbkText className={c2xStyles.itemTxt}> -</BbkText>
                <BbkText
                  className={classNames(c2xStyles.itemTxt, c2xStyles.left4)}
                >
                  ￥
                </BbkText>
                <BbkText
                  className={classNames(
                    c2xStyles.itemTxt,
                    c2xStyles.left4,
                    c2xStyles.itemName,
                  )}
                  numberOfLines={1}
                >
                  {couponList?.selectedCoupon?.deductionAmount}
                </BbkText>
                <BbkText type="icon" className={c2xStyles.itemArrow}>
                  {icon.arrowRight}
                </BbkText>
              </>
            ) : receiveCoupons?.length ? (
              <View
                className={c2xStyles.receiveWrap}
                testID={CarLog.LogExposure({
                  name: '曝光_填写页_本单可享_优惠券待领',
                })}
              >
                <BbkText className={c2xStyles.receiveTxt}>
                  未选优惠券, 去领取
                </BbkText>
                <BbkText
                  type="icon"
                  className={classNames(
                    c2xStyles.itemArrow,
                    c2xStyles.receiveArrow,
                  )}
                >
                  {icon.arrowRight}
                </BbkText>
              </View>
            ) : title ? (
              <>
                <BbkText className={c2xStyles.itemTxt}>{title}</BbkText>
                <BbkText
                  type="icon"
                  className={classNames(c2xStyles.itemArrow)}
                >
                  {icon.arrowRight}
                </BbkText>
              </>
            ) : (
              <View className={c2xStyles.noCoupon}>
                <BbkText className={c2xStyles.noTxt}>暂无可用的优惠券</BbkText>
                <BbkText type="icon" className={c2xStyles.noBtn}>
                  {icon.arrowRight}
                </BbkText>
              </View>
            )}
          </View>
        </BbkTouchable>
        {isMergeDeposit && (
          <>
            <View
              className={classNames(c2xStyles.line, isIos && c2xStyles.trans)}
            />
            <Deposit
              trackInfo={trackInfo}
              depositTitle={depositTitle}
              opeButtonInfo={opeButtonInfo}
              depositInfo={depositInfo}
              fundStringObjs={fundStringObjs}
              selectPassengerObj={selectPassengerObj}
              depositRuleObj={depositRuleObj}
              depositDescs={depositDescs}
              isZero={isZero}
              changePayMode={changePayMode}
              showEhiFreeDepositModalVisible={showEhiFreeDepositModalVisible}
              toSelectPassenger={toSelectPassenger}
              depositTitleDesc={depositTitleDesc}
              depositSimpleDescs={depositSimpleDescs}
            />
          </>
        )}
      </View>
    </View>
  );
};

export default NewCouponAndDeposit;
