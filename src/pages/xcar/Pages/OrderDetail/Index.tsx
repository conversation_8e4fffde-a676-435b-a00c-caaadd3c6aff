import { debounce as lodashDebounce, get as lodashGet } from 'lodash-es';
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-unused-expressions */
/* eslint-disable global-require */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import PixelRatio from '@c2x/apis/PixelRatio';
import ViewPort from '@c2x/components/ViewPort';
import CRNPage, { IBasePageProps } from '@c2x/components/Page';
import LoadingView from '@c2x/components/LoadingView';
import Event from '@c2x/apis/Event';
import Device from '@c2x/apis/Device';
import React from 'react';
import {
  xMergeStyles,
  XView as View,
  xRouter,
  xApplication as Application,
  XLinearGradient as LinearGradient,
  XViewExposure,
} from '@ctrip/xtaro';

import { AdSlider } from '@c2x/extraPackages/@ctrip/crn-ext-adsdk';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { ServiceProgressDTO } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/ServiceProgress';
import {
  color,
  layout,
  setOpacity,
  space,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils/index';
import { PackageDetailListType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import { PhoneNumber } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/QueryOrderNumberType';
// TODO JENNY 观察性能
import INps from '@c2x/extraPackages/@ctrip/crn-inps';
import c2xStyles from './orderDetail.module.scss';
import {
  CardResultType,
  BbkCustomScrollView,
  BbkOptimizeModal,
  CreateInsModal,
  InsFailedModal,
  RestAssuredBanner,
  OrderConfirmModal as ConfirmModal,
  VendorTagType,
  NewWarningTipsModal,
  WarningListResponseType,
  WarningDto,
  VocModal,
  DepositIntroduceModal,
  EasyLifeTagListModal,
  BbkSkeletonLoading,
  PageType,
  AdvanceReturnFeeDetailModal,
  FuelTypeModal,
  BbkInsuranceSuitsModalOsd,
  ExcessIntroduceModal,
  ExcessIntroduceModalOsd,
  InsuranceNoticeMustReadModal,
  InsuranceReminderEnglishModal,
  DepositRateDescriptionModal,
  BbkMaterialModalNew,
  BbkInsuranceDetail,
  CancelPolicy,
  CancelPolicyModal,
  CustomerInfo,
  ContinuePayTips,
  InvoiceModule,
  RenewTipsModal,
  PickupMaterialsModal,
  ReviewUnopenedModal,
  UpdateSnapShotData,
  UpdateContractTrackerSnapShotData,
  CarLabelsModal,
  PrivilegeTab,
  OrderRefundDetailModal,
  CancelPenaltyInfoModal,
  ServiceProgressModal,
  CarDetailModal,
  DriverInfo,
  ButtonsBarVendorCall,
  OsdFeeTitleExplainModal,
  ButtonsBottomBar,
  ClaimProcessModal,
  CESEntry,
  OsdModifyOrderModal,
  BottomSheetDargView,
  DargViewStatus,
  AddInstructModal,
  VehicleISD,
  BbkComponentWarningTips,
  NoLimitModal,
} from './Components/Index';
import {
  User,
  CarLog,
  AppContext,
  Utils,
  CarStorage,
  EventHelper,
  Channel,
  GetABCache,
  GetAB,
} from '../../Util/Index';
import CPage, { IStateType } from '../../Components/App/CPage';
import {
  LogKey,
  EventName,
  StorageKey,
  WarningInfo,
  PromptMessage,
  Url,
  Platform,
  OrderDetail as OrderDetailCons,
  ImageUrl,
  ApiResCode,
  UITestID,
} from '../../Constants/Index';

import {
  OdHeader,
  OrderStatus,
  OrderAmount,
  Vehicle,
  PickReturnTab,
  BuyInsurance,
  InsuranceProtections,
  AdditionalProduct,
  SesameContainer,
  ModifyOrderWarnModal,
  DepositDetailModal,
  CustomerPhoneModal,
  CustomerPersonPhoneModal,
  OrderLimitRulesComponentContainer,
  BusinessLicense,
  OrderDetaillSesameRepeatOrderModal,
  OrderDetaiPriceDetailModal,
  OrderDetaillBuyInsConfirmModalContainer,
  OrderEhiFreeDepositRuleModal,
  EhiModifyOrderModalContainer,
  Vendor,
  OrderDetailConfirmModalNew,
  CarServiceModal,
  CarService,
  CarServiceUpgrade,
  Guarantee,
  DepositPaymentModal,
  DepositPaymentModalOSD,
  ContinuePayFailDialog,
  OrderOptimizationStrengthenModal,
  OrderLessEnter,
  ContinuePayInterceptionModal,
  FlightDelayRulesModal,
  AdvanceReturnRecordModal,
  BusinessLicenseModal,
  EtcIntroModal,
  EtcUseHelperModal,
  VehicleUseNotesModal,
  PickUpMaterialsModal,
  OrderFulfillmentContainer,
  DistanceInvalidateModal,
  PolicyTips,
  OrderFeeDeduction,
  OrderChangeContainerIsd,
  ModifyFlightNoModal,
  OrderContactDoorStoreModal as ContactDoorStoreModal,
  TravelLimit,
  BusinessTimePolicyModal,
  BusinessTimeModal,
} from './Containers/Index';
import {
  CarRentalMustRead,
  PriceDetailModalOsd,
} from '../Product/Components/Index';

import { getStorePolicyProps } from '../../State/OrderDetail/Selectors';
import { AssistiveTouch, ErrorNetWork } from '../../Components/Index';
import {
  OrderDetailTexts as Texts,
  ctripSelected,
  ModifyOrderTexts,
  OrderDetailTextsV2,
} from '../../Constants/TextIndex';
import { PageRole } from '../../Constants/CommonEnums';
import {
  OrderModalsVisible,
  InsCallStatus,
  QueryOrderApiStatusType,
  QueryOrderFulfillmentInfoResponseType,
} from './Types';
import { CarServiceFromPageTypes } from '../../ComponentBusiness/CarService/src/Types';
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { BackgroudType } from '../../ComponentBusiness/ImageBackground/src/InsuranceBackground';
import { getMaterialsNew } from '../../State/Product/BbkMapper';

import { QConfigType } from '../../Types/Dto/QConfigResponseType';
import { AllOperationsType } from '../../Types/Dto/OrderDetailRespaonseType';
import {
  setOrderDetailStatusData,
  removeOrderDetailStatusData,
  OrderDetailStatusKeyList,
} from '../../Global/Cache/OrderStatusData';

import {
  ITravelLimit,
  ISelectedItem,
} from '../../ComponentBusiness/TravelLimit/src/Types';
import {
  OrderStatusCtrip,
  ORDER_BUTTON,
  InsuranceAnchorCategory,
  DirectOpenSub,
} from '../../Constants/OrderDetail';
import { initializeABOrderDetailPage } from '../../Util/CarABTesting/InitializeAB';
import { vh } from '../../Common/src/Utils/src/Utils';
import { DEFAULT_HEADER_HEIGHT } from '../../Common/src/Utils/src/Constants';
import FulfillmentModifyModal from '../../ComponentBusiness/FulfillmentModifyModal/FulfillmentModifyModal';
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
import { initialPhoneSurvey, setPhoneSurvey } from './Method';
import { LessDetailModal } from '../VendorList/Components/Index';
import FulfillmentModifyCard from './Components/FulfillmentModifyCard';
import Banner from './Components/ISD/Banner';

const LgColor = [
  color.linearGradientBlueDark,
  color.linearGradientBlueDark,
  setOpacity(color.grayBg, 0),
];

const { selector, getPixel, lazySelector, isAndroid, vw, isHarmony } = BbkUtils;
const AdWidth = vw(100) * PixelRatio.get();
const AdHeight = Math.round((AdWidth * 110) / 750);
// TODO 接入新的宽高比
const IsdAdWidth = AdWidth;
const IsdAdHeight = Math.round((AdWidth * 104) / 750);

const { PageIndexId } = WarningInfo;
const FIX_OFFESTTOP = 80; // 固定头部的滚动距离

const isdSlideVideoConfig = {
  bottom: getPixel(110),
  dotPosition: 'center',
  dotMargin: getPixel(16),
  dotWidthAndHeight: [getPixel(16), getPixel(16)],
  dotColor: setOpacity(color.black, 0.15),
  dotCurrentStyle: {
    backgroundColor: color.blueBase,
    width: getPixel(48),
    height: getPixel(16),
    borderRadius: getPixel(16),
    margin: getPixel(16),
  },
};

// 自助取还预加载图片
const selfServicePrefetchImages = [
  `${ImageUrl.DIMG04_PATH}1tg0i12000cw6mi0fC09A.gif`,
  `${ImageUrl.DIMG04_PATH}1tg5812000cwsbaht1CB4.png`,
  `${ImageUrl.DIMG04_PATH}1tg5z12000cwsbc4j55CB.png`,
  `${ImageUrl.DIMG04_PATH}1tg3x12000cwsb50q806A.png`,
  `${ImageUrl.DIMG04_PATH}1tg5612000cmm01fp54A6.gif`,
  `${ImageUrl.DIMG04_PATH}1tg4i12000ckehptsA008.png`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png?proc=corp/b55p`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png?proc=corp/b30p`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png`,
];

const styles = StyleSheet.create({
  pageContainer: { ...layout.flex1, backgroundColor: color.grayBg },
  contentBg: { backgroundColor: color.selfHelpBg },
  contentBgIsd: { backgroundColor: color.C_EDF2F8 },
  insWrap: {
    backgroundColor: color.white,
    marginTop: getPixel(Utils.isCtripIsd() ? 16 : 20),
  },
  npsWrap: {
    marginTop: getPixel(Utils.isCtripIsd() ? 16 : 20),
    marginLeft: 0,
    marginRight: 0,
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    backgroundColor: color.white,
  },
  shadow: {
    // @ts-expect-error
    shadowOffset: { width: 0, height: getPixel(-5) },
    shadowRadius: getPixel(5),
    shadowColor: color.black,
    shadowOpacity: 0.05,
    elevation: 20,
  },
  supplierWarp: {
    marginTop: getPixel(Utils.isCtripIsd() ? 16 : 20),
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    marginBottom: getPixel(56),
  },
  supplierWarpWithPerson: { marginBottom: getPixel(60) },
  carService: {
    backgroundColor: color.white,
    marginTop: getPixel(20),
    borderWidth: 0,
  },
  carServiceUpgrade: {
    backgroundColor: color.white,
    paddingBottom: getPixel(32),
    marginTop: -getPixel(10),
  },
  guarantee: {
    backgroundColor: color.white,
    width: '100%',
    marginTop: getPixel(20),
    borderWidth: 0,
  },
  travelLimitWrap: { marginTop: getPixel(16) },
  fulfillLoading: {
    backgroundColor: color.white,
    width: getPixel(702),
    left: getPixel(24),
    top: getPixel(-13),
    borderRadius: getPixel(16),
    marginTop: getPixel(16),
  },
  innerViewStyle: {
    height:
      vh(100) * 0.7 + DEFAULT_HEADER_HEIGHT + getPixel(isAndroid ? 9 : 30),
  },
});

let scoreSelectTimer = null;
const toUpgradeInsTimer = null;
interface PhoneNumberDataType {
  phoneNumberList: PhoneNumber[];
  fetchCompleted: boolean;
}
interface OrderPropsType extends IBasePageProps {
  config: QConfigType;
  fetchOrder2: (data?: any, interval?: boolean) => void;
  isLoading?: boolean;
  orderID?: number;
  orderId?: number;
  orderStatus?: number;
  hasUserAuthInfo: boolean;
  hasUserInfoItem: boolean;
  clearResult: () => void;
  idCardResult: CardResultType;
  driverResult: CardResultType;
  fetchQueryCancelFee: (data?: any) => void;
  isDebugMode?: boolean;
  orderIsdModalVisible?: boolean;
  labelsModalVisible?: boolean;
  carDetailModalVisible?: boolean;
  feeDeductionVisible?: boolean;
  modifyFlightNoModalVisible?: boolean;
  BbkInsuranceDetailProps?: any;
  fetchDone: boolean;
  finalQueryIsFinish: boolean;
  fullSearchNum: number;
  showInsDetailModal: ({ visible }) => void;
  setModifyFlightNoModalVisible: ({
    visible,
    type,
  }: {
    visible: boolean;
    type?: string;
  }) => void;
  setIsdOrderChangeModalVisible: ({ visible }) => void;
  showFeeDeduction: ({ visible }) => void;
  setLabelsModalVisible: (data?: any) => void;
  setEasyLifeTagModalVisible: (data?: any) => void;
  setCarDetailModalVisible: (data?: any) => void;
  payCountDownTimeOut: ({ timeOut }) => void;
  setFetchDone: ({ fetchDone }) => void;
  authSafeRentRecord: (data?: any) => void;
  fromPage?: string;
  reset?: (data?: any) => void;
  queryOrderApiStatus?: number;
  setPhoneModalVisible: (data?: any) => void;
  phoneModalVisible: boolean; // @ts-ignore
  phoneModalType?: OrderDetailCons.CustomerPhoneModalType;
  phoneList: [];
  personPhoneModalVisible: boolean;
  setPersonPhoneModalVisible: (data?: any) => void;
  carRentalMustReadData?: any;
  goIsdInsurancePayment: (data?: any) => void;
  modalsVisible?: OrderModalsVisible;
  setOrderModalsVisible: (OrderModalsVisible?: any) => void;
  isShowRenewStatusByStorage: () => void;
  saveRenewalOrderStatus: () => void;
  queryOrderStatus: (data?: any) => void;
  queryQuestionnaire: (data: any) => void;
  saveQuestionnaire: (data: any) => void;
  setVocModalVisible: (data: any) => void;
  setAdvanceApplySign: (data: { sign: any }) => void;
  setPriceDetailModalVisible: (visible: boolean) => void;
  queryServiceProgress: (data: any) => void;
  urgeServiceProgress: (data: any) => void;
  setStorageCardsTitle: (data: any) => void;
  clearAdvanceCache: () => void;
  orderWaringInfo?: WarningListResponseType;
  restAssuredTag?: VendorTagType;
  orderStatusDesc?: string;
  carTags?: any;
  safeRent?: boolean;
  easyLifeTagModalVisible?: boolean;
  easyLifeTags?: any;
  modifyOrderWarnModalVisible?: boolean;
  invoiceDesc?: any;
  orderStatusCtrip?: string;
  renewTipLogData?: any;
  questionnaires?: any;
  vocModalVisible: boolean;
  driverInfo?: any;
  cancelRuleInfo?: any;
  orderBaseInfo?: any;
  extendedInfo?: any;
  ipollInfo?: any;
  orderPriceInfo?: any;
  vendorInfo?: any;
  appResponseMap?: any;
  freezeDepositExplain?: any;
  vehicleInfo?: any; // @ts-ignore
  directOpen?: OrderDetailCons.DirectOpen;
  directOpenSub?: string;
  link?: string;
  callBarDesc: string;
  priceDetailModalVisible: boolean;
  ehiModifyOrderModalVisible?: boolean;
  serviceProgressList?: Array<ServiceProgressDTO>;
  urgeServiceIds?: any;
  serviceIds?: string;
  refundPenaltyInfo: any;
  supportInfo?: any;
  storageCardsTitle?: Array<string>;
  nextStorageCardsTitle?: Array<string>;
  getListWarningInfo: (data) => void;
  fetchWarningInfoLoading: any;
  storeAttendant?: any;
  orderDetailConfirmModalVisible: boolean;
  openOrderDetailConfirmModal: () => void;
  closeOrderDetailConfirmModal: () => void;
  queryCommentSummary: () => void;
  isHideCancelButton: boolean;
  operationButtons: AllOperationsType[];
  orderEhiFreeDepositVisible?: boolean;
  depositPaymentModalAutoShow: boolean;
  ctripContinuePay: (data: any) => void;
  advanceReturnFeeInfo: any;
  advanceReturnFeeInfoByVendor: any;
  advanceRecord: any;
  orderPriceInfoFee?: any;
  orderDetailResponse?: any;
  isuranceBox?: any;
  isShowTravelLimit?: boolean;
  crossPolicy?: ITravelLimit;
  setTravelLimitSelectedResult?: (data: ISelectedItem[]) => void;
  isNewCancelRule: boolean;
  setBusinessLicenseVisible?: (data) => void;
  isSelfService?: boolean;
  depositDetailModalVisible?: boolean;
  setDepositDetailModalVisible?: (data) => void;
  limitPopVisible?: boolean;
  setLimitRulePopVisible?: (data) => void;
  isOrderDataByPhone: boolean;
  createPreFetch?: (data) => void;
  isEasyLife2024: boolean;
  addInstructData?: any;
  claimProcessModalData?: any;
  vehicleUseNotesInfo?: any;
  setFlightDelayRulesModalVisible?: (data) => void;
  policyList?: any;
  isOsdShowBottomButtons?: boolean; // 出境是否展示底部按钮
  isShowFulfillmentCard?: boolean; // 是否展示履约卡，包含订单状态和履约版本判断
  phoneNumberData?: PhoneNumberDataType;
  fulfillmentData: QueryOrderFulfillmentInfoResponseType;
  locationDatePopVisible?: boolean;
  isNewOsdModifyOrder?: boolean;
  osdModifyOrderNote?: QueryOsdModifyOrderNoteResponseType;
  queryOsdModifyOrderNote: () => void;
  setLocationAndDatePopIsShow: (data) => void;
  isOsdModifyNewOrder?: boolean;
  isPickUpInDoor?: boolean; // 是否送车上门
  logBaseInfo?: any;
  isShelves2?: boolean;
  phoneSurveyShowCount?: number; // 展示电话问卷弹窗的次数
  setPhoneSurveyShowCount?: (data) => void;
  setStoreSurveyCommit?: (data) => void;
}

interface CarServiceDetailModalProps {
  data?: Array<PackageDetailListType>;
}
interface OrderState extends IStateType {
  lang?: string;
  messages?: any;
  headerScroll?: boolean;
  refreshResult?: any;
  isShowCancelPolicyModal: boolean;
  vendorCallModal: boolean;
  warningTipsModalVisible?: boolean;
  warningTipsModalCotent?: Array<WarningDto>;
  addInstructModalVisible: boolean;
  isShowCancelPenaltyInfoModal: boolean;
  isShowPageOtherModule: boolean;
  isShowDepositIntro: boolean;
  isConsultProgressModal: boolean;
  confirmModalAnchor?: LayoutPartEnum;
  carServiceModalVisible: boolean;
  carServiceDetailModalProps: CarServiceDetailModalProps;
  carServiceDetailModalVisible: boolean;
  serviceClaimMoreVisible: boolean;
  couponModalVisible: boolean;
  isFuelTypeModalVisible: boolean;
  lessModalVisible: boolean;
  fuelType: string;
  osdPriceDetailModalVisible: boolean;
  osdFeeTitleExplainModalVisible: boolean;
  osdInsuranceVisible: boolean;
  anchorCategory: string;
  osdExcessIntroduceVisible: boolean;
  insuranceNoticeMustReadVisible: boolean;
  insuranceReminderEnglishVisible: boolean;
  englishContent: string;
  insuranceNotice: string;
  feeTitle: string;
  feeTitleExplain: string;
  isShowIsdAd: boolean;
  isShowOsdAd: boolean;
  materialModalVisible: boolean;
  depositRateDescriptionModalVisible: boolean;
  printVoucherUrl?: string;
  isVehicleStatusPolling: boolean;
  isExpandContractTracker?: boolean;
  contractTrackerRefreshValue?: number;
  isFulfillScrollEnd?: boolean;
  isShowBigImgContainer?: boolean;
  contactDoorStoreModalVisible?: boolean;
  contactDoorStoreModalTabId?: number;
  noLimitModalVisible: boolean;
  noLimitDesc: string;
}

const defaultOrderQueryIntervalTime = 1000;
const commonOrderQueryIntervalTime = 8000;
export default class OrderDetail extends CPage<OrderPropsType, OrderState> {
  scroller: any;

  mainscroller: any;

  fulfillInnerScroll: any;

  fulfillmentCardScrollView: any; // 履约卡片滚动组件

  fulfillOrderPageSnapShot: any; // 履约模式下详情页快照

  fulfillmentCardSnapShot: any; // 履约卡片快照

  searchPanelModalRef: any; // 出境修改订单弹层

  height: any;

  refreshControl: any;

  inUploadImage: boolean;

  isUploadContractTrackerImage: boolean;

  originAppType: string;

  isKeyboardAwareScrollView: boolean;

  keyboardWillShowListener: any;

  keyboardWillHideListener: any;

  timerQueryOrderId = null;

  domsHeiObj = {};

  scrollToDomEnd = false;

  hasPageExposure = false;

  insConfirmData = {};

  // url 带参 dragBack=false
  // 禁用 native 侧滑
  disableDragBackFromUrl = false;

  contentHeight: number;

  orderStatusHeight: number;

  directScrollFinished: boolean;

  isKeyboardShow: boolean;

  isLogin: boolean;

  hasLeavePage: boolean;

  orderQueryIntervalTime: number;

  orderQueryOnCancelMaxCount: number;

  orderQueryOnCancelCount: number;

  orderStatusCtrip: string;

  triggerScreenSnapShot: any; // 截图上传触发

  triggerContractTrackerSnapShot: any; // 触发履约卡片快照上传

  handelFulfillReachBottom: any; // 履约卡滚动触底

  // timeout
  tempTimer1 = null;

  tempTimer2 = null;

  imagePrefetchTimer = null;

  contractTrackerSnapShotTimer = null;

  timeIsShowPage = null;

  sendOnScrollEvent = null;

  timingQueryOrderTime = new Date().getTime();

  claimMoreModalRef: any;

  isFulfillmentReachBottom: boolean;

  bottomSheetDargRef: any;

  constructor(props) {
    super(props);
    this.state = {
      headerScroll: false,
      refreshResult: null,
      isShowCancelPolicyModal: false,
      vendorCallModal: false,
      warningTipsModalVisible: false,
      warningTipsModalCotent: [],
      addInstructModalVisible: false,
      isShowCancelPenaltyInfoModal: false,
      isShowPageOtherModule: false,
      isShowDepositIntro: false,
      isConsultProgressModal: false,
      carServiceModalVisible: false,
      carServiceDetailModalProps: {},
      carServiceDetailModalVisible: false,
      serviceClaimMoreVisible: false,
      couponModalVisible: false,
      isFuelTypeModalVisible: false,
      lessModalVisible: false,
      fuelType: '',
      osdPriceDetailModalVisible: false,
      osdFeeTitleExplainModalVisible: false,
      osdInsuranceVisible: false,
      anchorCategory: '',
      osdExcessIntroduceVisible: false,
      insuranceNoticeMustReadVisible: false,
      insuranceReminderEnglishVisible: false,
      englishContent: '',
      insuranceNotice: '',
      feeTitle: '',
      feeTitleExplain: '',
      isShowIsdAd: false,
      isShowOsdAd: false,
      materialModalVisible: false,
      depositRateDescriptionModalVisible: false,
      isVehicleStatusPolling: true,
      isExpandContractTracker: false,
      contractTrackerRefreshValue: 0,
      isFulfillScrollEnd: false,
      isShowBigImgContainer: false,
      contactDoorStoreModalVisible: false,
      contactDoorStoreModalTabId: null,
      noLimitModalVisible: false,
      noLimitDesc: '',
    };
    // this.mainscroller = React.createRef();
    this.bottomSheetDargRef = React.createRef();
    this.height = 0;
    this.inUploadImage = false;
    this.isUploadContractTrackerImage = false; // 履约卡片快照正在上传
    this.originAppType = AppContext.CarEnv.appType;
    this.isKeyboardAwareScrollView = true; // 滚动组件是否为KeyboardAwareScrollView
    this.keyboardWillShowListener = Keyboard.addListener(
      'keyboardWillShow',
      this.keyboardWillShow.bind(this),
    );
    this.keyboardWillHideListener = Keyboard.addListener(
      'keyboardWillHide',
      this.keyboardWillHide.bind(this),
    );
    this.contentHeight = 0;
    this.orderStatusHeight = 0;
    this.isKeyboardShow = false;
    this.directScrollFinished = false;
    this.isLogin = false;
    this.hasLeavePage = false;
    this.orderQueryIntervalTime = defaultOrderQueryIntervalTime; // 轮询时间，默认为1s，解决进入订单页时状态更新过慢
    this.orderQueryOnCancelMaxCount = 3; // 取消订单最大轮询次数
    this.orderQueryOnCancelCount = 0;
    this.isFulfillmentReachBottom = false;
    this.triggerScreenSnapShot = lodashDebounce(this.updateSnapShotData, 1000);
    this.triggerContractTrackerSnapShot = lodashDebounce(
      this.updateContractTrackerSnapShotData,
      1000,
    );
    // 增加订详滚动监听，滚动效果结束，取消监听
    this.sendOnScrollEvent = lodashDebounce(this.handleScrollEvent, 1000, {
      leading: true,
      trailing: false,
    });

    removeOrderDetailStatusData();
    // 同步售前已缓存的AB实验结果
    GetABCache.syncCacheAb();
    initializeABOrderDetailPage();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Order.ID;
  }

  async componentDidMount() {
    super.componentDidMount();
    this.isLogin = User.isLoginSync();
    if (this.isLogin) {
      this.timingQueryOrder(true);
    } else {
      User.toLogin()
        .then(res => {
          if (res) {
            this.timingQueryOrder(true);
          } else {
            this.onLoadError();
          }
        })
        .catch(() => {
          this.onLoadError();
        });
    }
    // 注册页面名称供H5升级保险详情页面回退使用
    CRNPage.registerPage('ODetailContainer', () => {
      CarLog.LogCode({ name: '注册ODetailContainer页面' });
      this.timingQueryOrder();
    });
    CRNPage.registerPage(Channel.getPageId().Order.EN, () => {});
    this.emitEvent();
    this.setStorageCardsTitleFromStorageToRedux();
    this.handlePathUrlOpen();
    // 调用出境修改订单提示接口
    if (Utils.isCtripOsd()) {
      this.props.queryOsdModifyOrderNote();
    }
    if (GetABCache.isStoreInfoSurvey()) {
      const { orderId, setPhoneSurveyShowCount } = this.props;
      initialPhoneSurvey(orderId, setPhoneSurveyShowCount);
    }
  }

  setStorageCardsTitleFromStorageToRedux = async () => {
    const { orderId, setStorageCardsTitle } = this.props;
    const storageCardsTitle = await CarStorage.loadAsync(
      `${StorageKey.CAR_ORDER_DETAIL_CARDS}_${orderId}`,
    );
    let storageCardsTitleArr = [];
    try {
      storageCardsTitleArr = JSON.parse(storageCardsTitle) || [];
    } catch (e) {
      storageCardsTitleArr = [];
    }
    setStorageCardsTitle(storageCardsTitleArr);
  };

  setNextStorageCardsTitleFromReduxToStorage = () => {
    const { nextStorageCardsTitle, orderId } = this.props;
    CarStorage.save(
      `${StorageKey.CAR_ORDER_DETAIL_CARDS}_${orderId}`,
      nextStorageCardsTitle,
      '30d',
    );
  };

  emitEvent = () => {
    const {
      goIsdInsurancePayment,
      setOrderModalsVisible,
      orderId,
      setStoreSurveyCommit,
    } = this.props;
    EventHelper.addEventListener(EventName.orderInsurancePayCallback, data => {
      // 创建保险订单，并支付
      const status = lodashGet(data, 'status');
      const selectedInsuranceList =
        lodashGet(data, 'data.selectedInsuranceList') || [];
      if (
        (status === InsCallStatus.submit || status === InsCallStatus.cancel) &&
        selectedInsuranceList.length > 0
      ) {
        goIsdInsurancePayment(data);
      }
    });

    EventHelper.addEventListener(
      EventName.insConfirmBackToOrderDetail,
      data => {
        const status = lodashGet(data, 'status');
        if (
          status === InsCallStatus.submit ||
          status === InsCallStatus.cancel
        ) {
          const { ctripContinuePay } = this.props;
          ctripContinuePay({ confirmInsuranceData: data });
        }
      },
    );

    EventHelper.addEventListener(EventName.orderBtnsClickFromPhoneCheck, () => {
      setOrderModalsVisible({
        confirmModal: {
          visible: true,
          data: {
            get title() {
              return '当前账号无操作权限，请登录下单账号后操作';
            },
            btns: [
              {
                get name() {
                  return '知道了';
                },
                isPrimary: true,
                onPress: () => {
                  setOrderModalsVisible({
                    confirmModal: {
                      visible: false,
                    },
                  });
                },
              },
            ],
          },
        },
      });
    });
    if (GetABCache.isStoreInfoSurvey()) {
      // 添加提交反馈事件监听
      EventHelper.addEventListener(EventName.storeSurveyCommit, data => {
        const { params } = data || {};
        if (`${params?.[0]?.orderId}` === `${orderId}`) {
          setStoreSurveyCommit(true);
        }
      });
    }
  };

  scrollToPosition = async (y: number) => {
    const { isShowFulfillmentCard } = this.props;
    if (isShowFulfillmentCard) {
      this.closeFulfillmentCard();
      await new Promise(resolve => setTimeout(resolve, 500));
      this.handleMainscrollerScroll(y);
    } else {
      this.handleMainscrollerScroll(y + this.orderStatusHeight + getPixel(50));
    }
  };

  onDirectOpen = () => {
    const {
      directOpen,
      directOpenSub = '',
      orderStatusCtrip,
      setPriceDetailModalVisible,
      setCarDetailModalVisible,
      setDepositDetailModalVisible,
      setOrderModalsVisible,
    } = this.props;

    const delayTime = isAndroid ? 800 : 200;

    if (Number(directOpen) === OrderDetailTextsV2.DirectOpen.PickUpMaterials) {
      this.push(Channel.getPageId().Instructions.EN, {
        instructionsAnchorFromLastPage:
          orderStatusCtrip === OrderDetailCons.OrderStatusCtrip.IN_SERVICE
            ? 'driving'
            : 'before',
      });
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.FeeDetail) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          setPriceDetailModalVisible(true);
        }, delayTime);
      } else {
        setTimeout(() => {
          this.showOsdPriceDetailModal();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.CancelPolicy) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.switchCancelPolicyModal(true);
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.CarDetail) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          setCarDetailModalVisible(true);
        }, delayTime);
      }
    } else if (
      directOpen === OrderDetailTextsV2.DirectOpen.AdvanceReturnRecord
    ) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.showAdvanceReturnModal();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.renewListRecord) {
      setTimeout(() => {
        this.push('RenewList');
      }, delayTime);
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.DepositFree) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          setOrderModalsVisible({ depositPaymentModal: { visible: true } });
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.ServiceClaimMore) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.onPressServiceClaimMore();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.DepositDetail) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          setDepositDetailModalVisible({ visible: true });
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.LimitRule) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.showLimitPop();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.OrderRefundDetail) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.showOrderRefundDetailModal();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.Reviews) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.openConfirmModal(LayoutPartEnum.Reviews);
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.Attention) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.openConfirmModal(LayoutPartEnum.Attention);
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.EasyLife) {
      if (Utils.isCtripIsd()) {
        setTimeout(() => {
          this.showEasylifeModal();
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.SupplementList) {
      if (Utils.isCtripIsd()) {
        // 默认进入3, 违章tab
        setTimeout(() => {
          AppContext.PageInstance.push('SupplementList', {
            type: directOpenSub || '3',
          });
        }, delayTime);
      }
    } else if (directOpen === OrderDetailTextsV2.DirectOpen.OsdInsurance) {
      if (Utils.isCtripOsd()) {
        if (directOpenSub === DirectOpenSub.first) {
          // 打开保障服务弹层, 定位到"套餐构成"tab
          // showOsdInsuranceModal的第一个参数没有用到, 所以传缺省值, 下同
          setTimeout(() => {
            this.showOsdInsuranceModal(
              undefined,
              InsuranceAnchorCategory.Insurance,
            );
          }, delayTime);
        } else if (directOpenSub === DirectOpenSub.second) {
          // 打开保障服务弹层, 定位到"保障详情"tab
          setTimeout(() => {
            this.showOsdInsuranceModal(
              undefined,
              InsuranceAnchorCategory.insuranceDetail,
            );
          }, delayTime);
        } else if (directOpenSub === DirectOpenSub.third) {
          // 打开保障服务弹层, 定位到"理赔流程"tab
          setTimeout(() => {
            this.showOsdInsuranceModal(
              undefined,
              InsuranceAnchorCategory.insuranceClaim,
            );
          }, delayTime);
        }
      }
    }
  };

  handlePathUrlOpen = () => {
    const { directOpen, link } = this.props;
    if (Number(directOpen) === OrderDetailTextsV2.DirectOpen.Link && link) {
      xRouter.navigateTo({ url: decodeURIComponent(link) });
    }
  };

  setOrderIntervalTime = ({
    orderStatusCtrip,
    isQuickInterval,
  }: {
    orderStatusCtrip?: string;
    isQuickInterval?: boolean;
  }) => {
    if (isQuickInterval) {
      this.orderQueryIntervalTime = defaultOrderQueryIntervalTime;
      return;
    }

    // 订单中间状态缩短轮询时间
    const interval = [
      `${OrderDetailCons.OrderStatusCtrip.PROCESSING}`,
      `${OrderDetailCons.OrderStatusCtrip.CANCELLING}`,
      `${OrderDetailCons.OrderStatusCtrip.PAYING}`,
    ].includes(orderStatusCtrip)
      ? defaultOrderQueryIntervalTime
      : commonOrderQueryIntervalTime;

    if (this.orderQueryIntervalTime !== interval) {
      this.orderQueryIntervalTime = interval;
    }
  };

  closeFulfillmentCard() {
    this.bottomSheetDargRef?.current?.closeBottomSheet();
  }

  onPageReady() {
    this.performanceMonitor.responseMap = lodashGet(
      this.props,
      'appResponseMap',
    );
    this.performanceMonitor.interactiveStart = new Date();
    this.performanceMonitor.isLogin = this.isLogin;
    this.performanceMonitor.hasLeavePage = this.hasLeavePage;
    this.performanceMonitor.submit();
    this.performanceMonitor.contentShowStart =
      this.performanceMonitor.interactiveStart;
    this.performanceMonitor.submitTTI();
    this.setShowPageOtherModule();
  }

  componentWillUnmount() {
    this.clearTimingQueryOrder();
    this.setNextStorageCardsTitleFromReduxToStorage();
    if (this.tempTimer1) {
      clearTimeout(this.tempTimer1);
    }
    if (this.tempTimer2) {
      clearTimeout(this.tempTimer2);
    }
    if (this.imagePrefetchTimer) {
      clearTimeout(this.imagePrefetchTimer);
    }
    if (this.contractTrackerSnapShotTimer) {
      clearTimeout(this.contractTrackerSnapShotTimer);
    }
    if (this.timeIsShowPage) {
      clearTimeout(this.timeIsShowPage);
    }
    if (scoreSelectTimer) {
      clearTimeout(scoreSelectTimer);
    }
    if (toUpgradeInsTimer) {
      clearTimeout(toUpgradeInsTimer);
    }

    Event.removeEventListener(EventName.orderInsurancePayCallback);
    Event.removeEventListener(EventName.orderBtnsClickFromPhoneCheck);
    Event.removeEventListener(EventName.insConfirmBackToOrderDetail);
    if (GetABCache.isStoreInfoSurvey()) {
      Event.removeEventListener(EventName.storeSurveyCommit);
    }
    this.props.reset();
    // setTimeout(() => {
    //   const { reset } = this.props;
    //   reset();
    // }, 0);
  }

  refreshContractTracker() {
    this.setState({
      contractTrackerRefreshValue: new Date().getTime(),
    });
  }

  pageDidAppear() {
    super.pageDidAppear();
    // 如果触发pageDidAppear是应为关闭了营业执照的大图容器则不触发快轮训
    if (!this.state.isShowBigImgContainer) {
      this.setOrderIntervalTime({ isQuickInterval: true });
    }
    // 拨打电话后回到订单详情页触发展示拨打电话弹窗
    if (
      this.props.config?.isSurvey &&
      GetABCache.isStoreInfoSurvey() &&
      this.props.phoneSurveyShowCount === 0
    ) {
      this.showSurveyModal();
    }

    if (this.pageAppearCount > 1) {
      this.timingQueryOrder();
      this.initialServiceProgress();
      if (!this.state.isShowBigImgContainer) {
        this.fetchOrderFn();
        this.refreshContractTracker();
      } else {
        // 如果触发pageDidAppear是应为关闭了营业执照的大图容器则不触发请求列表
        this.handlChangeIsShowBigImgContainer(false);
      }
      // 调用出境修改订单提示接口
      if (Utils.isCtripOsd() && this.props.isNewOsdModifyOrder) {
        this.props.queryOsdModifyOrderNote();
      }
    }
    // 判断续租订单状态是否改变
    const { isShowRenewStatusByStorage } = this.props;
    isShowRenewStatusByStorage();
    // 清除提前还车页面缓存
    const { clearAdvanceCache } = this.props;
    clearAdvanceCache();
    // 用车助手车辆状态轮询开启
    this.setState({ isVehicleStatusPolling: true });
  }

  onDidFocus() {
    this.disableNativeDragBack();
  }

  pageDidDisappear() {
    this.hasLeavePage = true;
    const { orderId, saveRenewalOrderStatus, setAdvanceApplySign } = this.props;
    super.pageDidDisappear({ orderId });
    this.clearTimingQueryOrder();
    saveRenewalOrderStatus();
    // 清除提前还车状态
    setAdvanceApplySign({ sign: null });
    // 用车助手车辆状态轮询关闭
    this.setState({ isVehicleStatusPolling: false });
  }

  disableNativeDragBack() {
    // 是否禁用侧滑
    const { dragBack } = this.props.app.urlQuery;
    if (dragBack === 'false') {
      this.disableDragBackFromUrl = true;
      this.disableDragBack();
    }
  }

  onPageExposure() {
    if (!this.hasPageExposure) {
      const { orderId, orderStatusDesc } = this.props;
      CarLog.LogTrace({
        key: LogKey.c_car_trace_order_page_exposure_222025,
        info: {
          orderId,
          pageId: this.getPageId(),
          orderStatus: orderStatusDesc,
        },
      });
      this.hasPageExposure = true;
    }
  }

  /* eslint-disable camelcase */
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { queryOrderApiStatus, depositPaymentModalAutoShow } = this.props;
    const { isShowPageOtherModule } = this.state;
    if (
      queryOrderApiStatus === QueryOrderApiStatusType.unstart &&
      nextProps.queryOrderApiStatus !== QueryOrderApiStatusType.unstart
    ) {
      this.performanceMonitor.receiveResStart = new Date();
    }
    this.setOrderIntervalTime({ orderStatusCtrip: nextProps.orderStatusCtrip });
    // 当订单状态发生了变化，切其余模块加载完成,查询当前订单状态下的voc调查问题
    if (
      this.orderStatusCtrip !== nextProps.orderStatusCtrip &&
      isShowPageOtherModule
    ) {
      this.orderStatusCtrip = nextProps.orderStatusCtrip;
      this.initVocModal(nextProps.orderStatusCtrip);
    }

    if (
      depositPaymentModalAutoShow !== nextProps.depositPaymentModalAutoShow &&
      nextProps.depositPaymentModalAutoShow
    ) {
      this.initDepositPaymentModal();
    }
  }

  async initDepositPaymentModal() {
    try {
      const { orderId, setOrderModalsVisible } = this.props;
      const res =
        (await CarStorage.loadAsync(
          StorageKey.CAR_ORDERDETAIL_DEPOSITPAYMENT_MODAL,
        )) || '{}';
      const result = JSON.parse(res);
      const storeKey = `${orderId}`;
      if (!result[storeKey]) {
        setOrderModalsVisible(
          Utils.isCtripIsd()
            ? {
                depositPaymentModal: {
                  visible: true,
                },
              }
            : {
                depositPaymentModalOSD: {
                  visible: true,
                },
              },
        );
        result[storeKey] = true;
        CarStorage.save(
          StorageKey.CAR_ORDERDETAIL_DEPOSITPAYMENT_MODAL,
          result,
          '30d',
        );
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  async initVocModal(orderStatusCtrip) {
    try {
      const { orderId, queryQuestionnaire = Utils.noop } = this.props;
      const res =
        (await CarStorage.loadAsync(StorageKey.CAR_ORDERDETAIL_VOC_MODAL)) ||
        '{}';
      const result = JSON.parse(res);
      const storeKey = `${orderId}_${orderStatusCtrip}`;
      if (!result[storeKey]) {
        // 初始化voc调查弹窗数据
        queryQuestionnaire({ orderId });
        result[storeKey] = true;
        CarStorage.save(StorageKey.CAR_ORDERDETAIL_VOC_MODAL, result, '30d');
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  async componentDidUpdate(prevProps, preState) {
    const {
      queryOrderApiStatus,
      modifyOrderWarnModalVisible,
      vocModalVisible,
      orderEhiFreeDepositVisible,
      finalQueryIsFinish,
      isSelfService,
      fulfillmentData,
    } = this.props;
    const {
      modifyOrderWarnModalVisible: preModifyOrderWarnModalVisible,
      vocModalVisible: preVocModalVisible,
      orderEhiFreeDepositVisible: preOrderEhiFreeDepositVisible,
      fulfillmentData: preFulfillmentData,
    } = prevProps;
    const { isShowPageOtherModule } = this.state;

    if (
      prevProps.queryOrderApiStatus === QueryOrderApiStatusType.unstart &&
      queryOrderApiStatus !== QueryOrderApiStatusType.unstart
    ) {
      this.onPageReady();
    }

    if (
      (!preModifyOrderWarnModalVisible && modifyOrderWarnModalVisible) ||
      (!preVocModalVisible && vocModalVisible) ||
      (!preOrderEhiFreeDepositVisible && orderEhiFreeDepositVisible)
    ) {
      this.disableDragBack();
    } else if (
      (preModifyOrderWarnModalVisible && !modifyOrderWarnModalVisible) ||
      (preVocModalVisible && !vocModalVisible) ||
      (preOrderEhiFreeDepositVisible && !orderEhiFreeDepositVisible)
    ) {
      this.enableDragBack();
    }

    if (
      prevProps.queryOrderApiStatus === queryOrderApiStatus &&
      queryOrderApiStatus !== QueryOrderApiStatusType.unstart &&
      finalQueryIsFinish
    ) {
      this.onPageExposure();
      // 有一些未知原因会出现不触发onPageReady的场景，而因isShowPageOtherModule没有更新，导致第二屏模块不会渲染
      // 固在第一屏模块渲染完后，加一层打底处理，保证第二屏的模块可以正常渲染
      this.setShowPageOtherModule();
    }

    // 当加载完第二屏数据
    if (
      isShowPageOtherModule !== preState.isShowPageOtherModule &&
      isShowPageOtherModule
    ) {
      this.initialServiceProgress();
      this.initialPreFetch();
      if (isSelfService && !this.imagePrefetchTimer) {
        this.imagePrefetchTimer = setTimeout(() => {
          Utils.imagePrefetch(selfServicePrefetchImages);
        }, 100);
      }
    }

    // 履约卡片快照
    if (preFulfillmentData?.nodeHash !== fulfillmentData?.nodeHash) {
      this.contractTrackerSnapShotTimer = setTimeout(() => {
        this.triggerContractTrackerSnapShot();
      }, 1000);
    }
  }

  updateSnapShotData = async () => {
    const { fetchDone, orderId, orderStatus, isShowFulfillmentCard } =
      this.props;
    const { isShowPageOtherModule } = this.state;
    const pageSnapRef = isShowFulfillmentCard
      ? this.fulfillOrderPageSnapShot
      : this.mainscroller;
    if (
      fetchDone &&
      isShowPageOtherModule &&
      pageSnapRef &&
      !this.inUploadImage
    ) {
      this.inUploadImage = true;
      await UpdateSnapShotData({
        orderId,
        orderStatus,
        mainScrollViewRef: pageSnapRef,
      });
      this.inUploadImage = false;
      this.props.setFetchDone({ fetchDone: false });
    }
  };

  // 上传履约卡片快照
  updateContractTrackerSnapShotData = async () => {
    const { fulfillmentData, orderId, orderStatusCtrip } = this.props;
    if (
      !this.isUploadContractTrackerImage &&
      this.fulfillmentCardSnapShot &&
      fulfillmentData?.fulfillmentNodeList?.length > 0
    ) {
      this.isUploadContractTrackerImage = true;
      await UpdateContractTrackerSnapShotData({
        orderId,
        orderStatusCtrip,
        hashCode: fulfillmentData?.nodeHash,
        mainScrollViewRef: this.fulfillmentCardSnapShot,
      });
      this.isUploadContractTrackerImage = false;
    }
  };

  fetchOrderFn = () => {
    const { fetchOrder2, orderId } = this.props;
    fetchOrder2({ orderId });
  };

  payCountDownTimeOut = () => {
    const { payCountDownTimeOut } = this.props;
    payCountDownTimeOut({ timeOut: true });
    this.fetchOrderFn();
  };

  showConsultProgressModal = () => {
    const { orderId, serviceIds } = this.props;
    CarLog.LogCode({
      name: '点击_事件咨询进度',

      info: {
        orderId: `${orderId}`,
        eventId: serviceIds,
        pageId: this.getPageId(),
      },
    });

    this.setState({ isConsultProgressModal: true });
  };

  hideConsultProgressModal = () => {
    this.setState({ isConsultProgressModal: false });
  };

  onUrgeServiceProgress = data => {
    const { urgeServiceProgress } = this.props;
    const { orderId, eventId } = data;
    CarLog.LogCode({
      name: '点击_事件咨询进度_催处理按钮',

      info: {
        orderId: `${orderId}`,
        eventId,
        pageId: this.getPageId(),
      },
    });
    urgeServiceProgress(data);
  };

  initialServiceProgress = () => {
    const { queryServiceProgress = Utils.noop, orderId } = this.props;
    queryServiceProgress({ orderId });
  };

  // 预请求接口
  initialPreFetch = () => {
    const { createPreFetch = Utils.noop, orderId } = this.props;
    createPreFetch({
      orderId,
    });
  };

  showCancelPenaltyInfoModal = () => {
    this.setState({ isShowCancelPenaltyInfoModal: true });
  };

  hideCancelPenaltyInfoModal = () => {
    this.setState({ isShowCancelPenaltyInfoModal: false });
  };

  showFulfillmentModifyModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ fulfillmentModifyModal: { visible: true } });
  };

  showPhoneModal = type => {
    const { setPhoneModalVisible = Utils.noop } = this.props;
    setPhoneModalVisible({
      visible: true,
      phoneModalType: type || OrderDetailCons.CustomerPhoneModalType.Phone,
    });
  };

  timingQueryOrder = (isFetchStatic?: boolean) => {
    const { queryOrderStatus, orderId, orderStatusCtrip } = this.props;

    // 已取消订单执行3次请求后不再轮询
    if (orderStatusCtrip === OrderDetailCons.OrderStatusCtrip.CANCELLED) {
      if (this.orderQueryOnCancelCount <= this.orderQueryOnCancelMaxCount) {
        this.orderQueryOnCancelCount += 1;
      } else {
        return;
      }
    }

    if (this.timerQueryOrderId) return;
    queryOrderStatus({ orderId, isFetchStatic });
    this.timerQueryOrderId = setTimeout(() => {
      if (this.timerQueryOrderId) {
        this.clearTimingQueryOrder();
      }
      this.timingQueryOrderTime = new Date().getTime();
      this.timingQueryOrder();
    }, this.orderQueryIntervalTime);
  };

  clearTimingQueryOrder = () => {
    clearTimeout(this.timerQueryOrderId);
    this.timerQueryOrderId = null;
  };

  backCarHome = pageName => {
    CRNPage.getRegisteredPageList(res => {
      if (res && res.includes(pageName)) {
        CRNPage.popToPage(pageName);
        CarLog.LogCode({ name: pageName });
      } else {
        this.backToLastPage();
      }
    });
  };

  backToLastPage = () => {
    this.pop();
    CarLog.LogCode({ name: '点击_订单详情页_后退' });
  };

  pageGoBack = () => {
    const { from, fromUrl } = this.props.app.urlQuery;
    const { fromPage } = this.props;
    if (from === Platform.ORDER_BACK_PARAMS.book) {
      this.backCarHome('TripCarHome');
    } else if (fromUrl) {
      this.backCarHome(fromUrl);
    } else if (fromPage) {
      this.pop(fromPage);
    } else {
      this.backToLastPage();
    }
    EventHelper.sendEvent(EventName.orderBack2Home, {});
    this.setNextStorageCardsTitleFromReduxToStorage();
  };

  // 处理头部标题副标题切换
  handleHeadTitleOnScroll = y => {
    if (
      (y > FIX_OFFESTTOP || y === FIX_OFFESTTOP) &&
      !this.state.headerScroll &&
      y !== 0
    ) {
      this.setState({
        headerScroll: true,
      });
    }
    if (y < FIX_OFFESTTOP && !!this.state.headerScroll && y !== 0) {
      this.setState({
        headerScroll: false,
      });
    }
  };

  headerEvents = event => {
    const { y } = event.nativeEvent.contentOffset;
    this.height = y;
    this.handleHeadTitleOnScroll(this.height);
    // @ts-ignore
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    const distance =
      contentSize.height - contentOffset.y - layoutMeasurement.height;
    const { isShowFulfillmentCard } = this.props;
    if (isShowFulfillmentCard) {
      this.setState({
        isFulfillScrollEnd: distance <= 1,
      });
    }
  };

  switchCancelPolicyModal = (value: boolean) => {
    this.setState({
      isShowCancelPolicyModal: value,
    });
  };

  hideCancelPolicyModal = () => {
    this.switchCancelPolicyModal(false);
  };

  hideReviewUnopenedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ reviewUnopenedModal: { visible: false } });
  };

  showEasylifeModal = () => {
    const { setEasyLifeTagModalVisible, orderId, orderStatusDesc } = this.props;
    setEasyLifeTagModalVisible(true);
    CarLog.LogCode({
      name: '点击_订单详情页_无忧租详情',

      info: {
        orderId,
        orderStatus: orderStatusDesc,
      },
    });
  };

  goCredentialEntry = () => {
    CarLog.LogCode({ name: '点击_订单详情页_我的凭据详情' });
    this.push(Channel.getPageId().Credentials.EN);
  };

  closePhoneModal = () => {
    const { setPhoneModalVisible } = this.props;
    setPhoneModalVisible({
      visible: false,
    });
  };

  closePersonPhoneModal = () => {
    const { setPersonPhoneModalVisible } = this.props;
    setPersonPhoneModalVisible({
      visible: false,
    });
  };

  onAddInstructFormPress = () => {
    this.setState({
      addInstructModalVisible: true,
    });

    if (Utils.isCtripIsd()) {
      const { orderId, orderStatus } = this.props;
      CarLog.LogCode({
        name: '点击_订单详情页_额外驾驶员说明',

        info: {
          orderId,
          orderStatus,
        },
      });
    }
  };

  // 关闭额外驾驶员说明模态框
  onCloseAddInstructModal = () => {
    this.setState({
      addInstructModalVisible: false,
    });
  };

  onPullRelease = () => {
    this.fetchOrderFn();
    this.initialServiceProgress();
    clearTimeout(this.tempTimer1);
    this.tempTimer1 = setTimeout(() => {
      this.setState({
        refreshResult: true,
      });
    }, 2500);
    clearTimeout(this.tempTimer2);
    this.tempTimer2 = setTimeout(() => {
      this.setState({
        refreshResult: null,
      });
    }, 4000);
    // 调用出境修改订单提示接口
    if (Utils.isCtripOsd() && this.props.isNewOsdModifyOrder) {
      this.props.queryOsdModifyOrderNote();
    }
  };

  goToAccident = () => {
    const forwardurl = `${Url.ISD_CRN_URL}&initialPage=isdinsuranceagreement&type=1`;
    xRouter.navigateTo({ url: forwardurl });
    CarLog.LogCode({ name: '点击_详情页_事故处理流程' });
  };

  gotoPolicy = (policySelectedId, labName) => {
    const { orderId, orderStatus, logBaseInfo } = this.props;
    const param = {
      policySelectedId,
      labName,
      getDataFn: getStorePolicyProps,
      logBaseInfo,
    };
    CarLog.LogCode({
      name: '点击_订单详情页_全部政策入口',

      info: {
        orderId,
        orderStatus,
        type: labName,
      },
    });
    AppContext.PageInstance.push(Channel.getPageId().Policy.EN, param);
  };

  showModifyFlightNoModal = type => {
    this.props.setModifyFlightNoModalVisible({ visible: true, type });
  };

  showWarningTipsModal = content => {
    this.setState({
      warningTipsModalVisible: true,
      warningTipsModalCotent: content,
    });
  };

  closeWarningTipsModal = () => {
    this.setState({
      warningTipsModalVisible: false,
    });
  };

  onPressServiceProviderBar = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessLicenseModal: {
        visible: true,
      },
    });
  };

  getListWarningInfo = () => {
    const { getListWarningInfo } = this.props;
    getListWarningInfo({ pageIndexId: PageIndexId.Order });
  };

  renderTipView = orderBaseInfo => {
    const { orderStatus, orderTip = {} } = orderBaseInfo || {};
    const { orderWaringInfo, fetchWarningInfoLoading, isOsdModifyNewOrder } =
      this.props;
    const hasWarningInfo = orderWaringInfo?.warningDtos?.length > 0;
    const showContinuePayTips =
      !Utils.isCtripIsd() &&
      orderStatus === OrderDetailCons.ORDER_STATUS.Unsubmitted &&
      orderTip?.urgentWarning &&
      !isOsdModifyNewOrder; // 出境修改订单新单不展示

    return (
      <>
        {!!showContinuePayTips && (
          <ContinuePayTips
            continuePayTip={orderBaseInfo.orderTip.urgentWarning}
          />
        )}
        {!Utils.isCtripIsd() && !!hasWarningInfo && (
          <BbkComponentWarningTips
            fetchWarningInfoLoading={fetchWarningInfoLoading}
            waringInfo={orderWaringInfo}
            onClick={this.showWarningTipsModal}
            onRetry={this.getListWarningInfo}
          />
        )}
      </>
    );
  };

  showErr = (isFail: any, fn?: () => void) => {
    const errorMsg = '系统繁忙';
    if (isFail) {
      BbkToast.show(errorMsg, 3, () => {
        if (fn && typeof fn === 'function') fn();
      });
    }
  };

  onLoadError = () => {
    // 渲染异常页面
    this.showErr(true, () => {
      this.pageGoBack();
    });
  };

  onBackAndroid() {
    const {
      orderIsdModalVisible,
      feeDeductionVisible,
      modifyFlightNoModalVisible,
      BbkInsuranceDetailProps = {},
      priceDetailModalVisible,

      setIsdOrderChangeModalVisible,
      showFeeDeduction,
      showInsDetailModal,
      setModifyFlightNoModalVisible,
      setPriceDetailModalVisible,
      depositDetailModalVisible,
      setDepositDetailModalVisible,
      modalsVisible,
      carDetailModalVisible,
      setCarDetailModalVisible,
      orderDetailConfirmModalVisible,
      closeOrderDetailConfirmModal,
      limitPopVisible,
      setLimitRulePopVisible,
      locationDatePopVisible,
      setLocationAndDatePopIsShow,
    } = this.props;
    const {
      warningTipsModalVisible,
      isShowCancelPolicyModal,
      addInstructModalVisible,
      carServiceDetailModalVisible,
      serviceClaimMoreVisible,
    } = this.state;
    if (orderIsdModalVisible) {
      setIsdOrderChangeModalVisible({ visible: false });
    } else if (feeDeductionVisible) {
      showFeeDeduction({ visible: false });
    } else if (modifyFlightNoModalVisible) {
      setModifyFlightNoModalVisible({ visible: false });
    } else if (BbkInsuranceDetailProps.visible) {
      showInsDetailModal({ visible: false });
    } else if (warningTipsModalVisible) {
      this.setState({ warningTipsModalVisible: false });
    } else if (priceDetailModalVisible) {
      setPriceDetailModalVisible(false);
    } else if (depositDetailModalVisible) {
      // 关闭押金明细弹层
      setDepositDetailModalVisible(false);
    } else if (modalsVisible?.refundDetailModal?.visible) {
      // 关闭退款进度弹层
      this.hideOrderRefundDetailModal();
    } else if (isShowCancelPolicyModal) {
      // 关闭取消政策弹层
      this.hideCancelPolicyModal();
    } else if (carDetailModalVisible) {
      // 关闭车辆详情弹层
      setCarDetailModalVisible(false);
    } else if (limitPopVisible) {
      // 关闭限行规则弹层
      setLimitRulePopVisible(false);
    } else if (orderDetailConfirmModalVisible) {
      // 关闭门店详情弹层
      closeOrderDetailConfirmModal();
    } else if (addInstructModalVisible) {
      // 关闭增加额外驾驶员弹层
      this.onCloseAddInstructModal();
    } else if (carServiceDetailModalVisible) {
      // 关闭车行服务详情弹层
      this.hideCarServiceDetailModal();
    } else if (serviceClaimMoreVisible) {
      // 关闭查看理赔要求及须知弹层
      this.hideServiceClaimMoreModal();
      this.claimMoreModalRef?.hideServiceClaimMore();
    } else if (locationDatePopVisible) {
      // 出境修改订单弹层
      setLocationAndDatePopIsShow({ visible: false });
    } else {
      this.pageGoBack();
    }
  }

  onCloseInsuranceDetails = () => {
    this.props.showInsDetailModal({ visible: false });
  };

  toggleCloseDepositIntroModal = () => {
    this.setState({
      isShowDepositIntro: !this.state.isShowDepositIntro,
    });
  };

  onCloseDepositIntroModal = () => {
    this.setState({
      isShowDepositIntro: false,
    });
  };

  onBarVendorCallClick = () => {
    this.setState({
      vendorCallModal: true,
    });
    // this.props.showInsDetailModal({ visible: false });
  };

  showOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: true,
    });
  };

  hideOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: false,
    });
  };

  showOsdFeeTitleExplainModal = (feeTitle, feeTitleExplain) => {
    this.setState({
      osdFeeTitleExplainModalVisible: true,
      feeTitle,
      feeTitleExplain,
    });
  };

  hideOsdFeeTitleExplainModal = () => {
    this.setState({
      osdFeeTitleExplainModalVisible: false,
    });
  };

  showOsdInsuranceModal = (item, anchorCategory) => {
    this.setState({
      osdInsuranceVisible: true,
      anchorCategory: anchorCategory || '',
    });
    const { orderId, orderStatus } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_保险详情',

      info: {
        orderId,
        orderStatus,
      },
    });
  };

  hideOsdInsuranceModal = () => {
    this.setState({
      osdInsuranceVisible: false,
      anchorCategory: '',
    });
  };

  showExcessIntroduceModal = () => {
    this.setState({
      osdExcessIntroduceVisible: true,
    });
  };

  closeExcessIntroduceModal = () => {
    this.setState({
      osdExcessIntroduceVisible: false,
    });
  };

  showInsuranceNoticeMustReadModal = insuranceNotice => {
    const { insuranceNoticeMustReadVisible } = this.state;
    if (!insuranceNoticeMustReadVisible) {
      this.setState({
        insuranceNoticeMustReadVisible: true,
        insuranceNotice,
      });
    }
  };

  closeInsuranceNoticeMustReadModal = () => {
    const { insuranceNoticeMustReadVisible } = this.state;
    if (insuranceNoticeMustReadVisible) {
      this.setState({
        insuranceNoticeMustReadVisible: false,
      });
    }
  };

  showInsuranceReminderEnglishModal = englishContent => {
    const { insuranceReminderEnglishVisible } = this.state;
    if (!insuranceReminderEnglishVisible) {
      this.setState({
        insuranceReminderEnglishVisible: true,
        englishContent,
      });
    }
  };

  closeInsuranceReminderEnglishModal = () => {
    const { insuranceReminderEnglishVisible } = this.state;
    if (insuranceReminderEnglishVisible) {
      this.setState({
        insuranceReminderEnglishVisible: false,
      });
    }
  };

  openInsuranceNoticeRule = url => {
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  hideInsFailedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      insFailedModalVisible: {
        visible: false,
        data: {
          content: PromptMessage.InsMsg.addInsFailModalContent,
          showPayBtn: false,
        },
      },
    });
  };

  handleInsGoPay = () => {
    this.hideInsFailedModal();
    EventHelper.sendEvent(EventName.insConfirmBackToOrderDetail, { status: 2 });
  };

  hideRenewTipsModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ renewTipModal: { visible: false } });
  };

  hideOrderRefundDetailModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ refundDetailModal: { visible: false } });
  };

  showOrderRefundDetailModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ refundDetailModal: { visible: true } });
  };

  hideOptimizationStrengthenModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ optimizationStrengthenModal: { visible: false } });
  };

  showOptimizationStrengthenModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ optimizationStrengthenModal: { visible: true } });
  };

  isdAdCallBack = data => {
    const { isShowIsdAd } = this.state;
    if (isShowIsdAd !== data?.length > 0) {
      this.setState({
        isShowIsdAd: data?.length > 0,
      });
    }
  };

  osdAdCallBack = data => {
    const { isShowOsdAd } = this.state;
    if (isShowOsdAd !== data?.length > 0) {
      this.setState({
        isShowOsdAd: data?.length > 0,
      });
    }
  };

  getModifyOrderProps = () => {
    const { modalsVisible, setOrderModalsVisible } = this.props;
    if (modalsVisible?.confirmModal?.isModifyOrder) {
      return {
        btns: [
          {
            name: ModifyOrderTexts.gotit,
            isPrimary: true,
            onPress: () => {
              setOrderModalsVisible({ confirmModal: { visible: false } });
            },
          },
        ],
      };
    }
    return undefined;
  };

  handleContentOnLayout = e => {
    this.contentHeight = e.nativeEvent.layout.height;
    this.triggerScreenSnapShot();
  };

  handleMainscrollerScroll = (height: number, isAnimate = true) => {
    if (height >= 0 && this.mainscroller) {
      if (
        this.isKeyboardAwareScrollView &&
        this.mainscroller?.scrollToPosition
      ) {
        this.mainscroller?.scrollToPosition(0, height, isAnimate);
      } else {
        this.mainscroller?.scrollTo({ x: 0, y: height, animated: isAnimate });
      }
    }
  };

  handleFulfillInnerScroll = (height: number, isAnimate = true) => {
    if (height >= 0 && this.fulfillInnerScroll) {
      if (this.isKeyboardAwareScrollView) {
        this.fulfillInnerScroll?.scrollToPosition?.(0, height, isAnimate);
      } else {
        this.fulfillInnerScroll?.scrollTo?.({
          x: 0,
          y: height,
          animated: isAnimate,
        });
      }
    }
  };

  onScoreSelect = () => {
    if (this.isKeyboardShow) return;
    const { orderId, orderStatus } = this.props;
    clearTimeout(scoreSelectTimer);
    const delay = 200;
    scoreSelectTimer = setTimeout(() => {
      this.handleMainscrollerScroll(this.contentHeight);
    }, delay);
    CarLog.LogCode({
      name: '点击_订单详情页_NPS分数',

      info: {
        orderId,
        orderStatus,
      },
    });
  };

  keyboardWillShow = () => {
    this.isKeyboardShow = true;
  };

  keyboardWillHide = () => {
    this.isKeyboardShow = false;
  };

  hideInsModal = () => {
    this.props.setOrderModalsVisible({
      createInsModalVisible: { visible: false },
    });
  };

  hideAdvanceReturnModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnModal: { visible: false },
    });
  };

  showAdvanceReturnModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnModal: { visible: true },
    });
  };

  closeMaterialModal = () => {
    this.setState({
      materialModalVisible: false,
    });
  };

  showModifyDriverInfoModal = item => {
    this.showModifyFlightNoModal(item);
  };

  hideContactDoorStoreModal = () => {
    this.setState({
      contactDoorStoreModalVisible: false,
      contactDoorStoreModalTabId: null,
    });
  };

  showContactDoorStoreModal = (id?) => {
    this.setState({
      contactDoorStoreModalVisible: true,
      contactDoorStoreModalTabId: id,
    });
  };

  clickButtonsBarVendorCall = data => {
    const { setPhoneModalVisible } = this.props;
    if (Utils.isCtripOsd()) {
      this.showContactDoorStoreModal();
    } else {
      setPhoneModalVisible(data);
    }
  };

  showDepositRateDescriptionModal = () => {
    this.setState({
      depositRateDescriptionModalVisible: true,
    });
  };

  closeDepositRateDescriptionModal = () => {
    this.setState({
      depositRateDescriptionModalVisible: false,
    });
  };

  /**
   * 查看提车凭证
   */
  onPressVoucher = url => {
    CarLog.LogCode({ name: '点击_取车材料详情页_提车凭证' });
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  onPressHandleLicense = url => {
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  onPressToLicense = () => {
    AppContext.PageInstance.push(Channel.getPageId().License.EN);
  };

  onMaterialsPress = () => {
    // 未完成懒加载模块时，不跳转取车指引页
    if (!this.state.isShowPageOtherModule) {
      return;
    }
    const { extendedInfo, orderId, orderBaseInfo } = this.props;
    const isLicenseApprove = extendedInfo?.osdDetailVersion === 'B';
    let printVoucherUrl = '';
    if (isLicenseApprove) {
      const printVoucherBtn = orderBaseInfo?.allOperations?.find(
        btn => btn.operationId === ORDER_BUTTON.PrintVoucher,
      );
      printVoucherUrl = printVoucherBtn?.url;
    }
    CarLog.LogCode({ name: '点击_订单详情页_用车小贴士' });
    if (isLicenseApprove) {
      this.setState({
        materialModalVisible: true,
        printVoucherUrl,
      });
    } else {
      AppContext.PageInstance.push(Channel.getPageId().Materials.EN, {
        pickUpMaterials: extendedInfo?.pickUpMaterials,
        isLicenseApprove,
        isOrderDetail: true,
        orderId,
        printVoucherUrl,
      });
    }
  };

  hideAdvanceReturnFeeModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnFeeModal: { visible: false },
    });
  };

  hidePickupMaterialModal = () => {
    this.props.setOrderModalsVisible({ pickUpMaterials: { visible: false } });
  };

  showCarServiceModal = () => {
    this.setState({
      carServiceModalVisible: true,
    });
  };

  hideCarServiceModal = () => {
    this.setState({
      carServiceModalVisible: false,
    });
  };

  gotoInsurance = data => {
    const { insuranceOrderId, toDetailStatus } = data;
    CarLog.LogCode({ name: '点击_订单详情页_人身及财物险详情' });
    if (insuranceOrderId && toDetailStatus) {
      AppContext.PageInstance.push('InsuranceOrderDetail', {
        insuranceOrderId,
      });
      return;
    }
    const param = {
      type: 1,
    };
    /* eslint-disable max-len */
    const url = `${
      Platform.CAR_CROSS_URL.TravelInsuranceAgreement.ISD
    }&sparam=${encodeURIComponent(JSON.stringify(param))}`;
    Utils.openUrlWithTicket(url);
  };

  hideCarServiceDetailModal = () => {
    this.setState({
      carServiceDetailModalVisible: false,
      carServiceDetailModalProps: {},
    });
  };

  hideServiceClaimMoreModal = () => {
    this.setState({
      serviceClaimMoreVisible: false,
    });
  };

  setCarServiceDetailProps = data => {
    this.setState({
      carServiceDetailModalProps: data,
      carServiceDetailModalVisible: true,
    });
  };

  onPressServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: true,
    });
  };

  hideOrderCouponModal = () => {
    this.setState({
      couponModalVisible: false,
    });
  };

  showOrderCouponModal = () => {
    this.setState({
      couponModalVisible: true,
    });
  };

  handleOldInsLayout = e => {
    setOrderDetailStatusData(
      OrderDetailStatusKeyList.oldInsLayoutY,
      e?.nativeEvent?.layout?.y,
    );
  };

  handleDirectOpenScrollToSection = (e, directOpenName: string) => {
    if (this.directScrollFinished) return;
    const { directOpen, finalQueryIsFinish } = this.props;

    if (String(directOpen) === directOpenName && finalQueryIsFinish) {
      const y = e?.nativeEvent?.layout?.y;
      setTimeout(() => {
        this.scrollToPosition(y);
        this.directScrollFinished = true;
      }, 200);
    }
  };

  handleNewInsLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      String(OrderDetailTextsV2.DirectOpen.UpgradeCarService),
    );
  };

  handleIsdCarServiceLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailTextsV2.DirectOpen.IsdCarService,
    );
  };

  handleIsdGuaranteeLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailTextsV2.DirectOpen.IsdGuarantee,
    );
  };

  handleOsdInsLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailTextsV2.DirectOpen.OsdInsurance,
    );
  };

  handleTravelLimitLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      String(OrderDetailTextsV2.DirectOpen.TravelLimit),
    );
  };

  renderSecondScreen = () => {
    const {
      driverInfo = {},
      cancelRuleInfo = {},
      orderBaseInfo = {},
      extendedInfo,
      ipollInfo,
      carRentalMustReadData,
      invoiceDesc,
      orderStatusCtrip,
      storeAttendant,
      supportInfo,
      config,
      isShowTravelLimit,
      isNewCancelRule,
      isOrderDataByPhone,
      isEasyLife2024,
      logBaseInfo,
    } = this.props;
    const { orderId, orderStatus, directOpen } = this.props;
    const { inpsSceneId } = config || {};

    return (
      <>
        {Utils.isCtripIsd() && (
          <DriverInfo
            data={driverInfo}
            extendedInfo={extendedInfo}
            supportInfo={supportInfo}
            onPressAddMoreDriver={this.onAddInstructFormPress}
            orderId={orderId}
            orderStatus={orderStatus}
          />
        )}
        {!Utils.isCtripIsd() && (
          <CancelPolicy
            cancelRuleInfo={cancelRuleInfo}
            orderBaseInfo={orderBaseInfo}
            isNewCancelRule={isNewCancelRule}
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_取消政策',

              info: logBaseInfo,
            })}
          />
        )}
        {selector(
          !Utils.isCtripIsd(),
          <XViewExposure
            testID={CarLog.LogExposure({ name: '曝光_订单详情页_全部政策' })}
            className={c2xStyles.blockSection}
          >
            <CarRentalMustRead
              onAccidentPress={this.goToAccident}
              onPolicyPress={this.gotoPolicy}
              isCommonChannel={true}
              carRentalMustReadData={carRentalMustReadData}
              hideAccidentComponent={true}
            />
          </XViewExposure>,
        )}
        {/* 车行险重构 */}
        {Utils.isCtripIsd() && (
          <>
            <View onLayout={this.handleIsdCarServiceLayout}>
              <CarService
                isHideDetailBtn={!isEasyLife2024}
                isForceSingleServce={true}
                fromPage={CarServiceFromPageTypes.orderDetail}
                style={styles.carService}
                onPressCarServiceDetail={this.setCarServiceDetailProps}
                onPressServiceClaimMore={this.onPressServiceClaimMore}
              />
            </View>
            <View onLayout={this.handleNewInsLayout}>
              <CarServiceUpgrade
                style={styles.carServiceUpgrade}
                showCarServiceModal={this.showCarServiceModal}
              />
            </View>
            <View onLayout={this.handleIsdGuaranteeLayout}>
              <Guarantee
                fromPage={CarServiceFromPageTypes.orderDetail}
                style={styles.guarantee}
                backgroundType={BackgroudType.SaleAfterInsurance}
                onPressTitle={this.gotoInsurance}
              />
            </View>
          </>
        )}

        {Utils.isCtripOsd() && (
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_保险详情',
              info: logBaseInfo,
            })}
            style={styles.insWrap}
            onLayout={this.handleOsdInsLayout}
          >
            <InsuranceProtections
              showOsdInsuranceModal={this.showOsdInsuranceModal}
              openInsuranceReminderEnglishModal={
                this.showInsuranceReminderEnglishModal
              }
              autoRefresh={this.fetchOrderFn}
              directOpen={directOpen}
              handleMainscrollerScroll={this.handleMainscrollerScroll}
            />

            <BuyInsurance />
          </XViewExposure>
        )}

        {/* 旅行限制 */}
        {Utils.isCtripOsd() && isShowTravelLimit && (
          <View
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_旅行限制',
              info: logBaseInfo,
            })}
            onLayout={this.handleTravelLimitLayout}
          >
            <TravelLimit style={styles.travelLimitWrap} />
          </View>
        )}

        {/* 屏蔽附加产品模块等完整的产品需求 */}
        <AdditionalProduct />
        {Utils.isCtripOsd() && (
          <CustomerInfo
            data={driverInfo}
            showModifyDriverInfoModal={this.showModifyDriverInfoModal}
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_驾驶员信息模块',
            })}
          />
        )}
        {/* 国内发票 */}
        {Utils.isCtripIsd() &&
          orderStatusCtrip !== OrderDetailCons.OrderStatusCtrip.COMPLETED && (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_发票模块',

                info: {
                  orderId,
                  orderStatus,
                },
              })}
            >
              <InvoiceModule
                invoiceDesc={invoiceDesc}
                orderStatusCtrip={orderStatusCtrip}
                isOrderDataByPhone={isOrderDataByPhone}
                orderId={orderId}
              />
            </XViewExposure>
          )}

        <OrderLessEnter />

        <CESEntry title={ipollInfo?.title} url={ipollInfo?.h5Url} />

        {!!INps && !!inpsSceneId && (
          <XViewExposure
            testID={CarLog.LogExposure({ name: '曝光_订单详情页_NPS模块' })}
            className={Utils.isCtripOsd() && c2xStyles.mb50}
          >
            <INps
              sceneId={String(inpsSceneId)}
              env={Application.env}
              bizId="car"
              orderId={orderId}
              pageId={this.getPageId()}
              orderType="order"
              containerStyle={styles.npsWrap}
              onScoreChange={this.onScoreSelect}
            />
          </XViewExposure>
        )}

        {Utils.isCtripIsd() && (
          <View
            style={xMergeStyles([
              styles.supplierWarp,
              storeAttendant?.title && styles.supplierWarpWithPerson,
            ])}
          >
            <BusinessLicense onPress={this.onPressServiceProviderBar} />
          </View>
        )}
        {/* TODO-@zyr hramony暂不支持KeyboardAvoidingView键盘自适应功能 */}
        {/* 临时解决方案为给滚动视图底部加个固定高度view 后续框架支持再去掉 */}
        {isHarmony && <View style={{ height: 100 }} />}
      </>
    );
  };

  setShowPageOtherModule = () => {
    const delayTime = isAndroid ? 800 : 100;
    this.timeIsShowPage = setTimeout(() => {
      if (this.state.isShowPageOtherModule) {
        return;
      }
      this.lazyLoadOtherModules();
      this.setState({ isShowPageOtherModule: true }, () => {
        this.onDirectOpen();
      });
    }, delayTime);
  };

  handlePageContentLayout = e => {
    this.handleContentOnLayout(e);
  };

  handleOrderStatusLayout = e => {
    this.orderStatusHeight = getPixel(e.nativeEvent.layout.height);
  };

  handleContractTrackerLayout = () => {
    this.triggerContractTrackerSnapShot();
  };

  openConfirmModal = (anchor: LayoutPartEnum) => {
    // 打开弹层信息改由订详接口直接返回，只请求评论信息
    this.props.queryCommentSummary();
    this.props.openOrderDetailConfirmModal();
    this.setState({ confirmModalAnchor: anchor });
  };

  onPressVendor = () => {
    const { finalQueryIsFinish } = this.props;
    if (!finalQueryIsFinish) return;
    this.openConfirmModal(LayoutPartEnum.VehicleDetail);
  };

  onPressMileageLimit = () => {
    const { finalQueryIsFinish } = this.props;
    if (!finalQueryIsFinish) return;
    this.openConfirmModal(LayoutPartEnum.Mileage);
  };

  setEtcIntroModal = visible => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcIntroModal: {
        visible,
      },
    });
  };

  onPressEtcUseHelperModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcUseHelperModal: {
        visible: true,
      },
    });
    CarLog.LogCode({ name: '点击_门店与车型配置_ETC使用注意事项' });
  };

  closeEtcIntroModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcIntroModal: {
        visible: false,
      },
    });
  };

  closeEtcUseHelperModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcUseHelperModal: {
        visible: false,
      },
    });
  };

  showVehicleUseNotesModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      vehicleUseNotesModal: {
        visible: true,
      },
    });
  };

  closeVehicleUseNotesModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      vehicleUseNotesModal: {
        visible: false,
      },
    });
  };

  showClaimProcessModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      claimProcessVisible: {
        visible: true,
      },
    });
  };

  closeClaimProcessModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      claimProcessVisible: {
        visible: false,
      },
    });
  };

  closePickUpMaterialsModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      pickUpMaterialsModal: {
        visible: false,
      },
    });
  };

  closeBusinessTimePolicyModal = () => {
    const { setOrderModalsVisible, modalsVisible } = this.props;
    setOrderModalsVisible({
      businessTimePolicyModal: {
        visible: false,
        data: {
          type: modalsVisible.businessTimePolicyModal?.data?.type,
        },
      },
    });
  };

  closeBusinessTimeModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessTimeModal: {
        visible: false,
      },
    });
  };

  closeFulfillmentModifyModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      fulfillmentModifyModal: {
        visible: false,
      },
    });
  };

  showLimitPop = () => {
    const { setLimitRulePopVisible } = this.props;
    setLimitRulePopVisible({ visible: true });
  };

  showFlightDelayRulesModal = () => {
    const { setFlightDelayRulesModalVisible } = this.props;
    setFlightDelayRulesModalVisible(true);
  };

  handleScrollEvent = () => {
    EventHelper.sendEvent(EventName.OrderDetailOnScroll, {});
  };

  onScroll = event => {
    this.headerEvents(event);
    this.sendOnScrollEvent();
  };

  renderHeaderContent = () => {
    const { restAssuredTag, orderBaseInfo } = this.props;
    return (
      <>
        {this.renderTipView(orderBaseInfo)}
        <RestAssuredBanner tag={restAssuredTag} />
      </>
    );
  };

  isScrollViewReachBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }) => {
    return (
      layoutMeasurement?.height + contentOffset?.y >=
      contentSize?.height - getPixel(3)
    );
  };

  onDargViewAnimateEnd = ({ dragStatus }: { dragStatus: DargViewStatus }) => {
    this.setState({
      isExpandContractTracker: dragStatus === DargViewStatus.Expand,
      isFulfillScrollEnd: false,
    });
    if (dragStatus === DargViewStatus.Default) {
      this.handleFulfillInnerScroll(0, false);
    }
  };

  getMainViewHeight = () => {
    const { isOsdShowBottomButtons } = this.props;
    const isShowBottomBar = Utils.isCtripIsd() || isOsdShowBottomButtons;
    const bottomGap =
      (Device.isiPhoneX ? getPixel(69) : getPixel(33)) + getPixel(32);
    // 主信息层滚动区域高度 = 屏幕高度 - 头部 - 底部 - 订单产品信息头部区域 - 底部按钮组高度
    return (
      vh(100) -
      DEFAULT_HEADER_HEIGHT -
      bottomGap -
      getPixel(66) -
      (isShowBottomBar ? getPixel(130) : 0)
    );
  };

  // 订单状态模块
  renderOrderStatus = () => {
    const { isVehicleStatusPolling } = this.state;
    const { vendorInfo = {} } = this.props;
    return (
      <OrderStatus
        onLayout={this.handleOrderStatusLayout}
        payCountDownTimeOut={this.payCountDownTimeOut}
        showConsultProgressModal={this.showConsultProgressModal}
        pageId={this.getPageId()}
        showPhoneModal={this.showPhoneModal}
        showCancelPenaltyModal={this.showCancelPenaltyInfoModal}
        showOrderRefundDetailModal={this.showOrderRefundDetailModal}
        showFulfillmentModifyModal={this.showFulfillmentModifyModal}
        showAdvanceReturnModal={this.showAdvanceReturnModal}
        onMaterialsPress={this.onMaterialsPress}
        vendorInfo={vendorInfo}
        isVehicleStatusPolling={isVehicleStatusPolling}
        gotoPolicy={this.gotoPolicy}
      />
    );
  };

  // 订单信息模块
  renderOrderInfoContent = () => {
    const { isShowPageOtherModule } = this.state;
    const {
      safeRent,
      easyLifeTags,
      invoiceDesc,
      orderStatusCtrip,
      extendedInfo,
      finalQueryIsFinish,
      isOrderDataByPhone,
      isShowFulfillmentCard,
      logBaseInfo,
      isShelves2,
    } = this.props;

    const { isShowIsdAd, isShowOsdAd } = this.state;
    const { orderId, orderStatus, directOpen, config } = this.props;
    const isStoreInfoSurvey = GetABCache.isStoreInfoSurvey();
    // 订单状态 （0：订单待支付，1：订单待确认/确认中，2：订单已确认，3：订单已取消，4：服务已完成, 5:处理中,6:已取车）
    const showQiWeiEntry =
      [0, 2, 3, 4, 6].includes(orderStatus) &&
      GetAB.isISDHomeQiwei() &&
      !!config?.qiweiConfig?.orderdetail;
    return (
      <View className={c2xStyles.orderInfoBg}>
        <OrderAmount
          showOsdPriceDetailModal={this.showOsdPriceDetailModal}
          headerContent={this.renderHeaderContent}
          goCredentialEntry={this.goCredentialEntry}
          showCancelPolicyModal={this.switchCancelPolicyModal}
          onPressTandC={this.gotoPolicy}
          handlePress={this.toggleCloseDepositIntroModal}
          isShowPageOtherModule={isShowPageOtherModule}
          isShowFulfillmentCard={isShowFulfillmentCard}
        />

        {/* 国内发票 */}
        {Utils.isCtripIsd() &&
          orderStatusCtrip === OrderDetailCons.OrderStatusCtrip.COMPLETED && (
            <InvoiceModule
              invoiceDesc={invoiceDesc}
              orderStatusCtrip={orderStatusCtrip}
              isOrderDataByPhone={isOrderDataByPhone}
              orderId={orderId}
            />
          )}

        {/* 无忧租特权 Tab */}
        {!Utils.isCtripIsd() && !!safeRent && easyLifeTags?.length > 0 && (
          <PrivilegeTab
            onPress={this.showEasylifeModal}
            isNewNoWorry={extendedInfo?.newNoWorry}
          />
        )}

        {!showQiWeiEntry && isShowPageOtherModule && Utils.isCtripOsd() && (
          <View className={isShowOsdAd && c2xStyles.osdAdWrap}>
            <AdSlider
              width={AdWidth}
              height={AdHeight}
              getAdData={this.osdAdCallBack}
              impId="01011TUOYRH0128JNXKRWHNCNKC"
            />
          </View>
        )}
        {/* 企微入口 */}
        {showQiWeiEntry && (
          <Banner
            qiWeiConfig={config?.qiweiConfig?.orderdetail}
            orderStatus={orderStatus}
          />
        )}
        {/* TODO-@zyr 广告验证效果  */}
        {isShowPageOtherModule && Utils.isCtripIsd() && (
          <View className={isShowIsdAd && c2xStyles.isdAdWrap}>
            <AdSlider
              slideVideo={isdSlideVideoConfig}
              width={IsdAdWidth}
              height={IsdAdHeight}
              getAdData={this.isdAdCallBack}
              impId="01011TUOYRH0128JNXKRWBQUAOG"
            />
          </View>
        )}
        {/* 验证免押 */}
        {isShelves2 ? (
          <VehicleISD
            orderId={orderId}
            logBaseInfo={logBaseInfo}
            orderStatus={orderStatus}
            finalQueryIsFinish={finalQueryIsFinish}
            onPressVendor={this.onPressVendor}
          />
        ) : (
          <Vehicle safeRent={safeRent} logBaseInfo={logBaseInfo} />
        )}

        {Utils.isCtripIsd() ? (
          !isShelves2 && (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_供应商信息',

                info: {
                  orderId,
                  orderStatus,
                },
              })}
            >
              <Vendor
                onPressVendor={this.onPressVendor}
                onPressMileageLimit={this.onPressMileageLimit}
                setEtcIntroModal={this.setEtcIntroModal}
                onPressEtcUseHelperModal={this.onPressEtcUseHelperModal}
              />
            </XViewExposure>
          )
        ) : (
          <PickReturnTab
            orderId={orderId}
            isStoreInfoSurvey={isStoreInfoSurvey}
            onPolicyPress={this.gotoPolicy}
            onPressPhone={this.showContactDoorStoreModal}
          />
        )}

        {!finalQueryIsFinish && (
          <BbkSkeletonLoading
            visible={true}
            pageName={PageType.OrderDetailFirstScreen2}
          />
        )}

        {lazySelector(isShowPageOtherModule, this.renderSecondScreen)}
      </View>
    );
  };

  renderPageContent = () => {
    const {
      isExpandContractTracker,
      isVehicleStatusPolling,
      contractTrackerRefreshValue,
      isFulfillScrollEnd,
      isShowPageOtherModule,
    } = this.state;
    const { isShowFulfillmentCard, fulfillmentData } = this.props;

    const { refreshResult } = this.state;

    return isShowFulfillmentCard ? (
      <BottomSheetDargView
        // eslint-disable-next-line
        handleTitle="订单产品信息"
        onViewAnimateEnd={this.onDargViewAnimateEnd}
        innerViewStyle={styles.innerViewStyle}
        mainViewStyle={{ height: this.getMainViewHeight() }}
        ref={this.bottomSheetDargRef}
        onLayout={this.handlePageContentLayout}
        isInnerScrollEnd={isFulfillScrollEnd}
        getRef={ref => {
          this.fulfillOrderPageSnapShot = ref;
        }}
        getMainRef={ref => {
          this.mainscroller = ref;
        }}
        isRenderSnap={isShowPageOtherModule}
        innerView={
          <BbkCustomScrollView
            refreshIsGradient={!Utils.isTrip()}
            refreshGradientColor={LgColor}
            getRef={ref => {
              this.fulfillInnerScroll = ref;
            }}
            onScroll={this.onScroll}
            style={Utils.isCtripIsd() ? styles.contentBgIsd : styles.contentBg}
            onPullRelease={this.onPullRelease}
            refreshResult={refreshResult}
            isKeyboardAwareScrollView={this.isKeyboardAwareScrollView}
            isHideFooter={true}
          >
            {this.renderOrderStatus()}
            <LinearGradient
              start={{ x: 0.0, y: 1.0 }}
              end={{ x: 1.0, y: 1.0 }}
              locations={[0, 1]}
              className={
                isExpandContractTracker
                  ? c2xStyles.fulfillLGWrapExpand
                  : c2xStyles.fulfillLGWrap
              }
              colors={[color.C_008CFF, color.C_0070FD]}
            >
              {/* 顶部政策栏 */}
              <PolicyTips
                showLimitPopModal={this.showLimitPop}
                showMileageLimitModal={this.onPressMileageLimit}
                showStorePolicyModal={this.gotoPolicy}
                showClaimProcessModal={this.showClaimProcessModal}
                showVehicleUseNotesModal={this.showVehicleUseNotesModal}
                showFlightDelayRulesModal={this.showFlightDelayRulesModal}
              />

              {/* 订详页的履约变更卡片，只在已确认状态下露出 */}
              {Utils.isCtripIsd() &&
                this.orderStatusCtrip === OrderStatusCtrip.CONFIRMED && (
                  <FulfillmentModifyCard
                    style={{ marginBottom: getPixel(16) }}
                    showFulfillmentModifyModal={this.showFulfillmentModifyModal}
                  />
                )}

              {/* 履约可视化卡片 */}
              <OrderFulfillmentContainer
                isExpandContractTracker={isExpandContractTracker}
                isVehicleStatusPolling={isVehicleStatusPolling}
                refreshResult={refreshResult}
                refreshValue={contractTrackerRefreshValue}
                showContactDoorStoreModal={this.showContactDoorStoreModal}
              />

              {!fulfillmentData && (
                <BbkSkeletonLoading
                  visible={true}
                  style={styles.fulfillLoading}
                  pageName={PageType.Fulfillment}
                />
              )}
            </LinearGradient>
          </BbkCustomScrollView>
        }
      >
        {this.renderOrderInfoContent()}
      </BottomSheetDargView>
    ) : (
      <BbkCustomScrollView
        refreshIsGradient={true}
        refreshGradientColor={LgColor}
        getRef={ref => {
          this.mainscroller = ref;
        }}
        onScroll={this.onScroll}
        style={Utils.isCtripIsd() ? styles.contentBgIsd : styles.contentBg}
        onPullRelease={this.onPullRelease}
        refreshResult={refreshResult}
        isKeyboardAwareScrollView={this.isKeyboardAwareScrollView}
        footerEnable={false}
      >
        <View style={layout.flex1} onLayout={this.handlePageContentLayout}>
          {this.renderOrderStatus()}
          {this.renderOrderInfoContent()}
        </View>
      </BbkCustomScrollView>
    );
  };

  hideVocModal = () => {
    CarLog.LogCode({ name: '点击_订单详情页_VOC用户反馈模块_关闭按钮' });
    const { setVocModalVisible = Utils.noop } = this.props;
    setVocModalVisible({
      vocModalVisible: false,
    });
  };

  showSurveyModal = async () => {
    const { orderId, setPhoneSurveyShowCount } = this.props;
    setPhoneSurveyShowCount({
      count: 1,
    });
    setPhoneSurvey(orderId);
  };

  showFuelTypeModal = fuelType => {
    this.setState({
      isFuelTypeModalVisible: true,
      fuelType,
    });
  };

  hideFuelTypeModal = () => {
    this.setState({
      isFuelTypeModalVisible: false,
    });
  };

  showLessModal = () => {
    this.setState({
      lessModalVisible: true,
    });
  };

  hideLessModal = () => {
    this.setState({
      lessModalVisible: false,
    });
  };

  pressAnswer = (question, answer) => {
    const {
      orderId,
      orderStatusDesc,
      orderStatus,
      saveQuestionnaire = Utils.noop,
    } = this.props;
    const {
      questionId,
      content: questionContent,
      prCarExceptionCode,
    } = question;
    const { code: answerId, content: answerContent } = answer;
    // 回答问题
    saveQuestionnaire({
      orderId,
      questionId,
      answerCode: answerId,
      prCarExceptionCode,
      ctripStatusCode: orderStatus,
    });
    CarLog.LogCode({
      name: '点击_订单详情页_VOC用户反馈',

      info: {
        orderId,
        orderStatus: orderStatusDesc,
        questionId,
        questionContent,
        answerId,
        prCarExceptionCode,
        answerContent,
      },
    });
  };

  getTitle = (obj, vehicleName?: string) => {
    const { title = '' } = obj || {};
    let { description = '' } = obj || {};
    if (!title && !description) return null;
    if (vehicleName) {
      description = description.replace('{0}', vehicleName);
    }
    return {
      headerText: title,
      items: description
        .split('\n')
        .filter(v => v)
        .map(v => ({ title: v })),
    };
  };

  goMap = limitScope => {
    this.push(Channel.getPageId().LimitMap.EN, { limitScope });
  };

  handleBusinessLicenseClose = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessLicenseModal: {
        visible: false,
      },
    });
  };

  handlChangeIsShowBigImgContainer = (bool: boolean) => {
    if (bool === this.state.isShowBigImgContainer) {
      return;
    }
    this.setState({
      isShowBigImgContainer: bool,
    });
  };

  handShowBigImgContainer = () => {
    this.handlChangeIsShowBigImgContainer(true);
  };

  handleClaimMoreModalRef = ref => {
    this.claimMoreModalRef = ref;
  };

  handleSearchPanelModalRef = ref => {
    this.searchPanelModalRef = ref;
  };

  closeOsdModifyOrderModal = () => {
    this.props.setLocationAndDatePopIsShow({ visible: false });
  };

  showNoLimitModal = noLimitDesc => {
    this.setState({
      noLimitModalVisible: true,
      noLimitDesc,
    });
  };

  hideNoLimitModal = () => {
    this.setState({
      noLimitModalVisible: false,
    });
  };

  renderPageModals = () => {
    const {
      BbkInsuranceDetailProps,
      feeDeductionVisible,
      modifyFlightNoModalVisible,
      fetchQueryCancelFee,
      phoneModalVisible,
      phoneList,
      phoneModalType,
      personPhoneModalVisible,
      setPersonPhoneModalVisible,
      orderIsdModalVisible,
      orderId,
      orderStatus,
      orderStatusDesc,
      modalsVisible,
      setOrderModalsVisible,
      labelsModalVisible,
      setLabelsModalVisible,
      carDetailModalVisible,
      setCarDetailModalVisible,
      carTags,
      easyLifeTagModalVisible,
      easyLifeTags,
      safeRent,
      setEasyLifeTagModalVisible,
      renewTipLogData,
      questionnaires,
      queryOrderApiStatus,
      orderBaseInfo,
      cancelRuleInfo,
      freezeDepositExplain,
      vehicleInfo,
      vocModalVisible,
      ehiModifyOrderModalVisible,
      serviceProgressList,
      urgeServiceIds,
      refundPenaltyInfo,
      storeAttendant,
      isHideCancelButton,
      config,
      operationButtons,
      advanceReturnFeeInfo,
      advanceReturnFeeInfoByVendor,
      orderPriceInfoFee,
      orderDetailResponse,
      extendedInfo,
      isuranceBox,
      finalQueryIsFinish,
      vendorInfo,
      addInstructData,
      isEasyLife2024,
      claimProcessModalData,
      vehicleUseNotesInfo,
      isOsdShowBottomButtons,
      isShowFulfillmentCard,
      logBaseInfo,
      locationDatePopVisible,
      osdModifyOrderNote,
      setLocationAndDatePopIsShow,
    } = this.props;
    const {
      isShowCancelPolicyModal,
      warningTipsModalVisible,
      warningTipsModalCotent,
      addInstructModalVisible,
      isShowDepositIntro,
      isConsultProgressModal,
      isShowCancelPenaltyInfoModal,
      carServiceModalVisible,
      carServiceDetailModalProps,
      carServiceDetailModalVisible,
      serviceClaimMoreVisible,
      couponModalVisible,
      isFuelTypeModalVisible,
      lessModalVisible,
      fuelType,
      osdPriceDetailModalVisible,
      osdFeeTitleExplainModalVisible,
      osdInsuranceVisible,
      anchorCategory,
      osdExcessIntroduceVisible,
      feeTitle,
      feeTitleExplain,
      insuranceNoticeMustReadVisible,
      insuranceNotice,
      insuranceReminderEnglishVisible,
      englishContent,
      materialModalVisible,
      depositRateDescriptionModalVisible,
      printVoucherUrl,
      refreshResult,
      contractTrackerRefreshValue,
      contactDoorStoreModalVisible,
      contactDoorStoreModalTabId,
      noLimitModalVisible,
      noLimitDesc,
    } = this.state;
    const { penaltyAmount, refundAmount } = refundPenaltyInfo || {};
    const isLicenseApprove = extendedInfo?.osdDetailVersion === 'B';
    const isOsdInsurance = extendedInfo?.ctripInsuranceVersion === 'B';
    const isShowPage =
      queryOrderApiStatus === QueryOrderApiStatusType.success &&
      !!orderBaseInfo.orderId;

    const pageModalProps = {
      visible: addInstructModalVisible,
      onMaskPress: this.onCloseAddInstructModal,
      style: styles.shadow,
    };

    const introduce = false;
    const carProtection = false;
    const baseInfo = this.getTitle({
      get title() {
        return '车型基本信息';
      },
      description: '',
    });
    const possibleVehicles = null;

    const section = {
      introduce,
      carProtection,
      baseInfo,
      possibleVehicles,
    };

    const { packageInfos = [] } = isuranceBox;
    const curPackageId = packageInfos?.[0]?.insPackageId;
    const depositRateDescriptionInfo = extendedInfo?.promptInfos?.find(
      f => f.type === ApiResCode.ListPromptType.MarketBanner,
    );
    const materials = getMaterialsNew({
      materials: extendedInfo?.pickUpMaterials,
      isShowQuestion: !!depositRateDescriptionInfo,
      vendorId: vendorInfo?.bizVendorCode,
    });
    const isRefactor = extendedInfo?.attr?.isVehicle2 === '1';
    return (
      <>
        {/* 门店信息弹层 */}
        {Utils.isCtripIsd() && (
          <OrderDetailConfirmModalNew
            page={this}
            visible={this.props.orderDetailConfirmModalVisible}
            showFooter={false}
            anchor={this.state.confirmModalAnchor}
            onClose={this.props.closeOrderDetailConfirmModal}
            onPressEasyLife={this.showEasylifeModal}
            isOrderDetail={true}
            gotoPolicy={this.gotoPolicy}
            showOptimizationStrengthenModal={
              this.showOptimizationStrengthenModal
            }
            pageName={Channel.getPageId().Order.EN}
            onPressCarServiceDetail={this.setCarServiceDetailProps}
            onPressShowNoLimitModal={this.showNoLimitModal}
          />
        )}
        <OrderOptimizationStrengthenModal
          visible={
            this.props.modalsVisible?.optimizationStrengthenModal?.visible
          }
          onCancel={this.hideOptimizationStrengthenModal}
        />

        {/* 国内修改订单弹层，不会出现：对应的action setIsdOrderChangeModalVisible 没有置为true的时候 */}
        {!!orderIsdModalVisible && <OrderChangeContainerIsd />}
        {/* 不明扣费弹层，不会出现： showFeeDeduction这个action没有置为true的时候 */}
        {!!feeDeductionVisible && <OrderFeeDeduction />}
        {/* TODO 保险明细弹层：海外保险非B版会出现，需要与产品确认下线方案 */}
        {!!BbkInsuranceDetailProps?.visible && (
          <BbkInsuranceDetail
            isModal={true}
            style={BbkInsuranceDetailProps?.style}
            data={BbkInsuranceDetailProps?.data}
            packageId={BbkInsuranceDetailProps?.packageId}
            visible={BbkInsuranceDetailProps?.visible}
            onClose={this.onCloseInsuranceDetails}
          />
        )}
        {/* 无忧租弹层 */}
        {(!!safeRent || isEasyLife2024) && easyLifeTags?.length > 0 && (
          <EasyLifeTagListModal
            visible={easyLifeTagModalVisible}
            data={easyLifeTags}
            isEasyLife2024={isEasyLife2024}
            onCancel={() => setEasyLifeTagModalVisible(false)}
          />
        )}
        <SesameContainer useModal={true} />
        {/* 去免押弹层 & 放弃免押弹层 */}
        {Utils.isCtripIsd() ? (
          <DepositPaymentModal />
        ) : (
          <DepositPaymentModalOSD />
        )}
        {/* 押金政策 */}
        <DepositDetailModal />

        {/* 冻结押金 */}
        <DepositIntroduceModal
          freezeDepositExplain={freezeDepositExplain}
          visible={isShowDepositIntro}
          onClose={this.onCloseDepositIntroModal}
        />

        {/* 小问卷 */}
        {questionnaires?.length > 0 && !couponModalVisible && (
          <VocModal
            vocModalVisible={
              vocModalVisible &&
              !modalsVisible?.depositPaymentModal?.visible &&
              !modalsVisible?.depositPaymentModalOSD?.visible
            }
            questionnaires={questionnaires}
            onHide={this.hideVocModal}
            onPressAnswer={this.pressAnswer}
            orderId={orderId}
            orderStatus={orderStatusDesc}
          />
        )}
        {/* 额外驾驶员说明 */}
        <AddInstructModal
          pageModalProps={pageModalProps}
          addInstructData={addInstructData}
        />

        {Utils.isCtripIsd() && (
          <CancelPolicyModal
            onHide={this.hideCancelPolicyModal}
            modalVisible={isShowCancelPolicyModal}
            cancelRuleInfo={cancelRuleInfo}
            orderBaseInfo={orderBaseInfo}
            fetchQueryCancelFee={fetchQueryCancelFee}
            isHideButton={isHideCancelButton}
          />
        )}
        {Utils.isCtripIsd() && (
          <RenewTipsModal
            onHide={this.hideRenewTipsModal}
            modalVisible={modalsVisible?.renewTipModal?.visible}
            renewTipLogData={renewTipLogData}
          />
        )}
        {Utils.isCtripIsd() && (
          <OrderRefundDetailModal
            onHide={this.hideOrderRefundDetailModal}
            modalVisible={modalsVisible?.refundDetailModal?.visible}
          />
        )}
        {/* 咨询进度弹层 */}
        {serviceProgressList?.length > 0 && (
          <ServiceProgressModal
            urgeServiceIds={urgeServiceIds}
            orderId={orderId}
            orderStatus={orderStatus}
            pageId={this.getPageId()}
            urgeServiceProgress={this.onUrgeServiceProgress}
            onHide={this.hideConsultProgressModal}
            modalVisible={isConsultProgressModal}
            serviceProgressList={serviceProgressList}
          />
        )}
        {/* 申请退违约金弹层 */}
        {Utils.isCtripIsd() && (
          <CancelPenaltyInfoModal
            penaltyAmount={penaltyAmount}
            refundAmount={refundAmount}
            onHide={this.hideCancelPenaltyInfoModal}
            modalVisible={isShowCancelPenaltyInfoModal}
          />
        )}
        {/* 修改航班号弹层，对应的 SET_MODIFYFLIGHTNOMODALVISIBLE 永远不会置为true */}
        {!!modifyFlightNoModalVisible && <ModifyFlightNoModal />}
        {this.props.isDebugMode && (
          <AssistiveTouch
            onPress={() => {
              this.push('Debug');
            }}
          />
        )}
        {selector(
          (Utils.isCtripIsd() || isOsdShowBottomButtons) && isShowPage,
          <>
            <ButtonsBottomBar
              operationButtons={operationButtons}
              isFulfillmentOSD={isOsdShowBottomButtons}
              isPositionAbsolute={isShowFulfillmentCard}
            />

            <ButtonsBarVendorCall
              setPersonPhoneModalVisible={setPersonPhoneModalVisible}
              setPhoneModalVisible={this.clickButtonsBarVendorCall}
              storeAttendant={storeAttendant}
              orderId={orderId}
              orderStatus={orderStatus}
              finalQueryIsFinish={finalQueryIsFinish}
              isFulfillmentOSD={isOsdShowBottomButtons}
            />
          </>,
        )}
        {/* 车辆信息弹层 */}
        <CarDetailModal
          onPressHelp={this.showFuelTypeModal}
          onPressLess={this.showLessModal}
          onCancel={() => setCarDetailModalVisible(false)}
          vehicleInfo={vehicleInfo}
          section={section}
          visible={carDetailModalVisible}
          isRefactor={isRefactor}
        />

        <LessDetailModal
          visible={lessModalVisible}
          onClose={this.hideLessModal}
          data={vehicleInfo?.esgInfo?.reducedCarbonEmissionRatio}
        />

        {/* 能源说明弹窗，内部介绍了各种能源方式，比如插电式、增程式、纯电动是什么意思 */}
        <FuelTypeModal
          visible={isFuelTypeModalVisible}
          fuelType={fuelType}
          onCancel={this.hideFuelTypeModal}
          info={config?.fuelTypeModalInfo}
        />

        {/* 车辆标签弹层 */}
        <CarLabelsModal
          packageItems={carTags}
          pageModalProps={{
            visible: labelsModalVisible,
            onMaskPress: () => setLabelsModalVisible(false),
          }}
        />

        {/* 统一客服电话弹层 */}
        {selector(
          isShowPage,
          <CustomerPhoneModal
            type={phoneModalType}
            modalVisible={phoneModalVisible}
            // @ts-ignore 已定义，提示未找到
            menuList={phoneList}
            onRequestClose={this.closePhoneModal}
          />,
        )}
        {selector(
          isShowPage && Utils.isCtripIsd(),
          <CustomerPersonPhoneModal
            type={phoneModalType}
            modalVisible={personPhoneModalVisible}
            onRequestClose={this.closePersonPhoneModal}
          />,
        )}
        {/* 优选介绍弹窗，不会出现，optimizeModalVisible没有置为true的时候 */}
        <BbkOptimizeModal
          visible={modalsVisible.optimizeModalVisible.visible}
          onClose={() => {
            setOrderModalsVisible({ optimizeModalVisible: { visible: false } });
          }}
          items={ctripSelected}
        />

        {/* 保险订单创建loading */}
        <CreateInsModal
          visible={modalsVisible.createInsModalVisible.visible}
          onClose={this.hideInsModal}
          title={modalsVisible.createInsModalVisible?.data?.title}
          content={modalsVisible.createInsModalVisible?.data?.content}
        />

        {/* 保险订单创建失败弹窗 */}
        <InsFailedModal
          visible={modalsVisible.insFailedModalVisible.visible}
          onClose={this.hideInsFailedModal}
          onPay={this.handleInsGoPay}
          onBack={this.hideInsFailedModal}
          title={PromptMessage.InsMsg.insFailModalTitle}
          content={
            modalsVisible?.insFailedModalVisible?.data?.content ||
            PromptMessage.InsMsg.insFailModalContent
          }
          backBtnText={PromptMessage.InsMsg.backBtnText}
          payBtnText={PromptMessage.InsMsg.payBtnText}
          showPayBtn={
            modalsVisible?.insFailedModalVisible?.data?.showPayBtn ?? true
          }
        />

        {/* 取车材料要求，应该不会出现，没有找到pickUpMaterials赋值的地方 */}
        <PickupMaterialsModal
          visible={modalsVisible?.pickUpMaterials.visible}
          data={modalsVisible?.pickUpMaterials.data}
          onHide={this.hidePickupMaterialModal}
        />

        <ConfirmModal
          visible={modalsVisible.confirmModal.visible}
          {...modalsVisible.confirmModal.data}
          {...this.getModifyOrderProps()}
        />

        {/* 修改订单提示 */}
        <ModifyOrderWarnModal />
        {/* 一嗨修改订单提示 */}
        {ehiModifyOrderModalVisible && <EhiModifyOrderModalContainer />}
        {/* 不可点评弹层 ，样式丢失很久了，不知道会不会弹出来 */}
        <ReviewUnopenedModal
          onHide={this.hideReviewUnopenedModal}
          modalVisible={modalsVisible?.reviewUnopenedModal?.visible}
          orderBaseInfo={orderBaseInfo}
        />

        {/* 公告，配置出 */}
        <NewWarningTipsModal
          visible={warningTipsModalVisible}
          onClose={this.closeWarningTipsModal}
          title={Texts.warningTipDetailText}
          content={warningTipsModalCotent}
        />

        {/* 一嗨门店免押规则，不知道什么时候会出 */}
        <OrderEhiFreeDepositRuleModal />
        <OrderLimitRulesComponentContainer goMap={this.goMap} />

        {/** 芝麻重复订单提示弹层 */}
        <OrderDetaillSesameRepeatOrderModal />
        <OrderDetaiPriceDetailModal role={PageRole.ORDERDETAIL} />

        {/** 保险购买二次确认提示弹层 */}
        <OrderDetaillBuyInsConfirmModalContainer />
        {Utils.isCtripIsd() && (
          <CarServiceModal
            visible={carServiceModalVisible}
            onCancel={this.hideCarServiceModal}
            detailModalVisible={carServiceDetailModalVisible}
            detailModalData={carServiceDetailModalProps}
            closeCarServiceDetail={this.hideCarServiceDetailModal}
            claimMoreVisible={serviceClaimMoreVisible}
            claimMoreModalRefFn={this.handleClaimMoreModalRef}
            closeServiceClaimMore={this.hideServiceClaimMoreModal}
            // @ts-ignore 已定义，提示未找到
            fromPage={CarServiceFromPageTypes.orderDetail}
          />
        )}
        {/** 继续支付失败文案提示 */}
        <ContinuePayFailDialog
          confirmBtnText={Texts.gotIt}
          cancelBtnText={Texts.reBookingText}
        />

        {/* 继续支付重复下单拦截弹窗 */}
        {Utils.isCtripIsd() && <ContinuePayInterceptionModal />}
        {Utils.isCtripIsd() && (
          // 您已申请提前还车
          <AdvanceReturnRecordModal
            modalVisible={modalsVisible?.advanceReturnModal?.visible}
            onRequestClose={this.hideAdvanceReturnModal}
            isHideCancel={this.orderStatusCtrip === OrderStatusCtrip.COMPLETED}
          />
        )}
        {Utils.isCtripIsd() && (
          <NoLimitModal
            visible={noLimitModalVisible}
            onClose={this.hideNoLimitModal}
            content={noLimitDesc}
          />
        )}
        {Utils.isCtripIsd() && (
          // 您已申请提前还车 - 费用明细
          <AdvanceReturnFeeDetailModal
            visible={modalsVisible.advanceReturnFeeModal?.visible}
            onClose={this.hideAdvanceReturnFeeModal}
            feeInfo={
              modalsVisible.advanceReturnFeeModal?.data?.isVendor
                ? advanceReturnFeeInfoByVendor
                : advanceReturnFeeInfo
            }
          />
        )}
        {Utils.isCtripIsd() && (
          <BusinessLicenseModal
            visible={modalsVisible.businessLicenseModal?.visible}
            onClose={this.handleBusinessLicenseClose}
            handleClickImgCallBack={this.handShowBigImgContainer}
          />
        )}
        {isOsdInsurance && (
          <>
            <PriceDetailModalOsd
              visible={osdPriceDetailModalVisible}
              role={PageRole.ORDERDETAIL}
              data={orderPriceInfoFee}
              onClose={this.hideOsdPriceDetailModal}
              showExplainModal={this.showOsdFeeTitleExplainModal}
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_费用明细',

                info: logBaseInfo,
              })}
            />

            <OsdFeeTitleExplainModal
              visible={osdFeeTitleExplainModalVisible}
              feeTitle={feeTitle}
              feeTitleExplain={feeTitleExplain}
              onCancel={this.hideOsdFeeTitleExplainModal}
            />

            <BbkInsuranceSuitsModalOsd
              visible={osdInsuranceVisible}
              anchorCategory={anchorCategory}
              role={PageRole.ORDERDETAIL}
              data={orderDetailResponse}
              packageInfos={packageInfos}
              insPackageId={curPackageId}
              packageId={curPackageId}
              selectPackageId={curPackageId}
              onCancel={this.hideOsdInsuranceModal}
              openExcessIntroduceModal={this.showExcessIntroduceModal}
              openInsuranceNoticeMustRead={
                this.showInsuranceNoticeMustReadModal
              }
              openInsuranceNoticeRule={this.openInsuranceNoticeRule}
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_保障服务_保障服务弹层',

                info: logBaseInfo,
              })}
            />

            {Utils.isCtripOsd() ? (
              // 什么是起配额说明
              <ExcessIntroduceModalOsd
                visible={osdExcessIntroduceVisible}
                onCancel={this.closeExcessIntroduceModal}
                excessIntroduce={config?.excessIntroduceNew}
                excessIntroduceBgOneNew={config?.excessIntroduceBgOneNew}
                excessIntroduceBgTwoNew={config?.excessIntroduceBgTwoNew}
                excessIntroduceDesc={config?.excessIntroduceDesc}
              />
            ) : (
              <ExcessIntroduceModal
                visible={osdExcessIntroduceVisible}
                onCancel={this.closeExcessIntroduceModal}
                excessIntroduce={config?.excessIntroduce}
              />
            )}

            <InsuranceNoticeMustReadModal
              visible={insuranceNoticeMustReadVisible}
              onCancel={this.closeInsuranceNoticeMustReadModal}
              insuranceNotice={insuranceNotice}
            />

            <InsuranceReminderEnglishModal
              visible={insuranceReminderEnglishVisible}
              onCancel={this.closeInsuranceReminderEnglishModal}
              englishContent={englishContent}
            />
          </>
        )}
        {isLicenseApprove && (
          <BbkMaterialModalNew
            visible={materialModalVisible}
            materials={materials}
            printVoucherUrl={printVoucherUrl}
            onCancel={this.closeMaterialModal}
            onPressVoucher={this.onPressVoucher}
            onPressHandleLicense={this.onPressHandleLicense}
            onPressToLicense={this.onPressToLicense}
            onPressQuestion={this.showDepositRateDescriptionModal}
          />
        )}
        {Utils.isCtripOsd() && (
          <FlightDelayRulesModal
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_航班延误政策弹层_航班延误政策',

              info: logBaseInfo,
            })}
          />
        )}
        {isLicenseApprove && (
          <DepositRateDescriptionModal
            visible={depositRateDescriptionModalVisible}
            title={depositRateDescriptionInfo?.title}
            desc={
              depositRateDescriptionInfo?.contents?.[0]?.stringObjs?.[0]
                ?.content
            }
            onCancel={this.closeDepositRateDescriptionModal}
          />
        )}
        {Utils.isCtripIsd() && (
          <EtcIntroModal
            visible={modalsVisible.etcIntroModal?.visible}
            onClose={this.closeEtcIntroModal}
            orderDetailConfirmModalVisible={
              this.props.orderDetailConfirmModalVisible
            }
          />
        )}
        {Utils.isCtripIsd() && (
          <EtcUseHelperModal
            visible={modalsVisible.etcUseHelperModal?.visible}
            onClose={this.closeEtcUseHelperModal}
          />
        )}
        {/* 添加事故处理流程弹层 */}
        {Utils.isCtripIsd() && (
          <ClaimProcessModal
            visible={modalsVisible.claimProcessVisible?.visible}
            modalData={claimProcessModalData}
            onRequestClose={this.closeClaimProcessModal}
          />
        )}
        {/* 用车注意事项弹层 */}
        {Utils.isCtripIsd() && (
          <VehicleUseNotesModal
            visible={modalsVisible.vehicleUseNotesModal?.visible}
            onClose={this.closeVehicleUseNotesModal}
            vehicleUseNotesInfo={vehicleUseNotesInfo}
          />
        )}
        {/* 取车必备材料弹层 */}
        {Utils.isCtripIsd() && (
          <PickUpMaterialsModal
            visible={modalsVisible.pickUpMaterialsModal?.visible}
            onClose={this.closePickUpMaterialsModal}
          />
        )}
        {/* 自助取还距离提醒弹层 */}
        {Utils.isCtripIsd() && (
          <DistanceInvalidateModal
            visible={modalsVisible?.distanceInvalidateModal?.visible}
          />
        )}
        {/* 履约卡片快照 */}
        <View className={c2xStyles.fulfillmentCardSnapShotWrap}>
          <View
            className={c2xStyles.fulfillmentCardSnapShotContent}
            ref={ref => {
              this.fulfillmentCardSnapShot = ref;
            }}
            onLayout={this.handleContractTrackerLayout}
          >
            <OrderFulfillmentContainer
              isExpandContractTracker={true}
              isVehicleStatusPolling={false}
              refreshResult={refreshResult}
              refreshValue={contractTrackerRefreshValue}
            />
          </View>
        </View>
        {/* 境外营业时间收费标准弹层 */}
        {Utils.isCtripOsd() && (
          <BusinessTimePolicyModal
            currentType={modalsVisible.businessTimePolicyModal?.data?.type}
            visible={modalsVisible.businessTimePolicyModal.visible}
            onClose={this.closeBusinessTimePolicyModal}
          />
        )}
        {/* 境外营业时间详情弹层 */}
        {Utils.isCtripOsd() && (
          <BusinessTimeModal
            currentType={modalsVisible.businessTimeModal?.data?.type}
            visible={modalsVisible.businessTimeModal.visible}
            onClose={this.closeBusinessTimeModal}
          />
        )}
        {/* 出境修改订单弹层 */}
        {Utils.isCtripOsd() && (
          <OsdModifyOrderModal
            visible={locationDatePopVisible}
            setLocationAndDatePopIsShow={setLocationAndDatePopIsShow}
            searchPanelModalRefFn={this.handleSearchPanelModalRef}
            currentPageId={this.getPageId()}
            orderId={orderId}
            osdModifyOrderNote={osdModifyOrderNote}
            pressSearchCallback={this.closeOsdModifyOrderModal}
          />
        )}
        {Utils.isCtripOsd() && (
          <ContactDoorStoreModal
            visible={contactDoorStoreModalVisible}
            onClose={this.hideContactDoorStoreModal}
            activeIndex={contactDoorStoreModalTabId}
            pageName={Channel.getPageId().Order.EN}
            orderId={orderId}
            orderStatus={orderStatus}
          />
        )}
        {Utils.isCtripIsd() && (
          <FulfillmentModifyModal
            visible={modalsVisible?.fulfillmentModifyModal?.visible}
            onClose={this.closeFulfillmentModifyModal}
          />
        )}
      </>
    );
  };

  renderPage() {
    const { orderId, queryOrderApiStatus, orderPriceInfo, orderBaseInfo } =
      this.props;
    const isSuccess = [
      QueryOrderApiStatusType.success,
      QueryOrderApiStatusType.before,
    ].includes(queryOrderApiStatus);
    const isFail = queryOrderApiStatus === QueryOrderApiStatusType.fail;

    const { isShowPageOtherModule } = this.state;
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.pageContainer}>
        <OdHeader
          odGoback={this.pageGoBack}
          headerScroll={this.state.headerScroll}
          orderPriceInfo={orderPriceInfo}
          orderBaseInfo={orderBaseInfo}
          orderId={orderId}
          showCouponIcon={false}
          isShowPageOtherModule={isShowPageOtherModule}
          pressCouponIcon={this.showOrderCouponModal}
        />

        {isSuccess && (
          <>
            {this.renderPageContent()}
            {lazySelector(isShowPageOtherModule, this.renderPageModals)}
          </>
        )}
        {!isSuccess &&
          (isFail ? (
            <ErrorNetWork
              title="系统繁忙"
              buttonText="再试一次"
              hasError={isFail}
              operateButtonPress={this.fetchOrderFn}
              buttonTestId={UITestID.car_testid_page_order_retry}
            />
          ) : (
            <LoadingView />
          ))}
      </ViewPort>
    );
  }
}
