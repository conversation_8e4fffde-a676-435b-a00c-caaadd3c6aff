@import '../../../Common/src/Tokens/tokens/color.scss';

.returnTipWrap {
  position: absolute;
  background-color: $descItemDot;
  border-top-start-radius: 13px;
  border-top-end-radius: 12px;
  border-bottom-end-radius: 0px;
  border-bottom-start-radius: 13px;
  right: 15px;
  top: -20px;
  height: 28px;
  padding-left: 12px;
  padding-right: 12px;
  border-width: 1px;
  border-color: $white;
}
.returnTipText {
  margin-top: 1px;
  font-size: 18px;
  line-height: 24px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $white;
}
.selfServiceTipWrap {
  position: absolute;
  background-color: $descItemDot;
  border-top-start-radius: 13px;
  border-top-end-radius: 12px;
  border-bottom-end-radius: 0px;
  border-bottom-start-radius: 13px;
  right: 18px;
  top: -16px;
  height: 28px;
  padding-left: 12px;
  padding-right: 12px;
  border-width: 1px;
  border-color: $white;
}
.cnBtnsWarp {
  margin-top: 24px;
}
.earnCoinsButton {
  padding-top: 4px;
  background: $C_FEEEC2;
  border: 1px solid $white;
  border-radius: 19px 19px 0px 19px;
  position: absolute;
  top: -30px;
  right: 15px;
  height: 38px;
  min-width: 186px;
  justify-content: center;
  align-items: center;
}
.earnCoinsText {
  color: $C_996C00;
  font-size: 22px;
  line-height: 30px;
}
