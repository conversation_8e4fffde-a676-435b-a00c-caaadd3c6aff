@import '../../../Common/src/Tokens/tokens/color.scss';

.refundWrap {
  background-color: $grayBgSecondary;
  border-radius: 12px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 24px;
  padding-right: 24px;
}
.row {
  flex-direction: row;
  align-items: center;
}
.selectLogo {
  width: 48px;
  height: 48px;
  margin-right: 10px;
}
.refundPenaltyTip {
  font-size: 28px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  margin-top: 4px;
}
.helpIcon {
  font-size: 30px;
  color: $grayDescLine;
  line-height: 36px;
  margin-left: 9px;
}
.inputLeft {
  margin-right: 8px;
  font-size: 32px;
  line-height: 42px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.validateWrap {
  margin-top: 16px;
  margin-bottom: 4px;
}
.error {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $refundPenaltyError;
}
.info {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.buttonText {
  color: $blueBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.square {
  width: 8px;
  height: 8px;
  background-color: $grayBase;
  margin-right: 16px;
  margin-top: 15px;
}
.subTitle {
  margin-left: 30px;
  margin-top: 8px;
}
.cancelTip {
  padding-top: 28px;
  padding-bottom: 33px;
  border-radius: 12px;
  margin-top: 24px;
}
.cancelTipImg {
  width: 35px;
  height: 30px;
  position: absolute;
  left: -42px;
}
.cancelTipTitleColor {
  color: $cancelTipTitle;
  margin-right: 16px;
}
.cancelTipTitle {
  font-size: 24px;
  line-height: 32px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 15px;
  color: $fontPrimary;
}
.point {
  width: 4px;
  height: 4px;
  border-radius: 4px;
  left: -16px;
  background-color: $cancelTipTitle;
  position: absolute;
}
.rel {
  position: relative;
}
.triangleImg {
  width: 20px;
  height: 9px;
}
.reasonWrap {
  padding-bottom: 6px;
  margin-top: -6px;
  z-index: 10;
  background-color: $white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.logoIcon {
  width: 44px;
  height: 44px;
  margin-right: 12px;
}
.storeNoCarTitleText {
  font-size: 28px;
  line-height: 38px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $black;
}
.storeNoCarExplain {
  margin-top: 10px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.explain {
  flex: 1;
}
.storeNoCarExplainText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.lineThrough {
  text-decoration-line: line-through;
}
.freeDepositLableTxt {
  color: $orderCardStatusBlueShadow;
  font-size: 20px;
  line-height: 28px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.mt12 {
  margin-top: 12px;
}
.secondTip {
  margin-top: 8px;
  margin-bottom: 12px;
}
.tipWrap {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 16px;
  padding-bottom: 16px;
  background-color: $C_fff8f2;
}
.tipText {
  font-size: 24px;
  line-height: 34px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $C_ff7700;
}
.wrap {
  padding: 32px;
  padding-bottom: 8px;
}
.content {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 24px;
}
.txt {
  padding-bottom: 27px;
}
.cancelReasonTitle {
  color: $blueGrayBase;
  font-size: 32px;
  line-height: 42px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 12px;
}
.cancelReasonDesc {
  color: $blueGrayBase;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-bottom: 32px;
}
.osdCancelReasonTitle {
  color: $blueGrayBase;
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 32px;
}
.cancelOrderError {
  margin-bottom: 20px;
  margin-top: -12px;
  flex-direction: row;
}
.infoIcon {
  font-size: 30px;
  color: $refundPenaltyError;
  margin-right: 8px;
  margin-top: 4px;
}
.inputWrap {
  margin-top: 0px;
}
