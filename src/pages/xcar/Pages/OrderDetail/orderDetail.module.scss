@import '../../Common/src/Tokens/tokens/color.scss';

.blockSection {
  background-color: $white;
}
.mb50 {
  margin-bottom: 50px;
}
.osdAdWrap {
  margin-top: -20px;
}
.isdAdWrap {
  margin-top: -6px;
}
.fulfillLGWrapExpand {
  padding-bottom: 600px;
  margin-bottom: -492px;
}
.fulfillLGWrap {
  padding-bottom: 600px;
  margin-bottom: 0px;
}
.fulfillmentCardSnapShotWrap {
  position: absolute;
  left: 0px;
  bottom: 0px;
  z-index: -999;
  overflow: hidden;
  width: 1px;
  height: 1px;
}
.fulfillmentCardSnapShotContent {
  width: 100vw;
  height: 2000px;
}

.orderInfoBg {
  background-color: $grayBg;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  margin-bottom: 30px; // 增加底部边距，防止联系门店按钮遮挡
}
