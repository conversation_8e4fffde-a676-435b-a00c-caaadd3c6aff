/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-param-reassign */
import { createSelector, Selector } from 'reselect';
import { DateFormatter, BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ResponseMapType } from '@ctrip/rn_com_car/dist/src/CarFetch/src/FetchTypes';
import produce from 'immer';
import { ProductListType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';

import { getThemeConfigRes } from '../Home/Selectors';
import {
  IMarketThemeVendorList,
  ISkinConfig,
} from '../../Types/Dto/QueryThemeConfigResponseType';
import {
  QueryVehicleDetailListResponseType,
  VehicleInfoType,
  ReferenceType,
  VendorPriceListType,
  IncludeFeesType,
  ExtraInfosType,
  RecommendInfo,
  AlbumType,
  MediaType,
  Floor,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import {
  IPicSource,
  ILimitTipInfoType,
  IAtmosphere,
  ISectionHeader,
  ISectionFooter,
  ILocationAndDateMonitor,
  IBbkVehicleNameProps,
  IVehicleImage,
  IBouncesImage,
  IVendorListFirstScreenParamType,
  IVendorListModalVisibleType,
  IVendorListCurrentPageParams,
} from '../../Pages/VendorList/Types';
import Texts from '../../Pages/VendorList/Texts';
import { homeConfig } from '../../Pages/Home/Logic/Index';
import { getLimitRuleData } from '../../Global/Cache/ListResSelectors';
import {
  getIsDifferentLocation,
  getCarHeaderData,
  getPickUpCityName,
  getPointInfoParamsV2,
  getPickUpCityId,
  getDropOffCityId,
} from '../LocationAndDate/Selectors';
import { getSaleOutList } from '../List/Selectors';
import {
  Utils,
  CarABTesting,
  GetABCache,
  CarServerABTesting,
  AppContext,
} from '../../Util/Index';
import { ImgType } from '../../ComponentBusiness/EmptyComponent/Index';
import {
  getRenderVendorList,
  getRenderFloorVendorList,
  getVRLinkByABTesting,
} from './Mappers';
import { getVehPopData } from '../List/BbkMapper';
import {
  QueryVehicleDetailListRequestType,
  VendorListRecommendLogType,
} from '../../Types/Dto/QueryVehicleDetailListRequestType';
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import {
  getNationalChainTag,
  getTotalPriceModalDataV2,
} from '../List/VehicleListMappers';
import { Enums } from '../../ComponentBusiness/Common';
import { ApiResCode, ImageUrl } from '../../Constants/Index';
import { mappingLimitInfo, getLimitInfo } from '../Common/Mapper';
import { mappingResponseFees2PriceModal } from '../List/Mappers';
import { INVOKE_FROM } from '../../Constants/ServerMapping';
import { ILableCode } from '../../Constants/CommonEnums';
import {
  getSpecificVendorPriceList,
  getFilteredVendorPriceList,
} from './Method';
import { getCommonUserInfo } from '../Common/Selectors';

const { LabelCode } = Enums;

export const getIsLoading: Selector<any, boolean> = state =>
  state.VendorList.isLoading;

export const getIsError: Selector<any, boolean> = state =>
  state.VendorList.isError;

const getErrorInfo: Selector<any, any> = state => state.VendorList.errorInfo;

export const getTimeOutPopVisible: Selector<any, boolean> = state =>
  state.VendorList.timeOutPopVisible;

export const getIsLoginAtBookingPage: Selector<any, boolean> = state =>
  state.VendorList.isLoginAtBookingPage;

export const getShelvesCommentSummaryLoading: Selector<any, boolean> = state =>
  state.VendorList.isShelvesCommentSummaryLoading;

export const getUniqueCode = state => state.VendorList?.uniqueCode;

export const getSkuId = state => state.VendorList.skuId;

export const getVehicleModalVisible: Selector<any, boolean> = state =>
  state.VendorList.vehicleModalVisible;

export const getLimitRulePopVisible: Selector<any, boolean> = state =>
  state.VendorList.limitRulePopVisible;

export const getCouponModalVisible: Selector<any, boolean> = state =>
  state.VendorList.couponModalVisible;

export const getProductConfirmModalVisible: Selector<any, boolean> = state =>
  state.VendorList.productConfirmModalVisible;

export const getVirtualNumberDialogVisible: Selector<any, boolean> = state =>
  state.VendorList.isVirtualNumberDialogVisible;

export const getShelvesCommentSummary: Selector<any, object> = state =>
  state.VendorList.shelvesCommentSummary;

export const getPriceDetailModalVisible: Selector<any, boolean> = state =>
  state.VendorList.priceDetailModalVisible;

export const getProductConfirmAnchor: Selector<any, LayoutPartEnum> = state =>
  state.VendorList.productConfirmAnchor;

export const getVehicleIndex: Selector<any, number> = state =>
  state.VendorList.vehicleIndex;

export const getTotalPriceModalVisible: Selector<any, boolean> = state =>
  state.VendorList.totalPriceModalVisible;

export const getEasyLifePopVisible: Selector<any, boolean> = state =>
  state.VendorList.isEasyLifeModalVisible;

export const getIsCouponBookAtBookingPage: Selector<any, boolean> = state =>
  state.VendorList.isCouponBookAtBookingPage;

export const getOptimizationStrengthenModalVisible: Selector<
  any,
  boolean
> = state => state.VendorList.optimizationStrengthenModalVisible;

export const getCarServiceDetailVisible: Selector<any, boolean> = state =>
  state.VendorList.carServiceDetailVisible;

export const getVirtualNumberStoreModalVisible: Selector<
  any,
  boolean
> = state => state.VendorList.virtualNumberStoreModalVisible;

const getIsSecretBoxListToBook: Selector<any, boolean> = state =>
  state.VendorList?.isSecretBoxListToBook;

export const getVehicleDetailListResponse: Selector<
  any,
  QueryVehicleDetailListResponseType
> = state => state.VendorList?.queryVehicleDetailListRes;

export const getShelvesFloor = state =>
  state.VendorList?.queryVehicleDetailListRes?.floor;

export const getShelvesVehicleInfo = state =>
  state.VendorList?.queryVehicleDetailListRes?.vehicleInfo;

export const getNeedVenderNameLabel = state =>
  state.VendorList?.queryVehicleDetailListRes?.extras
    ?.goodsShelves2EhaiLogic === '1';

export const getFloorId = state => state.VendorList?.floorId;

export const getPackageCode = state => state.VendorList?.packageCode;

export const getVehicleDetailListRequest: Selector<
  any,
  QueryVehicleDetailListRequestType
> = state => {
  return state.VendorList.queryVehicleDetailListReq;
};

const getFirstScreenParam: Selector<
  any,
  IVendorListFirstScreenParamType
> = state => state.VendorList.firstScreenParam;

export const getVehicleInfo = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  IVendorListFirstScreenParamType,
  any
>(
  [getVehicleDetailListResponse, getFirstScreenParam],
  (response, firstScreenParam) => {
    // 当列表页有透传首屏数据时，优先使用透传数据
    if (!firstScreenParam?.isUsed && firstScreenParam?.vehicleInfo) {
      return firstScreenParam.vehicleInfo;
    }
    return response?.vehicleInfo || null;
  },
);

export const getTangramEntranceInfos = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  any
>([getVehicleDetailListResponse], response => {
  return response?.promptInfos?.filter(
    item => item.type === ApiResCode.ListPromptType.Market,
  );
});

export const getVehicleModalInfo = createSelector<any, VehicleInfoType, any>(
  [getVehicleInfo],
  vehicleInfo => {
    return getVehPopData({
      vehicleCode: vehicleInfo?.vehicleCode || '',
      curVehInfo: vehicleInfo || {},
    });
  },
);

export const getMarketingAtmosphere = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  number
>([getVehicleDetailListResponse], response => {
  return response?.marketingAtmosphere;
});

export const getShareVehicleInfo = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  number
>([getVehicleDetailListResponse], response => {
  return response?.shareVehicleInfo;
});

export const getHasLaborDayLabel = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  boolean
>([getVehicleDetailListResponse], response => {
  const { homeThemeConfig } = homeConfig;
  const isSpecificBaborDay = !!getSpecificVendorPriceList(response)?.find(
    vendorPrice =>
      vendorPrice?.allTags?.find(tag => tag.labelCode === LabelCode.LaborDay),
  );
  const isFloorBaborDay = !!response?.floor?.find(fItem =>
    fItem?.alltags?.find(tag => tag.labelCode === LabelCode.LaborDay),
  );
  return (
    !!homeThemeConfig?.vendorListBg && (isSpecificBaborDay || isFloorBaborDay)
  );
});

export const getVendorListMarketTheme = createSelector<
  any,
  ISkinConfig,
  IMarketThemeVendorList
>([getThemeConfigRes], themeConfigRes => {
  return themeConfigRes?.detailPage;
});

export const getIsShowMarketTheme = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  IMarketThemeVendorList,
  boolean
>(
  [getVehicleDetailListResponse, getVendorListMarketTheme],
  (response, vendorListTheme) => {
    const { filterCode, skinUrl } = vendorListTheme || {};
    const isSpecificTheme = !!getSpecificVendorPriceList(response)?.find(
      vendorPrice =>
        vendorPrice?.allTags?.find(tag => tag.labelCode === filterCode),
    );
    const isFloorTheme = !!response?.floor?.find(fItem =>
      fItem?.alltags?.find(tag => tag.labelCode === filterCode),
    );
    return !!skinUrl && (isSpecificTheme || isFloorTheme);
  },
);

export const getAppResponseMap = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  ResponseMapType
>([getVehicleDetailListResponse], response => {
  // 当服务端告知isFromSearch为false时，则认为是此次请求是命中了同一批列表页缓存
  let curAppResponseMap = null;
  if (response?.appResponseMap) {
    curAppResponseMap = produce(response.appResponseMap, draftMap => {
      draftMap.isFromCache = !!(
        response &&
        response?.baseResponse?.isSuccess &&
        response?.isFromSearch === false
      );
    });
  }
  return curAppResponseMap;
});

export const getBbkVehicleNameProps = createSelector<
  any,
  VehicleInfoType,
  IBbkVehicleNameProps
>([getVehicleInfo], vehicleInfo => {
  return {
    name: vehicleInfo?.style
      ? `${vehicleInfo?.name} (${vehicleInfo?.style})`
      : vehicleInfo?.name,
    isSimilar: !vehicleInfo?.isSpecialized,
    isHotLabel: vehicleInfo?.isHot,
    licenseTag: vehicleInfo?.license,
    licenseType: vehicleInfo?.licenseStyle,
  };
});

export const getIsShowRestAssured = createSelector<any, number, boolean>(
  [getMarketingAtmosphere],
  marketingAtmosphere => {
    return [
      IAtmosphere.RestAssured,
      IAtmosphere.WarmSpring,
      IAtmosphere.Yunnan,
    ].includes(marketingAtmosphere);
  },
);

// 组装车图组件数据

const isEqualImage = (first, second) => {
  const pre = first?.replace(`?${ImageUrl.YiChePictureSuffix}`, '');
  const next = second?.replace(`?${ImageUrl.YiChePictureSuffix}`, '');
  return pre === next;
};

// 组装多媒体二期图片数据
export const getBouncesImageInfo = createSelector<
  any,
  VehicleInfoType,
  IVendorListFirstScreenParamType,
  IBouncesImage
>([getVehicleInfo, getFirstScreenParam], (vehicleInfo, firstScreenParam) => {
  const listImageUrl = firstScreenParam?.vehicleInfo?.imageList?.[0];
  // 列表页的图片在详情页展示需要控制边距（除了视屏封面图，因为视屏播放器的封面图无法调整）
  if (
    !vehicleInfo?.multimediaAlbums &&
    firstScreenParam?.vehicleInfo?.realityImageUrl
  ) {
    const isHasVideo = firstScreenParam?.vehicleInfo?.mediaTypes?.includes(
      MediaType.Video,
    );
    return {
      totalPhotos: [
        {
          url: firstScreenParam?.vehicleInfo.realityImageUrl,
          type: MediaType.Picture,
          // 列表页的图片在详情页展示需要控制边距
          isEqualListImage:
            !isHasVideo &&
            !!listImageUrl &&
            isEqualImage(
              listImageUrl,
              firstScreenParam?.vehicleInfo?.realityImageUrl,
            ),
        },
      ],

      vehicleCode: firstScreenParam?.vehicleInfo?.vehicleCode,
      tabNameMap: {},
      tabNameMapIndex: {},
      mediaGroup: [],
    };
  }

  const album = vehicleInfo?.multimediaAlbums?.find(
    alItem => alItem.albumType === AlbumType.Official,
  );
  const totalPhotos = [];
  const tabNameMap = {};
  const tabNameMapIndex = {};
  const sortMediaGroup = BbkUtils.cloneDeep(album?.mediaGroup || [])?.sort(
    (first, next) => (first?.groupSortNum || 0) - (next?.groupSortNum || 0),
  );
  let totalNum = 0;
  sortMediaGroup?.forEach(group => {
    const { medias, groupName, groupType } = group;
    tabNameMap[groupName] = totalNum;

    medias?.forEach((media, index) => {
      tabNameMapIndex[totalNum + index] = groupName;
      const imageUrl =
        media?.type === MediaType.Video || media?.type === MediaType.VR
          ? media?.cover
          : media.url;
      totalPhotos.push({
        ...media,
        groupType,
        // 列表页的图片在详情页展示需要控制边距（除了视屏封面图，因为视屏播放器的封面图无法调整）
        isEqualListImage:
          media?.type !== MediaType.Video &&
          !!listImageUrl &&
          isEqualImage(listImageUrl, imageUrl),
      });
    });
    totalNum += medias?.length || 0;
  });

  return {
    totalPhotos,
    vehicleCode: vehicleInfo?.vehicleCode,
    tabNameMap,
    tabNameMapIndex,
    note: album?.note,
    mediaGroup: sortMediaGroup,
  };
});

enum FuelTypeEnum {
  PureElectric = '纯电动',
  ExtendedProgram = '增程式',
  PlugInType = '插电式',
  Gasoline = '汽油',
}
type DescItemType = {
  code: string;
  text: any;
  iconUrl: string;
};
export const getVehicleBaseDescListB = createSelector(
  [getVehicleInfo],
  vehicleInfo => {
    const {
      groupName, // 车型组
      doorNo = '', // 门数
      passengerNo = '', // 座位数
      transmissionName, // 挡位
      style = '', // 年款
      displacement: originDisplacement = '', // 排量
      fuelType, // 能源类型
      shortEndurance, // 续航短描述
    } = vehicleInfo || {};

    const doorText = doorNo ? `${doorNo}${Texts.door}` : '';
    const seatText = passengerNo ? `${passengerNo}${Texts.seat}` : '';
    const doorAndPassengerNo = `${seatText}${doorText}`;
    const displacement = `${originDisplacement}`;

    let transmissionAndDisplacement: DescItemType;
    switch (fuelType) {
      case FuelTypeEnum.PureElectric:
        transmissionAndDisplacement = {
          code: 'transmissionAndDisplacement',
          text: shortEndurance ? `纯电${shortEndurance}` : '纯电',
          iconUrl: `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`,
        };
        break;
      case FuelTypeEnum.ExtendedProgram:
        transmissionAndDisplacement = {
          code: 'transmissionAndDisplacement',
          text: FuelTypeEnum.ExtendedProgram,
          iconUrl: `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`,
        };
        break;
      case FuelTypeEnum.PlugInType:
        transmissionAndDisplacement = {
          code: 'transmissionAndDisplacement',
          text: `${FuelTypeEnum.PlugInType}${displacement}`,
          iconUrl: `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`,
        };
        break;
      case FuelTypeEnum.Gasoline:
      default:
        transmissionAndDisplacement = {
          code: 'transmissionAndDisplacement',
          text: `${transmissionName}${displacement}`,
          iconUrl: `${ImageUrl.DIMG04_PATH}1tg3q12000iq70slkE9AC.png`,
        };
        break;
    }

    const descList: {
      code: string;
      text: any;
      iconUrl: string;
    }[] = [
      groupName && {
        code: 'groupName',
        text: groupName,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg5q12000iq70twf4E54.png`,
      },
      doorAndPassengerNo && {
        code: 'doorAndPassengerNo',
        text: doorAndPassengerNo,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg0i12000in25kg3061E.png`,
      },
      transmissionAndDisplacement && transmissionAndDisplacement,
    ];

    return descList;
  },
);

const getEnergyType = (fuelType: string, transmissionName: string) => {
  switch (fuelType) {
    case FuelTypeEnum.PureElectric:
      return {
        code: 'energyTypePureElectric',
        text: '低碳纯电',
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg6g12000jpsq5552A55.png`,
      };
    case FuelTypeEnum.ExtendedProgram:
      return {
        code: 'energyTypeExtendedProgram',
        text: '低碳车型',
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg6g12000jpsq5552A55.png`,
      };
    case FuelTypeEnum.PlugInType:
      return {
        code: 'energyTypePlugInType',
        text: '低碳插电',
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg6g12000jpsq5552A55.png`,
      };
    case FuelTypeEnum.Gasoline:
    default: {
      return {
        code: 'energyTypeGasoline',
        text: transmissionName,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg0q12000jpr9k7d32DD.png`,
      };
    }
  }
};

export const getVehicleBaseDescList2 = createSelector(
  [getVehicleInfo],
  vehicleInfo => {
    const {
      groupName, // 车型组
      doorNo = '', // 门数
      passengerNo = '', // 座位数
      transmissionName, // 挡位
      style = '', // 年款
      displacement: originDisplacement = '', // 排量
      fuelType, // 能源类型
      shortEndurance, // 续航短描述
    } = vehicleInfo || {};

    const doorText = doorNo ? `${doorNo}${Texts.door}` : '';
    const seatText = passengerNo ? `${passengerNo}${Texts.seat}` : '';
    const doorAndPassengerNo = `${seatText}${doorText}`;
    const displacement = `${originDisplacement}`;

    let transmissionAndDisplacement: DescItemType;

    switch (fuelType) {
      case FuelTypeEnum.PureElectric:
        if (shortEndurance) {
          transmissionAndDisplacement = {
            code: 'pureElectric',
            text: shortEndurance,
            iconUrl: `${ImageUrl.DIMG04_PATH}1tg3w12000jpsuyo29623.png`,
          };
        }
        break;
      case FuelTypeEnum.ExtendedProgram:
        transmissionAndDisplacement = {
          code: 'extendedProgram',
          text: '增程式',
          iconUrl: `${ImageUrl.DIMG04_PATH}1tg0w12000jpsi412C75E.png`,
        };
        break;
      case FuelTypeEnum.PlugInType:
        if (displacement) {
          transmissionAndDisplacement = {
            code: 'plugInType',
            text: displacement,
            iconUrl: `${ImageUrl.DIMG04_PATH}1tg0u12000jpraod12787.png`,
          };
        }
        break;
      case FuelTypeEnum.Gasoline:
      default:
        if (displacement && transmissionName) {
          transmissionAndDisplacement = {
            code: `${transmissionName}`,
            text: displacement,
            iconUrl: `${ImageUrl.DIMG04_PATH}1tg0u12000jpraod12787.png`,
          };
        }
        break;
    }

    const descList: {
      code: string;
      text: any;
      iconUrl: string;
    }[] = [
      groupName && {
        code: 'groupName',
        text: groupName,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg0z12000jpqpext93C4.png`,
      },
      doorAndPassengerNo && {
        code: 'doorAndPassengerNo',
        text: doorAndPassengerNo,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg4p12000jph4esp2AEF.png`,
      },
      getEnergyType(fuelType, transmissionName),
    ];
    if (transmissionAndDisplacement) {
      descList.push(transmissionAndDisplacement);
    }

    return descList;
  },
);

export const getVehicleDescList = createSelector(
  [getVehicleInfo],
  vehicleInfo => {
    const descList = [];
    const doorText = vehicleInfo?.doorNo
      ? `${vehicleInfo.doorNo}${Texts.door}`
      : '';
    const seatText = vehicleInfo?.passengerNo
      ? `${vehicleInfo.passengerNo}${Texts.seat}`
      : '';
    const doorAndPassengerNo = `${doorText} ${seatText}`;
    if (vehicleInfo?.groupName) descList.push({ text: vehicleInfo.groupName });
    if (vehicleInfo?.passengerNo)
      descList.push({ text: `${vehicleInfo.passengerNo}${Texts.seat}` });
    if (vehicleInfo?.doorNo)
      descList.push({ text: `${vehicleInfo.doorNo}${Texts.door}` });
    if (vehicleInfo?.transmissionName)
      descList.push({ text: vehicleInfo.transmissionName });
    if (vehicleInfo?.displacement)
      descList.push({
        text: vehicleInfo.displacement,
      });
    if (ApiResCode.NewEnergyVehFuelTypes.includes(vehicleInfo?.fuelType)) {
      descList.push({ text: vehicleInfo?.fuelType });
    }
    return descList;
  },
);

/**
 * 新详情页限行提示文案取值逻辑为：
 * 1、若新详情页接口返回的licenseDescription有值,则代表该城市有限行信息，但该车型不限行
 * 2、若新详情页接口返回的licenseDescription没有值,但列表页限行接口返回的limitDesc有值，则代表该城市有限行信息，且该车型限行
 * 3、若两个接口返回的字段都没有值,则代表没有限行信息
 * 4、为了保证新详情页不先出现列表页的限行提示，再刷新最终的限行提示，所以当详情页接口没有数据时，不展示限行信息
 */
export const getLimitTipInfo = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  ILimitTipInfoType
>([getPickUpCityName, getVehicleDetailListResponse], (cityName, response) => {
  if (!response) {
    return {
      isShowTip: false,
      isLimit: true,
      tipText: '',
    };
  }
  const limitDesc = mappingLimitInfo(getLimitRuleData());
  const tipText = response?.vehicleInfo?.licenseDescription || limitDesc?.title;
  return {
    isShowTip: limitDesc?.title || (response?.vehicleInfo && !!tipText),
    isLimit: !response?.vehicleInfo?.licenseDescription,
    tipText,
  };
});

export const getCurFloor = createSelector<any, Array<Floor>, string, Floor>(
  [getShelvesFloor, getFloorId],
  (shelvesFloor, floorId) => {
    const curFloor =
      shelvesFloor?.find(floor => floor.floorId === floorId) || Utils.EmptyObj;
    return curFloor;
  },
);

export const getUniquePriceInfo = createSelector<
  any,
  string,
  QueryVehicleDetailListResponseType,
  boolean,
  Array<Floor>,
  string,
  string,
  VendorPriceListType
>(
  [
    getUniqueCode,
    getVehicleDetailListResponse,
    getIsSecretBoxListToBook,
    getShelvesFloor,
    getFloorId,
    getPackageCode,
  ],
  (
    uniqueCode,
    response,
    isSecretBoxListToBook,
    shelvesFloor,
    floorId,
    packageCode,
  ) => {
    // 如果是货架2。0，获取价格方式发生变化
    if (CarServerABTesting.isISDShelves2B() && floorId && packageCode) {
      const curFloor = shelvesFloor?.find(floor => floor.floorId === floorId);
      const curPriceInfo = curFloor?.packageList?.find(
        cPackage => cPackage.code === packageCode,
      );
      return {
        ...curPriceInfo,
        alltags: curFloor?.alltags,
        isSelect: curFloor.isSelect,
        vendorName: curFloor?.vendorName,
        ctripVehicleCode: response?.vehicleInfo?.vehicleCode,
        easyLifeInfo: {
          isEasyLife: curPriceInfo?.reference?.isEasyLife,
        },
        decorateVehicleName: '',
      };
    }
    if (isSecretBoxListToBook) {
      // 盲盒首页直接跳转填写页，没有uniqueCode，直接取vendorPriceList第一个
      return (
        getSpecificVendorPriceList(response)?.[0] ||
        getFilteredVendorPriceList(response)?.[0]
      );
    }
    return (
      getSpecificVendorPriceList(response)?.find(
        v => v.uniqueCode === uniqueCode,
      ) ||
      getFilteredVendorPriceList(response)?.find(
        v => v.uniqueCode === uniqueCode,
      ) ||
      null
    );
  },
);

export const getUniqueReference = createSelector<
  any,
  VendorPriceListType,
  ReferenceType
>([getUniquePriceInfo], priceInfo => {
  return priceInfo?.reference;
});

export const getFloorVendorList = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  QueryVehicleDetailListRequestType,
  VehicleInfoType,
  number,
  Array<VendorPriceListType>
>(
  [
    getVehicleDetailListResponse,
    getVehicleDetailListRequest,
    getVehicleInfo,
    getVehicleIndex,
  ],

  (response, request, vehicleInfo, vehicleIndex) => {
    return getRenderFloorVendorList(
      response?.floor,
      request?.filters,
      vehicleInfo,
      vehicleIndex,
      CarABTesting.packageAbversion(response),
      request?.sortType,
    );
  },
);

export const getSpecificVendorList = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  boolean,
  QueryVehicleDetailListRequestType,
  VehicleInfoType,
  number,
  Array<VendorPriceListType>
>(
  [
    getVehicleDetailListResponse,
    getIsDifferentLocation,
    getVehicleDetailListRequest,
    getVehicleInfo,
    getVehicleIndex,
  ],

  (response, isDifferentLocation, request, vehicleInfo, vehicleIndex) => {
    const isFit = true;
    return getRenderVendorList(
      response?.specificProductGroups?.vendorPriceList,
      isDifferentLocation,
      request?.browVendorCode,
      request?.filters,
      vehicleInfo,
      vehicleIndex,
      isFit,
      CarABTesting.packageAbversion(response),
      request?.sortType,
    );
  },
);

export const getFilteredVendorList = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  boolean,
  QueryVehicleDetailListRequestType,
  VehicleInfoType,
  number,
  Array<VendorPriceListType>
>(
  [
    getVehicleDetailListResponse,
    getIsDifferentLocation,
    getVehicleDetailListRequest,
    getVehicleInfo,
    getVehicleIndex,
  ],

  (response, isDifferentLocation, request, vehicleInfo, vehicleIndex) => {
    const isFit = false;
    return getRenderVendorList(
      response?.filteredProductGroups?.vendorPriceList,
      isDifferentLocation,
      request?.browVendorCode,
      request?.filters,
      vehicleInfo,
      vehicleIndex,
      isFit,
      CarABTesting.packageAbversion(response),
    );
  },
);

export const getShelvesVendorList = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  boolean,
  QueryVehicleDetailListRequestType,
  VehicleInfoType,
  number,
  any
>(
  [
    getVehicleDetailListResponse,
    getIsDifferentLocation,
    getVehicleDetailListRequest,
    getVehicleInfo,
    getVehicleIndex,
  ],

  (response, isDifferentLocation, request, vehicleInfo, vehicleIndex) => {
    const first = getRenderVendorList(
      response?.specificProductGroups?.vendorPriceList,
      isDifferentLocation,
      request?.browVendorCode,
      request?.filters,
      vehicleInfo,
      vehicleIndex,
      true,
      CarABTesting.packageAbversion(response),
      request?.sortType,
    );
    const firstFilter = getRenderVendorList(
      response?.filteredProductGroups?.vendorPriceList,
      isDifferentLocation,
      request?.browVendorCode,
      request?.filters,
      vehicleInfo,
      vehicleIndex,
      false,
      CarABTesting.packageAbversion(response),
      undefined,
    );
    return {
      detailPage: [
        {
          vendorList: first,
          filteredVendorList: firstFilter,
        },
      ],
      isNoMatch: !(first?.length > 0) && !(firstFilter?.length > 0),
    };
  },
);

// 对应有结果但售罄的场景
// http://design.ctripcorp.com/draft/613888f704928c0053ff693a/start.html#id=m5tt3x&p=%E7%AD%9B%E9%80%89%E7%BB%93%E6%9E%9C%E5%B1%95%E7%A4%BA&g=1
export const getIsHasSpecificButSoldOut = createSelector<
  any,
  Array<VendorPriceListType>,
  Array<VendorPriceListType>,
  Array<string>,
  boolean
>(
  [getSpecificVendorList, getFilteredVendorList, getSaleOutList],
  (specificVendorList, filteredVendorList, saleOutList) => {
    return (
      specificVendorList.length > 0 &&
      filteredVendorList.length > 0 &&
      saleOutList.length > 0 &&
      !specificVendorList.find(
        item => !Utils.validateIsSaleOut(saleOutList, item),
      )
    );
  },
);

// 对应筛选后无结果的场景
// http://design.ctripcorp.com/draft/613888f704928c0053ff693a/start.html#id=m5tt3x&p=%E7%AD%9B%E9%80%89%E7%BB%93%E6%9E%9C%E5%B1%95%E7%A4%BA&g=1
export const getIsNoSpecificButHasFiltered = createSelector<
  any,
  Array<VendorPriceListType>,
  Array<VendorPriceListType>,
  boolean
>(
  [getSpecificVendorList, getFilteredVendorList],
  (specificVendorList, filteredVendorList) => {
    return specificVendorList.length === 0 && filteredVendorList.length > 0;
  },
);

// 符合条件全部售罄且没有不符合条件场景
const getIsSpecificSoldOutAndNoFiltered = createSelector<
  any,
  Array<VendorPriceListType>,
  Array<VendorPriceListType>,
  Array<string>,
  boolean
>(
  [getSpecificVendorList, getFilteredVendorList, getSaleOutList],
  (specificVendorList, filteredVendorList, saleOutList) => {
    return (
      specificVendorList.length > 0 &&
      filteredVendorList.length === 0 &&
      saleOutList.length > 0 &&
      !specificVendorList.find(
        item => !Utils.validateIsSaleOut(saleOutList, item),
      )
    );
  },
);

export const getLocationDate = createSelector<
  any,
  any,
  ILocationAndDateMonitor
>([getCarHeaderData], carHeaderData => {
  const { pickupLocation, returnLocation, ptime, rtime } = carHeaderData;
  const { pickUpDateStr, dropOffDateStr } =
    DateFormatter.pickUpAndDropOffDateFormat(ptime, rtime);
  return {
    pickupLocation,
    returnLocation,
    ptime: pickUpDateStr,
    rtime: dropOffDateStr,
  };
});

// 获取接口异常类型
export const getErrorImgType = createSelector(
  [getIsError, getErrorInfo, getSpecificVendorList, getFilteredVendorList],
  (isError, errorInfo, specificVendorList, filteredVendorList) => {
    const hasFilteredVendor = filteredVendorList?.length > 0;
    const hasSpecificVendor = specificVendorList?.length > 0;
    if (isError) {
      return Utils.validateIsServerError(errorInfo)
        ? ImgType.No_Response
        : ImgType.No_Network;
    }
    if (!hasFilteredVendor && !hasSpecificVendor) {
      return ImgType.Sold_Out;
    }
    if (hasFilteredVendor && !hasSpecificVendor) {
      return ImgType.No_Match;
    }
    return null;
  },
);

export const getVehicleCode = createSelector<any, VehicleInfoType, any>(
  [getVehicleInfo],
  vehicleInfo => {
    return vehicleInfo?.vehicleCode || '';
  },
);

export const getReducedCarbonEmissionRatio = createSelector(
  [getVehicleInfo],
  vehicleInfo => {
    return vehicleInfo?.esgInfo?.reducedCarbonEmissionRatio || 0;
  },
);

export const getSpecificSectionHeader = createSelector<
  any,
  QueryVehicleDetailListRequestType,
  ImgType,
  Array<VendorPriceListType>,
  boolean,
  ILocationAndDateMonitor,
  string,
  ISectionHeader
>(
  [
    getVehicleDetailListRequest,
    getErrorImgType,
    getSpecificVendorList,
    getIsLoading,
    getLocationDate,
    getVehicleCode,
  ],

  (
    request,
    errorImgType,
    specificVendorList,
    isLoading,
    locationDate,
    vehicleCode,
  ) => {
    const hasSpecificVendor = specificVendorList?.length > 0;
    // 列表页存在筛选项时，即使没有不符合，也展示符合的title； 不筛选不用分组和title。
    const isShowFitTitle =
      !isLoading && request?.filters?.length > 0 && !errorImgType;
    const isShowFitBottomGradientLine =
      !isLoading &&
      (request?.filters?.length > 0 || !!errorImgType) &&
      !Utils.isCtripIsd();
    return {
      isFit: true,
      isShowFitTitle,
      isShowFitBottomGradientLine,
      isShowGradient: isLoading || hasSpecificVendor,
      locationDate,
      vehicleCode,
    };
  },
);

export const getSpecificSectionHeaderNew = createSelector<
  any,
  QueryVehicleDetailListRequestType,
  ImgType,
  Array<VendorPriceListType>,
  boolean,
  ILocationAndDateMonitor,
  string,
  ISectionHeader
>(
  [
    getVehicleDetailListRequest,
    getErrorImgType,
    getSpecificVendorList,
    getIsLoading,
    getLocationDate,
    getVehicleCode,
  ],

  (
    request,
    errorImgType,
    specificVendorList,
    isLoading,
    locationDate,
    vehicleCode,
  ) => {
    const hasSpecificVendor = specificVendorList?.length > 0;
    // 列表页存在筛选项时，即使没有不符合，也展示符合的title； 不筛选不用分组和title。
    const isShowFitTitle = !isLoading && request?.filters?.length > 0;
    const isShowFitBottomGradientLine =
      !isLoading &&
      (request?.filters?.length > 0 || !!errorImgType) &&
      !Utils.isCtripIsd();
    return {
      isFit: true,
      isShowFitTitle,
      isShowFitBottomGradientLine,
      isShowGradient: isLoading || hasSpecificVendor,
      locationDate,
      vehicleCode,
    };
  },
);

export const getFloorSectionHeader = createSelector<
  any,
  QueryVehicleDetailListRequestType,
  ImgType,
  Array<VendorPriceListType>,
  boolean,
  ILocationAndDateMonitor,
  string,
  ISectionHeader
>(
  [
    getVehicleDetailListRequest,
    getErrorImgType,
    getFloorVendorList,
    getIsLoading,
    getLocationDate,
    getVehicleCode,
  ],

  (
    request,
    errorImgType,
    floorVendorList,
    isLoading,
    locationDate,
    vehicleCode,
  ) => {
    const hasSpecificVendor = floorVendorList?.length > 0;
    // 列表页存在筛选项时，即使没有不符合，也展示符合的title； 不筛选不用分组和title。
    const isShowFitTitle = !isLoading && request?.filters?.length > 0;
    const isShowFitBottomGradientLine =
      !isLoading &&
      (request?.filters?.length > 0 || !!errorImgType) &&
      !Utils.isCtripIsd();
    return {
      isFit: true,
      isShowFitTitle,
      isShowFitBottomGradientLine,
      isShowGradient: isLoading || hasSpecificVendor,
      locationDate,
      vehicleCode,
    };
  },
);

export const getSpecificSectionFooter = createSelector<
  any,
  boolean,
  ISectionFooter
>([getIsSpecificSoldOutAndNoFiltered], isSpecificSoldOutAndNoFiltered => {
  return {
    isShowBackToListPage: isSpecificSoldOutAndNoFiltered,
  };
});

export const getIncludeFees = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  IncludeFeesType
>([getVehicleDetailListResponse], response => {
  return response?.includeFees;
});

export const getTotalPriceModalData = createSelector<
  any,
  VendorPriceListType,
  any
>([getUniquePriceInfo], vendorPrice => {
  return getTotalPriceModalDataV2(
    vendorPrice?.priceInfo?.currentDailyPrice,
    vendorPrice?.priceInfo?.currentTotalPrice,
    Enums.TotalPriceModalType.Vendor,
  );
});

export const getVendorListShowModalKey: Selector<any, string> = state => {
  return Object.keys(IVendorListModalVisibleType).find(
    key => state.VendorList[key],
  );
};

// 组装新版填写页首屏渲染数据
export const getFirstScreenParamToBooking = createSelector(
  [getUniquePriceInfo, getVehicleDetailListResponse],
  (priceInfo: VendorPriceListType, response) => {
    return {
      vehicleInfo: {
        ...response?.vehicleInfo,
      },
      vendorInfo: {
        vendorName: priceInfo?.vendorName,
      },
      isSelect: priceInfo?.isSelect,
      isEasyLife: priceInfo?.easyLifeInfo?.isEasyLife,
      nationalChainTagTitle: getNationalChainTag(priceInfo?.allTags)?.title,
      allTags: {
        vehicleTagList: priceInfo?.allTags?.filter(flex => flex.groupId === 2),
      },
      isSelfService: !!priceInfo?.allTags?.find(
        item => item?.labelCode === ILableCode.SelfService,
      ),
    };
  },
);

// 当前产品是否是领券订产品
export const getIsCouponBook = createSelector<
  any,
  VendorPriceListType,
  boolean
>([getUniquePriceInfo], priceInfo => {
  return priceInfo?.reference?.rCoup === 1;
});

// 通过uniqueCode获取当前供应商报价
export const getVendorInfoByUniqueCode = (
  response: QueryVehicleDetailListResponseType,
  uniqueCode: string,
) => {
  const curVendor =
    getSpecificVendorPriceList(response)?.find(
      v => v.uniqueCode === uniqueCode,
    ) ||
    getFilteredVendorPriceList(response)?.find(
      v => v.uniqueCode === uniqueCode,
    ) ||
    null;
  return curVendor;
};

export const getCouponBookInfoByUniqueCode = (
  response: QueryVehicleDetailListResponseType,
  uniqueCode: string,
) => {
  const curVendor = getVendorInfoByUniqueCode(response, uniqueCode);
  const curPromtId = curVendor?.reference?.promtId;
  return {
    isCouponBook: curVendor?.reference?.rCoup === 1,
    promotionId: curPromtId,
    promotionSecretId: response?.promotMap?.[curPromtId],
  };
};
export const getFeeMap = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  any
>([getVehicleDetailListResponse], response => {
  return response?.feeMap;
});

export const getVehicleVR = createSelector<any, VehicleInfoType, any>(
  [getVehicleInfo],
  vehicleInfo => getVRLinkByABTesting(vehicleInfo?.vr),
);

export const getYunnanBannerInfo = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  ExtraInfosType
>([getVehicleDetailListResponse], response => {
  const yunnanPromptInfo = response?.promptInfos?.find(
    f => f.type === ApiResCode.ListPromptType.YunnanEnter,
  );
  return yunnanPromptInfo?.extraInfos;
});

export const getMinTPriceVendor = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  any
>([getVehicleDetailListResponse], response => {
  const floorMinPrice = response?.floor?.find(fItem => fItem.lowestPrice === 1)
    ?.packageList?.[0];
  const minPriceInfo = getSpecificVendorPriceList(response)?.reduce(
    (pre, next) =>
      pre.priceInfo?.currentTotalPrice < next.priceInfo?.currentTotalPrice
        ? pre
        : next,
  );
  return floorMinPrice || minPriceInfo;
});

export const getMinTotalPriceOtherDesc = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  any
>([getVehicleDetailListResponse], response => {
  const isSpecificLength = getSpecificVendorPriceList(response)?.length > 1;
  const isFloorLength =
    response?.floor?.length > 1 ||
    response?.floor?.[0]?.packageList?.length > 1;
  return isSpecificLength || isFloorLength ? Texts.recommendPriceTitle : '';
});

// 详情页缓存 按照当前取车城市&限行提示弹窗是否展示 创建限行提示缓存
export const getLimitData = createSelector(
  [getPickUpCityName, getLimitRulePopVisible],
  (pickUpCityName, visible) => getLimitInfo(),
);

export const getLocationDatePopVisible: Selector<any, boolean> = state =>
  state.VendorList.locationDatePopVisible;

export const getLocationDatePopType: Selector<any, number> = state =>
  state.VendorList.locationDatePopType;

export const getLocationDatePopEnterPosition: Selector<any, number> = state =>
  state.VendorList.locationDatePopEnterPosition;

const getRecommendInfo = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  RecommendInfo
>([getVehicleDetailListResponse], response => {
  return response?.recommendInfo;
});

export const getRecommendVehicleList = createSelector(
  [getVehicleDetailListResponse],
  response => {
    return response?.recommendVehicleList;
  },
);
export const getRecommendProducts = createSelector<
  any,
  QueryVehicleDetailListResponseType,
  ProductListType[]
>([getVehicleDetailListResponse], response => {
  return response?.recommendProducts;
});
export const getNoMatchData = createSelector<
  any,
  Array<VendorPriceListType>,
  Array<VendorPriceListType>,
  ProductListType[],
  RecommendInfo,
  {
    isPartNomatch: boolean;
    title: string | null;
    subTitle: string | null;
    isNomatch: boolean;
  }
>(
  [
    getSpecificVendorList,
    getFilteredVendorList,
    getRecommendProducts,
    getRecommendInfo,
  ],

  (
    specificVendorList,
    filteredVendorList,
    recommendProducts,
    recommendInfo,
  ) => {
    return {
      isPartNomatch:
        !specificVendorList?.length &&
        !!(filteredVendorList?.length || recommendProducts?.length),
      title: recommendInfo?.reason,
      subTitle: recommendInfo?.recommend,
      isNomatch:
        !specificVendorList?.length &&
        !filteredVendorList?.length &&
        !recommendProducts?.length,
    };
  },
);

export const getNoMatchDataNew = createSelector<
  any,
  any,
  RecommendInfo,
  {
    isPartNomatch: boolean;
    title: string | null;
    subTitle: string | null;
    isNomatch: boolean;
  }
>(
  [getShelvesVendorList, getRecommendInfo],
  (shelvesVendorList, recommendInfo) => {
    return {
      isPartNomatch: false,
      title: recommendInfo?.reason,
      subTitle: recommendInfo?.recommend,
      isNomatch: shelvesVendorList?.isNoMatch,
    };
  },
);

export const getRecommendListPriceSummaryModalVisible = state =>
  state.VendorList.recommendListPriceSummaryModalData.visible;

const getRecommendListPriceSummaryModalData = state =>
  state.VendorList.recommendListPriceSummaryModalData.data;

export const getRecommendListTotalPriceModalVisible = state =>
  state.VendorList.recommendListTotalPriceModalData.visible;

export const getRecommendListTotalPriceModalData = state =>
  state.VendorList.recommendListTotalPriceModalData.data;

// 组装看了又看模块总价说明弹层日历价请求参数
export const getRequestQuery = createSelector(
  [getRecommendListPriceSummaryModalData, getPointInfoParamsV2],
  (priceSummaryModalData, pointInfoParmas) => {
    const { pickupPointInfo, returnPointInfo } = pointInfoParmas(false);
    return {
      reference: priceSummaryModalData?.reference,
      pickupPointInfo: {
        ...pickupPointInfo,
        pickUpLevel: priceSummaryModalData?.pickUpLevel,
      },
      returnPointInfo: {
        ...returnPointInfo,
        pickOffLevel: priceSummaryModalData?.pickOffLevel,
      },
    };
  },
);
// 组装看了又看模块总价说明弹层数据
export const packagePriceSummaryModalData = createSelector(
  [getRecommendListPriceSummaryModalData, getFeeMap, getRecommendVehicleList],
  (data, feeMap, vehicleList) => {
    return {
      data: mappingResponseFees2PriceModal(
        data?.fees,
        data?.totalPriceName,
        feeMap,
      ),
      title: data?.title,
      footerText: data?.footer,
      invokeFrom: INVOKE_FROM.LIST,
      vendorListPageParam: data?.vendorListPageParam,
      vehicleIndex: data?.vehicleIndex,
      priceListLen: data?.priceListLen,
      vehicleList,
    };
  },
);

export const getRecommendListInfo = createSelector<
  any,
  QueryVehicleDetailListRequestType,
  VehicleInfoType,
  VendorListRecommendLogType
>([getVehicleDetailListRequest, getVehicleInfo], (request, vehicleInfo) => {
  return {
    sortType: request?.sortType ?? 0,
    vehicleCode: vehicleInfo?.vehicleCode || '',
  };
});

export const getCurrentPageParams: Selector<
  any,
  IVendorListCurrentPageParams
> = state => state.VendorList.vendorListCurrentPageParams;

// 获取门店实拍埋点信息
export const getImageListLogInfo = createSelector(
  [getVehicleInfo, getUniqueReference],
  (vehicleInfo, productReference) => {
    return {
      // 携程车型ID
      vehicleCode: vehicleInfo?.vehicleCode,
      // 供应商ID
      vendorCode: productReference?.vendorCode,
      // 供应商车型ID
      vendorVehicleId: productReference?.vendorVehicleCode,
      // 取车携程门店ID
      pstoreCode: productReference?.pStoreCode,
      // 还车携程门店ID
      rstoreCode: productReference?.rStoreCode,
    };
  },
);

export const getMultimediaAlbum = state => state.VendorList.multimediaAlbum;

export const getTotalPhotos = state => state.VendorList.totalPhotos;

export const getIsLoadingAlbum = state => state.VendorList.isLoadingAlbum;

export const getIsBusinessLicenseModalVisible = state =>
  state.VendorList.isBusinessLicenseModalVisible;

export const getIsEtcIntroModalVisible = state =>
  state.VendorList.isEtcIntroModalVisible;

// 获取商品弹层埋点数据
export const getVehicleDetailModalLogInfo = createSelector(
  [getVehicleInfo, getUniqueReference, getSkuId],
  (vehicleInfo, productReference, skuId) => {
    return {
      // 携程车型ID
      vehicleCode: vehicleInfo?.vehicleCode,
      // 取车携程门店ID
      pstoreCode: productReference?.pStoreCode,
      // 还车携程门店ID
      rstoreCode: productReference?.rStoreCode,
      // 商品 ID
      skuId,
    };
  },
);

export const getIsInsuranceDetails = state => state.VendorList.insuranceDetails;

export const getIpollConfigData = state => state.VendorList.ipollConfig;

// 获得ipoll问卷传参数据
export const getIpollLogData = createSelector(
  [
    getVehicleInfo,
    getPickUpCityId,
    getDropOffCityId,
    getLocationDate,
    getCommonUserInfo,
  ],
  (vehicleInfo, pCityId, rCityId, locationDate, userInfo) => {
    const { queryVid } = AppContext.UserTrace;
    return {
      queryVid,
      // 携程车型ID
      vehicleCode: vehicleInfo?.vehicleCode,
      // 取车城市id：
      pCityId,
      // 还车城市id
      rCityId,
      // 取车城市地点
      pickupLocation: locationDate?.pickupLocation?.locationName,
      // 还车城市地点
      returnLocation: locationDate?.returnLocation?.locationName,
      // 取车时间
      ptime: locationDate?.ptime,
      // 还车时间
      rtime: locationDate?.rtime,
      gradeName: userInfo?.gradeName,
    };
  },
);
