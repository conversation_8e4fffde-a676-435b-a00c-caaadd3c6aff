import {
  filter as lodashFilter,
  isEmpty as lodashIsEmpty,
  map as lodashMap,
} from 'lodash-es';
import { createSelector, Selector } from 'reselect';
import uuid from 'uuid';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import {
  CouponListType,
  ActivityDetailType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import {
  ModifyOrderResponseType,
  PageInfoType,
  InfoType,
  FeeInfoType,
  ProductInfoType,
  CancelRulesType,
  ActivityType,
  ModifyDeposit,
  DiscountInfoType,
} from '../../Types/Dto/ModifyOrderResponseType';
import { IModifyOrderModalPropsType, IConfirmModalPropsType } from './Types';
import { getModifyOrderResponse } from '../ModifyOrder/Selector';
import {
  getOrderId,
  getVendorInfo,
  getVehicleInfo,
  getMiddlePayVendorId,
} from '../OrderDetail/Selectors';
import { getDriversMap } from '../Booking/Selectors';
import AppContext from '../../Util/AppContext';
import Utils from '../../Util/Utils';

export const getPageInfo = createSelector<
  any,
  ModifyOrderResponseType,
  PageInfoType
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.pageInfo,
);

export const getDiscountInfo = createSelector<
  any,
  ModifyOrderResponseType,
  DiscountInfoType
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.discountInfo,
);

export const getModifyRuleExplain = createSelector<
  any,
  ModifyOrderResponseType,
  string
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.modifyRuleExplain,
);

export const getComPriceCode = createSelector<
  any,
  ModifyOrderResponseType,
  string
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.comPriceCode,
);

export const getPriceCode = createSelector<
  any,
  ModifyOrderResponseType,
  string
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.priceCode,
);

export const getInfo = createSelector<any, ModifyOrderResponseType, InfoType>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.info,
);

const getModifyOrderFeeInfo = createSelector<
  any,
  ModifyOrderResponseType,
  FeeInfoType
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.feeInfo,
);

export const getProductInfo = createSelector<
  any,
  ModifyOrderResponseType,
  ProductInfoType
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.productInfo,
);

export const getCancelRules = createSelector<
  any,
  ModifyOrderResponseType,
  Array<CancelRulesType>
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.cancelRules,
);

export const getActivity = createSelector<
  any,
  ModifyOrderResponseType,
  ActivityType
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.activity,
);

export const getPayDeadline = createSelector<
  any,
  ModifyOrderResponseType,
  Date
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.payDeadline,
);

const getModifyOrderConfirmFeeInfo: Selector<any, FeeInfoType> = state =>
  state.ModifyOrderConfirm.feeInfo;

export const getSelectedInsuranceId: Selector<any, Array<string>> = state =>
  state.ModifyOrderConfirm.selectedInsuranceId;

export const getSelectedCouponsCodes: Selector<any, Array<string>> = state =>
  state.ModifyOrderConfirm.selectedCouponsCodes;

export const getFeeInfo = state =>
  getModifyOrderConfirmFeeInfo(state) || getModifyOrderFeeInfo(state);

export const getIsPriceLoading: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.isPriceLoading;

export const getIsSubmitting: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.isSubmitting;

export const getModifyOrderConfirmState: Selector<any, any> = state =>
  state.ModifyOrderConfirm;

export const getConfirmModalVisible: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.confirmModalVisible;

export const getConfirmModalProps: Selector<
  any,
  IConfirmModalPropsType
> = state => state.ModifyOrderConfirm.confirmModalProps;

export const getCreateInsModalVisible: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.createInsModalVisible;

export const getInsFailedModalVisible: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.insFailedModalVisible;

export const getModifyOrderModalProps: Selector<
  any,
  IModifyOrderModalPropsType
> = state => state.ModifyOrderConfirm.modifyOrderModalProps;

export const getExplain: Selector<any, string> = state =>
  state.ModifyOrderConfirm.discountInfo.explain;

export const getActivityDetail: Selector<any, ActivityDetailType> = state => {
  const { activityDetails, activityDetail } = state.ModifyOrderConfirm.discountInfo;
  if (!activityDetails && activityDetail) {
    return [activityDetail];
  }
  return activityDetails;
};

export const getCouponList: Selector<any, CouponListType> = state =>
  state.ModifyOrderConfirm.discountInfo.couponList;

export const getIsLoading: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.isLoading;

export const getPayTimeOutModalVisible: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.payTimeOutModalVisible;

export const getEnablePressCoupon: Selector<any, boolean> = state =>
  state.ModifyOrderConfirm.enablePressCoupon;

// TODO: 调参数
export const getPaymentParams = createSelector(
  [
    getOrderId,
    getVendorInfo,
    getVehicleInfo,
    getFeeInfo,
    getInfo,
    getDriversMap,
    getProductInfo,
    getSelectedInsuranceId,
    getMiddlePayVendorId,
  ],

  (
    orderId,
    vendorInfo,
    vehicleInfo,
    feeInfo,
    info,
    driver,
    insProductInfo,
    selectedInsuranceId,
    vendorId,
  ) => {
    const requestId = uuid();
    const { products } = insProductInfo || {};
    const selectProducts = lodashFilter(products, ins =>
      selectedInsuranceId.includes(ins.code),
    );

    return {
      orderId,
      title: vehicleInfo.name,
      subtitle: vendorInfo.vendorName,
      vendorId,
      currency: 'CNY',
      amount: feeInfo?.payAmount,
      get payName() {
        return '在线预付';
      },
      ptime: dayjs(info?.pickUpLocation?.time).format('YYYY-MM-DD HH:mm:ss'),
      rtime: dayjs(info?.returnLocation?.time).format('YYYY-MM-DD HH:mm:ss'),
      pickupLocation: info?.pickUpLocation?.name,
      returnLocation: info?.returnLocation?.name,
      driver,
      payAmountInfo: {},
      requestId,
      isHertzPrepay: false,
      freeCancel: '',
      chargesInfos: [],
      insExtend: lodashIsEmpty(selectProducts)
        ? {}
        : {
            insuranceinfos: lodashMap(selectProducts, ins => ({
              provider: 1, // 用车产品目前都走的是携程代保
              amount: ins?.localTotalPrice,
              currency: 'CNY',
            })),
          },
    };
  },
);

export const getInsConfirmReqParam = createSelector(
  [getInfo, getProductInfo, getSelectedInsuranceId],
  (info, productInfo, selectInsuranceId) => {
    const { driver = {} } = info || {};
    const { products } = productInfo || {};
    // @ts-ignore
    // eslint-disable-next-line no-undef
    const params: BuildInsuranceParamsRequestType = {};
    const insuredInfo = {
      name: driver.name,
      idCardType: driver.certificateType,
      idCardNo: driver.certificateNumber,
      // @ts-ignore
      age: driver.age,
      insuredId: AppContext.UserInfo.userId,
    };

    const curAllCtripInsurances = products || [];

    if (Utils.isCtripIsd()) {
      params.insuredList = [insuredInfo];
      params.policyHolder = insuredInfo;
    }

    const selectedInsuranceList = [];
    const insuranceList = [];
    if (selectInsuranceId && selectInsuranceId.length > 0) {
      selectInsuranceId.forEach(item => {
        /* eslint-disable eqeqeq */
        const curIns = curAllCtripInsurances.find(f => f.code == item) || {};
        const insPrice = Utils.isCtripIsd()
          ? curIns.currentTotalPrice
          : curIns.currentDailyPrice;
        let selectedInsInfo = {
          insuranceId: item,
        };
        if (Utils.isCtripIsd()) {
          selectedInsInfo = {
            ...selectedInsInfo,
            // @ts-ignore
            insuredId: AppContext.UserInfo.userId,
          };
        }
        selectedInsuranceList.push(selectedInsInfo);
        let priceText = '';
        if (insPrice && Utils.isCtripIsd()) {
          priceText = `¥${insPrice}`;
        }
        insuranceList.push({
          insuranceId: item,
          priceNoteList: [
            {
              priceText,
            },
          ],
        });
      });
    }
    params.selectedInsuranceList = selectedInsuranceList;
    params.insuranceList = insuranceList;
    params.callbackType = 1; // 回调方式，1: event，2: url跳转，3: 原生小程序跳转

    return params;
  },
);

const getModifyDeposit = createSelector<
  any,
  ModifyOrderResponseType,
  ModifyDeposit
>(
  [getModifyOrderResponse],
  modifyOrderResponse => modifyOrderResponse?.modifyDeposit,
);

export const getDepositDescTable = createSelector(
  [getModifyDeposit],
  modifyDeposit => {
    const { depositItems } = modifyDeposit || {};

    const depositDescTable = {
      items: [],
      notices: null,
    };

    if (depositItems) {
      depositDescTable.items = depositItems.map(item => ({
        retractable: item.deposit > 0,
        title: item.depositTitle,
        currencyCode: '¥',
        showFree: item.depositStatus === 1,
        currentTotalPrice: item.deposit,
        description: item.explain,
      }));
    }

    return depositDescTable;
  },
);

export const getDepositDesc = createSelector(
  [getModifyDeposit],
  modifyDeposit => {
    return modifyDeposit?.desc;
  },
);

// 修改订单确认重复下单拦截数据
const getModifyOrderConfirmInterceptionData = state =>
  state.ModifyOrderConfirm.modifyOrderConfirmInterceptionData;

export const getModifyOrderConfirmInterceptionModalVisible = createSelector(
  [getModifyOrderConfirmInterceptionData],
  modifyOrderConfirmInterceptionData =>
    modifyOrderConfirmInterceptionData?.visible,
);

export const getModifyOrderConfirmInterceptionModalResultCode = createSelector(
  [getModifyOrderConfirmInterceptionData],
  modifyOrderConfirmInterceptionData =>
    modifyOrderConfirmInterceptionData?.resultCode,
);

export const getModifyOrderConfirmInterceptionModalResultMsg = createSelector(
  [getModifyOrderConfirmInterceptionData],
  modifyOrderConfirmInterceptionData =>
    modifyOrderConfirmInterceptionData?.resultMsg,
);

export const getModifyOrderConfirmInterceptionModalRequestParams =
  createSelector(
    [getModifyOrderConfirmInterceptionData],
    modifyOrderConfirmInterceptionData =>
      modifyOrderConfirmInterceptionData?.requestParams,
  );

export const getModifyOrderConfirmInterceptionModalStrongSubmit =
  createSelector(
    [getModifyOrderConfirmInterceptionData],
    modifyOrderConfirmInterceptionData =>
      modifyOrderConfirmInterceptionData?.strongSubmit,
  );
