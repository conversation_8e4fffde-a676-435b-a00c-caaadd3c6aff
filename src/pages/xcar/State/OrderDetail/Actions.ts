import {
  GET_ORDER_DATA,
  FETCH_ORDER_CALLBAC<PERSON>,
  FETCH_QUERYCANCELFEE,
  FETCH_QUERYCANCELFEE_CALLBACK,
  FETCH_TOCANCELBOOK,
  SET_PRICEMODALVISIBLE,
  SHOW_RE<PERSON><PERSON><PERSON><PERSON>CESSMODAL,
  SHOW_REVIEWMODAL,
  SET_REFUN<PERSON>ODALVISIBLE,
  SET_CHANGE<PERSON>DERMODALVISIBLE,
  QUERY_SCOREANDSUGGESTIONSCALLBACK,
  FETCH_MODIFYORDERCALLBACK,
  <PERSON><PERSON><PERSON>_<PERSON>RASSISTANTCALLBACK,
  SET_ISDCHANGEORDERMODALVISIBLE,
  FETCH_ISDBUYINSURANCE,
  SET_FEEDEDUCTIONVISIBLE,
  SET_IN<PERSON><PERSON><PERSON><PERSON>MODALVISIBLE,
  GET_LIMIT_CONTENT_SUCCESS,
  LIMITRULE_POP_VISIBLE,
  GET_LIMIT_CONTENT_DATA,
  CREATE_ORDER_PAYMENT,
  SET_HISTORY_MODAL_VISIBLE,
  QUERY_ORDER_PRICE_SUCCESS,
  QUERY_ORDER_PRICE_INFO,
  QUERY_CUSTOMERSERVICEUEL_SUCCESS,
  SET_CREDIT_NOTIFICATION,
  FETCH_CREDIT_NOTIFICATION,
  FETCH_UPDATE_PAYMENT,
  UPDATE_PAYMENT_CALLBACK,
  SET_MODIFYFLIGHTNOMODALVISIBLE,
  QUERYCCTRIPCONTINUEPAY,
  CONTINUEPAYMENT,
  QUERYADDITIONPAYMENT,
  GETRENTALMUSTREAD,
  SET_DEPOSIT_DEATIL_MODAL_VISIBLE,
  SET_FETCH_DONE,
  CREDIT_RENT_AUTH,
  UPDATA_FREE_DEPOSITINFO,
  SET_PHONE_MODAL_VISIBLE,
  SET_PERSON_PHONE_MODAL_VISIBLE,
  RESET,
  QUERY_ORDER_DETAIL,
  GET_SUPPLEMENT_LIST,
  GET_SUPPLEMENT_LIST_CALLBACK,
  SET_VEHICLE_DAMAGE_ID,
  SET_SUPPLEMENT_LIST_NEW,
  ISDINSURANCEPAYMENT,
  SETMODALSVISIBLE,
  SET_RENEW_STATUS_BYSTORAGE,
  SET_SELECTOR_DATA,
  SAVE_RENEWALORDERSTATUS,
  SET_ORDER_STATUS_SIGN,
  QUERY_ORDER_STATUS,
  PAYCOUNTDOWNTIMEOUT,
  SET_CLMODALVISIBLE,
  SET_CDMODALVISIBLE,
  FETCH_EASYLIFETAG_CALLBACK,
  SET_EASYLIFETAGMODALVISIBLE,
  SET_TIPPOP_DATA,
  QUERY_CAR_ASSISTANT_V2,
  QUERY_CAR_ASSISTANT_V2_CALLBACK,
  SET_STORAGE_CARDS_TITLE,
  UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY,
  SET_PRICE_DETAIL_MODAL_VISIBLE,
  DEPOSIT_PAY_ONLINE,
  SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE,
  QUERY_ORDERINSUANDXPRODUCT_CALLBACK,
  SET_SCANNED_IMAGES,
  QUERY_ORDERCANCELINFO_CALLBACK,
  SET_CONTINUE_PAY_FAIL_DIALOG_INFO,
  HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL,
  QUERY_FIRST_SCREEN_DATA,
  FETCH_ORDER_DATA_BY_GRAPHQL,
  SET_CONTINUE_PAY_INTERCEPTION_DATA,
  FETCH_QUERY_CANCEL_INFO,
  TO_CANCEL_CALLBACK,
  SET_TRAVEL_LIMIT_SELECTED_RESULT,
  FETCH_MODIFY_CROSS_LOCATION,
  SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE,
  QUERY_DRIVER_LICENSE_ORDERS,
  QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
  QUERY_EXTRA_INSRUANCE,
  SET_IS_QUERY_ORDER_LOADING,
  QUERY_VEHICLE_STATUS,
  QUERY_VEHICLE_STATUS_CALLBACK,
  SELF_SERVICE_OPERATION,
  SET_CANCEL_ORDER_SUBMIT_ID,
  ORDERDETAIL_PAGE_PRE_CACHE,
  SET_FULFILMENT_DATA,
  CHECK_SUBMIT_RETURN_CAR,
  SET_DISTANCE,
  SET_ORDER_NOTICE_FROM_DID,
  QUERY_ORDER_NOTICE_FROM_DID,
  SET_LOCATIONDATEPOP_VISIBLE,
  QUERY_OSD_MODIFY_ORDER_NOTE,
  SET_OSD_MODIFY_ORDER_NOTE,
  OSD_MODIFY_ORDER_INIT,
  QUERY_COUNTRYS_INFO,
  SET_COUNTRYS_INFO,
  QUERY_ORDER_INFO,
  SET_QUERYDEDUCTION_DATA,
  QUERY_ORDER_SUCCESS,
  QUERY_ORDER_WARNINGINFO,
  QUERY_ORDER_INFO_FLAG2,
  SET_ORDER_FULFILLMENT_MODIFY_INFO,
  SET_FULFILLMENT_MODAL_VISIBLE,
  FULFILLMENT_MODIFY_CONFIRM,
  QUERY_FULFILLMENT,
} from './Types';
import { OrderModalsVisible } from '../../Pages/OrderDetail/Types';

export const fetchOrder2 = (data?: any, interval?: boolean) => ({
  type: GET_ORDER_DATA,
  data,
  interval,
});

export const queryOrderDetail = (data?: any) => ({
  type: QUERY_ORDER_DETAIL,
  data,
});
export const fetchOrderSuccess = data => ({
  type: FETCH_ORDER_CALLBACK,
  data,
});

export const queryOrderSuccess = data => ({
  type: QUERY_ORDER_SUCCESS,
  data,
});

export const fetchQueryCancelFee = data => ({
  type: FETCH_QUERYCANCELFEE,
  data,
});

export const fetchQueryCancelFeeCallBack = data => ({
  type: FETCH_QUERYCANCELFEE_CALLBACK,
  data,
});

export const fetchEasyLifeTagInfoCallBack = data => ({
  type: FETCH_EASYLIFETAG_CALLBACK,
  data,
});

export const setEasyLifeTagModalVisible = data => ({
  type: SET_EASYLIFETAGMODALVISIBLE,
  data,
});

export const toCancelBook = data => ({
  type: FETCH_TOCANCELBOOK,
  data,
});

export const setPriceModalVisible = data => ({
  type: SET_PRICEMODALVISIBLE,
  data,
});

export const showReviewSuccessModal = data => ({
  type: SHOW_REVIEWSUCCESSMODAL,
  data,
});

export const showReviewModal = data => ({
  type: SHOW_REVIEWMODAL,
  data,
});

export const setRefundModalVisible = data => ({
  type: SET_REFUNDMODALVISIBLE,
  data,
});

export const setFulfillmentModifyModalVisible = data => ({
  type: SET_FULFILLMENT_MODAL_VISIBLE,
  data,
});

export const setOrderChangeModalVisible = data => ({
  type: SET_CHANGEORDERMODALVISIBLE,
  data,
});

export const queryScoreAndSuggestionsCallback = data => ({
  type: QUERY_SCOREANDSUGGESTIONSCALLBACK,
  data,
});

export const fetchOrderModifyCallBack = data => ({
  type: FETCH_MODIFYORDERCALLBACK,
  data,
});

export const fetchCarAssistantCallBack = data => ({
  // 这个action可用来存数据
  type: FETCH_CARASSISTANTCALLBACK,
  data,
});

export const setIsdOrderChangeModalVisible = data => ({
  type: SET_ISDCHANGEORDERMODALVISIBLE,
  data,
});

export const buyIsdInsuranceOrder = data => ({
  type: FETCH_ISDBUYINSURANCE,
  data,
});

export const showFeeDeduction = data => ({
  type: SET_FEEDEDUCTIONVISIBLE,
  data,
});

export const showInsDetailModal = data => ({
  type: SET_INSDETAILMODALVISIBLE,
  data,
});

export const setLabelsModalVisible = data => ({
  type: SET_CLMODALVISIBLE,
  data,
});

export const setCarDetailModalVisible = data => ({
  type: SET_CDMODALVISIBLE,
  data,
});

export const setPhoneModalVisible = data => ({
  type: SET_PHONE_MODAL_VISIBLE,
  data,
});

export const setPersonPhoneModalVisible = data => ({
  type: SET_PERSON_PHONE_MODAL_VISIBLE,
  data,
});

export const getLimitContentData = data => ({
  type: GET_LIMIT_CONTENT_DATA,
  data,
});

export const getLimitContentSuccess = data => ({
  type: GET_LIMIT_CONTENT_SUCCESS,
  data,
});

export const setLimitRulePopVisible = data => ({
  type: LIMITRULE_POP_VISIBLE,
  data,
});

export const createPayment = data => ({
  type: CREATE_ORDER_PAYMENT,
  data,
});

export const setHistoryModalVisible = data => ({
  type: SET_HISTORY_MODAL_VISIBLE,
  data,
});

export const setDepositDetailModalVisible = data => ({
  type: SET_DEPOSIT_DEATIL_MODAL_VISIBLE,
  data,
});

export const queryOrderPriceInfoCallBack = data => ({
  type: QUERY_ORDER_PRICE_SUCCESS,
  data,
});

export const queryOrderPriceInfoFun = data => ({
  type: QUERY_ORDER_PRICE_INFO,
  data,
});

export const fetchQueryDepositNoticeCallback = data => ({
  type: SET_CREDIT_NOTIFICATION,
  data,
});

export const fetchQueryDepositNotice = data => ({
  type: FETCH_CREDIT_NOTIFICATION,
  data,
});

export const fetchUpdatePaymentCallback = data => ({
  type: UPDATE_PAYMENT_CALLBACK,
  data,
});

export const updatePayment = data => ({
  type: FETCH_UPDATE_PAYMENT,
  data,
});

export const fetchCustomerServiceUrlSuccess = data => ({
  type: QUERY_CUSTOMERSERVICEUEL_SUCCESS,
  data,
});

export const setModifyFlightNoModalVisible = data => ({
  type: SET_MODIFYFLIGHTNOMODALVISIBLE,
  data,
});

export const ctripContinuePay = (data, goToInsFun) => ({
  type: QUERYCCTRIPCONTINUEPAY,
  data,
  goToInsFun,
});

export const continuePayment = (data?: any) => ({
  type: CONTINUEPAYMENT,
  data,
});

export const getAdditionPayment = (data?: any) => ({
  type: QUERYADDITIONPAYMENT,
  data,
});

export const fetchRentalMustRead = (data?: any) => ({
  type: GETRENTALMUSTREAD,
  data,
});

export const setFetchDone = (data: any) => ({
  type: SET_FETCH_DONE,
  data,
});

export const creditRentAuth = (data: any) => ({
  type: CREDIT_RENT_AUTH,
  data,
});

export const updateFreeDepositInfo = (data: any) => ({
  type: UPDATA_FREE_DEPOSITINFO,
  data,
});

export const reset = () => ({
  type: RESET,
});

export const getSupplementList = data => ({
  type: GET_SUPPLEMENT_LIST,
  data,
});
export const setQueryDeductionData = data => ({
  type: SET_QUERYDEDUCTION_DATA,
  data,
});

export const getSupplementListCallBack = data => ({
  type: GET_SUPPLEMENT_LIST_CALLBACK,
  data,
});

export const setVehicleDamageId = data => ({
  type: SET_VEHICLE_DAMAGE_ID,
  data,
});

export const setSupplementListNew = data => ({
  type: SET_SUPPLEMENT_LIST_NEW,
  data,
});

export const goIsdInsurancePayment = data => ({
  type: ISDINSURANCEPAYMENT,
  data,
});

export const setOrderModalsVisible = (data: OrderModalsVisible) => ({
  type: SETMODALSVISIBLE,
  data,
});

export const isShowRenewStatusByStorage = data => ({
  type: SET_RENEW_STATUS_BYSTORAGE,
  data,
});

export const setSelectorData = data => ({
  type: SET_SELECTOR_DATA,
  data,
});

export const saveRenewalOrderStatus = data => ({
  type: SAVE_RENEWALORDERSTATUS,
  data,
});

export const queryOrderStatus = data => ({
  type: QUERY_ORDER_STATUS,
  data,
});

export const setOrderStatusSign = data => ({
  type: SET_ORDER_STATUS_SIGN,
  data,
});

export const payCountDownTimeOut = data => ({
  type: PAYCOUNTDOWNTIMEOUT,
  data,
});

export const setTipPopData = data => ({
  type: SET_TIPPOP_DATA,
  data,
});

export const queryCarAssistantV2 = data => ({
  type: QUERY_CAR_ASSISTANT_V2,
  data,
});

export const queryCarAssistantV2CallBack = data => ({
  type: QUERY_CAR_ASSISTANT_V2_CALLBACK,
  data,
});

export const setStorageCardsTitle = data => ({
  type: SET_STORAGE_CARDS_TITLE,
  data,
});

export const updateFreeDepositInfoByContinuePay = () => ({
  type: UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY,
});

export const setPriceDetailModalVisible = data => ({
  type: SET_PRICE_DETAIL_MODAL_VISIBLE,
  data,
});

export const depositPayOnline = data => {
  return {
    type: DEPOSIT_PAY_ONLINE,
    playload: { ...data },
  };
};

export const setOrderDetailConfirmModalVisible = (visible: boolean) => ({
  type: SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE,
  visible,
});

export const queryOrderInsAndXProductCallBack = data => ({
  type: QUERY_ORDERINSUANDXPRODUCT_CALLBACK,
  data,
});
export const setScannedImages = data => ({
  type: SET_SCANNED_IMAGES,
  data,
});

export const queryOrderCancelInfo = data => ({
  type: FETCH_QUERY_CANCEL_INFO,
  data,
});

export const queryOrderCancelInfoCallBack = data => ({
  type: QUERY_ORDERCANCELINFO_CALLBACK,
  data,
});

export const setContinuePayFailDiaLogInfo = data => ({
  type: SET_CONTINUE_PAY_FAIL_DIALOG_INFO,
  data,
});

export const handleContinuePayFailDialogCancel = () => ({
  type: HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL,
});

export const queryFirstScreenData = data => ({
  type: QUERY_FIRST_SCREEN_DATA,
  data,
});

export const fetchOrderDataByGraphql = data => ({
  type: FETCH_ORDER_DATA_BY_GRAPHQL,
  data,
});

export const setContinuePayInterceptionData = data => ({
  type: SET_CONTINUE_PAY_INTERCEPTION_DATA,
  data,
});

export const toCancelCallBack = data => ({
  type: TO_CANCEL_CALLBACK,
  data,
});

export const setCancelOrderSubmitId = data => ({
  type: SET_CANCEL_ORDER_SUBMIT_ID,
  data,
});

export const setTravelLimitSelectedResult = data => ({
  type: SET_TRAVEL_LIMIT_SELECTED_RESULT,
  data,
});

export const fetchModifyCrossLocation = data => ({
  type: FETCH_MODIFY_CROSS_LOCATION,
  data,
});

export const setFlightDelayRulesModalVisible = (data: boolean) => ({
  type: SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE,
  data,
});

export const queryDriverLicenseOrders = data => ({
  type: QUERY_DRIVER_LICENSE_ORDERS,
  data,
});

export const queryDriverLicenseOrdersCallback = data => ({
  type: QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
  data,
});

export const queryExtraInsurance = data => ({
  type: QUERY_EXTRA_INSRUANCE,
  data,
});

export const setIsQueryOrderLoading = (data: boolean) => ({
  type: SET_IS_QUERY_ORDER_LOADING,
  data,
});

export const queryVehicleStatus = data => ({
  type: QUERY_VEHICLE_STATUS,
  data,
});

export const queryVehicleStatusCallback = data => ({
  type: QUERY_VEHICLE_STATUS_CALLBACK,
  data,
});

export const selfServiceOperation = data => ({
  type: SELF_SERVICE_OPERATION,
  data,
});

export const createPreFetch = data => ({
  type: ORDERDETAIL_PAGE_PRE_CACHE,
  data,
});

export const queryFulfillment = data => ({
  type: QUERY_FULFILLMENT,
  data,
});

export const setFulfillmentData = data => ({
  type: SET_FULFILMENT_DATA,
  data,
});

export const checkSubmitReturnCar = data => ({
  type: CHECK_SUBMIT_RETURN_CAR,
  data,
});

export const setDistance = data => ({
  type: SET_DISTANCE,
  data,
});

export const setDidNoticeData = data => ({
  type: SET_ORDER_NOTICE_FROM_DID,
  data,
});

export const queryDidNoticeData = data => ({
  type: QUERY_ORDER_NOTICE_FROM_DID,
  data,
});

export const setLocationAndDatePopIsShow = data => ({
  type: SET_LOCATIONDATEPOP_VISIBLE,
  data,
});

export const queryOsdModifyOrderNote = () => ({
  type: QUERY_OSD_MODIFY_ORDER_NOTE,
});

export const setOsdModifyOrderNote = data => ({
  type: SET_OSD_MODIFY_ORDER_NOTE,
  data,
});

export const osdModifyOrderInit = () => ({
  type: OSD_MODIFY_ORDER_INIT,
});
export const queryCountrysInfo = () => ({
  type: QUERY_COUNTRYS_INFO,
});
export const setCountrysInfo = data => ({
  type: SET_COUNTRYS_INFO,
  data,
});

export const queryOrderInfo = data => ({
  type: QUERY_ORDER_INFO,
  data,
});

export const queryOrderInfoFlag2 = data => ({
  type: QUERY_ORDER_INFO_FLAG2,
  data,
});

export const fulfillmentModifyConfirm = data => ({
  type: FULFILLMENT_MODIFY_CONFIRM,
  data,
});

export const queryOrderWarningInfo = () => ({
  type: QUERY_ORDER_WARNINGINFO,
});

export const setOrderFulfillmentModifyInfo = data => ({
  type: SET_ORDER_FULFILLMENT_MODIFY_INFO,
  data,
});
