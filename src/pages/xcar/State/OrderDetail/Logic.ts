import { get as lodashGet } from 'lodash-es';
import Alert from '@c2x/apis/Alert';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import Loading from '@c2x/apis/Loading';
import { xShowToast, xRouter, xApplication as Application } from '@ctrip/xtaro';

// User,
import {
  put,
  select,
  takeEvery,
  delay,
  takeLatest,
  all,
} from 'redux-saga/effects';

import UIToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

import { removeCache } from '@ctrip/rn_com_car/dist/src/Logic/src/Fetch/CarFetchHelper';
import { ActionType } from '../../Types/ActionType';
import { setOrderAuthInfo } from '../OnlineAuth/Actions';
import { fetchListWarningInfo } from '../Common/Actions';
import { getQConfig } from '../Common/Selectors';
import {
  CreateInsOrderResCode,
  FreeDepositWayType,
  OperationType,
  VehicleControl,
  SelfServicePageName,
  CreditAuthScene,
  DepositTipsDepositType,
} from '../../Constants/OrderDetail';
import {
  CarLog,
  Utils,
  CarFetch,
  AppContext,
  CarFetchHelper,
  CarStorage,
  Channel,
  MiddlePay,
  EventHelper,
  GetAB,
} from '../../Util/Index';
import {
  GET_ORDER_DATA,
  FETCH_TOCANCELBOOK,
  FETCH_CREATECOMMENT,
  FETCH_MODIFYORDERCALLBACK,
  QUERY_SCOREANDSUGGESTIONS,
  FETCH_ISDBUYINSURANCE,
  GET_LIMIT_CONTENT_DATA,
  CREATE_ORDER_PAYMENT,
  QUERY_ORDER_PRICE_INFO,
  FETCH_CREDIT_NOTIFICATION,
  FETCH_UPDATE_PAYMENT,
  QUERY_CUSTOMERSERVICEUEL,
  QUERYCCTRIPCONTINUEPAY,
  CONTINUEPAYMENT,
  QUERYADDITIONPAYMENT,
  GETRENTALMUSTREAD,
  GET_SUPPLEMENT_LIST,
  FETCH_QUERYCANCELFEE,
  CREDIT_RENT_AUTH,
  UPDATA_FREE_DEPOSITINFO,
  QUERY_ORDER_DETAIL,
  ISDINSURANCEPAYMENT,
  SET_RENEW_STATUS_BYSTORAGE,
  SAVE_RENEWALORDERSTATUS,
  QUERY_ORDER_STATUS,
  UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY,
  DEPOSIT_PAY_ONLINE,
  QUERY_CAR_ASSISTANT_V2,
  QUERY_ORDERINSUANDXPRODUCT,
  HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL,
  QUERY_FIRST_SCREEN_DATA,
  FETCH_ORDER_DATA_BY_GRAPHQL,
  FETCH_QUERY_CANCEL_INFO,
  FETCH_MODIFY_CROSS_LOCATION,
  QUERY_DRIVER_LICENSE_ORDERS,
  QUERY_EXTRA_INSRUANCE,
  QUERY_VEHICLE_STATUS,
  SELF_SERVICE_OPERATION,
  CHECK_SUBMIT_RETURN_CAR,
  QUERY_ORDER_NOTICE_FROM_DID,
  QUERY_OSD_MODIFY_ORDER_NOTE,
  OSD_MODIFY_ORDER_INIT,
  QUERY_COUNTRYS_INFO,
  QUERY_ORDER_INFO,
  SET_QUERYDEDUCTION_DATA,
  QUERY_ORDER_SUCCESS,
  QUERY_ORDER_WARNINGINFO,
  QUERY_ORDER_INFO_FLAG2,
  FULFILLMENT_MODIFY_CONFIRM,
  ModifyConfirmStatus,
  QUERY_FULFILLMENT,
} from './Types';
import {
  fetchOrderSuccess,
  fetchQueryCancelFeeCallBack,
  fetchOrder2,
  queryScoreAndSuggestionsCallback,
  showReviewSuccessModal,
  setOrderChangeModalVisible,
  fetchCarAssistantCallBack,
  getLimitContentSuccess,
  queryOrderPriceInfoCallBack,
  fetchQueryDepositNoticeCallback,
  fetchUpdatePaymentCallback,
  fetchCustomerServiceUrlSuccess,
  continuePayment,
  fetchRentalMustRead,
  getAdditionPayment,
  setFetchDone,
  getSupplementListCallBack,
  getSupplementList,
  setSupplementListNew,
  setOrderModalsVisible,
  goIsdInsurancePayment,
  setSelectorData,
  setOrderStatusSign,
  updateFreeDepositInfo,
  queryCarAssistantV2,
  queryCarAssistantV2CallBack,
  queryOrderInsAndXProductCallBack,
  queryOrderCancelInfoCallBack,
  toCancelBook,
  setContinuePayFailDiaLogInfo,
  queryFirstScreenData,
  fetchOrderDataByGraphql,
  setContinuePayInterceptionData,
  queryOrderCancelInfo,
  toCancelCallBack,
  setTravelLimitSelectedResult,
  queryDriverLicenseOrdersCallback,
  setIsQueryOrderLoading,
  queryVehicleStatusCallback,
  queryVehicleStatus as queryVehicleStatusAction,
  setDistance,
  setCancelOrderSubmitId,
  setDidNoticeData,
  queryDidNoticeData,
  setOsdModifyOrderNote,
  setCountrysInfo,
  queryCountrysInfo,
  queryOrderInfo,
  setQueryDeductionData,
  queryOrderSuccess,
  queryOrderWarningInfo,
  queryOrderInfoFlag2,
  queryFulfillment,
  setFulfillmentData,
  createPreFetch,
} from './Actions';
import {
  setSupplierData,
  queryOrderDetailSupplierData,
} from '../SupplierData/Actions';
import {
  getReqOrderParams,
  getPickupStore,
  getVehicleInfo,
  getVendorInfo,
  getModifyOrderParam,
  getCancelReason,
  getElsePaymentParams,
  getUseCityID,
  getPickUpTime,
  getDropOffTime,
  getIsdFreeDeposit,
  getCustomerServiceUrl,
  getPaymentParams,
  getDepositPaymentParams,
  getAuthOrderCount,
  getContinueBackPay,
  getFreeDepositWay,
  getFreeDepositType,
  getOrderId,
  getCreditRiskResult,
  getCreditRiskRequestId,
  getFreeDeposit,
  queryContinuePayParams,
  getaddInsParams,
  getisdInsData,
  getCreateInsModalVisible,
  getRenewalOrder,
  getOrderStatusHashSign,
  getReturnStore,
  getRentalPolicyParams,
  getIsModifyOrderAddPayment,
  updateFreeDepositInfoParams,
  getDepositPayType,
  getOrderDataByPhone,
  getInsConfirmReqParam,
  getNewHomeParamFromOrder,
  getResCancelFee,
  getNextPageOffset,
  getIsLastPage,
  isKlbVersion,
  getQueryOrderAllDataSuccess,
  getCancelOrderSubmitId,
  getOrderInfo,
  getExtendedInfo,
  getPickUpAreaCode,
  getIsMergeOrderServer,
  getFulfillIsCanRenew,
} from './Selectors';
import { setLocationDateInfo } from '../LocationAndDate/Actions';
import { setAge } from '../DriverAgeAndNumber/Actions';
import { initSesameAuthState } from '../Sesame/Actions';
import {
  modifyOrderSuccess,
  setInitialDataCallback,
} from '../ModifyOrder/Actions';
import {
  StorageKey,
  EventName,
  WarningInfo,
  ApiResCode,
  LogKeyDev,
  LogKey,
  Platform,
  Url,
  FrontEndConfig,
} from '../../Constants/Index';
import {
  TRAVEL_INSURANCE_ID,
  WUYOUZUNXIANG_ID,
} from '../../Constants/OrderIInsurance';
import {
  QueryOrderApiStatusType,
  CheckSubmitReturnCarCode,
} from '../../Pages/OrderDetail/Types';
import { PayScene, PayType, PAY_TITLE_TYPE } from '../../Constants/PayEnums';
import { Enums } from '../../ComponentBusiness/Common';
import { getRenewTipLogData, getDepositPayOnlineParams } from './Mappers';
import { CarPayParams, MiddlePayRes } from '../../Types/PaymentType';
import { LogErrorInfoType } from '../../Types/CarLogTypes';
import { getInitPTime, getInitRTime } from '../LocationAndDate/Mappers';
import { graphqlFetch, graphqlPath } from '../../Util/CarFetch/GraphqlFetch';
import getQuerySchema from '../../Graphql/Schemas/Index';
import { fetchMetricLog } from '../../Util/CarFetchHelper';
import {
  ICrossType,
  ILocation,
} from '../../ComponentBusiness/TravelLimit/src/Types';
import { FilterGroupCode } from '../../ComponentBusiness/Common/src/Enums';
import getOrderCache, { setOrderCache } from '../../Global/Cache/OrderCache';
import {
  getCredentialsList,
  getCredentialsListCallBack,
} from '../Credentials/Actions';
import { OrderDetailTexts as Texts } from '../../Constants/TextIndex';
import { getMapGuideCacheKey } from '../Guide/Logic';

const LicensePageSize = 10;

const alertFn = ({ subTitle, submitTxt, cancelTxt, submitFn, cancelFn }) => {
  Alert.alert(
    '',
    subTitle,
    [
      {
        text: submitTxt,
        onPress: () => {
          submitFn();
        },
      },
      {
        style: 'cancel',
        text: cancelTxt,
        onPress: () => {
          cancelFn();
        },
      },
    ],

    { cancelable: false },
  );
};

// 获取订详首屏静态数据
export function* apiQueryFirstScreenData() {
  yield takeEvery(QUERY_FIRST_SCREEN_DATA, function* logic(action) {
    // @ts-ignore
    const { orderId, isAppLoad } = action.data || {};
    const res = yield CarFetch.getOrderDetailFirstScreenData({ orderId }).catch(
      () => {},
    );
    const state: any = yield select();
    const isSuccess = res?.baseResponse?.isSuccess;
    if (
      state.OrderDetail.queryOrderApiStatus !== QueryOrderApiStatusType.success
    ) {
      if (isSuccess) {
        yield put(
          fetchOrderSuccess({
            response: res,
            queryOrderApiStatus: QueryOrderApiStatusType.success,
            isMergeOrderServer: res?.orderDetailVersion === 'B', // A:不合并接口；B:合并接口,
          }),
        );
      }
    }
    if (!isAppLoad) {
      yield put(fetchOrder2({ orderId }));
    }
  });
}

export function* apiQueryOrderStatus() {
  yield takeEvery(QUERY_ORDER_STATUS, function* logic(action) {
    const state: any = yield select();
    const { orderId: oid } = AppContext.UrlQuery;
    const processData: any = action;
    const orderId = oid || processData?.data?.orderId;
    const { isFetchStatic } = processData?.data || {};
    if (isFetchStatic) {
      // 第一次请求
      yield put(fetchOrder2({ orderId, isFetchStatic }));
    }
    const res = yield CarFetch.queryOrderStatus({ orderId }).catch(() => {});
    const orderStatusHashSign = getOrderStatusHashSign(state);
    yield put(setOrderStatusSign({ orderStatusHashSign: res?.sign }));
    if (!!orderStatusHashSign && orderStatusHashSign !== res?.sign) {
      yield put(fetchOrder2({ orderId }));
    }
  });
}

export function* apiOrderQueryData() {
  yield takeEvery(GET_ORDER_DATA, function* logic(action) {
    const state: any = yield select();
    const processData: any = action;
    const { data = {} } = processData;
    const { callback, isFetchStatic } = data;
    const { orderId: oid } = AppContext.UrlQuery;
    // dispatch参数orderId||当前页面链接orderId参数||state中的orderid
    const orderId = oid || data.orderId || getReqOrderParams(state)?.orderId;

    // const previousOrderStatus = getOrderStatus(state);

    // 本地开发时可以去掉
    if (!orderId) {
      UIToast.show('The order number or account number is wrong');
      return;
    }
    const pageId = Channel.getPageId().Order.ID;
    const params = {
      orderId,
      channelType: Number(AppContext.MarketInfo.channelId),
      pageId,
      requestId: BbkUtils.uuid(),
    };
    let openResultInfo: LogErrorInfoType = {
      eventResult: true,
      expPoint: 'apiOrderQueryData',
    };
    try {
      yield put(setIsQueryOrderLoading(true));
      // 第一次请求全量数据接口的同时，增加发起首屏静态数据请求
      if (isFetchStatic) {
        yield put(queryFirstScreenData({ orderId }));
        const sOrder = getOrderCache(orderId);
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_app_order_cache,
          info: { eventResult: !!sOrder, extraData: sOrder, orderId },
        });
        if (sOrder) {
          yield put(
            fetchOrderSuccess({
              response: sOrder,
              queryOrderApiStatus: QueryOrderApiStatusType.before,
            }),
          );
        }
        return;
      }
      if (getIsMergeOrderServer(state)) {
        yield put(queryOrderInfo({ orderId, manOrderFlag: '1', callback }));
        return;
      }
      if (Utils.isCtripIsd()) {
        yield put(queryCarAssistantV2({ orderId }));
      }
      // 国内海外都需要查询车损违章的扣除记录（海外的展示即押金扣款）
      yield put(getSupplementList({ orderId, isLoading: false })); // 补款
      const res = yield CarFetch.queryOrder(params).catch(() => {});

      const isSuccess = res?.baseResponse?.isSuccess;
      if (isSuccess) {
        yield put(queryOrderSuccess({ res, orderId }));
        if (res?.orderBaseInfo?.orderContact) {
          yield put(getCredentialsList({}));
        }
        yield put(
          fetchOrderDataByGraphql({
            orderId,
            vendorId: res?.vendorInfo?.vendorID,
          }),
        );
      } else if (!getQueryOrderAllDataSuccess(state)) {
        yield put(
          fetchOrderSuccess({
            queryOrderApiStatus: QueryOrderApiStatusType.fail,
          }),
        );
        // 只上报服务有返回场景
        if (res) {
          openResultInfo = {
            eventResult: false,
            expCode: res.baseResponse?.code,
            expMsg: res.baseResponse?.returnMsg,
            expPoint: 'orderIdEmpty',
            error: res,
          };
        }
      }
      if (callback && typeof callback === 'function') callback();
      yield put(fetchRentalMustRead({}));
      if (Utils.isCtripIsd()) {
        // 修改订单成功弹窗提示
        yield put(modifyOrderSuccess());
      } else {
        yield put(queryDidNoticeData({ orderId }));
      }

      // 是否全部请求结束
      yield put(setFetchDone({ fetchDone: true }));
    } catch (error) {
      if (!getQueryOrderAllDataSuccess(state)) {
        yield put(
          fetchOrderSuccess({
            queryOrderAllDataSuccess: false,
            queryOrderApiStatus: QueryOrderApiStatusType.fail,
          }),
        );
      }
      openResultInfo = {
        eventResult: false,
        expCode: Utils.getFrontEndExpCode(null, error),
        expMsg: error?.message,
        expPoint: 'logicCatch',
        error,
      };
    } finally {
      yield put(setIsQueryOrderLoading(false));
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_trace_order_detail_open_result,
        info: openResultInfo,
      });
    }
  });
}
export function* queryOrderSuccessLogic() {
  yield takeEvery(QUERY_ORDER_SUCCESS, function* logic(action) {
    // @ts-ignore
    const { res, orderId } = action.data || {};
    yield put(
      fetchOrderSuccess({
        response: { ...res, firstLoadSucTime: dayjs().second() },
        request: { orderId },
        queryOrderApiStatus: QueryOrderApiStatusType.success,
        queryOrderAllDataSuccess: true,
      }),
    );
    setOrderCache({ orderId, data: res });
    // 保存isdVendorInsurance到store
    if (res?.isdVendorInsurance) {
      CarStorage.saveIsd(
        StorageKey.CAR_ISD_VENDORINSURANCE,
        res.isdVendorInsurance,
        undefined,
        'rn_car_isd',
      );
    }
    yield put(
      initSesameAuthState({
        isOrderDetail: true,
      }),
    );
    yield put(queryCountrysInfo());
    // 旅行限制数据初始化
    const crossCountrySelectedLocations =
      res?.extendedInfo?.crossLocationsPolicy?.crossLocationsInfos
        ?.find(
          crossLocationItem =>
            crossLocationItem?.crossType === ICrossType.CrossCountry,
        )
        ?.locations?.filter(item => !!item.isSelected);
    if (crossCountrySelectedLocations?.length > 0) {
      yield put(
        setTravelLimitSelectedResult(
          crossCountrySelectedLocations?.map((item: ILocation) => ({
            code: item?.regionId,
            name: item?.name,
            isSelected: item?.isSelected,
            groupCode: FilterGroupCode.CrossPlace,
            ...item,
          })),
        ),
      );
    }
  });
}

export function* queryOrderWarningInfoLogic() {
  yield takeEvery(QUERY_ORDER_WARNINGINFO, function* () {
    const state = yield select();
    yield put(
      fetchListWarningInfo({
        pageIndexId: WarningInfo.PageIndexId.Order,
        locationAndDate: {
          pickUpTime: getPickUpTime(state),
          dropOffTime: getDropOffTime(state),
          pickUpCityId: getPickupStore(state)?.cityId,
          dropOffCityId: getReturnStore(state)?.cityId,
        },
      }),
    );
  });
}

export function* apiFetchOrderDataByGraphql() {
  yield takeEvery(
    FETCH_ORDER_DATA_BY_GRAPHQL,
    function* logics(action: ActionType) {
      const isIsd = Utils.isCtripIsd();
      const isOsd = Utils.isCtripOsd();
      const { orderId, vendorId } = action?.data || {};
      const state = yield select();
      const paramsWithOrderId = { orderId };
      yield put(queryOrderWarningInfo());
      const getEasyLifeTagInfoParams = {
        ...getReqOrderParams(state),
        timeout: 120,
      };
      // 获取供应商资质数据
      const getLicenseParams = isIsd ? { vendorID: vendorId } : null;
      const pageId = AppContext.PageInstance.getPageId();
      // 获取对应的售后客服地址
      const getcustomerserviceParams =
        isOsd && !getCustomerServiceUrl(state)
          ? {
              orderId,
              pageId,
              preSale: 0,
              basicRequest: {},
              abTest: 'A', // 服务端接收参数,A始终代表新版,B始终代表旧版
            }
          : null;
      const queryCertificateV3Params = CarFetchHelper.parameterBuilder({
        param: paramsWithOrderId,
      });
      const requestId = BbkUtils.uuid();
      try {
        const variables = {
          queryAdditionPayment: paramsWithOrderId,
          queryPriceInfo: paramsWithOrderId,
          getEasyLifeTagInfo: getEasyLifeTagInfoParams,
          getLicense: getLicenseParams,
          getcustomerservice: getcustomerserviceParams,
          querySimilarVehicle: isOsd ? paramsWithOrderId : null,
          // 状态变更修订单详情页证件上传入口没出bug todo-待测
          queryCertificateV3: isIsd ? queryCertificateV3Params : null,
          queryOrderInsAndXProduct: Utils.isCtripIsd()
            ? paramsWithOrderId
            : null,
        };
        const timeStart = new Date().getTime();

        const graphqlQueries = getQuerySchema(variables);

        const actionType = 'Order_SecondFetch_UseGraphql';

        // log
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_trace_graphql,
          info: {
            graphqlQueries,
            requestId,
            actionType,
            isActionTypeNull: !actionType,
            expPoint: 'OrderDetail-FETCH_ORDER_DATA_BY_GRAPHQL',
            eventResult: true,
          },
        });

        if (!actionType) return;
        // @ts-ignore
        const graphqlResult = yield graphqlFetch(graphqlQueries, {
          variables,
          requestId,
        });

        const timeEnd = new Date().getTime();
        fetchMetricLog({
          url: graphqlPath,
          isSuccess: true,
          networkCost: timeEnd - timeStart,
          requestId,
          actionType,
          pageId,
          expPoint: 'OrderDetail-FETCH_ORDER_DATA_BY_GRAPHQL',
          eventResult: true,
        });
        const {
          // eslint-disable-next-line @typescript-eslint/no-shadow
          queryAdditionPayment,
          queryPriceInfo,
          getEasyLifeTagInfo,
          getLicense,
          getcustomerservice,
          querySimilarVehicle,
          queryCertificateV3,
          queryOrderInsAndXProduct,
        } = graphqlResult;

        yield put(
          setSelectorData({
            additionPaymentInfo: queryAdditionPayment,
            orderDetailPrice: {
              ...queryPriceInfo?.feeDetailInfo,
              queryOrderDetailPriceLoaded: true,
            },
            easyLifeTags: getEasyLifeTagInfo?.easyLifeTag || [],
            similarVehicleInfo: querySimilarVehicle,
            newOrderInsAndXRes: queryOrderInsAndXProduct,
          }),
        );

        const serviceUrl = getcustomerservice?.url
          ? getcustomerservice?.url
          : yield Utils.getRequestUrl(
              `/webapp/livechath5/chat?GroupCode=aftercar4&origin=250002&version=3.1&orderid=${orderId}&case=-1&platform=1&exinfo=&productid=`,
            );
        yield put(
          fetchCustomerServiceUrlSuccess({
            customerServiceUrl: serviceUrl,
          }),
        );
        if (queryCertificateV3) {
          yield put(
            setOrderAuthInfo({
              supportInfo: {
                ...queryCertificateV3?.supportInfo,
                authCardHistory: queryCertificateV3?.attrDto?.history,
              },
              policy: queryCertificateV3?.policy,
              certificateV3List: queryCertificateV3?.certificateV3List,
            }),
          );
        }
        const { licenseDesc, companyName, licenseImgUrl } = getLicense || {};
        yield put(setSupplierData({ licenseDesc, companyName, licenseImgUrl }));
      } catch (error) {
        fetchMetricLog({
          url: graphqlPath,
          isSuccess: false,
          requestId,
          error: Utils.composeError2String(error),
          actionType: 'Order_SecondFetch_UseGraphql',
          pageId,
          eventResult: false,
          expCode: Utils.getFrontEndExpCode(null, error),
          expMsg: Utils.getErrorMessage(error),
        });
      }
    },
  );
}

export function* queryOrderDetail() {
  yield takeEvery(QUERY_ORDER_DETAIL, function* logic(action: any) {
    const { data = {} }: any = action;
    const { orderId, callback, notifyModifyOrderCallback } = data;
    const pageId = AppContext.PageInstance.getPageId();
    const request = {
      orderId,
      channelType: Number(AppContext.MarketInfo.channelId),
      pageId,
      requestId: BbkUtils.uuid(),
    };

    const response = yield CarFetch.queryOrder(request).catch(() => {});

    yield put(fetchOrderSuccess({ response, request }));
    if (callback) {
      callback(!!response?.orderBaseInfo?.orderId);
    }
    if (notifyModifyOrderCallback) {
      yield put(
        setInitialDataCallback({
          isSuccess: !!response?.orderBaseInfo?.orderId,
        }),
      );
    }
  });
}

export function* apiFetchQueryCancelFee() {
  yield takeEvery(FETCH_QUERYCANCELFEE, function* logic(action: ActionType) {
    const goToCancelPage = () => {
      AppContext.PageInstance.push(Channel.getPageId().OrderCancel.EN, {
        visible: true,
      });
    };

    const { withCancelInfo = true, callback = goToCancelPage } =
      action.data || {};
    const state = yield select();
    const isKlb = isKlbVersion(state);
    const params = { ...getReqOrderParams(state), timeout: 120 };
    Loading.showMaskLoading();
    const res = yield CarFetch.queryCancelFee(params).catch(() => {});
    if (res?.isSuccessful) {
      yield put(fetchQueryCancelFeeCallBack({ resCancelFee: res }));
      // 国内默认请求取消页信息
      if (Utils.isCtripIsd() || GetAB.isOSDNewOrderCancel(isKlb)) {
        if (withCancelInfo) {
          yield put(queryOrderCancelInfo({ callback }));
        }
      } else {
        callback();
      }
    } else {
      UIToast.show('系统繁忙');
    }
    Loading.hideMaskLoading();
  });
}

export function* queryCancelInfo() {
  yield takeEvery(FETCH_QUERY_CANCEL_INFO, function* logic(action: ActionType) {
    const { callback } = action.data || {};
    const state = yield select();
    const request = {
      orderId: getOrderId(state),
      extraMaps: {
        osdModifyOrderVersion:
          getExtendedInfo(state)?.osdModifyOrderVersion || '',
      },
    };
    const cancelInfo = yield CarFetch.cancelInfo(request).catch(() => {});
    yield put(queryOrderCancelInfoCallBack(cancelInfo));
    callback?.();
  });
}

function* apiFetchCreditNotificate() {
  yield takeEvery(FETCH_CREDIT_NOTIFICATION, function* logic(action) {
    const state = yield select();
    const depositState = getIsdFreeDeposit(state);
    // @ts-ignore
    const { uuid } = action.data;
    const params = {
      oid: depositState.orderId,
      deposit: depositState.creditInfo && depositState.creditInfo.deposit,
      quickPayNo: uuid,
      timeout: 120,
    };
    Loading.showMaskLoading();
    const res = yield CarFetch.getDepositNotice(params).catch(() => {});
    Loading.hideMaskLoading();
    yield put(fetchQueryDepositNoticeCallback({ res }));
  });
}

function* apiUpdatePayment() {
  yield takeEvery(FETCH_UPDATE_PAYMENT, function* logic(action: any) {
    const state = yield select();
    const depositState = getIsdFreeDeposit(state);
    // @ts-ignore
    const { uuid } = action.data;
    const params = {
      orderId: depositState.orderId,
      // request id or quickPayNo
      quickPayNo: uuid,
      uid: AppContext.UserInfo.userId,
      timeout: 120,
    };
    Loading.showMaskLoading();
    const res = yield CarFetch.updatePayment(params).catch(() => {});
    Loading.hideMaskLoading();

    yield put(fetchUpdatePaymentCallback({ res }));
  });
}

export function* apiToCancelBook() {
  yield takeEvery(FETCH_TOCANCELBOOK, function* logic(action) {
    const { refundPenaltyAmount, reasonCode, callback } =
      // @ts-ignore
      action.data;
    Loading.showMaskLoading();
    const state = yield select();
    // @ts-ignore
    const reason = getCancelReason(state);
    const penaltyAmount = getResCancelFee(state)?.amount;
    const params = {
      ...getReqOrderParams(state),
      reason,
      penaltyAmount,
      refundPenaltyAmount,
      reasonCode,
    };
    if (Utils.isCtripIsd()) {
      // 全程保留订单项目-标识
      params.strongSubmit = {
        type: 3,
      };
      // 全程保留订单项目-submitId
      const submitId = getCancelOrderSubmitId(state);
      if (submitId) {
        params.strongSubmit.submitId = submitId;
      }
    }
    const res = yield CarFetch.cancelOSDOrder(params).catch(() => {});
    Loading.hideMaskLoading();
    let cancelOrderResultInfo: LogErrorInfoType = {
      eventResult: true,
      expMsg: '',
      expCode: '',
      expPoint: 'apiToCancelBook',
    };
    let isPenaltyChange;
    if (res && res.baseResponse && res.baseResponse.isSuccess) {
      UIToast.show('取消成功', 2);
      // 取消订单成功 发送事件 清空列表页缓存
      EventHelper.sendEvent(EventName.removeListCache, {});
      // dispatch(setCancelModalVisible({ visible: false }));
      // 全程保留订单项目-取消订单成功-清除submitId
      if (Utils.isCtripIsd()) {
        yield put(setCancelOrderSubmitId(''));
      }
    } else if (res?.baseResponse?.code === '403') {
      isPenaltyChange = true;
      yield put(
        toCancelCallBack({
          isPenaltyChange,
          penaltyChangeTip: res?.penatlyChangeTip, // 展示在取消政策弹窗中
          penaltyChangeCancelTip: res?.cancelTip, // 展示在确认取消弹窗中
        }),
      );
    } else {
      if (Utils.isCtripIsd() && res?.cancelTip?.desc?.[0]) {
        // 全程保留订单项目-设置SubmitId
        yield put(setCancelOrderSubmitId(res?.strongSubmit?.submitId));
        // 全程保留订单项目-取消订单拦截弹窗
        yield put(
          setOrderModalsVisible({
            cancelOrderConfirmModal: {
              visible: true,
              data: {
                contentText: res?.cancelTip?.desc?.[0],
              },
            },
          }),
        );
      } else {
        UIToast.show('取消失败，请稍后重试', 2);
      }
      cancelOrderResultInfo = {
        eventResult: false,
        expMsg: res?.baseResponse?.returnMsg,
        expCode: res?.baseResponse?.code,
        expPoint: 'apiToCancelBookFailed',
      };
    }
    callback?.({
      isPenaltyChange,
      isShowCancelTip: Utils.isCtripIsd() && !!res?.cancelTip?.desc?.[0],
    });
    yield put(fetchOrder2({}));
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_order_detail_cancel_order_result,
      info: cancelOrderResultInfo,
    });
  });
}
export function* apiFetchQueryScoreAndSuggestions() {
  yield takeEvery(QUERY_SCOREANDSUGGESTIONS, function* logic() {
    // 获取请求的批次
    const state = yield select();
    const { orderId } = getReqOrderParams(state);
    const params = {
      orderId,
      channelType: Number(AppContext.MarketInfo.channelId),
    };
    const res = yield CarFetch.queryScoreAndSuggestions(params).catch(() => {});
    yield put(queryScoreAndSuggestionsCallback({ res }));
  });
}

function* apiCreateComment() {
  yield takeEvery(FETCH_CREATECOMMENT, function* logic(action) {
    Loading.showMaskLoading();
    const state = yield select();
    const { orderId } = getReqOrderParams(state);
    const pickupStore = getPickupStore(state);
    const vehicleInfo = getVehicleInfo(state);
    const vendorInfo = getVendorInfo(state);
    // @ts-ignore
    const { scores, commentContent } = action.data;
    const params = {
      ...scores,
      commentContent,
      orderId,
      storeCode: pickupStore.storeCode,
      vehicleName: vehicleInfo.vehicleName,
      vendorName: vendorInfo.vendorName,
      site: 0, // todo
      channelType: Number(AppContext.MarketInfo.channelId),
    };
    const res = yield CarFetch.createComment({ ...params }).catch(() => {});
    Loading.hideMaskLoading();
    if (res && Number(res.resultCode) === 2000) {
      yield put(showReviewSuccessModal({ visible: true }));
      yield put(fetchOrder2({}));
    } else {
      const sharkObj = {
        review_commentFail_4010: '点评内容过长',
        review_commentFail_3010: '重复点评，订单已点评',
      };
      UIToast.show(
        res &&
          res.resultCode &&
          sharkObj[`review_commentFail_${res.resultCode}`]
          ? sharkObj[`review_commentFail_${res.resultCode}`]
          : '系统繁忙',

        2,
      );
    }
  });
}
export function* queryOrderPriceInfo() {
  yield takeEvery(QUERY_ORDER_PRICE_INFO, function* logic(action: any) {
    const state = yield select();
    const orderId = action?.data?.orderId || getReqOrderParams(state).orderId;
    const res = yield CarFetch.queryOrderPriceInfo({
      orderId: orderId || getReqOrderParams(state).orderId,
    }).catch(() => {});
    yield put(
      queryOrderPriceInfoCallBack({
        orderDetailPrice: {
          ...res?.feeDetailInfo,
          queryOrderDetailPriceLoaded: true,
        },
      }),
    );
    const { callBack } = action.data;
    if (callBack && typeof callBack === 'function') callBack();
  });
}
export function* apiModifyOrder() {
  yield takeEvery(FETCH_MODIFYORDERCALLBACK, function* logic() {
    // @ts-ignore
    const state = yield select();
    const params = {
      ...getModifyOrderParam(state),
      channelType: Number(AppContext.MarketInfo.channelId),
    };
    Loading.showMaskLoading();
    const res = yield CarFetch.modifyOrder(params).catch(() => {});
    Loading.hideMaskLoading();
    UIToast.show(
      res && res.success ? '信息修改成功' : res.errorMessage || '',
      2,
    );
    yield put(setOrderChangeModalVisible({ visible: false }));
    AppContext.PageInstance.pop();
    yield put(fetchOrder2({}));
  });
}

const getCreateInsOrder = async (orderId, processData) => {
  // 优享新流程
  const params = {
    orderId,
    name: processData.name,
    ctripCode: processData.code,
    sourceFrom: processData.sourceFrom,
    price: processData.price,
    amount: processData.price,
    quantity: processData.quantity,
    quantityName: processData.quantityName,
    description: processData.description,
    additionalId: processData.additionalId,
    status: processData.status,
    token: processData.insuranceToken,
    packageDetail: processData.packageDetail,
  };
  const res = await CarFetch.ISDBuyInsOrder(params).catch(() => {});
  return {
    isSuccess: res && res.baseResponse && !!res.baseResponse.isSuccess,
    refNo: res && res.referenceNo,
    businessType: res?.businessType,
    businessId: res?.additionalId,
    resultCode: res?.baseResponse?.code,
  };
};
export function* apiBuyInsOrder() {
  yield takeEvery(FETCH_ISDBUYINSURANCE, function* logic(action) {
    // 获取请求的批次
    // @ts-ignore
    const state = yield select();
    let processData: any = action;
    processData = processData.data;
    processData.code = processData.code || processData.uniqueCode;
    // 保险/优享加购升级鉴权提示
    const isOrderDataByPhone = getOrderDataByPhone(state);

    if (isOrderDataByPhone) {
      EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
      return;
    }

    const remoteQConfig = yield CarFetch.getQConfig();
    if (
      processData.code !== TRAVEL_INSURANCE_ID ||
      !remoteQConfig?.insuranceFlag
    ) {
      yield put(goIsdInsurancePayment({ ...processData }));
      return;
    }
    const orderParams = { ...getReqOrderParams(state) };
    const { orderId } = orderParams;
    processData.orderId = orderId;
    processData.amount = processData.price;
    processData.isInsOrder = processData.code === TRAVEL_INSURANCE_ID;
    const addInsParams = getaddInsParams(state);
    if (!addInsParams) {
      return;
    }
    if (processData && processData.code === WUYOUZUNXIANG_ID) {
      CarLog.LogCode({
        name: processData.canUpgrade
          ? '点击_订详_升级尊享'
          : '点击_订详_加购尊享',
      });
    } else {
      CarLog.LogCode({ name: '点击_订详_加购优享' });
    }
    if (typeof processData?.callback === 'function') {
      processData.callback(addInsParams, getCreateInsModalVisible(state));
    }
  });
}

export function* isdInsurancePayment() {
  yield takeEvery(ISDINSURANCEPAYMENT, function* logic(action: any) {
    const state = yield select();
    // eslint-disable-next-line prefer-destructuring
    const orderParams = { ...getReqOrderParams(state) };
    const { orderId } = orderParams;
    let processData: any = action;
    let insData = processData.data;
    if (processData?.data?.token) {
      // 保代回调的情况
      // eslint-disable-next-line prefer-destructuring
      insData = getisdInsData(state)[0];
    }
    processData = {
      ...insData,
      orderId,
      amount: insData?.price,
      isInsOrder: insData?.code === TRAVEL_INSURANCE_ID,
      insuranceToken: processData?.data?.token ?? '',
      titletype: PAY_TITLE_TYPE.Normal,
      ordertitle: insData?.title,
      hideOrderPaySummary: true,
    };
    const { status } = processData;
    // status == 0 保险未下单 status == 1 保险待支付 status == 2 保险支付中 status == 3 保险支付成功
    const payFn = function* (res?) {
      const { refNo, businessType, businessId } = res;
      if (refNo) {
        processData.requestId = refNo;
        processData.businessType = businessType;
        processData.businessId = businessId;
      }
      const data = getElsePaymentParams(processData, state);
      const payRes = (yield MiddlePay({
        params: data,
        scene: PayScene.OrderInsurancePay,
      })) as any;
      if (payRes?.success) {
        yield put(fetchOrder2({}));
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
          info: {
            eventResult: true,
            expCode: res?.baseResponse?.code,
            expMsg: res?.baseResponse?.returnMsg,
            expPoint: 'isdInsurancePayment success',
          },
        });
      } else {
        UIToast.show('支付失败');
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
          info: {
            eventResult: false,
            expCode: ApiResCode.TraceCode.E1001,
            expMsg: 'payError',
            expPoint: 'payError',
          },
        });
      }
    };

    if (status === 1) {
      yield payFn();
    } else {
      const res = yield getCreateInsOrder(orderId, processData);
      if (res && res.isSuccess) {
        yield payFn(res);
      } else if (
        [
          CreateInsOrderResCode.getPriceFail1,
          CreateInsOrderResCode.getPriceFail2,
          CreateInsOrderResCode.priceUnMatch,
        ].includes(res.resultCode)
      ) {
        yield put(
          setOrderModalsVisible({
            buyInsConfirmModal: {
              visible: true,
              data: {
                get title() {
                  return '门店调整了服务费价格，请重新核对价格后购买';
                },
                get submitTxt() {
                  return '知道了';
                },
              },
            },
          }),
        );
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
          info: {
            eventResult: false,
            expCode: res?.baseResponse?.code,
            expMsg: res?.baseResponse?.returnMsg,
            expPoint: 'serverError',
          },
        });
      } else {
        UIToast.show('系统异常，请稍后重试');
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
          info: {
            eventResult: false,
            expCode: res?.baseResponse?.code,
            expMsg: res?.baseResponse?.returnMsg,
            expPoint: 'serverError',
          },
        });
      }
    }
  });
}

export function* createOrderAdditionalPay() {
  yield takeEvery(CREATE_ORDER_PAYMENT, function* logic(action) {
    const state = yield select();
    // @ts-ignore
    const payParams = action.data;
    const {
      orderId,
      amount,
      payDeadline,
      bizScene,
      additionalPaymentId,
      isModifyToPay,
    } = payParams || {};
    Loading.showMaskLoading();
    const res = yield CarFetch.createOrderAdditionalPay(payParams).catch(
      () => {},
    );
    Loading.hideMaskLoading();
    let successInfo: LogErrorInfoType;
    if (res?.referenceNo) {
      const hideOrderPaySummary = getIsModifyOrderAddPayment(state);
      const payData = getElsePaymentParams(
        {
          orderId,
          amount,
          get ordertitle() {
            return '补款';
          },
          payremindTime:
            bizScene === Enums.BizSceneType.ModifyOrder
              ? payDeadline
              : undefined,
          hideOrderPaySummary,
          titletype: hideOrderPaySummary ? PAY_TITLE_TYPE.Normal : undefined,
          requestId: res.referenceNo,
          businessType: 1, // 补款
          businessId: additionalPaymentId,
        },
        state,
      );
      const payRes = (yield MiddlePay({
        params: payData,
        scene: PayScene.OrderAdditionPay,
      })) as any;
      if (payRes?.success) {
        if (isModifyToPay) {
          AppContext.PageInstance.pop();
        }
        yield put(fetchOrder2({}));
        successInfo = {
          eventResult: true,
          expCode: res?.baseResponse?.code,
          expMsg: res?.baseResponse?.returnMsg,
          expPoint: 'pay',
        };
      } else {
        successInfo = {
          eventResult: false,
          expCode: ApiResCode.TraceCode.E1001,
          expMsg: 'payError',
          expPoint: 'payError',
        };
        UIToast.show('支付失败');
      }
    } else {
      successInfo = {
        eventResult: false,
        expCode: res?.baseResponse?.code,
        expMsg: res?.baseResponse?.returnMsg,
        expPoint: 'serverError',
      };
    }
    yield put(getAdditionPayment({ orderId }));

    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_order_detail_addtion_pay,
      info: successInfo,
    });
    // 支付后延时去加载新状态
    delay(3000);
    yield put(fetchOrder2({}));
  });
}

function* getLimitContent() {
  yield takeLatest(GET_LIMIT_CONTENT_DATA, function* logic() {
    const curState = yield select();
    const cid = getUseCityID(curState);
    const ptime = dayjs(getPickUpTime(curState)).format('YYYY-MM-DD HH:mm:ss');
    const rtime = dayjs(getDropOffTime(curState)).format('YYYY-MM-DD HH:mm:ss');

    const param = {
      transLimitReq: {
        cityId: parseInt(cid, 10),
        vendorId: 0,
        ctripVehicleId: 0,
        pickupDate: ptime,
        returnDate: rtime,
      },
    };
    const parameter = CarFetchHelper.parameterBuilder({
      param,
      cachePolicy: { enableCache: true },
    });
    const res = yield CarFetch.getLimitContent(parameter).catch(() => {});
    if (res?.baseResponse?.isSuccess) {
      yield put(
        getLimitContentSuccess({
          limitCont: res,
        }),
      );
    }
  });
}
// 查询境外客服地址
function* queryCustomerServiceUrl() {
  yield takeEvery(QUERY_CUSTOMERSERVICEUEL, function* logic() {
    const state = yield select();
    const orderId = getReqOrderParams(state)?.orderId;
    const pageId = AppContext.PageInstance.getPageId();
    let serviceUrl = '';
    // 调用接口,获取对应的售后客服地址
    const param = {
      orderId,
      pageId,
      preSale: 0,
      basicRequest: {},
      abTest: 'A', // 服务端接收参数,A始终代表新版,B始终代表旧版
    };
    const res = yield CarFetch.getCustomerService(param).catch(() => {});
    if (res && res.url) {
      serviceUrl = res.url;
    } else {
      serviceUrl = '/webapp/livechath5/chat?GroupCode=aftercar4&origin=250002&';
      serviceUrl += `version=3.1&orderid=${orderId}&case=-1&platform=1&exinfo=&productid=`;
      serviceUrl = yield Utils.getRequestUrl(serviceUrl);
    }
    yield put(
      fetchCustomerServiceUrlSuccess({
        customerServiceUrl: serviceUrl,
      }),
    );
  });
}

// 继续支付失败文案优化处理
export function* handleContinuePayFail(response) {
  const { resultMessage, tipType, resultCode } = response || {};
  if (tipType === 4) {
    yield put(
      setContinuePayFailDiaLogInfo({
        visible: true,
        type:
          resultCode === ApiResCode.ContinuePayResultCode.VerificationFailed
            ? 'alert'
            : 'confirm',
        content: resultMessage,
      }),
    );
  } else {
    UIToast.show(resultMessage, 2);
  }
}

// 继续支付失败弹窗左侧重新预订处理
function* handleContinuePayFailDialogCancel() {
  yield takeLatest(HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL, function* logic() {
    // 1、主动取消订单
    yield put(
      toCancelBook({
        get reason() {
          return '继续支付失败取消';
        },
      }),
    );
    // 2、关闭弹窗
    yield put(setContinuePayFailDiaLogInfo({ visible: false }));
    // 3、回到首页
    const curState = yield select();
    const rentalLocationAndDate = getNewHomeParamFromOrder(curState);
    // 若订单的租车时间已过期，则重新取初始化时间
    if (dayjs(rentalLocationAndDate.rentalDate.pickup).isBefore(dayjs())) {
      rentalLocationAndDate.rentalDate = {
        pickup: getInitPTime(),
        dropoff: getInitRTime(),
      };
    }
    const rentalDate = {
      pickUp: {
        dateTime: dayjs(rentalLocationAndDate.rentalDate.pickup).format(
          'YYYYMMDDHHmmss',
        ),
      },
      dropOff: {
        dateTime: dayjs(rentalLocationAndDate.rentalDate.dropoff).format(
          'YYYYMMDDHHmmss',
        ),
      },
    };
    const data = {
      rentalLocation: rentalLocationAndDate.rentalLocation,
      rentalDate,
    };
    const baseUrl = Utils.isCtripIsd()
      ? Platform.CAR_CROSS_URL.CTQHOME.ISD
      : Platform.CAR_CROSS_URL.CTQHOME.OSD;
    const url = `${baseUrl}&data=${encodeURIComponent(JSON.stringify(data))}`;
    xRouter.navigateTo({ url });
  });
}

// 继续支付
export function* queryCtripContinuePay() {
  yield takeEvery(QUERYCCTRIPCONTINUEPAY, function* logic(action) {
    const state = yield select();
    const payMode = lodashGet(state.OrderDetail, 'orderBaseInfo.payMode');
    const orderId = lodashGet(state.OrderDetail, 'orderBaseInfo.orderId');
    const authOrderCount = getAuthOrderCount(state);
    Loading.showMaskLoading();
    const processData: any = action;
    const { data = {}, goToInsFun = () => {} } = processData;
    const { confirmInsuranceData } = data;
    const buildInsuranceParams = getInsConfirmReqParam(state);
    const insuranceFlag = getQConfig(state)?.insuranceFlag;
    // 跳转保险代理页
    if (buildInsuranceParams && insuranceFlag && !confirmInsuranceData) {
      Loading.hideMaskLoading();
      goToInsFun(buildInsuranceParams);
      CarLog.LogCode({
        name: '点击_订详_跳保代页面',

        info: { buildInsuranceParams },
      });
      return;
    }
    const continuePayParams: any = queryContinuePayParams(
      state,
      confirmInsuranceData,
    );
    // 2021-12-14 是否反选了保险
    const unSelectIns =
      confirmInsuranceData &&
      confirmInsuranceData?.data?.selectedInsuranceList?.length === 0;
    // 2021-12-14 到店付的订单加购自营险后变成预付定金, 反选自营险后无需跳支付
    if (unSelectIns && Utils.isCtripIsd() && payMode === 12) {
      // 预付定金
      Loading.hideMaskLoading();
      return;
    }
    try {
      const taskQueryPriceInfo = unSelectIns
        ? CarFetch.queryOrderPriceInfo({ orderId })
        : null;
      // 2021-12-14 反选自营险后，需要同时调继续支付接口和价格接口，以保证跳支付页时，支付页披露的费用明细不再含自营险费用
      const continuePayRequest = CarFetchHelper.parameterBuilder({
        param: continuePayParams,
        verifyResponseIsSuccess: res => !(res.resultCode === '0' || !!res.rst),
      });
      const result = yield Promise.all([
        CarFetch.ctripContinuePay(continuePayRequest),
        taskQueryPriceInfo,
      ]);

      const [response, priceInfoRes] = result;
      if (unSelectIns) {
        yield put(
          queryOrderPriceInfoCallBack({
            orderDetailPrice: {
              ...priceInfoRes?.feeDetailInfo,
              queryOrderDetailPriceLoaded: true,
            },
          }),
        );
      }
      Loading.hideMaskLoading();
      // 重复下单拦截
      yield put(setContinuePayInterceptionData(null));
      const { resultCode, resultMessage, strongSubmit } = response;
      if (
        [
          ApiResCode.OrderCheckResultCode.weakInterception,
          ApiResCode.OrderCheckResultCode.strongInterception,
        ].includes(resultCode)
      ) {
        yield put(
          setContinuePayInterceptionData({
            visible: true,
            resultCode,
            resultMsg: resultMessage,
            requestParams: data,
            strongSubmit,
          }),
        );
        return;
      }

      if (response.resultCode === '-1') {
        // 如果是芝麻重复订单执行回调
        const alipayTip =
          authOrderCount === 1
            ? '您已经有一笔信用免押订单在使用中，如果要继续支付这笔信用免押订单，需要去授权第二笔信用免押'
            : '如果要继续支付这笔信用免押订单，您需要去授权芝麻分';

        yield put(
          setOrderModalsVisible({
            sesameRepeatOrderModal: {
              visible: true,
              data: {
                title: alipayTip,
                get submitTxt() {
                  return '去授权';
                },
                get cancelTxt() {
                  return '再看看';
                },
              },
            },
          }),
        );
      } else if (response.resultCode === '0' || !!response.rst) {
        yield put(
          fetchCarAssistantCallBack({
            isdContinuePayAmount: response.amt,
          }),
        );
        yield put(continuePayment());
      } else {
        // 2022-8-17 继续支付失败文案优化处理
        yield handleContinuePayFail(response);
      }
    } catch (err) {
      UIToast.show('支付失败，请稍后重试');
    }
  });
}

// 继续支付授权后更新订单免押信息
function* updateFreeDepositInfoByContinuePay() {
  yield takeEvery(UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY, function* logic() {
    const state = yield select();
    const updateFreeDepositParams: any = updateFreeDepositInfoParams(state);
    yield put(updateFreeDepositInfo({ ...updateFreeDepositParams }));
  });
}

const depoistFreeAuthResultTraceLog = info => {
  CarLog.LogTrace({
    key: LogKey.vac_car_trace_orderdetail_depoistfree_result,
    info,
  });
};

// 继续支付-调支付
export function* createContinuePayment() {
  yield takeEvery(CONTINUEPAYMENT, function* logic() {
    const state = yield select();
    const continueBackPay = getContinueBackPay(state);
    const payData: any = getPaymentParams(state);
    let payRes: MiddlePayRes;
    const creditRiskResult = getCreditRiskResult(state);
    const orderId = getOrderId(state);
    const creditRiskRequestId = `${
      getCreditRiskRequestId(state) || BbkUtils.uuid()
    }${orderId}`;
    if (Utils.isCtripIsd()) {
      const params: CarPayParams = {
        ...payData,
        pageId: AppContext.PageInstance.getPageId(),
        isOnlyAlipay: false, // todo
        isNotWeChatPay: false, // 不支持微信支付  osd马甲包不支持微信支付
        bizMode: 0, // 国内修改订单标识
        payType: PayType.RegularPay,
        isOnlyCreditCard: false,
        requestId: creditRiskRequestId,
        extensions: {
          freeDepositWay: getFreeDepositWay(state),
          depositPayType: getFreeDepositType(state),
        },
      };
      if (continueBackPay) {
        if (creditRiskResult === 'T') {
          // 后付 payType:16
          payRes = (yield MiddlePay({
            params: {
              ...params,
              // todo-xt 售后单独自己定义
              payType: PayType.CtripCredit,
              busType: Platform.BUS_TYPE.ISD_CREDIT,
              payRequestId: BbkUtils.uuid(), // 预授权支付 和 程信分后扣款需要传 payRequestId
              creditInfoMap: getFreeDeposit(state)?.creditMap,
            },
            scene: PayScene.OrderContinueCreditRentAuth,
          })) as MiddlePayRes;
          const { success, payInfo = {}, isNew, payRequestId } = payRes;
          if (success) {
            // 后付 更新
            yield CarFetch.iSDUpdateFreeDepositInfo({
              orderId,
              quickPayNo: isNew ? payRequestId : payInfo.requestId,
              freeDepositWay: getFreeDepositWay(state),
              freeDepositType: getFreeDepositType(state),
            }).catch(() => {});
            yield put(fetchOrder2({}));
          } else {
            UIToast.show('支付失败，请稍后重试');
          }
        } else {
          alertFn({
            get subTitle() {
              return '您当前信用未达标，暂时无法享受信用租·押金双免，请重新选择车型后再预订';
            },
            get submitTxt() {
              return '重新选择';
            },
            get cancelTxt() {
              return '取消订单';
            },
            submitFn: () => {
              DeviceEventEmitter.emit(EventName.orderToRebookNotice, 'rebook');
            },
            cancelFn: () => {
              DeviceEventEmitter.emit(EventName.orderToRebookNotice, 'cancel');
            },
          });
        }
      } else {
        // payType:1
        payRes = (yield MiddlePay({
          params,
          scene: PayScene.OrderContinueRegularPay,
        })) as any;
        if (payRes?.success) {
          yield put(fetchOrder2({}));
          // 支付成功 发送事件 清空列表页缓存
          EventHelper.sendEvent(EventName.removeListCache, {});
        } else {
          UIToast.show('支付失败');
        }
      }
    } else {
      const OSDPayParams = {
        ...payData,
        requestId: BbkUtils.uuid(),
        busType: Platform.BUS_TYPE.NEWOSD,
        payType: PayType.RegularPay,
      };
      payRes = (yield MiddlePay({
        params: OSDPayParams,
        scene: PayScene.OrderOSDContinuePay,
      })) as any;
      if (payRes?.success) {
        yield put(fetchOrder2({}));
      } else {
        UIToast.show('支付失败');
      }
    }
  });
}

export function* creditRentAuth() {
  yield takeEvery(CREDIT_RENT_AUTH, function* logic(action: any) {
    const state = yield select();
    const orderId = getOrderId(state);
    const creditRiskRequestId = `${getCreditRiskRequestId(state)}${orderId}`;
    const payData: any = getDepositPaymentParams(state);
    let successInfo: LogErrorInfoType = {
      eventResult: true,
      expCode: '',
      expMsg: '',
      expPoint: 'creditRentAuth',
    };
    const params: CarPayParams = {
      ...payData,
      pageId: AppContext.PageInstance.getPageId(),
      isOnlyAlipay: false, // todo
      isNotWeChatPay: false, // 不支持微信支付  osd马甲包不支持微信支付
      bizMode: 0, // 国内修改订单标识
      payType: PayType.CtripCredit,
      busType: Platform.BUS_TYPE.ISD_CREDIT,
      payRequestId: BbkUtils.uuid(), // 预授权支付 和 程信分后扣款需要传 payRequestId
      isOnlyCreditCard: false,
      requestId: creditRiskRequestId,
      extensions: {
        freeDepositWay: Utils.isCtripIsd()
          ? FreeDepositWayType.CreditRent
          : FreeDepositWayType.OSDCreditRent,
        depositPayType: getDepositPayType(state),
      },
      isAfterOrder: true,
      creditInfoMap: getFreeDeposit(state)?.creditMap,
    };
    const payRes: MiddlePayRes = yield MiddlePay({
      params,
      scene: PayScene.OrderCreditRentAuth,
    });
    let verificationResults = 1;
    if (payRes?.status?.status === 0) {
      const { isNew, payRequestId } = payRes;
      yield put(
        updateFreeDepositInfo({
          quickPayNo: isNew ? payRequestId : params.payRequestId,
          freeDepositType: getFreeDeposit(state).freeDepositType,
          freeDepositWay: FreeDepositWayType.CreditRent,
        }),
      );
    } else {
      if (Utils.isCtripOsd()) {
        UIToast.show('授权失败');
      }
      verificationResults = 0;
      successInfo = {
        eventResult: false,
        expCode: ApiResCode.TraceCode.E1001,
        expMsg: payRes?.result,
        expPoint: 'creditRentAuthFailed',
      };
    }
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_order_detail_deposit_auth,
      info: successInfo,
    });

    // 场景名称映射
    const sceneNameMap = {
      [CreditAuthScene.DepositPaymentModalOSD]:
        '订单详情页_免押弹层_免押金去授权返回操作结果',
      [CreditAuthScene.FreeDepositEntryOSD]:
        '订单详情页_免押金去授权返回操作结果',
    };

    CarLog.LogTrace({
      key: LogKey.c_car_trace_module_exposure,
      info: {
        name:
          sceneNameMap[action?.data?.scene] ||
          '订单详情页_免押金去授权返回操作结果',
        verificationResults,
        depositType: DepositTipsDepositType.CreditRent,
      },
    });
  });
}

// export const getPmsSelfHelp = createLogic({
//   type: GETRENTALMUSTREAD,
//   /* eslint-disable no-empty-pattern */
//   async process({ action, getState }: { action: any, getState: any }, dispatch, done) {
//     const state = getState();
//     // dispatch参数orderId||当前页面链接orderId参数||state中的orderid
//     const orderId = getReqOrderParams(state).orderId;
//     const pms = {
//       orderId,
//     };
//     const resPms = await CarFetch.queryPmsInfo({ ...pms });
//     if (Number(resPms.baseResponse.code) === 1) {
//       dispatch(queryPmsInfoCallBack({
//         pmsInfo: resPms.pickTaskInfo,
//       }));
//     }
//     done();
//   },
// });
const getRentalPolicy = async params => {
  const { orderId, storeCode, vendorCode, isEasyLife } = params;
  const config = await CarFetch.getQConfig();
  if (config?.isGetBookingNotice) {
    return CarFetch.getBookingNotice({
      orderId,
      storeCode,
      vendorCode,
      isEasyLife,
    }).then(result => {
      let resInfo = {};
      if (result) {
        const { mustReads, rentalMustReadTable, rentalMustReadPicture } =
          result;
        resInfo = {
          mustReads,
          rentalMustReadTable,
          rentalMustReadPicture,
        };
      }
      return { resInfo };
    });
  }
  return CarFetch.rentalMustRead({
    reqInfo: {
      storeCode,
      vendorCode,
      isEasyLife,
    },
  });
};

// 获取门店政策
export function* getRentalMustRead() {
  yield takeEvery(GETRENTALMUSTREAD, function* logic() {
    const state = yield select();
    if (Utils.isCtripIsd()) {
      const params = getRentalPolicyParams(state);
      const { resInfo = {} } =
        (yield getRentalPolicy(params).catch(() => {})) || {};
      yield put(
        fetchCarAssistantCallBack({
          carRentalMustRead: resInfo.mustReads || [],
          rentalMustReadTable: resInfo.rentalMustReadTable || [],
          rentalMustReadPicture: resInfo.rentalMustReadPicture || [],
        }),
      );
    } else {
      const { orderId } = getReqOrderParams(state);
      const pms = {
        orderId,
      };
      const response = yield CarFetch.queryOsdPolicy(pms).catch(() => {});

      yield put(
        fetchCarAssistantCallBack({
          carRentalMustRead: response?.carRentalMustRead,
        }),
      );
    }
  });
}

export function* queryAdditionPayment() {
  yield takeEvery(QUERYADDITIONPAYMENT, function* logic(action) {
    const processData: any = action;
    const { data = {} } = processData;
    // dispatch参数orderId||当前页面链接orderId参数||state中的orderid
    const { orderId } = data;
    const pms = {
      orderId,
    };

    const res = yield CarFetch.queryAdditionPayment({ ...pms }).catch(() => {});

    if (res?.baseResponse?.isSuccess) {
      yield put(
        fetchCarAssistantCallBack({
          additionPaymentInfo: res,
        }),
      );
    }
  });
}

export function* getSupplementListLogic() {
  yield takeEvery(GET_SUPPLEMENT_LIST, function* logic(action) {
    const state = yield select();
    const { orderId: urlQueryOrderId } = AppContext.UrlQuery;
    // @ts-ignore
    const { orderId, isLoading } = action.data;
    const parameter = CarFetchHelper.parameterBuilder({
      param: {
        orderId: orderId || getOrderId(state) || urlQueryOrderId,
      },
    });
    if (isLoading) Loading.show();
    const res = yield CarFetch.getSuplementListApi(parameter).catch(() => {});
    yield put(setQueryDeductionData({ res }));
    if (isLoading) Loading?.hide();
  });
}
export function* setDeductionDataLogic() {
  yield takeEvery(SET_QUERYDEDUCTION_DATA, function* logic(action) {
    // @ts-ignore
    const { res } = action.data || {};
    const { orderId } = AppContext.UrlQuery;
    if (res?.baseResponse?.isSuccess) {
      yield put(
        getSupplementListCallBack({
          violationList: res.violationLst,
          vehicleDamageList: res.vehicleDamageLst,
          osdDeductionList: res.oSDDeductionList,
          removeDetail: res.removeDetail,
          violationDesc: res.violationDesc,
          isShowViolationDamageEntry: Number(res.baseResponse.code) === 0,
        }),
      );

      const storageTemp = yield CarStorage.load(
        StorageKey.SUPPLEMENT_LIST_LENGTH,
      );
      const supplementList = JSON.parse(storageTemp) || {};
      if (res?.violationLst?.length > 0 || res?.vehicleDamageLst?.length > 0) {
        if (supplementList[orderId]) {
          const isViolationChange =
            supplementList[orderId].violationListLength <
            res.violationLst.length;
          const isDamageChange =
            supplementList[orderId].vehicleDamageList <
            res.vehicleDamageLst.length;
          if (isViolationChange || isDamageChange) {
            yield put(
              setSupplementListNew({
                visible: true,
              }),
            );
          }
        } else {
          yield put(
            setSupplementListNew({
              visible: true,
            }),
          );
        }
      }

      supplementList[orderId] = {
        violationListLength: (res.violationLst && res.violationLst.length) || 0,
        vehicleDamageList:
          (res.vehicleDamageLst && res.vehicleDamageLst.length) || 0,
      };

      CarStorage.save(
        StorageKey.SUPPLEMENT_LIST_LENGTH,
        JSON.stringify(supplementList),
      );
    }
  });
}
export function* apiUpdateFreeDepositInfo() {
  yield takeEvery(UPDATA_FREE_DEPOSITINFO, function* logic(action: any) {
    const state = yield select();
    Loading.showMaskLoading({
      cancelable: false,
    });
    // 境外不需要调同步接口，直接提示授权成功
    if (Utils.isCtripOsd()) {
      UIToast.show('授权成功，已享免押');
      yield delay(1000); // 延迟1秒
      yield put(
        fetchOrder2({
          callback: () => {
            Loading.hideMaskLoading();
          },
        }),
      );
      return;
    }
    const {
      freeDepositWay,
      freeDepositType,
      preAmountForCar,
      vendorId,
      quickPayNo,
    } = action.data;
    const updateRes = yield CarFetch.iSDUpdateFreeDepositInfo({
      orderId: getOrderId(state),
      quickPayNo: quickPayNo || BbkUtils.uuid(),
      freeDepositWay,
      freeDepositType,
      preAmountForCar,
      vendorId,
    }).catch(() => {});
    const freeAuthLogData = getRenewTipLogData(state);
    if (updateRes?.baseResponse?.isSuccess) {
      yield put(
        setOrderModalsVisible({
          depositPaymentModal: {
            visible: false,
          },
        }),
      );
      if (updateRes?.resultMsg) {
        UIToast.show(updateRes?.resultMsg);
      } else {
        UIToast.show('信用免押成功');
      }
      depoistFreeAuthResultTraceLog({
        ...freeAuthLogData,
        get depositFreeResult() {
          return '信用免押成功';
        },
      });
      yield put(
        fetchOrder2({
          callback: () => {
            Loading.hideMaskLoading();
          },
        }),
      );
    } else {
      depoistFreeAuthResultTraceLog({
        ...freeAuthLogData,
        get depositFreeResult() {
          return '信用免押失败';
        },
      });
      Loading.hideMaskLoading();
      UIToast.show('出错了，请稍后重试');
    }
  });
}

export function* setRenewStatusByStorage() {
  yield takeEvery(SET_RENEW_STATUS_BYSTORAGE, function* logic() {
    const state = yield select();
    // StorageKey.CAR_RENWWAL_STATUS_VISIBLE+renewalOrderId
    const data = getRenewalOrder(state);
    if (data) {
      const { renewalStatusName, renewalOrderId } = data || {};
      const storageKey = `${StorageKey.CAR_RENWWAL_STATUS_VISIBLE}_${renewalOrderId}`;
      const storageOrders = (yield CarStorage.load(storageKey, true)) || '{}';
      const isShow =
        renewalStatusName !== JSON.parse(storageOrders)?.renewalStatusName;
      // 设置是否展示订单状态右边续租状态
      yield put(
        setSelectorData({
          orderRenewStatusVisible: isShow,
        }),
      );
    }
  });
}

export function* saveRenewalOrderStatus() {
  yield takeEvery(SAVE_RENEWALORDERSTATUS, function* logic() {
    const state = yield select();
    const data = getRenewalOrder(state);
    if (data) {
      const { renewalStatusName, renewalOrderId } = data || {};
      const storageKey = `${StorageKey.CAR_RENWWAL_STATUS_VISIBLE}_${renewalOrderId}`;
      CarStorage.save(
        storageKey,
        JSON.stringify({ renewalStatusName }),
        undefined,
        true,
      );
    }
  });
}

export function* depositPayOnlineSaga() {
  yield takeLatest(DEPOSIT_PAY_ONLINE, function* logics() {
    const payParams = yield select(getDepositPayOnlineParams);

    Loading.showMaskLoading({
      cancelable: false,
    });

    try {
      const payRes = yield MiddlePay({
        params: payParams,
        scene: PayScene.OrderOnlinePayDeposit,
      });
      const { success } = payRes;
      if (success) {
        yield put(fetchOrder2());
      } else {
        xShowToast({ title: '支付失败', duration: 3000 });
      }
    } catch (e) {
      xShowToast({ title: '支付失败', duration: 3000 });
    }

    Loading.hideMaskLoading();
  });
}

export function* queryCarAssistantV2Logic() {
  yield takeEvery(QUERY_CAR_ASSISTANT_V2, function* logics(action: ActionType) {
    const state = yield select();
    const request = { orderId: action.data?.orderId || getOrderId(state) };
    try {
      const response = yield CarFetch.queryCarAssistantV2(request);
      yield put(queryCarAssistantV2CallBack(response));
    } catch (e) {
      yield put(queryCarAssistantV2CallBack(null));
    }
  });
}

export function* apiGueryOrderInsAndXProduct() {
  yield takeEvery(QUERY_ORDERINSUANDXPRODUCT, function* logics(action) {
    // @ts-ignore
    const request = { orderId: action.data?.orderId };
    try {
      const response = yield CarFetch.queryOrderInsAndXProduct(request);
      yield put(queryOrderInsAndXProductCallBack(response));
    } catch (e) {
      yield put(queryOrderInsAndXProductCallBack(null));
    }
  });
}

// 修改旅行限制
export function* apiModifyCrossLocation() {
  yield takeEvery(FETCH_MODIFY_CROSS_LOCATION, function* logic(action) {
    Loading.showMaskLoading({
      cancelable: false,
    });
    // @ts-ignore
    const { selectedResult, callback } = action?.data || {};
    const state = yield select();
    const { orderId } = getReqOrderParams(state);
    const crossLocations =
      selectedResult?.map(item => ({
        crossType: ICrossType.CrossCountry,
        crossId: item.regionId,
      })) || [];
    const params = {
      orderId,
      crossLocations,
    };
    const res = yield CarFetch.modifyCrossLocation(params).catch(() => {});
    Loading.hideMaskLoading();
    if (res?.baseResponse?.isSuccess) {
      yield put(setTravelLimitSelectedResult(selectedResult));
      if (typeof callback === 'function') {
        callback();
      }
      return;
    }
    UIToast.show('当前网络不佳，请稍后重试');
  });
}

export function* queryDriverLicenseOrders() {
  yield takeEvery(
    QUERY_DRIVER_LICENSE_ORDERS,
    function* logics(action: ActionType) {
      const { PageIndex = 1 } = action.data;
      const state = yield select();
      const Offset = getNextPageOffset(state);
      const isLastPage = getIsLastPage(state);
      if (isLastPage) {
        return;
      }
      const request = {
        BizTypes: ['Translation'],
        OrderStatusClassify: 'All',
        Channel: 'Hybrid',
        ClientVersion: Application.version,
        Count: LicensePageSize,
        Offset,
      };
      let openResultInfo: LogErrorInfoType = {
        request,
        eventResult: true,
        expPoint: 'apiQueryDriverLicenseOrders',
      };
      try {
        const response = yield CarFetch.queryDriverLicenseOrders(request);
        openResultInfo = {
          request,
          response,
          eventResult: true,
          expCode: response.baseResponse?.code,
          expMsg: response.baseResponse?.returnMsg,
          expPoint: 'apiQueryDriverLicenseOrdersSuccess',
        };
        const nextIsLastPage = response?.OrderEnities?.length < LicensePageSize;
        yield put(
          queryDriverLicenseOrdersCallback({
            nextPageOffset: response?.Offset,
            orderEnities: response?.OrderEnities,
            PageIndex,
            isLastPage: nextIsLastPage,
          }),
        );
      } catch (e) {
        openResultInfo = {
          request,
          eventResult: false,
          expMsg: e?.message,
          expCode: ApiResCode.TraceCode.E1001,
          expPoint: 'apiQueryDriverLicenseOrdersFailed',
        };
        yield put(queryDriverLicenseOrdersCallback(null));
      } finally {
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_driver_license,
          info: openResultInfo,
        });
      }
    },
  );
}

// 查询海外保险订单状态
export function* queryExtraInsurance() {
  yield takeEvery(QUERY_EXTRA_INSRUANCE, function* logic(action) {
    Loading.showMaskLoading({
      cancelable: false,
    });
    // @ts-ignore
    const { callback } = action?.data || {};
    const state = yield select();
    const { orderId } = getReqOrderParams(state);
    const params = {
      orderId,
    };
    const res = yield CarFetch.queryExtraInsurance(params).catch(() => {});
    Loading.hideMaskLoading();
    if (res?.baseResponse?.isSuccess) {
      if (typeof callback === 'function') {
        callback(res?.platformInsuranceOrderExtraList);
      }
      return;
    }
    UIToast.show('当前网络不佳，请稍后重试');
  });
}

export function* queryVehicleStatus() {
  yield takeEvery(QUERY_VEHICLE_STATUS, function* logics(action: any) {
    const { orderId } = action?.data || {};
    const params = {
      orderId,
      checkRange: false,
    };
    const response = yield CarFetch.queryVehicleStatus(params).catch(() => {});
    if (response?.baseResponse?.isSuccess) {
      yield put(
        queryVehicleStatusCallback({
          vehicleStatus: response,
        }),
      );
    }
  });
}

// 埋点需求
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=2254150407
const selfServiceOperationLog = ({
  isSuccess,
  code,
  message,
  orderId,
  vehicleControl,
}: {
  orderId: number;
  isSuccess: boolean;
  code: string;
  message: string;
  vehicleControl: VehicleControl;
}) => {
  let [name, buttonName] = ['', ''];

  if (
    vehicleControl === VehicleControl.UNLOCK ||
    vehicleControl === VehicleControl.LOCK
  ) {
    buttonName =
      vehicleControl === VehicleControl.LOCK ? Texts.lock : Texts.unLock;
    name = '点击_订单详情页_车辆控制卡片_开锁或关锁';
  }

  if (vehicleControl === VehicleControl.RING) {
    name = '点击_订单详情页_车辆控制卡片_鸣笛闪烁';
  }

  CarLog.LogCode({
    name,
    orderId: String(orderId),
    info: {
      buttonName,
      clickResult: isSuccess ? 1 : 0,
      errorCode: isSuccess ? '' : code,
      errorMessage: isSuccess ? '' : message,
      pageId: Utils.getCurPageId(),
    },
  });
};

export function* selfServiceOperation() {
  yield takeEvery(SELF_SERVICE_OPERATION, function* logics(action: any) {
    const { orderId, vehicleControl } = action?.data || {};
    const params = {
      orderId,
      operationType: OperationType.VEHICLE_CONTROL, // 车机操作
      vehicleControl, // 车机操作类型
    };
    const response = yield CarFetch.selfServiceOperation(params).catch(
      () => {},
    );

    const { baseResponse } = response || {};
    const { isSuccess, code = '', returnMsg = '' } = baseResponse || {};

    // 车机操作成功，查询车机状态
    if (isSuccess) {
      yield put(queryVehicleStatusAction({ orderId }));
    }

    selfServiceOperationLog({
      isSuccess,
      code,
      message: returnMsg,
      orderId,
      vehicleControl,
    });
  });
}

export function* checkSubmitReturnCar() {
  yield takeEvery(CHECK_SUBMIT_RETURN_CAR, function* logics(action: any) {
    const { orderId } = action?.data || {};
    const params = {
      orderId,
    };

    const response = yield CarFetch.checkSubmitReturnCar(params).catch(
      () => {},
    );

    if (
      response?.checkItemList?.includes(
        CheckSubmitReturnCarCode.returnCarDistanceInvalidateFail,
      )
    ) {
      yield put(setDistance(response?.distance || 500));
      yield put(
        setOrderModalsVisible({
          distanceInvalidateModal: {
            visible: true,
          },
        }),
      );
    } else {
      xRouter.navigateTo({
        url: `${Url.rNXTaroCarOrderBaseUrl}&initialPage=${SelfServicePageName.DROP_OFF_UPLOAD_STATUS}&orderId=${orderId}`,
      });
    }
  });
}

export function* queryOrderNoticeFromDid() {
  yield takeEvery(QUERY_ORDER_NOTICE_FROM_DID, function* logics(action: any) {
    const { orderId } = action?.data || {};
    const params = {
      orderId,
    };

    const response = yield CarFetch.queryOrderNoticeFromDid(params).catch(
      () => {},
    );
    if (response?.baseResponse?.isSuccess) {
      const { noticeList, noticeTitle, history } = response;
      yield put(setDidNoticeData({ noticeList, noticeTitle, history }));
    }
  });
}

export function* queryOsdModifyOrderNote() {
  yield takeEvery(QUERY_OSD_MODIFY_ORDER_NOTE, function* logics() {
    const state = yield select();
    const { response } = yield CarFetch.queryOsdModifyOrderNote({
      orderId: getOrderId(state),
    }).catch(() => {});
    if (response?.baseResponse?.isSuccess) {
      yield put(setOsdModifyOrderNote({ osdModifyOrderNote: response }));
    }
  });
}
export function* osdModifyOrderInit() {
  yield takeEvery(OSD_MODIFY_ORDER_INIT, function* logics() {
    const state = yield select();
    const orderInfo = getOrderInfo(state);
    // 设置取还时间地点
    yield put(setLocationDateInfo(orderInfo));
    const customerAge = orderInfo?.age;
    const { AgeConfig } = FrontEndConfig;
    if (customerAge) {
      let curAge = customerAge;
      const age = Number(customerAge);
      if (age < AgeConfig.MIN_AGE) {
        curAge = AgeConfig.MIN_AGE.toString();
      } else if (age > AgeConfig.MAX_AGE) {
        curAge = AgeConfig.MAX_AGE.toString();
      } else if (
        age >= AgeConfig.DEFAULT_AGE.min &&
        age <= AgeConfig.DEFAULT_AGE.max
      ) {
        curAge = AgeConfig.DEFAULT_AGE.getVal();
      }
      // 设置年龄
      yield put(setAge(curAge));
    }
  });
}

export function* queryCountriesInfoLogic() {
  yield takeLatest(QUERY_COUNTRYS_INFO, function* logic() {
    const state = yield select();
    if (getPickUpAreaCode(state)) return;
    const { countryId } = getPickupStore(state);
    const param = {
      showGAT: true, // 返回港澳台
      dataSource: 'OCH', // 固定用车数据源
      extraTags: { withCountrySuffix: '1' },
    };

    const res = yield CarFetch.queryAppCountryIdList(param).catch(() => {});

    const countryInfo = res?.countries?.find(
      item => item?.countryId === countryId,
    );
    yield put(setCountrysInfo(countryInfo));
  });
}
export function* queryOrderInfoLogic() {
  yield takeEvery(QUERY_ORDER_INFO, function* logic(action: any) {
    const state = yield select();
    let openResultInfo: LogErrorInfoType = {
      eventResult: true,
      expPoint: 'apiOrderQueryData',
    };
    const { orderId, manOrderFlag, callback } = action?.data || {};
    const isCanRenew = getFulfillIsCanRenew(state);
    const manOrderRequest = {
      orderId,
      blnCanRenew: isCanRenew,
      extraTags: { manOrderFlag },
      extraMaps: { cesNew: '1' }, // 请求新版订详CES
    };
    const response = yield CarFetch.queryMainOrder(manOrderRequest).catch(
      () => {},
    );
    const {
      queryCarAssistantV2: carAssistantResponse,
      queryDeduction,
      oSDQueryOrder,
      queryOrderContactPic,
      getBookingNotice,
      queryPolicy,
      queryOrderNoticeFromDid: queryOrderNoticeFromDidRes,
      querySimilarVehicle,
      queryAdditionPayment: queryAdditionPaymentRes,
      queryPriceInfo,
      queryCertificateV3,
      queryOrderInsAndXProduct,
      // queryOrderFulfill,
    } = response || {};
    if (response?.baseResponse?.isSuccess) {
      yield all([
        put(queryCarAssistantV2CallBack(carAssistantResponse)),
        put(setQueryDeductionData({ res: queryDeduction })),
        put(queryOrderSuccess({ res: oSDQueryOrder, orderId })),
        put(
          setSelectorData({
            additionPaymentInfo: queryAdditionPaymentRes,
            orderDetailPrice: {
              ...queryPriceInfo?.feeDetailInfo,
              queryOrderDetailPriceLoaded: true,
            },
            similarVehicleInfo: querySimilarVehicle,
            newOrderInsAndXRes: queryOrderInsAndXProduct,
            // fulfillmentData: queryOrderFulfill,
          }),
        ),
      ]);
      if (Utils.isCtripIsd()) {
        const vendorId = oSDQueryOrder?.vendorInfo?.vendorID;
        yield put(queryOrderDetailSupplierData({ vendorId }));
      }
    } else {
      yield put(
        fetchOrderSuccess({
          queryOrderApiStatus: QueryOrderApiStatusType.fail,
        }),
      );
      // 只上报服务有返回场景
      if (oSDQueryOrder) {
        openResultInfo = {
          eventResult: false,
          expCode: oSDQueryOrder.baseResponse?.code,
          expMsg: oSDQueryOrder.baseResponse?.returnMsg,
          expPoint: 'fromQueryManOrder',
          error: oSDQueryOrder,
        };
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_open_result,
          info: openResultInfo,
        });
      }
    }

    yield put(queryOrderWarningInfo());

    if (queryCertificateV3) {
      yield put(
        setOrderAuthInfo({
          supportInfo: {
            ...queryCertificateV3?.supportInfo,
            authCardHistory: queryCertificateV3?.attrDto?.history,
          },
          policy: queryCertificateV3?.policy,
          certificateV3List: queryCertificateV3?.certificateV3List,
        }),
      );
    }
    if (queryPolicy?.baseResponse?.isSuccess) {
      yield put(
        fetchCarAssistantCallBack({
          carRentalMustRead: queryPolicy?.carRentalMustRead,
        }),
      );
    }
    const { noticeList, noticeTitle, history } =
      queryOrderNoticeFromDidRes || {};
    if (noticeList?.length) {
      yield put(setDidNoticeData({ noticeList, noticeTitle, history }));
    }
    // 依赖订单返回的接口处理
    if (
      Utils.isCtripIsd() &&
      (!queryOrderContactPic?.baseResponse?.isSuccess ||
        !getBookingNotice?.baseResponse?.isSuccess)
    ) {
      const { vendorID, vendorConfirmCode } = getVendorInfo(state) || {};
      const { storeCode, vendorCode, isEasyLife } =
        getRentalPolicyParams(state) || {};
      yield put(
        queryOrderInfoFlag2({
          orderId,
          manOrderFlag: '2',
          vendorId: vendorID,
          orderCode: vendorConfirmCode,
          storeCode,
          vendorCode,
          isEasyLife,
        }),
      );
    } else {
      if (queryOrderContactPic?.baseResponse?.isSuccess) {
        yield put(
          getCredentialsListCallBack({
            contacts: queryOrderContactPic.contacts,
          }),
        );
      }
      if (getBookingNotice?.baseResponse?.isSuccess) {
        yield put(
          setSelectorData({
            carRentalMustRead: getBookingNotice.mustReads || [],
            rentalMustReadTable: getBookingNotice.rentalMustReadTable || [],
            rentalMustReadPicture: getBookingNotice.rentalMustReadPicture || [],
          }),
        );
      }
    }
    if (callback && typeof callback === 'function') callback();
    if (Utils.isCtripIsd()) {
      // 修改订单成功弹窗提示
      yield put(modifyOrderSuccess());
    }
    const easyLifeTagInfo = yield CarFetch.getEasyLifeTagInfo(
      manOrderRequest,
    ).catch(() => {});
    yield put(
      setSelectorData({
        easyLifeTags: easyLifeTagInfo?.easyLifeTag || [],
      }),
    );
    if (Utils.isCtripOsd()) {
      if (!getCustomerServiceUrl(state)) {
        const customerserviceRes = yield CarFetch.getCustomerService(
          manOrderRequest,
        ).catch(() => {});
        const serviceUrl = customerserviceRes?.url
          ? customerserviceRes?.url
          : yield Utils.getRequestUrl(
              `/webapp/livechath5/chat?GroupCode=aftercar4&origin=250002&version=3.1&orderid=${orderId}&case=-1&platform=1&exinfo=&productid=`,
            );
        yield put(
          fetchCustomerServiceUrlSuccess({
            customerServiceUrl: serviceUrl,
          }),
        );
      }
    }

    // 是否全部请求结束
    yield put(setFetchDone({ fetchDone: true }));
  });
}
// 第二批有依赖主接口的数据请求
export function* queryOrderInfoFlag2Logic() {
  yield takeEvery(QUERY_ORDER_INFO_FLAG2, function* logic(action: any) {
    const {
      orderId,
      manOrderFlag,
      vendorId,
      orderCode,
      storeCode,
      vendorCode,
      isEasyLife,
    } = action?.data || {};
    const manOrderRequest = {
      orderId,
      extraTags: { manOrderFlag },
      extraMaps: { cesNew: '1' }, // 请求新版订详CES
      vendorId,
      orderCode,
      storeCode,
      vendorCode,
      isEasyLife,
    };
    const response = yield CarFetch.queryMainOrder(manOrderRequest).catch(
      () => {},
    );
    const { queryOrderContactPic, getBookingNotice } = response || {};
    if (queryOrderContactPic?.baseResponse?.isSuccess) {
      yield put(
        getCredentialsListCallBack({
          contacts: queryOrderContactPic.contacts,
        }),
      );
    }
    if (getBookingNotice?.baseResponse?.isSuccess) {
      yield put(
        setSelectorData({
          carRentalMustRead: getBookingNotice.mustReads || [],
          rentalMustReadTable: getBookingNotice.rentalMustReadTable || [],
          rentalMustReadPicture: getBookingNotice.rentalMustReadPicture || [],
        }),
      );
    }
  });
}

export function* queryOrderFulfillment() {
  yield takeEvery(QUERY_FULFILLMENT, function* logic(action: any) {
    const state = yield select();
    const { orderId } = action?.data || {};
    const isCanRenew = getFulfillIsCanRenew(state);
    const params = {
      orderId,
      blnCanRenew: isCanRenew,
    };
    const { response } = yield CarFetch.queryFulfillment(params).catch(
      () => {},
    );
    if (response?.baseResponse?.isSuccess) {
      yield put(setFulfillmentData(response));
    }
  });
}

export function* fulfillmentModifyConfirmLogic() {
  yield takeEvery(FULFILLMENT_MODIFY_CONFIRM, function* logic(action: any) {
    const { orderId, modifyId, confirmStatus } = action?.data || {};
    const param = {
      orderId,
      modifyId,
      confirmStatus,
      type: 1,
    };

    Loading.showMaskLoading();
    const res = yield CarFetch.modifyConfirm(param).catch(() => {});

    if (res?.baseResponse?.isSuccess) {
      if (confirmStatus === ModifyConfirmStatus.Agree) {
        xShowToast({ title: '已同意变更', duration: 3000 });
      } else if (confirmStatus === ModifyConfirmStatus.Disagree) {
        xShowToast({ title: '已拒绝变更', duration: 3000 });
      }
      yield put(fetchOrder2({}));
      yield put(queryFulfillment({ orderId }));
      // 变更成功后，删除地图缓存，getMapGuide 接口的 tip 字段有变更
      removeCache(getMapGuideCacheKey(orderId));
      // 重新请求 getMapGuide 的缓存，优化开屏速度
      yield put(createPreFetch({ orderId }));
    } else {
      xShowToast({ title: '变更失败', duration: 3000 });
    }
    Loading.hideMaskLoading();
  });
}

export default [
  apiQueryOrderStatus(),
  apiOrderQueryData(),
  apiFetchQueryCancelFee(),
  apiBuyInsOrder(),
  apiToCancelBook(),

  apiFetchQueryScoreAndSuggestions(),
  apiCreateComment(),
  apiModifyOrder(),
  getLimitContent(),
  createOrderAdditionalPay(),
  queryOrderPriceInfo(),
  apiFetchCreditNotificate(),
  apiUpdatePayment(),
  queryCustomerServiceUrl(),
  queryCtripContinuePay(),
  createContinuePayment(),
  getRentalMustRead(),
  queryAdditionPayment(),
  getSupplementListLogic(),
  creditRentAuth(),
  apiUpdateFreeDepositInfo(),
  queryOrderDetail(),
  isdInsurancePayment(),
  setRenewStatusByStorage(),
  saveRenewalOrderStatus(),

  updateFreeDepositInfoByContinuePay(),
  depositPayOnlineSaga(),
  queryCarAssistantV2Logic(),
  apiGueryOrderInsAndXProduct(),
  handleContinuePayFailDialogCancel(),
  apiQueryFirstScreenData(),
  apiFetchOrderDataByGraphql(),
  queryCancelInfo(),
  apiModifyCrossLocation(),
  queryDriverLicenseOrders(),
  queryExtraInsurance(),
  queryVehicleStatus(),
  selfServiceOperation(),
  checkSubmitReturnCar(),
  queryOrderNoticeFromDid(),
  queryOsdModifyOrderNote(),
  osdModifyOrderInit(),
  queryCountriesInfoLogic(),
  queryOrderInfoLogic(),
  queryOrderSuccessLogic(),
  queryOrderInfoFlag2Logic(),
  setDeductionDataLogic(),
  queryOrderWarningInfoLogic(),
  queryOrderFulfillment(),
  fulfillmentModifyConfirmLogic(),
];
