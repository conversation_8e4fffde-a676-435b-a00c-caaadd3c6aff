import { AppContext } from '../../Util/Index';
import {
  GET_ORDER_DATA,
  FETCH_ORDER_CALLBACK,
  FETCH_QUERYCANCELFEE,
  FETCH_QUERYCANCELFEE_CALLBACK,
  <PERSON><PERSON><PERSON>_TOCANCELBOOK,
  SET_PRICEMODALVISIBLE,
  QUERY_<PERSON>OREA<PERSON>SUGGESTIONS,
  QUERY_<PERSON><PERSON>EA<PERSON><PERSON>GGESTIONSCALLBACK,
  FETCH_CREATECOMMENT,
  <PERSON>ETCH_CREDIT_NOTIFICATION,
  SHOW_REVIEWSUCCESSMODAL,
  SHOW_REVIEWMODAL,
  SET_REFUNDMODALVISIBLE,
  SET_CHAN<PERSON>ORDERMODALVISIBLE,
  <PERSON><PERSON><PERSON>_<PERSON>ODIFYORDERCALLBACK,
  <PERSON><PERSON><PERSON>_CARASSISTANTCALLBACK,
  SET_ISDCHANGEORDERMODALVISIBLE,
  SET_FEEDEDUCTIONVISIBLE,
  SET_INSDETAILMODALVISIBLE,
  SET_CLMODALVISIBLE,
  SET_CDMODALVISIBLE,
  SET_EASYLIFETAGMODALVISIBLE,
  <PERSON>ETCH_EASYLIFETAG_CALLBACK,
  QUERY_PMSINFOCALLBACK,
  LIMITRULE_POP_VISIBLE,
  GET_LIMIT_CONTENT_SUCCESS,
  CREATE_ORDER_PAYMENT_PARAMS,
  GET_CREATE_PAYMENT_SUCCESS,
  SET_HISTORY_MODAL_VISIBLE,
  QUERY_SIMILAR_VEHICLE,
  QUERY_ORDER_PRICE_SUCCESS,
  FETCH_UPDATE_PAYMENT,
  QUERY_CUSTOMERSERVICEUEL,
  QUERY_CUSTOMERSERVICEUEL_SUCCESS,
  SET_MODIFYFLIGHTNOMODALVISIBLE,
  SET_DEPOSIT_DEATIL_MODAL_VISIBLE,
  SET_FETCH_DONE,
  SET_PHONE_MODAL_VISIBLE,
  SET_PERSON_PHONE_MODAL_VISIBLE,
  RESET,
  GET_SUPPLEMENT_LIST_CALLBACK,
  SET_VEHICLE_DAMAGE_ID,
  SET_SUPPLEMENT_LIST_NEW,
  SETMODALSVISIBLE,
  SET_SELECTOR_DATA,
  SET_ORDER_STATUS_SIGN,
  PAYCOUNTDOWNTIMEOUT,
  SET_TIPPOP_DATA,
  QUERY_CAR_ASSISTANT_V2,
  QUERY_CAR_ASSISTANT_V2_CALLBACK,
  SET_STORAGE_CARDS_TITLE,
  SET_PRICE_DETAIL_MODAL_VISIBLE,
  SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE,
  QUERY_ORDERINSUANDXPRODUCT_CALLBACK,
  SET_SCANNED_IMAGES,
  QUERY_ORDERCANCELINFO_CALLBACK,
  QUERYORDERCOUPON_CALLBACK,
  FETCH_CASHBACK_CALLBACK,
  SET_CONTINUE_PAY_FAIL_DIALOG_INFO,
  SET_FINAL_QUERY_IS_FINISH,
  SET_CONTINUE_PAY_INTERCEPTION_DATA,
  FETCH_QUERY_CANCEL_INFO,
  TO_CANCEL_CALLBACK,
  SET_TRAVEL_LIMIT_SELECTED_RESULT,
  SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE,
  QUERY_DRIVER_LICENSE_ORDERS,
  QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
  SET_IS_QUERY_ORDER_LOADING,
  QUERY_VEHICLE_STATUS_CALLBACK,
  SET_FULFILMENT_DATA,
  SET_DISTANCE,
  SET_CANCEL_ORDER_SUBMIT_ID,
  SET_ORDER_NOTICE_FROM_DID,
  SET_OSD_MODIFY_ORDER_NOTE,
  SET_LOCATIONDATEPOP_VISIBLE,
  SET_COUNTRYS_INFO,
  ORDER_CROSS_PARAMS,
} from './Types';
import { QueryOrderApiStatusType } from '../../Pages/OrderDetail/Types';

const { orderId } = AppContext.UrlQuery;

const defaultTipPopData = {
  visible: false,
  data: {},
};

const defaultCancelFee = {
  amount: 0,
  currencyCode: 0,
  canRefund: false,
  auditTime: '',
};

export const getInitalState = () => ({
  isLoading: true,
  isFail: false,
  isPolling: false,
  response: {},
  reqOrderParams: {
    orderId,
  }, // 请求参数
  rentalDays: 0,
  priceModalVisible: false,
  cancelModalVisible: false,
  orderChangeModalVisible: false,
  orderIsdChangeModalVisible: false,
  reviewSuccessModalVisible: false,
  reviewModalVisible: false,
  reqModifyOrderParam: {},
  npsResponseParam: {},
  npsResponse: {}, // nps评价
  hasNps: 1, // 默认不展示nps
  resCancelFee: defaultCancelFee,
  orderBaseInfo: {
    orderId: '',
    orderStatus: '',
  },
  cancelReason: '',
  feeDeductionVisible: false,
  isdVendorInsurance: {},
  packageInfos: [],
  productDetails: [],
  isdFeeInfo: {},
  ctripInsuranceInfos: [],
  labelsModalVisible: false,
  carDetailModalVisible: false,
  easyLifeTagModalVisible: false,
  phoneModalVisible: false,
  phoneModalType: '',
  personPhoneModalVisible: false,
  pmsInfo: null,
  limitPopVisible: false,
  limitCont: null,
  paymentParam: {},
  createPaymentResponse: null,
  replenishHistoryVisible: false,
  depositDetailModalVisible: false,
  similarVehicleInfo: null,
  cancelRuleInfo: {
    cancelReasons: [],
  },
  orderDetailPrice: null,
  rentCenter: {
    id: -1,
  },
  insurance: [],
  fetchDone: false,
  freeDeposit: null,
  pickupStore: {},
  returnStore: {},
  renewalOrders: [],
  violationList: [],
  osdDeductionList: [],
  removeDetail: false,
  violationDesc: {},
  vehicleDamageList: [],
  vehicleDamageId: null,
  isShowViolationDamageEntry: false,
  isShowSupplementRedIcon: false,
  isQueryOrderLoading: false,
  modalsVisible: {
    optimizeModalVisible: {
      visible: false,
    },
    createInsModalVisible: {
      visible: false,
    },
    insFailedModalVisible: {
      visible: false,
    },
    confirmModal: {
      visible: false,
    },
    pickUpMaterials: {
      visible: false,
    },
    renewTipModal: {
      visible: false,
    },
    refundDetailModal: {
      visible: false,
    },
    // 芝麻重复订单提示弹层
    sesameRepeatOrderModalauthModal: {
      visible: false,
    },
    ehiModifyOrderModal: {
      visible: false,
    },
    damageFeeDetailModalVisible: {
      visible: false,
    },
    // 一嗨双免
    ehiFreeDepositModal: {
      visible: false,
    },
    // 返现弹窗
    orderCashBackModal: {
      visible: false,
    },
    // 国内免押弹窗
    depositPaymentModal: {
      visible: false,
    },
    // 出境免押弹窗
    depositPaymentModalOSD: {
      visible: false,
    },
    // 点评开放提醒弹层
    reviewUnopenedModal: {
      visible: false,
    },
    // 优化增强弹层
    optimizationStrengthenModal: {
      visible: false,
    },
    // 提前还车记录
    advanceReturnModal: {
      visible: false,
    },
    // 提前还车费用明细
    advanceReturnFeeModal: {
      visible: false,
    },
    // 供应商资质弹层
    businessLicenseModal: {
      visible: false,
    },
    // ETC 介绍弹层
    etcIntroModal: {
      visible: false,
    },
    // ETC 使用帮助
    etcUseHelperModal: {
      visible: false,
    },
    // 距离校验不通过弹窗
    distanceInvalidateModal: {
      visible: false,
    },
    // 取消订单确认弹窗
    cancelOrderConfirmModal: {
      visible: false,
    },
    // 售后营业时间收费规则
    businessTimePolicyModal: {
      visible: false,
      type: '',
    },
    // 售后营业时间详情弹层
    businessTimeModal: {
      visible: false,
      type: '',
    },
    // 联系门店社交方式等弹层
    contactDoorStoreModal: {
      visible: false,
      data: null,
    },
    // 履约修改弹层
    fulfillmentModifyModal: {
      visible: false,
    },
  },
  queryOrderApiStatus: QueryOrderApiStatusType.unstart, // 0:表示未开始请求，1：请求成功了，2：请求失败了
  firstLoadSucTime: 0,
  continuePayInfo: {
    leftMinutes: 0,
    leftSeconds: 0,
  },
  orderRenewStatusVisible: false,
  orderStatusHashSign: '',
  payCountDownTimeOut: false,
  easyLifeTags: [],
  tipPopData: defaultTipPopData,
  queryCarAssistantV2Response: null,
  storageCardsTitle: [],
  priceDetailModalVisible: false, // 费用明细弹层是否展示
  orderDetailConfirmModalVisible: false, // 信息确认弹层是否展示
  orderWelfareRes: null, // 订详加油劵领劵入口接口响应
  newOrderInsAndXRes: {},
  scannedImages: [], // 新增车损图片已阅状态
  couponLists: [],
  cashBackInfo: {},
  // 继续支付失败提示弹窗相关信息
  continuePayFailDialogInfo: {
    visible: false,
    type: '', // 弹窗类型，confirm或alert
    content: '', // 提示信息
  },
  queryOrderAllDataSuccess: false, // 订详全量数据接口请求是否结束
  fullSearchNum: 0, // 订详全量数据接口请求次数
  freezeDepositExplain: [],
  insuranceAndXProductDesc: [],
  continuePayInterceptionData: null,
  isFetchCancelInfoLoading: false, // 刷新取消页信息
  isPenaltyChange: false, // 违约金发生变更
  penaltyChangeTip: null, // 违约金变更信息
  travelLimitSelectedResult: [], // 旅行限制选择结果
  flightDelayRulesModalVisible: false, // 航班延误政策弹层
  driverLicenseOrdersEnities: [], // 驾照翻译件订单列表
  driverLicensePageNo: 1, // 驾照翻译件订单列表页数
  nextPageOffset: null, // 翻下一页时传上一页接口返回的Offset
  isLastPage: false, // 是否是最后一页
  vehicleStatus: null,
  fulfillmentData: null, // 履约可视化
  distance: null, // 距离
  cancelOrderSubmitId: '',
  didNoticeData: { noticeList: [] },
  osdModifyOrderNote: null,
  locationDatePopVisible: false,
  modifyDriverInfoType: undefined,
  pickUpCountryInfo: {},
  isMergeOrderServer: false,
  orderFulfillmentModifyInfo: {},
  orderFulfillmentModifyInfoTip: '',
});

const initalState = getInitalState();

const setTipPopData = (state, { visible, data = {} as any } = {} as any) => {
  const { data: oldData = {}, visible: oldVisible } = state.tipPopData || {};
  const fixVisible = visible !== undefined ? visible : oldVisible;
  const { style = {} } = oldData;
  const { style: newStyle = {} } = data;
  return {
    ...state,
    tipPopData: {
      visible: fixVisible,
      data: {
        ...oldData,
        ...data,
        style: { ...style, ...newStyle },
      },
    },
  };
};

const setTravelLimitSelectedResult = (state, data = []) => ({
  ...state,
  travelLimitSelectedResult: data,
});

// eslint-disable-next-line @typescript-eslint/default-param-last
export default (state = initalState, action) => {
  let driverLicenseOrdersEnities;
  switch (action.type) {
    case FETCH_ORDER_CALLBACK:
      return {
        ...state,
        response: action.data.response,
        ...action.data?.response,
        reqOrderParams: action.data?.request || state.reqOrderParams,
        isLoading: false,
        isPolling: true,
        queryOrderAllDataSuccess: action.data?.queryOrderAllDataSuccess,
        queryOrderApiStatus:
          action.data?.queryOrderApiStatus ?? state.queryOrderApiStatus,
        isMergeOrderServer:
          state.isMergeOrderServer || action.data?.isMergeOrderServer,
      };
    case SET_ORDER_STATUS_SIGN:
      return {
        ...state,
        orderStatusHashSign: action.data?.orderStatusHashSign,
      };
    case FETCH_TOCANCELBOOK:
      return {
        ...state,
        cancelReason: action.data.reason,
      };
    case GET_ORDER_DATA:
    case FETCH_QUERYCANCELFEE:
    case QUERY_SCOREANDSUGGESTIONS:
    case FETCH_CREATECOMMENT:
    case QUERY_CUSTOMERSERVICEUEL:
    case FETCH_UPDATE_PAYMENT:
    case FETCH_CREDIT_NOTIFICATION:
    case QUERY_CAR_ASSISTANT_V2:
      return state;
    case SET_PRICEMODALVISIBLE:
      return {
        ...state,
        priceModalVisible: action.data.visible,
      };
    case SET_MODIFYFLIGHTNOMODALVISIBLE:
      return {
        ...state,
        modifyFlightNoModalVisible: action.data.visible,
        modifyDriverInfoType: action.data.type,
      };
    case SET_FEEDEDUCTIONVISIBLE:
      return {
        ...state,
        feeDeductionVisible: action.data.visible,
        feeDeductionData: action.data.feeDeductionData,
      };
    case SET_REFUNDMODALVISIBLE:
      return {
        ...state,
        refundModalVisible: action.data.visible,
      };
    case SET_CHANGEORDERMODALVISIBLE:
      return {
        ...state,
        orderChangeModalVisible: action.data.visible,
      };
    case SET_ISDCHANGEORDERMODALVISIBLE:
      return {
        ...state,
        orderIsdChangeModalVisible: action.data.visible,
      };

    case QUERY_SCOREANDSUGGESTIONSCALLBACK:
      return {
        ...state,
        hasNps: action.data.res.resultCode,
      };
    case SHOW_REVIEWSUCCESSMODAL:
      return {
        ...state,
        reviewSuccessModalVisible: action.data.visible,
      };
    case SHOW_REVIEWMODAL:
      return {
        ...state,
        reviewModalVisible: action.data.visible,
      };
    case FETCH_MODIFYORDERCALLBACK:
      return {
        ...state,
        reqModifyOrderParam: action.data,
      };
    case FETCH_QUERYCANCELFEE_CALLBACK:
      return {
        ...state,
        resCancelFee: action.data.resCancelFee,
      };
    case SETMODALSVISIBLE: // 设置弹层visible状态
      return {
        ...state,
        modalsVisible: {
          ...state.modalsVisible,
          ...action.data,
        },
      };
    case FETCH_CARASSISTANTCALLBACK:
    case SET_SELECTOR_DATA:
      return {
        ...state,
        ...action.data,
      };
    case SET_INSDETAILMODALVISIBLE:
      return {
        ...state,
        BbkInsuranceDetailProps: action.data,
      };
    case SET_CLMODALVISIBLE:
      return {
        ...state,
        labelsModalVisible: action.data,
      };
    case SET_CDMODALVISIBLE:
      return {
        ...state,
        carDetailModalVisible: action.data,
      };
    case SET_EASYLIFETAGMODALVISIBLE:
      return {
        ...state,
        easyLifeTagModalVisible: action.data,
      };
    case FETCH_EASYLIFETAG_CALLBACK:
      return {
        ...state,
        easyLifeTags: action.data,
      };
    case QUERY_PMSINFOCALLBACK:
      return {
        ...state,
        pmsInfo: action.data.pmsInfo,
      };
    case LIMITRULE_POP_VISIBLE:
      return {
        ...state,
        limitPopVisible: action.data.visible,
      };
    case GET_LIMIT_CONTENT_SUCCESS:
      return {
        ...state,
        limitCont: action.data.limitCont,
      };
    case CREATE_ORDER_PAYMENT_PARAMS:
      return {
        ...state,
        paymentParam: action.data.params,
      };
    case GET_CREATE_PAYMENT_SUCCESS:
      return {
        ...state,
        createPaymentResponse: action.data.response,
      };
    case SET_HISTORY_MODAL_VISIBLE:
      return {
        ...state,
        replenishHistoryVisible: action.data.visible,
      };
    case SET_DEPOSIT_DEATIL_MODAL_VISIBLE:
      return {
        ...state,
        depositDetailModalVisible: action.data.visible,
      };
    case SET_PHONE_MODAL_VISIBLE:
      return action.data.phoneModalType
        ? {
            ...state,
            phoneModalVisible: action.data.visible,
            phoneModalType: action.data.phoneModalType,
            phoneModalFromWhere: action.data.phoneModalFromWhere,
          }
        : {
            ...state,
            phoneModalVisible: action.data.visible,
            phoneModalFromWhere: action.data.phoneModalFromWhere,
          };
    case SET_PERSON_PHONE_MODAL_VISIBLE:
      return {
        ...state,
        personPhoneModalVisible: action.data.visible,
        phoneModalType: action.data.phoneModalType,
      };
    case QUERY_SIMILAR_VEHICLE:
      return {
        ...state,
        similarVehicleInfo: action.data.similarVehicleInfo,
      };
    case QUERY_ORDER_PRICE_SUCCESS:
      return {
        ...state,
        orderDetailPrice: action.data.orderDetailPrice,
      };
    case QUERY_CUSTOMERSERVICEUEL_SUCCESS:
      return {
        ...state,
        customerServiceUrl: action.data.customerServiceUrl,
      };
    case SET_FETCH_DONE:
      return {
        ...state,
        fetchDone: action.data.fetchDone,
      };

    case RESET:
      return getInitalState();
    case GET_SUPPLEMENT_LIST_CALLBACK:
      return {
        ...state,
        ...action.data,
      };
    case SET_VEHICLE_DAMAGE_ID:
      return {
        ...state,
        vehicleDamageId: action.data,
      };
    case SET_SUPPLEMENT_LIST_NEW:
      return {
        ...state,
        isShowSupplementRedIcon: action.data.visible,
      };
    case PAYCOUNTDOWNTIMEOUT:
      return {
        ...state,
        payCountDownTimeOut: action.data.timeOut,
      };
    case SET_TIPPOP_DATA:
      return setTipPopData(state, action.data);
    case QUERY_CAR_ASSISTANT_V2_CALLBACK:
      return {
        ...state,
        queryCarAssistantV2Response: action.data,
      };
    case SET_STORAGE_CARDS_TITLE:
      return {
        ...state,
        storageCardsTitle: action.data,
      };
    case SET_PRICE_DETAIL_MODAL_VISIBLE:
      return {
        ...state,
        priceDetailModalVisible: action.data,
      };
    case SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE:
      return {
        ...state,
        orderDetailConfirmModalVisible: action.visible,
      };
    case QUERY_ORDERINSUANDXPRODUCT_CALLBACK:
      return {
        ...state,
        newOrderInsAndXRes: action.data,
      };
    case SET_SCANNED_IMAGES:
      return {
        ...state,
        scannedImages: action.data,
      };
    case TO_CANCEL_CALLBACK:
      return {
        ...state,
        isPenaltyChange: action.data?.isPenaltyChange,
        penaltyChangeTip: action.data?.penaltyChangeTip,
        penaltyChangeCancelTip: action.data?.penaltyChangeCancelTip,
      };
    case FETCH_QUERY_CANCEL_INFO:
      return {
        ...state,
        isFetchCancelInfoLoading: true,
        isPenaltyChange: false, // 请求取消信息，重置违约金状态
        penaltyChangeTip: null,
        penaltyChangeCancelTip: null,
      };
    case QUERY_ORDERCANCELINFO_CALLBACK:
      return {
        ...state,
        isFetchCancelInfoLoading: false,
        orderCancelInfo: action.data,
      };
    case QUERYORDERCOUPON_CALLBACK:
      return {
        ...state,
        couponLists: action.data,
      };
    case FETCH_CASHBACK_CALLBACK:
      return {
        ...state,
        cashBackInfo: action.data,
      };
    case SET_CONTINUE_PAY_FAIL_DIALOG_INFO:
      return {
        ...state,
        continuePayFailDialogInfo: action.data,
      };
    case SET_FINAL_QUERY_IS_FINISH:
      return {
        ...state,
        finalQueryIsFinish: action.data,
        fullSearchNum: state.fullSearchNum + 1,
      };
    case SET_CONTINUE_PAY_INTERCEPTION_DATA:
      return {
        ...state,
        continuePayInterceptionData: action.data,
      };
    case SET_TRAVEL_LIMIT_SELECTED_RESULT:
      return setTravelLimitSelectedResult(state, action.data);
    case SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE:
      return {
        ...state,
        flightDelayRulesModalVisible: action.data,
      };
    case QUERY_DRIVER_LICENSE_ORDERS:
      return {
        ...state,
        driverLicenseOrdersEnities:
          action.data?.PageIndex === 1 ? [] : state.driverLicenseOrdersEnities,
        driverLicensePageNo:
          action.data?.PageIndex === 1 ? 1 : state.driverLicensePageNo,
        nextPageOffset:
          action.data?.PageIndex === 1 ? null : state.nextPageOffset,
        isLastPage: action.data?.PageIndex === 1 ? false : state.isLastPage,
      };
    case QUERY_DRIVER_LICENSE_ORDERS_CALLBACK:
      // 非首页数据追加展示
      if (state.driverLicensePageNo > 1) {
        if (action.data?.orderEnities?.length > 0) {
          driverLicenseOrdersEnities = [
            ...state.driverLicenseOrdersEnities,
            ...action.data?.orderEnities,
          ];
        } else {
          driverLicenseOrdersEnities = state.driverLicenseOrdersEnities;
        }
      } else {
        driverLicenseOrdersEnities = action.data?.orderEnities;
      }
      return {
        ...state,
        driverLicenseOrdersEnities,
        driverLicensePageNo:
          state.driverLicensePageNo +
          (action.data?.orderEnities?.length > 0 ? 1 : 0),
        nextPageOffset: action.data?.nextPageOffset,
        isLastPage: action.data?.isLastPage,
      };
    case SET_IS_QUERY_ORDER_LOADING:
      return {
        ...state,
        isQueryOrderLoading: action.data,
      };
    case QUERY_VEHICLE_STATUS_CALLBACK:
      return {
        ...state,
        vehicleStatus: action.data?.vehicleStatus,
      };
    case SET_FULFILMENT_DATA: {
      return {
        ...state,
        fulfillmentData: action.data,
      };
    }
    case SET_DISTANCE: {
      return {
        ...state,
        distance: action.data,
      };
    }
    case SET_CANCEL_ORDER_SUBMIT_ID:
      return {
        ...state,
        cancelOrderSubmitId: action?.data,
      };
    case SET_ORDER_NOTICE_FROM_DID:
      return {
        ...state,
        didNoticeData: action?.data,
      };
    case SET_LOCATIONDATEPOP_VISIBLE:
      return {
        ...state,
        locationDatePopVisible: action.data.visible,
      };
    case SET_OSD_MODIFY_ORDER_NOTE:
      return {
        ...state,
        osdModifyOrderNote: action.data.osdModifyOrderNote,
      };
    case SET_COUNTRYS_INFO:
      return {
        ...state,
        pickUpCountryInfo: action.data,
      };
    case ORDER_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
};
