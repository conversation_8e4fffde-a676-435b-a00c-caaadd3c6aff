import { get as lodashGet } from 'lodash-es';
import Loading from '@c2x/apis/Loading';
import { xShowToast } from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { put, select, takeEvery, delay } from 'redux-saga/effects';
import {
  fetchApiIdCardList,
  selectDriver,
  updateCurCertificates,
} from '../DriverList/Actions';
import { setDateInfo, setLocationInfo } from '../LocationAndDate/Actions';
import {
  queryOrderDetail,
  setOrderModalsVisible,
} from '../OrderDetail/Actions';
import {
  resetConfirmData,
  setModifyDiscountInfo,
  changeCoupon,
} from '../ModifyOrderConfirm/Actions';
import {
  getCustomerInfo,
  getNewHomeParamFromOrder,
  getVehicleInfo,
  getModifyInfoDto,
  getVendorInfo,
  getModifyCancelRule,
  getOrderId,
  getPickupStore,
  getOrderPriceInfoFee,
  getOrderStatusCtrip,
  getIsCancelOrder,
  getOrderStatus,
} from '../OrderDetail/Selectors';
import { getModifyOrderRequestInfo, getRebookPassenger } from './Selector';
import {
  SELECT_MODIFY_DRIVER,
  SET_INITIAL_DATA,
  SET_INITIAL_DATA_CALLBACK,
  CREATE,
  REBOOK,
  MODIFY_SUCCESS,
  MODIFY_CANCEL,
} from './Types';
import {
  setLoading,
  setIsFail,
  setMaskLoading,
  setModifyOrderWarnModalVisible,
  setModifyOrderResponse,
  setModifyOrderRequest,
  setPassengerLoaded,
} from './Actions';
import {
  ModifyStatusType,
  ModifyTipInfoCodeType,
  ModifyType,
} from '../../Types/Dto/OrderDetailRespaonseType';
import { ModifyOrderTexts as Texts } from '../../Constants/TextIndex';
import { ResultTypeType } from '../../Types/Dto/ModifyOrderResponseType';
import { ModifyOrderRequestType } from '../../Types/Dto/ModifyOrderRequestType';
import { TitleHightlightType } from '../../ComponentBusiness/OrderConfirmModal/Index';
import { changeFormData } from '../Booking/Actions';
import { ModifiedPopStorage as ModifiedStorage } from './ModifiedStorage';
import {
  getIsDifferentLocation,
  getRentalDate,
  getRentalLocation,
} from '../LocationAndDate/Selectors';
import { AppContext, Utils, CarLog, CarFetch } from '../../Util/Index';
import { openURL } from '../../Helpers/WebHelper';
import { Platform, LogKeyDev, ApiResCode } from '../../Constants/Index';
import { SesameGuiderType } from '../../ComponentBusiness/SesameCard';
import { ActionType } from '../../Types/ActionType';
import { LogErrorInfoType } from '../../Types/CarLogTypes';
import { OrderStatusCtrip } from '../../Constants/OrderDetail';

export function* create() {
  yield takeEvery(CREATE, function* logic(action: ActionType) {
    const state = yield select();
    let successInfo: LogErrorInfoType = {
      eventResult: true,
      expCode: '',
      expMsg: '',
      expPoint: 'create',
    };
    const modifyCancelRule = getModifyCancelRule(state);
    const params: ModifyOrderRequestType = {
      orderId: action.data,
      info: getModifyOrderRequestInfo(state),
    };
    yield put(setModifyOrderRequest(params));
    yield put(setMaskLoading(true));

    try {
      const res = yield CarFetch.modifyOrderV2(params);
      yield put(setMaskLoading(false));
      const {
        resultType,
        resultInfo,
        discountInfo,
        baseResponse: { isSuccess, returnMsg },
      } = res;
      if (!isSuccess) {
        xShowToast({ title: returnMsg, duration: 3000 });
        successInfo = {
          eventResult: false,
          expCode: res?.baseResponse?.code,
          expMsg: res?.baseResponse?.returnMsg,
          expPoint: 'serverError',
          request: params,
        };
        return;
      }
      if (
        [ResultTypeType.reorder, ResultTypeType.soldout].includes(resultType)
      ) {
        const content = {
          header: resultInfo?.title,
          title: resultInfo?.msg,
          isWarnTip: modifyCancelRule?.color !== 1,
          desc: modifyCancelRule?.contentAlert,
        };
        yield put(setModifyOrderWarnModalVisible(true, content));
        return;
      }
      yield put(resetConfirmData());
      yield put(setModifyOrderResponse(res));
      if (discountInfo) {
        yield put(
          setModifyDiscountInfo({
            discountInfo: res?.discountInfo,
            enablePressCoupon: true,
          }),
        );
        if (discountInfo?.couponList?.selectedCoupon?.code) {
          yield put(
            changeCoupon([discountInfo.couponList.selectedCoupon.code], true),
          );
        }
      }
    } catch (error) {
      xShowToast({ title: '网络失败', duration: 3000 });
      yield put(setMaskLoading(false));
      successInfo = {
        eventResult: false,
        expCode: ApiResCode.TraceCode.E1001,
        expMsg: error.message,
        expPoint: 'catchError',
      };
    } finally {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_trace_order_detail_modify,
        info: successInfo,
      });
    }
  });
}

export function* cancel() {
  yield takeEvery(MODIFY_CANCEL, function* logic() {
    const state = yield select();
    const orderId = getOrderId(state);
    Loading.showMaskLoading();
    try {
      const res = yield CarFetch.cancelModify(orderId);
      if (res?.errorMsg) {
        xShowToast({ title: res?.errorMsg, duration: 3000 });
      }
      yield put(queryOrderDetail({ orderId }));
    } catch {
      xShowToast({ title: '网络错误', duration: 3000 });
    }
    Loading.hideMaskLoading();
  });
}

export function* selectModifyDriver() {
  yield takeEvery(SELECT_MODIFY_DRIVER, function* logic() {
    const state = yield select();
    const customerInfo = getCustomerInfo(state);
    const vendorInfo = getVendorInfo(state);
    const storeInfo = getPickupStore(state);
    const currentPassenger = {
      passengerId: 'temp',
      fullName: customerInfo.name,
      age: customerInfo.age,
      countryCode: customerInfo.areaCode,
      mobile: customerInfo.decryptTelphone,
      email: customerInfo.email,
      certificateList: [
        {
          certificateNo: customerInfo.decryptIDCardNo,
          certificateType: String(customerInfo.iDCardType),
        },
      ],
    };
    yield put(fetchApiIdCardList({ vendorId: vendorInfo?.vendorID }));
    yield put(updateCurCertificates({ temp: String(customerInfo.iDCardType) }));
    yield put(selectDriver(currentPassenger));
    const res = yield CarFetch.queryDriverList({
      vendorCode: vendorInfo?.vendorID || '',
      storeId: storeInfo?.storeID || '',
    }).catch(() => {});
    const passengerList = res?.passengerList || [];
    const passengeNow = passengerList.find(
      v =>
        v.fullName === currentPassenger.fullName &&
        v.certificateList.find(
          s =>
            s.certificateNo ===
            currentPassenger.certificateList[0].certificateNo,
        ),
    );
    if (passengeNow) {
      passengeNow.mobile = customerInfo.decryptTelphone;
      yield put(selectDriver(passengeNow));
      yield delay(100);
      yield put(
        changeFormData([
          { type: 'mobilePhone', value: customerInfo.decryptTelphone },
        ]),
      );
      yield put(setPassengerLoaded(true));
      return;
    }

    yield put(setPassengerLoaded(true));
  });
}

export function* setInitalData() {
  yield takeEvery(SET_INITIAL_DATA, function* logic(action: ActionType) {
    const { data } = action;
    const state = yield select();
    const { orderId } = data;
    const isOrderDataExsit = !!getVehicleInfo(state);
    if (!isOrderDataExsit) {
      yield put(setLoading(true));
      yield put(
        queryOrderDetail({
          orderId,
          // callback 逻辑迁移至 SET_INITIAL_DATA_CALLBACK
          notifyModifyOrderCallback: true,
        }),
      );
      return;
    }
    const commonData = getNewHomeParamFromOrder(state);
    yield put(setLocationInfo(commonData?.rentalLocation));
    yield put(setDateInfo(commonData?.rentalDate));
  });
}

export function* setInitialDataCallback() {
  yield takeEvery(
    SET_INITIAL_DATA_CALLBACK,
    function* logic(action: ActionType) {
      const { isSuccess } = action.data;
      yield put(setLoading(false));
      yield put(setIsFail(!isSuccess));
    },
  );
}

export const getRebookUrl = ({
  rentalLocation,
  rentalDate,
  orderId,
  vendorInfo,
  vehicleInfo,
  pickupStore,
  landingToPage = 'rebookhome',
  passenger,
  priceInfo,
  cancelCode,
  cancelOrder,
  orderStatus,
}) => {
  const dateFormat = 'YYYYMMDDHHmmss';
  const locationAndDateParams = {
    rentalDate: {
      pickUp: {
        dateTime: dayjs(rentalDate?.pickUp?.dateTime).format(dateFormat),
      },
      dropOff: {
        dateTime: dayjs(rentalDate?.dropOff?.dateTime).format(dateFormat),
      },
    },
    rentalLocation: {
      pickUp: rentalLocation?.pickUp,
      dropOff: rentalLocation?.dropOff,
      isShowDropOff: rentalLocation?.isShowDropOff,
      isNotShowDropOff: rentalLocation?.isShowDropOff,
    },
    isShowDropOff: rentalLocation?.isShowDropOff,
  };

  const rebookParams = {
    ctripOrderId: orderId,
    vendorId: vendorInfo.vendorID,
    vehicleId: vehicleInfo.vendorVehicleID,
    ctripVehicleCode: vehicleInfo.ctripVehicleID,
    storeCode: pickupStore.storeID,
    cancelOrder,
    cancelCode,
    orderStatus,
  };
  const originalActivityIds = [];
  const originalActivityNames = [];
  const { activityInfo } = priceInfo || {};
  if (activityInfo?.items) {
    activityInfo.items?.forEach(item => {
      originalActivityIds.push(item?.activityId || '');
      originalActivityNames.push(item?.subTitle || '');
    });
  } else if (activityInfo?.activityId) {
    originalActivityIds.push(activityInfo?.activityId || '');
    originalActivityNames.push(activityInfo?.subTitle || '');
  }
  const opParams = {
    encryptUid: AppContext?.encryptUid,
    fromType: AppContext?.fromType,
    originOrderId: orderId,
    channelId: AppContext?.channelId,
    eid: AppContext?.eid,
    // TODO: test param
    // @ts-ignore
    env: AppContext?.Env,
    modifyVendorOrderCode: vendorInfo.vendorConfirmCode,
    originVendorId: vendorInfo.vendorID,
    originalCouponCode: lodashGet(priceInfo, 'couponInfos[0].code', ''),
    originalActivityIds,
    originalActivityNames,
  };

  const paramsQuery = Utils.changeObject2QueryString({
    ...rebookParams,
    ...opParams,
  });

  const basePlatform = Platform.CAR_CROSS_URL.REBOOK.ISD;
  const baseUrl = `${basePlatform}st=client&fromurl=common&apptype=ISD_C_APP`;
  return (
    `${baseUrl}&data=${encodeURIComponent(
      JSON.stringify(locationAndDateParams),
    )}` +
    `&landingto=${landingToPage}&${paramsQuery}` +
    `&passenger=${encodeURIComponent(JSON.stringify(passenger))}`
  );
};

export function* rebook() {
  yield takeEvery(REBOOK, function* logic(action: ActionType) {
    const state = yield select();
    const isInfoFromOrder = action.data;
    const orderId = getOrderId(state);
    const orderStatus = getOrderStatus(state);
    const vendorInfo = getVendorInfo(state);
    const vehicleInfo = getVehicleInfo(state);
    const pickupStore = getPickupStore(state);
    // 从redux拿数据, 跳转列表页
    const isShowDropOff = getIsDifferentLocation(state);
    let landingToPage = 'list';
    let rentalLocation: any = { ...getRentalLocation(state), isShowDropOff };
    let rentalDate: any = getRentalDate(state);
    if (isInfoFromOrder) {
      // 从订单拿取还车数据, 跳转搜索页
      const {
        rentalLocation: rentalLocationOrder,
        rentalDate: rentalDateOrder,
      } = getNewHomeParamFromOrder(state);
      rentalLocation = rentalLocationOrder;
      rentalDate = {
        pickUp: {
          dateTime: rentalDateOrder?.pickup,
        },
        dropOff: {
          dateTime: rentalDateOrder?.dropoff,
        },
      };
      landingToPage = 'rebookhome';
    }
    const passenger = getRebookPassenger(state);
    const priceInfo = getOrderPriceInfoFee(state);
    const cancelOrder = getIsCancelOrder(state);
    const url = getRebookUrl({
      rentalLocation,
      rentalDate,
      orderId,
      vendorInfo,
      vehicleInfo,
      pickupStore,
      landingToPage,
      passenger,
      priceInfo,
      cancelCode: isInfoFromOrder,
      cancelOrder,
      orderStatus,
    });
    openURL(url);
  });
}

export function* success() {
  yield takeEvery(MODIFY_SUCCESS, function* logic() {
    const state = yield select();
    const modifyInfoDto = getModifyInfoDto(state);
    const tipInfo = modifyInfoDto?.tipInfo;
    const modifyStatus = modifyInfoDto?.modifyStatus;
    const modifyType = modifyInfoDto?.modifyType;
    const orderId = getOrderId(state);
    const isShowed = yield ModifiedStorage.loadOne(orderId);

    // 只有原单修改的订单需要弹窗
    if (
      isShowed ||
      !tipInfo ||
      modifyType === ModifyType.reBook ||
      [ModifyStatusType.processing, ModifyStatusType.canceled].includes(
        modifyStatus,
      )
    ) {
      return;
    }
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_trace_modify_order_pop,
      info: {
        modifyInfoDto,
        orderId,
      },
    });
    if (modifyInfoDto?.goDeposit) {
      const tip = tipInfo?.find(
        v => v.code === ModifyTipInfoCodeType.ehiDeposit,
      );

      yield put(
        setOrderModalsVisible({
          ehiModifyOrderModal: {
            visible: true,
            data: {
              get btnText() {
                return '去信用免押';
              },
              tips: [],
              type: SesameGuiderType.VERIFY_SUCCESS_ISD,
              verifyRes: {
                boldTitle: tip?.title,
                subTitle: tip?.content,
              },
            },
          },
        }),
      );
      ModifiedStorage.save(orderId);
      return;
    }

    const toast = tipInfo?.find(v => v.code === ModifyTipInfoCodeType.success);
    if (toast?.content) {
      xShowToast({ title: toast.content, duration: 3000 });
      ModifiedStorage.save(orderId);
      return;
    }

    if ([ModifyStatusType.success].includes(modifyStatus)) {
      return;
    }

    if (modifyStatus === ModifyStatusType.payFail) {
      const contentText = tipInfo?.find(
        v => v.code === ModifyTipInfoCodeType.payFail,
      );
      yield put(
        setOrderModalsVisible({
          confirmModal: {
            visible: true,
            isModifyOrder: true,
            data: {
              title: Texts.modifyFailSimple,
              contentText: contentText?.content,
              titleHeightlightStyle: TitleHightlightType.Warning,
              // 下面的 done 掉会导致 onPress 的 dispatch 失效
              // 在 OrderDetail 页面 getModifyOrderProps 传值
              // btns: [{
              //   name: Texts.gotit,
              //   isPrimary: true,
              //   onPress: () => {
              //     dispatch(setOrderModalsVisible({ confirmModal: { visible: false } }));
              //     done();
              //   },
              // }],
            },
          },
        }),
      );
      ModifiedStorage.save(orderId);
      return;
    }
    const failCancelRule = tipInfo?.find(
      v => v.code === ModifyTipInfoCodeType.failCancelRule,
    );
    const failText = tipInfo?.find(
      v => v.code === ModifyTipInfoCodeType.failText,
    );
    const isOrderCanceled =
      getOrderStatusCtrip(state) === OrderStatusCtrip.CANCELLED;
    const content = {
      header: Texts.modifyFail,
      title: failText?.content,
      subTitle: isOrderCanceled ? '' : Texts.modifyFailTip,
      isWarnHeader: true,
      isWarnTip: failCancelRule?.cancelTipColor !== 1,
      tip: failCancelRule?.content,
    };
    yield put(setModifyOrderWarnModalVisible(true, content));
    ModifiedStorage.save(orderId);
  });
}

export default [
  selectModifyDriver(),
  setInitalData(),
  create(),
  rebook(),
  success(),
  cancel(),
  setInitialDataCallback(),
];
