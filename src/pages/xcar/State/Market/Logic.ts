import { get as lodashGet } from 'lodash-es';

import { takeLatest, put, call } from 'redux-saga/effects';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import MarketInfo from '../../Util/MarketInfo';
import { LOAD, LOADING_TYPE, FROM_URL } from './Types';
import { getDefaultPageName, getPageName } from './Helpers';
import { setDateInfo, setLocationInfo } from '../LocationAndDate/Actions';
import { setAge, setAgeAdultAndChildNum } from '../DriverAgeAndNumber/Actions';
import { setTempLocation, fetchAreaList } from '../Area/Actions';
import { setRebookParams, setRebookParamsOsd } from '../ModifyOrder/Actions';
import { loadMarketCompleted } from './Actions';
import { AppContext, Utils, CarFetch, Channel, CarLog } from '../../Util/Index';
import * as TimeZoneHelper from '../../Util/TimeZoneHelper';
import { ListEnum, Platform, LogKeyDev } from '../../Constants/Index';
import { Enums } from '../../ComponentBusiness/Common';
import { ActionType } from '../../Types/ActionType';

enum MarketInfoPlatform {
  App = 'app',
}

const getMarketInfoParam = (param = {}) => ({
  query: AppContext.UrlQuery,
  appid: Platform.APP_ID.CTRIP,
  platform: MarketInfoPlatform.App,
  path: AppContext?.PageInstance?.props?.app?.url,
  pageID: Utils.getCurPageId(),
  ubt: Utils.getUBT(),
  ...param,
});

// http://conf.ctripcorp.com/pages/viewpage.action?pageId=192826013
// http://*************:5389/index.ios.bundle?CRNModuleName=rn_xtaro_car_main&CRNType=1&apptype=ISD_C_APP&landingto=list&vehgroupid=G02&pcid=347&initialPage=Market&st=ser&fromurl=comm

// 2020-05-11 无忧租落地页跳转链接
// 支持定位到无忧租车型组和筛选中指定供应商
/* eslint-disable max-len */
// /rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&CRNType=1&initialPage=Market&landingto=list&st=client&fromurl=easylife&filters=%5B%22Seats_SeatsBetween45%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%A3%9E%E5%BA%A6%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%94%8B%E8%8C%83%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%93%A5%E7%91%9E%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%9B%85%E9%98%81%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E8%89%BE%E5%8A%9B%E7%BB%85%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E6%80%9D%E5%9F%9F%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E7%BC%A4%E6%99%BA%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%9B%85%E9%98%81(%E6%B7%B7%E5%8A%A8)%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%A5%A5%E5%BE%B7%E8%B5%9B%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E4%BA%AB%E5%9F%9F%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%87%8C%E6%B4%BE%22%5D&vehgroupid=hot&data=%7B%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220200512100000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220200514100000%22%7D%7D%2C%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22area%22%3A%7B%22lng%22%3A110.343315%2C%22lat%22%3A19.984078%2C%22id%22%3A%22%22%2C%22name%22%3A%22%E6%B5%B7%E5%8F%A3%E4%B8%9C%E7%AB%99%22%2C%22type%22%3A%222%22%7D%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22cname%22%3A%22%E6%B5%B7%E5%8F%A3%22%2C%22cid%22%3A%2242%22%7D%2C%22dropOff%22%3A%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22area%22%3A%7B%22lng%22%3A110.343315%2C%22lat%22%3A19.984078%2C%22id%22%3A%22%22%2C%22name%22%3A%22%E6%B5%B7%E5%8F%A3%E4%B8%9C%E7%AB%99%22%2C%22type%22%3A%222%22%7D%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22cname%22%3A%22%E6%B5%B7%E5%8F%A3%22%2C%22cid%22%3A%2242%22%7D%2C%22isShowDropOff%22%3Afalse%7D%7D
/* eslint-disable max-len */
// /rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&CRNType=1&initialPage=Market&landingto=list&st=client&fromurl=easylife&vehgroupid=hot&filters=encodeURIComponent(JSON.stringify(data))&data=encodeURIComponent(JSON.stringify(data))
// http://*************:5389/index.ios.bundle?CRNModuleName=rn_xtaro_car_main&CRNType=1&initialPage=Market&landingto=list&st=client&fromurl=easylife&filters=%5B%22Seats_SeatsBetween45%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%A3%9E%E5%BA%A6%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%94%8B%E8%8C%83%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%93%A5%E7%91%9E%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%9B%85%E9%98%81%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E8%89%BE%E5%8A%9B%E7%BB%85%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E6%80%9D%E5%9F%9F%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E7%BC%A4%E6%99%BA%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E9%9B%85%E9%98%81(%E6%B7%B7%E5%8A%A8)%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%A5%A5%E5%BE%B7%E8%B5%9B%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E4%BA%AB%E5%9F%9F%22%2C%22Brand_%E6%9C%AC%E7%94%B0_%E6%9C%AC%E7%94%B0%E5%87%8C%E6%B4%BE%22%5D&vehgroupid=hot&data=%7B%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220200612100000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220200614100000%22%7D%7D%2C%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22area%22%3A%7B%22lng%22%3A110.343315%2C%22lat%22%3A19.984078%2C%22id%22%3A%22%22%2C%22name%22%3A%22%E6%B5%B7%E5%8F%A3%E4%B8%9C%E7%AB%99%22%2C%22type%22%3A%222%22%7D%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22cname%22%3A%22%E6%B5%B7%E5%8F%A3%22%2C%22cid%22%3A%2242%22%7D%2C%22dropOff%22%3A%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22area%22%3A%7B%22lng%22%3A110.343315%2C%22lat%22%3A19.984078%2C%22id%22%3A%22%22%2C%22name%22%3A%22%E6%B5%B7%E5%8F%A3%E4%B8%9C%E7%AB%99%22%2C%22type%22%3A%222%22%7D%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22cname%22%3A%22%E6%B5%B7%E5%8F%A3%22%2C%22cid%22%3A%2242%22%7D%2C%22isShowDropOff%22%3Afalse%7D%7D
// encodeURIComponent(JSON.stringify(data));
//
// 列表页测试链接
/* eslint-disable max-len */
// http://127.0.0.1:5388/index.ios.bundle?CRNModuleName=rn_xtaro_car_main&initialPage=Market&CRNType=1&st=client&landingto=list&initialpage=Market&fromurl=comm&landingto=list&data=%7B%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22cid%22%3A313%2C%22cname%22%3A%22SFO%22%2C%22country%22%3A%22American%22%2C%22realcountry%22%3A%22%22%2C%22area%22%3A%7B%22id%22%3A%22%22%2C%22name%22%3A%22%22%2C%22lat%22%3A0%2C%22lng%22%3A0%2C%22type%22%3A%22%22%7D%7D%2C%22dropOff%22%3A%7B%22cid%22%3A313%2C%22cname%22%3A%22SFO%22%2C%22country%22%3A%22American%22%2C%22realcountry%22%3A%22%22%2C%22area%22%3A%7B%22id%22%3A%22%22%2C%22name%22%3A%22%22%2C%22lat%22%3A0%2C%22lng%22%3A0%2C%22type%22%3A%22%22%7D%7D%2C%22isOneWay%22%3Atrue%7D%2C%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A111%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A222%7D%7D%7D

// 跳转浏览历史
// http://10.32.116.114:5389/index.ios.bundle?CRNModuleName=rn_xtaro_car_main&CRNType=1&initialPage=Market&landingto=list&st=client&fromurl=history&apptype=ISD_C_APP&channelid=14514&data=%7B%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22cid%22%3A30%2C%22cname%22%3A%22%E6%B7%B1%E5%9C%B3%22%2C%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22isDomestic%22%3Atrue%2C%22area%22%3A%7B%22id%22%3A%222309%22%2C%22name%22%3A%22%E6%B7%B1%E5%9C%B3%E7%AB%99%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2222.531626%22%2C%22lng%22%3A%22114.117197%22%2C%22typename%22%3A%22%E7%81%AB%E8%BD%A6%22%7D%2C%22sortIndex%22%3A46%7D%2C%22dropOff%22%3A%7B%22cid%22%3A30%2C%22cname%22%3A%22%E6%B7%B1%E5%9C%B3%22%2C%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22isDomestic%22%3Atrue%2C%22area%22%3A%7B%22id%22%3A%222309%22%2C%22name%22%3A%22%E6%B7%B1%E5%9C%B3%E7%AB%99%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2222.531626%22%2C%22lng%22%3A%22114.117197%22%2C%22typename%22%3A%22%E7%81%AB%E8%BD%A6%22%7D%2C%22sortIndex%22%3A46%7D%2C%22isShowDropOff%22%3Afalse%7D%2C%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220200627100000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220200629100000%22%7D%7D%7D

const marketInfo = MarketInfo.getInstance(MarketInfoPlatform.App);

let params = {
  rentalDate: null,
  rentalLocation: null,
  age: null,
  adultSelectNum: null,
  childSelectNum: null,
  isNoPoiData: false,
  isNoRPoiData: false,
  isTimePassed: false,
  timeZone: null,
};

const listFilterLoader = (
  /* eslint-disable @typescript-eslint/default-param-last */
  vehgroupid = '',
  filters,
  vehicleid,
  currentDailyPrice = '',
  uniqSign = '',
  license = '',
  skuId = '',
  vehicleDayPrice = '',
) => {
  let filterArrs = filters;
  if (filterArrs) {
    try {
      filterArrs = JSON.parse(decodeURIComponent(filterArrs));
    } catch (err) {
      filterArrs = [];
    }
  }

  if (vehgroupid === ListEnum.NavGroupCode.easyLife) {
    filterArrs = (filterArrs || []).concat([
      Enums.SpecialFilterCode.EASYLIFE_NEW,
    ]);
    // eslint-disable-next-line no-param-reassign
    vehgroupid = '';
  }

  AppContext.setRouterListLoader({
    groupCode: vehgroupid,
    filters: filterArrs,
    vehicleId: vehicleid,
    license,
    // 时间过期
    // 不传递uniqSign，防止服务端读错缓存
    // 不传递currentDailyPrice, 防止多重toast引起用户误解
    ...(!params.isTimePassed && {
      currentDailyPrice,
      uniqSign,
    }),
    // 营销跳转变价埋点新增字段
    skuId,
    vehicleDayPrice,
  });
};

const { HMTCid } = Utils;

// 是否传递了城市相关的信息，具体规则详见http://conf.ctripcorp.com/pages/viewpage.action?pageId=347788219
const isServerCrossRentalLocation = fakeUrl => {
  const fakeQuery = Utils.composeQsParse(fakeUrl);
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  const { pcid, plid, orderid, p_province_id, p_country_id } = fakeQuery;
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  return !!pcid || !!plid || !!orderid || !!p_province_id || !!p_country_id;
};

const handleServerParams = async fakeUrl => {
  const fixUrl = fakeUrl || decodeURIComponent(AppContext.Url);
  const isCrossRentalLocation = isServerCrossRentalLocation(fixUrl);
  const response = await CarFetch.getRouterAdapter({
    landingUrl: fixUrl,
  }).catch(() => {});
  if (
    response.baseResponse &&
    response.baseResponse.code === 200 &&
    response.baseResponse.isSuccess &&
    response.params
  ) {
    const serParams = JSON.parse(response.params);
    const { NoPoiTypeCode } = Enums;
    params.rentalDate = serParams.rentalDate;
    params.rentalLocation = serParams.rentalLocation;
    params.rentalLocation.isShowDropOff = serParams.rentalLocation.isOneWay;
    params.isNoPoiData =
      response.toast === NoPoiTypeCode.PickUpPoint ||
      response.toast === NoPoiTypeCode.ReturnPoint ||
      false;
    params.isTimePassed = response.toast === NoPoiTypeCode.TimePassed;
    if (response.toast === NoPoiTypeCode.ReturnPoint) {
      params.rentalLocation.isShowDropOff = true;
      params.isNoRPoiData = true;
    }
    // 没有传递城市相关参数，不使用服务端返回的默认城市区域信息
    if (!isCrossRentalLocation) {
      params.rentalLocation = null;
    }
  }
};

function* handleClientParams(gotoPageName) {
  const {
    data = '{}',
    listParameter,
    fromurl,
    backPageName,
    location,
    apptype,
    pageChannel,
  } = AppContext.UrlQuery;

  params = listParameter || Utils.getParams(data);
  if (Utils.isCtripOsd()) {
    TimeZoneHelper.Instance.setTimeZone(
      {
        cityId: params?.rentalLocation?.pickUp?.cid,
        timeZone: params?.timeZone,
        ticks: +new Date(),
        type: TimeZoneHelper.TimeZoneType.Url,
      },
      true,
    );
  }
  if (
    fromurl === FROM_URL.EASYLIFE ||
    fromurl === FROM_URL.COMMON ||
    fromurl === FROM_URL.HISTORY
  ) {
    params = Utils.getParams(data);
  }

  // 2021/11/18 首页融合项目，跳转列表页通过openURL
  // data 参数影响了 Image 页面了的逻辑，进行删除处理
  if (pageChannel === Platform.PROJECT_SYMBOL.CTRIP_HOME_PAGE) {
    AppContext.setUrlQuery(AppContext.UrlQuery, 'data', null);
  }

  if (gotoPageName === 'City' || gotoPageName === 'Area') {
    const parsedLocation = Utils.getParams(location);
    const isDomestic = Utils.isCtripIsdByType(apptype);
    parsedLocation.isDomestic = isDomestic;
    if (!isDomestic && HMTCid.indexOf(parsedLocation.cid) < 0) {
      parsedLocation.country = '';
    }
    params.rentalLocation = {};
    params.rentalLocation.pickUp = parsedLocation;
    AppContext.setCallCityAreaPageInfo(backPageName);
  } else {
    const isDomestic = Utils.isCtripIsdByType(apptype);
    if (
      lodashGet(params, 'rentalLocation.pickUp') &&
      lodashGet(params, 'rentalLocation.dropOff')
    ) {
      params.rentalLocation.pickUp.isDomestic = isDomestic;
      params.rentalLocation.dropOff.isDomestic = isDomestic;
    }
  }

  if (gotoPageName === 'Area') {
    const tempLocation = params.rentalLocation.pickUp;
    yield put(setTempLocation({ tempLocation }));
    yield put(fetchAreaList({}));
  }

  if (
    gotoPageName === Channel.getPageId().RebookHome.EN ||
    gotoPageName === Channel.getPageId().List.EN
  ) {
    const {
      ctripOrderId,
      vendorId,
      vehicleId,
      ctripVehicleCode,
      storeCode,
      osdOriginOrderId,
      from,
    } = AppContext.UrlQuery;
    AppContext.setIsModifyOrderRebook(!!ctripOrderId);
    if (ctripOrderId) {
      const getNumVal = val => {
        if (val === '0') return 0;
        return Number(val) || undefined;
      };
      const rebookParams = {
        ctripOrderId: getNumVal(ctripOrderId),
        vendorId: getNumVal(vendorId),
        vehicleId: getNumVal(vehicleId),
        ctripVehicleCode,
        storeCode,
        couponCode: AppContext.originalCouponCode,
      };
      yield put(setRebookParams(rebookParams));
    }
    if (Utils.isCtripOsd() && osdOriginOrderId) {
      yield put(
        setRebookParamsOsd({
          ctripOrderId: osdOriginOrderId,
          from,
        }),
      );
    }
  }
}

const handleUrlParams = () => {
  const {
    filters,
    vehgroupid,
    vehicleid,
    currentDailyPrice,
    uniqSign,
    license,
    skuId,
    vehicleDayPrice,
  } = AppContext.UrlQuery;

  // 加载列表页筛选项
  listFilterLoader(
    vehgroupid,
    filters,
    vehicleid,
    currentDailyPrice,
    uniqSign,
    license,
    skuId,
    vehicleDayPrice,
  );
};

function* handleParams(st, gotoPageName, fakeUrl) {
  if (st === LOADING_TYPE.SERVER) {
    yield handleServerParams(fakeUrl);
  }
  if (st === LOADING_TYPE.CLIENT) {
    yield call(handleClientParams, gotoPageName);
  }
  handleUrlParams();
}

// 时间适配
// 国内适配规则
// 境外适配规则
const rentalDataAdapter = ({
  pickUp,
  dropOff,
}: {
  pickUp: string;
  dropOff: string;
}) => {
  let pDate = dayjs(Utils.dateTimeFormat(pickUp));
  let rDate = dayjs(Utils.dateTimeFormat(dropOff));
  const { RENTAL_GAP, RENTAL_NEXT_GAP } = Platform;
  // 放开境外取车时间小于当前时间的限制，境外可能出现小于北京时间的场景
  if (!pDate.isValid() || (Utils.isCtripIsd() && pDate < dayjs())) {
    /* eslint-disable no-nested-ternary */
    const gap = Utils.isCtripIsd()
      ? RENTAL_NEXT_GAP.ISD
      : Utils.isCtripOsd()
        ? RENTAL_NEXT_GAP.OSD
        : RENTAL_NEXT_GAP.IBU;
    pDate = dayjs().startOf('hour').add(gap, 'd').hour(10);
  }
  if (rDate <= pDate || !rDate.isValid()) {
    /* eslint-disable no-nested-ternary */
    const gap = Utils.isCtripIsd()
      ? RENTAL_GAP.ISD
      : Utils.isCtripOsd()
        ? RENTAL_GAP.OSD
        : RENTAL_GAP.IBU;
    rDate = dayjs(pDate).add(gap, 'd');
  }
  return {
    pickup: pDate.format('YYYY-MM-DD HH:mm:ss'),
    dropoff: rDate.format('YYYY-MM-DD HH:mm:ss'),
  };
};

function* updateState(fakeUrl = '') {
  const fakeQuery = Utils.composeQsParse(fakeUrl);
  const urlQuery = fakeUrl ? fakeQuery : AppContext.UrlQuery;
  const { searchDiff, isHomeCombine, isHomeRanking } = urlQuery || {};

  // 榜单跳转列表页，需回传搜索条件至首页
  if (isHomeCombine && isHomeRanking && !searchDiff) {
    AppContext.setIsListCombineEventSwitch(true);
  }

  if (params.rentalDate) {
    const date = rentalDataAdapter({
      pickUp: lodashGet(params, 'rentalDate.pickUp.dateTime'),
      dropOff: lodashGet(params, 'rentalDate.dropOff.dateTime'),
    });
    if (Utils.isCtripIsd()) {
      yield put(setDateInfo({ ...date, notDisPatchPreFetch: true }));
    } else {
      yield put(setDateInfo(date));
    }
  }

  if (params.rentalLocation) {
    yield put(
      setLocationInfo({
        ...params.rentalLocation,
        isLocationFormUrl: false,
        isNoRPoiData: params.isNoRPoiData,
      }),
    );
  }

  if (params.age) {
    yield put(setAge({ age: params.age }));
  }

  if (params.adultSelectNum || params.childSelectNum) {
    yield put(
      setAgeAdultAndChildNum({
        adultSelectNum: params.adultSelectNum,
        childSelectNum: params.childSelectNum,
      }),
    );
  }
}

function isCheckPoiPage(pageName) {
  return (
    pageName === Channel.getPageId().Home.EN ||
    pageName === Channel.getPageId().List.EN
  );
}

export function* load() {
  yield takeLatest(LOAD, function* logic(action: ActionType) {
    const { fakeUrl = '' } = action.data || {};
    const fakeQuery = Utils.composeQsParse(fakeUrl);
    let gotoPageName = '';
    const defaultPageName = getDefaultPageName();
    const { landingto } = fakeUrl ? fakeQuery : AppContext.UrlQuery;
    // setCarChannelID
    const marketInfoParam = getMarketInfoParam({
      query: fakeUrl ? fakeQuery : AppContext.UrlQuery,
      path: fakeUrl || AppContext?.PageInstance?.props?.app?.url,
    });
    marketInfo.setCarChannelID(marketInfoParam);

    try {
      const {
        st,
        encryptUid,
        fromType,
        originOrderId,
        eid,
        env,
        modifyVendorOrderCode,
        originVendorId,
        originalCouponCode = '',
        originalActivityIds,
        originalActivityNames,
        klbVersion,
        orignScenes,
        isMarketing,
      } = fakeUrl ? fakeQuery : AppContext.UrlQuery;
      const { ChannelID } = marketInfo.getCarMarket() || {};
      AppContext.setEncryptUid(encryptUid);
      AppContext.setFromType(fromType);
      AppContext.setOriginOrderId(originOrderId);
      AppContext.setChannelId(ChannelID);
      AppContext.setEid(eid);
      AppContext.setFatEnv(env);
      AppContext.setModifyVendorOrderCode(modifyVendorOrderCode);
      AppContext.setOriginVendorId(originVendorId);
      AppContext.setOriginalCouponCode(originalCouponCode);
      AppContext.setOriginalActivityId(originalActivityIds?.split(','));
      AppContext.setOriginalActivityName(originalActivityNames?.split(','));
      AppContext.setKlbVersion(klbVersion);
      AppContext.setOrignScenes(orignScenes);
      AppContext.setIsMarketing(isMarketing);

      gotoPageName = getPageName(landingto);
      yield call(handleParams, st, gotoPageName, fakeUrl);
      yield call(updateState, fakeUrl);
    } catch (err) {
      const { isHomeRanking } = fakeUrl ? fakeQuery : AppContext.UrlQuery;
      // 因榜单跳转走服务端路由，无网络场景进catch，跳转默认页（融合版首页），需正常跳转至列表页
      gotoPageName = isHomeRanking ? getPageName(landingto) : defaultPageName;
    }
    const urlQuery = fakeUrl ? fakeQuery : AppContext.UrlQuery;
    const { searchDiff, isHomeRanking } = urlQuery;
    const isPoiPage = isCheckPoiPage(gotoPageName);
    const replacePageData = {
      isFromMarket: true,
      fromurl: AppContext.UrlQuery.fromurl,
      isNoPoiData: isPoiPage && params?.isNoPoiData,
      isNoRPoiData: isPoiPage && params?.isNoRPoiData,
      isTimePassed: isPoiPage && isHomeRanking && params?.isTimePassed, // 榜单跳转列表页toast时间过期
      searchDiff,
      urlQuery,
    };
    yield put(loadMarketCompleted({ gotoPageName, replacePageData }));
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_market_landingto,
      info: {
        landingto,
      },
    });
  });
}

export default [load()];
