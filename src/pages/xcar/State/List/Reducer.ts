import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import {
  SET_STATUS,
  GET_STATUS,
  INIT_SET_GROUPID,
  SET_GROUPID,
  FETCH_LIST_BATCH,
  SET_ACTIVE_FILTER_BAR_CODE,
  UPDATE_SELECTED_FILTER,
  DELETE_SELECTED_FILTER,
  C<PERSON>AR_SELECTED_FILTER,
  SET_LOCATIONDATEPOP_VISIBLE,
  SET_AGEPICKER_VISIBLE,
  SET_AGETIPPOP_VISIBLE,
  SET_PROGRESS_ISFINISH,
  SET_SORTANDFILTER_VISIBLE,
  RESET,
  SET_SCROLL_VIEW_HEIGHT,
  SET_DRIVERLICENSE_POP_DATA,
  SET_SALEOUT_LIST,
  ADD_SALEOUT_LIST,
  REMOVE_SALEOUT_LIST,
  ADD_VEHICLE_SALEOUT_LIST,
  FETCH_LIMIT_CONTENT,
  LIMITRULE_POP_VISIBLE,
  SET_VEHPOP_DATA,
  GET_C2B_ORDERSTATUS,
  FETCH_ISD_IMURL,
  SET_TIMEOUT_POP_DATA,
  SET_VENDORLISTMODAL_DATA,
  SET_SHOW_FILTERED_PROGRESS,
  SET_EASYLIFE_POP_VISIBLE,
  SET_FILTER_NO_RESULT,
  UPDATE_FETCH_CACHE_STATUS,
  PRE_FETCH_LIST_BATCH,
  SET_BATCH_REQUEST,
  SET_LIMITTIP_LOADFINISH,
  SET_TOTALPRICEMODAL_DATA,
  SET_TIPPOP_DATA,
  FETCH_LIST_PAGE,
  SET_GROUP_LOADING,
  SET_FILTER_LOADING,
  SET_PAGE_STATUS,
  SET_FILTER_STATUS,
  SET_GROUP_STATUS,
  PRE_FETCH_LIST_PAGE,
  SET_NEXT_PAGE_LOADING,
  SET_NEXT_PAGE_STATUS,
  SET_CACHEEXPIRE_MODAL_VISIBLE,
  UPDATE_PAGE_NUM,
  RESET_PAGE_NUM,
  UPDATE_STATUS_BEFORE_FILTER_SEARCH,
  UPDATE_STATUS_BEFORE_GROUP_SEARCH,
  UPDATE_STATUS_BEFORE_NEXT_PAGE_SEARCH,
  HANDLE_RES_GROUPID_FILTERS,
  SET_PAGE_RES_STATUS,
  SET_IS_SHOW_TOAST,
  SET_NO_MORE_TOAST_TEXT,
  SET_PRICE_SUMMARY_MODAL,
  UPDATE_SELECTED_FILTER_LISTNOREFRESH,
  FETCH_LIST_RECOMMEND,
  FETCH_LIST_RECOMMEND_CALLBACK,
  SET_IS_MORE_AGE,
  SET_SECRET_BOX_MODAL_DATA,
  SET_LICENSE_MODAL_DATA,
  SET_SELF_SERVICE_BANNER_INFO,
  SET_QUERY_PACKAGE_COMPARISON,
  SET_USER_BROWSING_HISTORY_CALLBACK,
  RESET_USER_BROWSING_HISTORY,
  SET_EASYLIFE_2024_TAGS,
  SET_IS_GROUP_CHANGE_TOAST,
  SET_RECOMMEND_TYPE,
  CHECK_OSD_POI_CALLBACK,
  REMOVE_SALEOUT_ALL,
  SET_OSD_USER_BROWSED,
  SET_LIST_IPOLL_CONFIG,
  SET_LIST_SEARCH_WORDS,
  SET_OSD_USER_BROWSING_HISTORY_CALLBACK,
} from './Types';
import { Utils, CarStorage } from '../../Util/Index';
import { ListEnum, StorageKey } from '../../Constants/Index';
import { isValidRentalDate } from '../LocationAndDate/Util';

import { getAllCarsConfig } from './Mappers';
import AppContext from '../../Util/AppContext';

const defaultSelectedFilters = {
  sortFilter: '1',
  priceFilter: [],
  bitsFilter: [],
  filterLabels: [],
  commentFilter: [],
};
const defaultSaleOutList = [];
const defaultTipPopData = {
  visible: false,
  data: {},
};

export const getInitalState = (osdUserBrowsed = null) => ({
  isLoading: true,
  isFilterLoading: false, // 是否展示"点击筛选时的loading"
  isGroupLoading: false, // 是否展示"点击车型组时的loading"
  isNextPageLoading: false, // 是否正在加载下一页
  isRecommendLoading: false,
  isRecommendNoResult: false, // 是否推荐无结果
  recommendAllVehicleCount: 0, // 推荐车型数
  recommendAllVendorPriceCount: 0, // 推荐车型报价数
  isFail: false,
  isShowGroupChangeToast: false,
  isFilterFail: false, // 更新筛选后请求是否失败
  isGroupFail: false, // 更新车型组后请求是否失败
  isNextPageFail: false, // 加载下一页时是否失败
  isError: false,
  progress: 0,
  progressIsFinish: false,
  activeGroupId: Utils.isCtripIsd() ? '' : getAllCarsConfig().groupCode,
  activeFilterBarCode: '',
  activeFilterBarName: '',
  filterNoResult: false, // url跳转筛选是否无结果
  selectedFilters: defaultSelectedFilters,
  deleteCode: '',
  bitsFilterByBar: {
    // filterbar上选项所对应的选中筛选项
    filter: [],
  },
  batchesRequest: [], // 记录当前页面响应回来的请求次数, resCode: 201/200, result: 1成功，-1失败
  locationDatePopVisible: false,
  pickUpAvailableTime: null, // 车型推荐取车时间
  returnAvailableTime: null, // 车型推荐还车时间
  availableLocation: '', // 车型推荐地点名称
  longitude: '', // 车型推荐经度
  latitude: '', // 车型推荐纬度
  recommendTitle: '', // 推荐信息标题
  recommendDesc: '', // 推荐信息描述
  recommendTip: '', // 推荐信息提示标题
  recommendButtonTitle: '', // 推荐信息按钮文案
  recommendType: '', // 车型推荐类型
  subStrategyType: '', // 推荐策略子类型(目前只有时间推荐有子类型)
  recUniqsign: '', // 推荐车型对应的uniqSign字段，用于产品详情页命中缓存
  recommendCid: '', // 推荐城市
  agePickerVisible: false,
  ageTipPopVisible: false,
  sortAndFilterVisible: false,
  filterBarIsShow: false,
  quickFilterBarIsShow: false,
  scrollViewHeight: Utils.heightWithStatusBar,
  driverlicensePopData: {
    visible: false,
    content: '',
    leftText: '',
  },
  saleOutList: defaultSaleOutList,
  priceSoldOutTextMap: {},
  vehicleSoldOutList: defaultSaleOutList,
  limitRulePopVisible: false,
  vehPopData: {
    visible: false,
    vehicleCode: '',
  },
  rentCenter: null,
  timeOutPopData: {
    visible: false,
    title: '',
    content: '',
    rightText: '',
  },
  vendorModalData: {
    visible: false,
    data: {},
  },
  showFilteredProgress: false,
  easyLifePopVisible: false,
  limitTipLoadFinish: false,
  totalPriceModalData: {
    visible: false,
    data: {},
  },
  priceSummaryModalData: {
    visible: false,
    data: {},
    secretBoxPriceModalVisible: false,
  },
  licenseModalData: {
    visible: false,
    data: {},
    title: '',
  },
  tipPopData: defaultTipPopData,
  cacheExpireModalVisible: false,
  pageNumInfo: {},
  isShowToast: false, // 是否展示报价toast
  noMoreToastText: '', // 当前车型组无车型时的toast文案
  secretBoxModalVisible: false,
  secretBoxModalData: {},
  selfServiceBannerInfo: null,
  packageComparisonData: {
    status: 'pending',
    response: null,
  },
  easyLife2024TagsData: {
    status: 'pending',
    response: null,
  },
  userBrowsingHistories: new Map(), // 用户浏览过的车型价格
  osdUserBrowsingHistories: new Map(), // 出境列表页浏览记录（灰底）
  noResultRecTip: '', // 无结果聚合顶部文案
  checkPoiRes: {}, // 拦截结果
  osdUserBrowsed: osdUserBrowsed || {}, // 出境列表页浏览记录
  ipollConfig: [], // ipoll配置
  curSearchWordObj: {
    uniqueCode: '',
  }, // 当前搜索词数据
});

const initActiveGroupId = (state, action) => ({
  ...state,
  activeGroupId:
    (!state.activeGroupId && action.data.activeGroupId) || state.activeGroupId,
});

const updateSoldOutList = (originList, soldOutKey) => {
  const newSoldOutList = Object.assign([], originList);
  if (soldOutKey && !newSoldOutList.includes(soldOutKey)) {
    newSoldOutList.push(soldOutKey);
  }
  return newSoldOutList;
};

export const addSaleOutList = (state, data) => {
  let saleOutProductKey = '';
  const { priceSoldOutTextMap } = state;
  if (Utils.isObject(data)) {
    saleOutProductKey = data?.saleOutProductKey;
    priceSoldOutTextMap[data?.saleOutProductKey] = data?.priceSoldOutText;
  } else {
    saleOutProductKey = data;
  }
  const newSaleOutList = updateSoldOutList(
    state.saleOutList,
    saleOutProductKey,
  );
  return {
    ...state,
    saleOutList: newSaleOutList,
    priceSoldOutTextMap,
  };
};

export const removeSaleOutList = (state, data) => {
  const saleOutProductKey = data?.saleOutProductKey;
  const newSaleOutList = state.saleOutList.filter(
    item => item !== saleOutProductKey,
  );
  return {
    ...state,
    saleOutList: newSaleOutList,
  };
};

const addVehicleSoldOutList = (state, soldOutVehicleKey) => {
  const newVehicleSoldOutList = updateSoldOutList(
    state.vehicleSoldOutList,
    soldOutVehicleKey,
  );
  return {
    ...state,
    vehicleSoldOutList: newVehicleSoldOutList,
  };
};

const setTipPopData = (state, { visible, data = {} as any } = {} as any) => {
  const { data: oldData = {}, visible: oldVisible } = state.tipPopData || {};
  const fixVisible = visible !== undefined ? visible : oldVisible;
  // 修改取还车条件气泡和总价气泡不可同时出现，优先展示修改取还车条件气泡
  if (oldData.type !== undefined && oldData.type !== data.type && fixVisible) {
    return state;
  }
  // 如果是总价提示，且关闭操作，则不在展示总价提示
  if (oldData.type === ListEnum.TipPopType.TotalPrice && !visible) {
    CarStorage.save(StorageKey.TOTAL_PRICE_POP, true);
  }
  const { style = {} } = oldData;
  const { style: newStyle = {} } = data;
  return {
    ...state,
    tipPopData: {
      visible: fixVisible,
      data: {
        ...oldData,
        ...data,
        style: { ...style, ...newStyle },
      },
    },
  };
};

const handlePageNumOfGroupId = (state, data) => {
  const { activeGroupId, pageNumInfo } = state;
  const { type } = data;
  const nextPageNumInfo = { ...pageNumInfo };
  let nextPageNum = pageNumInfo[activeGroupId] || 1;
  if (type === 'plus') {
    nextPageNum += 1;
  } else if (type === 'minus') {
    nextPageNum -= 1;
  }

  nextPageNumInfo[activeGroupId] = nextPageNum;
  return {
    ...state,
    pageNumInfo: nextPageNumInfo,
  };
};

const resetPageNumOfGroupId = state => {
  const { activeGroupId, pageNumInfo } = state;
  const nextPageNumInfo = { ...pageNumInfo };
  nextPageNumInfo[activeGroupId] = 1;
  return {
    ...state,
    pageNumInfo: nextPageNumInfo,
  };
};

const handleBeforeFilterSearch = (state, action) => {
  const newState = resetPageNumOfGroupId(state);
  return {
    ...state,
    ...newState,
    isFilterFail: false,
    isFilterLoading: !action.data.hasCaCheData,
    isNextPageLoading: false,
    isNextPageFail: false,
    isGroupLoading: false,
    isGroupFail: false,
  };
};

const handleBeforeGroupSearch = state => {
  const newState = resetPageNumOfGroupId(state);
  return {
    ...state,
    ...newState,
    isGroupFail: false,
    isGroupLoading: true,
    isNextPageLoading: false,
    isNextPageFail: false,
    isFilterFail: false,
    isFilterLoading: false,
  };
};

const handleBeforeNextPageSearch = state => {
  const newState = handlePageNumOfGroupId(state, { type: 'plus' });
  return {
    ...state,
    ...newState,
    isNextPageLoading: true,
    isNextPageFail: false,
  };
};

const handlePageResStatus = (state, action) => ({
  ...state,
  activeGroupId: action.data.activeGroupId || state.activeGroupId,
  selectedFilters: action.data.selectedFilters || state.selectedFilters,
  filterNoResult: action.data.filterNoResult || state.filterNoResult,
});

const handleCurSearchWord = (state, action) => {
  const { curSearchWordObj, selectedFilters } = state || {};
  let clearSearchWord = false;
  const { filterLabels, isFromSearchWord, bitsFilter, isSort } =
    action.data || {};
  const uniqueCode = curSearchWordObj?.uniqueCode || '';
  if (
    !isSort && // 非排序时
    !isFromSearchWord &&
    selectedFilters?.bitsFilter?.length > 0 &&
    uniqueCode &&
    selectedFilters?.bitsFilter?.includes(uniqueCode) &&
    !bitsFilter?.includes(uniqueCode)
  ) {
    // 反选筛选项时，如果和当前搜索词是同一筛选项，需清除当前搜索词
    const searchWord = selectedFilters.bitsFilter.find(
      item => item === uniqueCode,
    );
    if (searchWord) {
      return {};
    }
  }
  if (!isFromSearchWord && filterLabels?.length > 0) {
    const curFilterLabel = filterLabels[filterLabels.length - 1];
    const { groupCode, isSingleChoice, code } = curFilterLabel || {};
    if (
      groupCode &&
      isSingleChoice &&
      uniqueCode?.startsWith(groupCode) &&
      uniqueCode !== code
    ) {
      // 搜索词和当前选择的筛选条件是同一类筛选条件时，并且是单选时，需清除当前搜索词
      clearSearchWord = true;
    }
  }
  return clearSearchWord ? {} : curSearchWordObj;
};

const initalState = getInitalState();
// eslint-disable-next-line @typescript-eslint/default-param-last
export default (state = initalState, action) => {
  const { data } = action;
  let pickUpAvailableTime = null;
  let returnAvailableTime = null;

  switch (action.type) {
    case SET_STATUS:
      return {
        ...state,
        isLoading: action.data.isLoading,
        isFail: action.data.isFail,
        isError: action.data.isError,
        progress: action.data.progress,
      };
    case FETCH_LIST_RECOMMEND:
      return {
        ...state,
        isRecommendLoading: true,
        isRecommendNoResult: false,
        recommendAllVehicleCount: 0,
        recommendAllVendorPriceCount: 0,
        recommendTitle: '',
        recommendDesc: '',
        recommendTip: '',
        recommendButtonTitle: '',
        recommendType: '',
        subStrategyType: '',
        recUniqsign: '',
      };
    case FETCH_LIST_RECOMMEND_CALLBACK:
      return {
        ...state,
        isRecommendLoading: false,
        isRecommendNoResult: action.data.isRecommendNoResult,
        recommendAllVehicleCount: action.data.recommendAllVehicleCount,
        recommendAllVendorPriceCount: action.data.recommendAllVendorPriceCount,
        recommendTitle: action.data.recommendTitle,
        recommendDesc: action.data.recommendDesc,
        recommendTip: action.data.recommendTip,
        recommendButtonTitle: action.data.recommendButtonTitle,
        recommendType: action.data.recommendType,
        subStrategyType: action.data.subStrategyType,
        recUniqsign: action.data.recUniqsign,
        pickUpAvailableTime: action.data.pickUpAvailableTime,
        returnAvailableTime: action.data.returnAvailableTime,
        availableLocation: action.data.availableLocation,
        availableLocationCode: action.data.availableLocationCode,
        longitude: action.data.longitude,
        latitude: action.data.latitude,
        recommendCid: action.data.cid,
        noResultRecTip: action.data.noResultRecTip,
      };
    case INIT_SET_GROUPID:
      return initActiveGroupId(state, action);
    case SET_GROUPID:
      return {
        ...state,
        activeGroupId: action.data.activeGroupId || state.activeGroupId,
      };
    case SET_ACTIVE_FILTER_BAR_CODE:
      return {
        ...state,
        activeFilterBarCode: action.data.activeFilterBarCode,
        activeFilterBarName: action.data.activeFilterBarName,
      };
    case UPDATE_SELECTED_FILTER:
    case UPDATE_SELECTED_FILTER_LISTNOREFRESH:
      return {
        ...state,
        activeGroupId: action.activeGroupId || state.activeGroupId,
        selectedFilters: { ...state.selectedFilters, ...action.data },
        showFilteredProgress: action.data.isHideProgress !== true,
        selfServiceBannerInfo: null,
        curSearchWordObj: handleCurSearchWord(state, action),
      };
    case SET_LOCATIONDATEPOP_VISIBLE:
      if (action.data.visible === false) {
        AppContext.setRouterListLoaderDispatch(true);
      }
      if (
        !!action?.data?.pickUpAvailableTime &&
        !!action?.data?.returnAvailableTime &&
        isValidRentalDate(
          action.data.pickUpAvailableTime,
          action.data.returnAvailableTime,
        )
      ) {
        pickUpAvailableTime = dayjs(
          action.data.pickUpAvailableTime,
          'YYYYMMDDHHmmss',
        );
        returnAvailableTime = dayjs(
          action.data.returnAvailableTime,
          'YYYYMMDDHHmmss',
        );
      }
      return {
        ...state,
        locationDatePopVisible: action.data.visible,
        pickUpAvailableTime: pickUpAvailableTime || state.pickUpAvailableTime,
        returnAvailableTime: returnAvailableTime || state.returnAvailableTime,
        availableLocation:
          action.data.availableLocation || state.availableLocation,
        longitude: action.data.longitude || state.longitude,
        latitude: action.data.latitude || state.latitude,
      };
    case DELETE_SELECTED_FILTER:
      return {
        ...state,
        deleteCode: { deleteCode: state.deleteCode, ...action.data },
      };
    case CLEAR_SELECTED_FILTER:
      return {
        ...state,
        selectedFilters: {
          ...state.selectedFilters,
          priceFilter: [],
          bitsFilter: [],
          filterLabels: [],
          commentFilter: [],
        },
      };
    case SET_AGEPICKER_VISIBLE:
      return {
        ...state,
        agePickerVisible: action.data.visible,
      };
    case SET_AGETIPPOP_VISIBLE:
      return {
        ...state,
        ageTipPopVisible: action.data.visible,
      };
    case SET_PROGRESS_ISFINISH:
      return {
        ...state,
        progressIsFinish: action.data,
      };
    case SET_SORTANDFILTER_VISIBLE:
      return {
        ...state,
        sortAndFilterVisible: action.data.visible,
      };

    case RESET:
      return getInitalState(state.osdUserBrowsed);
    case SET_SCROLL_VIEW_HEIGHT:
      return {
        ...state,
        scrollViewHeight: action.data,
      };
    case SET_DRIVERLICENSE_POP_DATA:
      return {
        ...state,
        driverlicensePopData: {
          visible: action.data.visible || false,
          content: action.data.content || '',
          leftText: action.data.leftText || '',
        },
      };
    case SET_SALEOUT_LIST:
      return {
        ...state,
        saleOutList: action.data.saleOutList,
      };
    case ADD_SALEOUT_LIST:
      return addSaleOutList(state, action.data);
    case REMOVE_SALEOUT_LIST:
      return removeSaleOutList(state, action.data);
    case REMOVE_SALEOUT_ALL:
      return {
        ...state,
        saleOutList: defaultSaleOutList,
        vehicleSoldOutList: defaultSaleOutList,
      };
    case ADD_VEHICLE_SALEOUT_LIST:
      return addVehicleSoldOutList(state, action.data);
    case LIMITRULE_POP_VISIBLE:
      return {
        ...state,
        limitRulePopVisible: action.data.visible,
      };
    case SET_VEHPOP_DATA:
      return {
        ...state,
        vehPopData: {
          visible: action.data.visible,
          // 当不传vehicleCode时，使用当前vehPopData的vehicleCode
          vehicleCode: action.data.vehicleCode ?? state.vehPopData.vehicleCode,
        },
      };

    case SET_TIMEOUT_POP_DATA:
      return {
        ...state,
        timeOutPopData: {
          visible: action.data.visible || false,
          title: action.data.title || '',
          content: action.data.content || '',
          rightText: action.data.rightText || '',
        },
      };
    case SET_VENDORLISTMODAL_DATA:
      return {
        ...state,
        vendorModalData: {
          visible: action.data.visible,
          data: action.data.data,
        },
      };
    case SET_SHOW_FILTERED_PROGRESS:
      return {
        ...state,
        showFilteredProgress: action.data,
      };
    case SET_EASYLIFE_POP_VISIBLE:
      return {
        ...state,
        easyLifePopVisible: action.data.visible,
      };
    case SET_FILTER_NO_RESULT:
      return {
        ...state,
        filterNoResult: action.data,
      };
    case SET_IS_GROUP_CHANGE_TOAST:
      return {
        ...state,
        isShowGroupChangeToast: action.data,
      };
    case UPDATE_FETCH_CACHE_STATUS:
      return {
        ...state,
        isLoading: action.data.isLoading,
        isFail: action.data.isFail,
        progress: action.data.progress,
        progressIsFinish: action.data.progressIsFinish,
        rentCenter: action.data.rentCenter,
      };
    case SET_BATCH_REQUEST:
      return {
        ...state,
        batchesRequest: action.data,
      };
    case SET_LIMITTIP_LOADFINISH: {
      return {
        ...state,
        limitTipLoadFinish: action.data,
      };
    }
    case SET_TOTALPRICEMODAL_DATA:
      return {
        ...state,
        totalPriceModalData: {
          visible: action.data.visible,
          data: action.data.data,
        },
      };
    case SET_PRICE_SUMMARY_MODAL:
      return {
        ...state,
        priceSummaryModalData: {
          visible: !!data.visible,
          secretBoxPriceModalVisible: !!data.secretBoxPriceModalVisible,
          data:
            data.data === undefined
              ? state.priceSummaryModalData.data
              : data.data,
        },
      };

    case SET_QUERY_PACKAGE_COMPARISON:
      return {
        ...state,
        packageComparisonData: action.data,
      };
    case SET_EASYLIFE_2024_TAGS:
      return {
        ...state,
        easyLife2024TagsData: action.data,
      };
    case SET_TIPPOP_DATA:
      return setTipPopData(state, action.data);
    case SET_FILTER_LOADING:
      return { ...state, isFilterLoading: action.data };
    case SET_GROUP_LOADING:
      return { ...state, isGroupLoading: action.data };
    case SET_NEXT_PAGE_LOADING:
      return { ...state, isNextPageLoading: action.data };
    case SET_FILTER_STATUS:
      return {
        ...state,
        isFilterLoading: action.data.isFilterLoading,
        isFilterFail: action.data.isFilterFail,
        isShowToast: action.data.isShowToast,
        saleOutList: defaultSaleOutList,
      };
    case SET_GROUP_STATUS: {
      return {
        ...state,
        isShowGroupChangeToast: action.data.isShowGroupChangeToast,
        isGroupLoading: action.data.isGroupLoading,
        isGroupFail: action.data.isGroupFail,
        saleOutList: defaultSaleOutList,
      };
    }
    case SET_PAGE_STATUS:
      return {
        ...state,
        ...handlePageResStatus(state, action),
        isLoading: action.data.isLoading,
        isFail: action.data.isFail,
        progress: action.data.progress,
        progressIsFinish: action.data.progressIsFinish,
        isShowToast: action.data.isShowToast,
      };
    case SET_NEXT_PAGE_STATUS:
      return {
        ...state,
        isNextPageLoading: action.data.isNextPageLoading,
        isNextPageFail: action.data.isNextPageFail,
      };
    case SET_CACHEEXPIRE_MODAL_VISIBLE:
      return {
        ...state,
        cacheExpireModalVisible: action.data,
      };
    case UPDATE_PAGE_NUM:
      return handlePageNumOfGroupId(state, action.data);
    case RESET_PAGE_NUM:
      return resetPageNumOfGroupId(state);
    case UPDATE_STATUS_BEFORE_FILTER_SEARCH:
      return handleBeforeFilterSearch(state, action);
    case UPDATE_STATUS_BEFORE_GROUP_SEARCH:
      return handleBeforeGroupSearch(state);
    case UPDATE_STATUS_BEFORE_NEXT_PAGE_SEARCH:
      return handleBeforeNextPageSearch(state);
    case SET_PAGE_RES_STATUS:
      return handlePageResStatus(state, action);
    case SET_IS_SHOW_TOAST:
      return { ...state, isShowToast: action.data };
    case SET_NO_MORE_TOAST_TEXT:
      return { ...state, noMoreToastText: action.data };
    case SET_IS_MORE_AGE:
      return { ...state, isMoreAge: action.data };
    case SET_SECRET_BOX_MODAL_DATA:
      return {
        ...state,
        secretBoxModalVisible: action.data.visible,
        secretBoxModalData: action.data.data,
      };
    case SET_LICENSE_MODAL_DATA:
      return {
        ...state,
        licenseModalData: {
          visible: !!data.visible,
          data: data.data,
          title: data.title,
        },
      };
    case SET_SELF_SERVICE_BANNER_INFO:
      if (action.data) {
        return {
          ...state,
          selfServiceBannerInfo: action.data,
        };
      }
      return state;
    case SET_USER_BROWSING_HISTORY_CALLBACK:
      if (action.data) {
        return {
          ...state,
          userBrowsingHistories: action.data,
        };
      }
      return state;
    case SET_OSD_USER_BROWSING_HISTORY_CALLBACK:
      return {
        ...state,
        osdUserBrowsingHistories: action.data,
      };
    case RESET_USER_BROWSING_HISTORY:
      if (action.data) {
        return {
          ...state,
          userBrowsingHistories: new Map(),
        };
      }
      return state;
    case SET_RECOMMEND_TYPE:
      return {
        ...state,
        newRecommendType: action.data,
      };
    case SET_OSD_USER_BROWSED:
      return {
        ...state,
        osdUserBrowsed: action.data || state.osdUserBrowsed,
      };
    case CHECK_OSD_POI_CALLBACK:
      return {
        ...state,
        checkPoiRes: action.data,
      };
    case SET_LIST_IPOLL_CONFIG:
      return {
        ...state,
        ipollConfig: action.data,
      };
    case SET_LIST_SEARCH_WORDS:
      return {
        ...state,
        curSearchWordObj: action.data,
      };
    case GET_STATUS:
    case FETCH_LIST_BATCH:
    // eslint-disable-next-line no-fallthrough
    case FETCH_LIMIT_CONTENT:
    case GET_C2B_ORDERSTATUS:
    case FETCH_ISD_IMURL:
    case PRE_FETCH_LIST_BATCH:
    case FETCH_LIST_PAGE:
    case PRE_FETCH_LIST_PAGE:
    case HANDLE_RES_GROUPID_FILTERS:
    default:
      return state;
  }
};
