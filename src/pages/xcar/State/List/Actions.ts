import { get as lodashGet } from 'lodash-es';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';

import { getAllProductGroups } from '../../Global/Cache/ListResSelectors';
import ListReqAndResData, {
  listStatusKeyList,
} from '../../Global/Cache/ListReqAndResData';
import { Enums } from '../../ComponentBusiness/Common';
import { CarLog, CarABTesting } from '../../Util/Index';
import { ListConstants, ListEnum, LogKey } from '../../Constants/Index';
import {
  SET_STATUS,
  GET_STATUS,
  SET_GROUPID,
  INIT_SET_GROUPID,
  FETCH_LIST_BATCH,
  FETCH_LIST_RECOMMEND,
  FETCH_LIST_RECOMMEND_CALLBACK,
  SET_BATCH_REQUEST,
  SET_ACTIVE_FILTER_BAR_CODE,
  UPDATE_SELECTED_FILTER,
  DELETE_SELECTED_FILTER,
  CLEAR_SELECTED_FILTER,
  SET_LOCATIONDATEPOP_VISIBLE,
  SET_AGEPICKER_VISIBLE,
  SET_AGETIPPOP_VISIBLE,
  SET_PROGRESS_ISFINISH,
  SET_SORTANDFILTER_VISIBLE,
  RESET,
  SET_SCROLL_VIEW_HEIGHT,
  SET_DRIVERLICENSE_POP_DATA,
  SET_SALEOUT_LIST,
  ADD_SALEOUT_LIST,
  REMOVE_SALEOUT_LIST,
  ADD_VEHICLE_SALEOUT_LIST,
  FETCH_LIMIT_CONTENT,
  LIMITRULE_POP_VISIBLE,
  SET_VEHPOP_DATA,
  GET_C2B_ORDERSTATUS,
  FETCH_ISD_IMURL,
  SET_TIMEOUT_POP_DATA,
  SET_VENDORLISTMODAL_DATA,
  SET_SHOW_FILTERED_PROGRESS,
  SET_EASYLIFE_POP_VISIBLE,
  SET_FILTER_NO_RESULT,
  UPDATE_FETCH_CACHE_STATUS,
  SET_LIMITTIP_LOADFINISH,
  PRE_FETCH_LIST_BATCH,
  SET_TOTALPRICEMODAL_DATA,
  SET_TIPPOP_DATA,
  FETCH_LIST_PAGE,
  SET_PAGE_STATUS,
  SET_FILTER_STATUS,
  SET_GROUP_STATUS,
  PRE_FETCH_LIST_PAGE,
  SET_NEXT_PAGE_STATUS,
  SET_CACHEEXPIRE_MODAL_VISIBLE,
  UPDATE_PAGE_NUM,
  UPDATE_STATUS_BEFORE_FILTER_SEARCH,
  UPDATE_STATUS_BEFORE_GROUP_SEARCH,
  UPDATE_STATUS_BEFORE_NEXT_PAGE_SEARCH,
  HANDLE_RES_GROUPID_FILTERS,
  SET_PAGE_RES_STATUS,
  SET_IS_SHOW_TOAST,
  SET_NO_MORE_TOAST_TEXT,
  SET_PRICE_SUMMARY_MODAL,
  UPDATE_SELECTED_FILTER_LISTNOREFRESH,
  SET_IS_MORE_AGE,
  SET_SECRET_BOX_MODAL_DATA,
  LIST_GO_TO_BOOKING,
  LIST_GO_TO_BOOKING_EASYLIFE,
  SET_LICENSE_MODAL_DATA,
  SET_SELF_SERVICE_BANNER_INFO,
  QUERY_PACKAGE_COMPARISON,
  SET_QUERY_PACKAGE_COMPARISON,
  SET_USER_BROWSING_HISTORY,
  SET_USER_BROWSING_HISTORY_CALLBACK,
  RESET_USER_BROWSING_HISTORY,
  FETCH_EASYLIFE_2024_TAGS,
  SET_EASYLIFE_2024_TAGS,
  SET_IS_GROUP_CHANGE_TOAST,
  SET_RECOMMEND_TYPE,
  CHECK_OSD_POI,
  CHECK_OSD_POI_CALLBACK,
  REMOVE_SALEOUT_ALL,
  SET_OSD_USER_BROWSED,
  QUERY_LIST_IPOLL_CONFIG,
  SET_LIST_IPOLL_CONFIG,
  SET_LIST_SEARCH_WORDS,
  SET_OSD_USER_BROWSING_HISTORY,
  SET_OSD_USER_BROWSING_HISTORY_CALLBACK,
} from './Types';
import {
  QueryProductsRequestType,
  QueryProductsResponseType,
  SuggestionType,
} from '../../Types/Dto/QueryProductsType';

const { SpecialFilterCode } = Enums;
const {
  noMoreFilterToastFirst,
  noMoreFilterToastSecond,
  noMoreFilterToastEasyLife,
  vehicleAllText,
  vehicleText,
  noMoreFilterToastFirstForPage,
  noMoreFilterToastSecondForPage,
  noMoreFilterToastEasyLifeForPage,
} = ListConstants;
const { NavGroupCode, GroupBarEnums, FetchListPageType } = ListEnum;

export const setStatus = (data: any) => ({
  type: SET_STATUS,
  data,
});

export const getStatus = (data: any) => ({
  type: GET_STATUS,
  data,
});

export const setActiveGroupId = (data: any) => ({
  type: SET_GROUPID,
  data,
});

export const initActiveGroupId = data => ({
  type: INIT_SET_GROUPID,
  data,
});

export interface ListParamType {
  isHitCache?: boolean;
  isHitLimitCache?: boolean;
  type?: number;
  pageNum?: number;
  filters?: Array<string>;
  uniqRequestKey?: string;
  uniqSign?: string;
  isPreFetch?: boolean;
  response?: QueryProductsResponseType;
  params?: QueryProductsRequestType;
  isAutoChange?: boolean;
  isNotHitCacheKeyElse?: boolean;
  searchWord?: string;
  searchSuggestions?: SuggestionType[];
  callbackFun?: () => void;
}

export const fetchListBatchQuery = (data?: ListParamType) => {
  if (CarABTesting.isListInPage()) {
    const newData = {
      ...data,
      type: lodashGet(data, 'type') || FetchListPageType.Search,
    };
    return {
      type: FETCH_LIST_PAGE,
      data: newData,
    };
  }
  return {
    type: FETCH_LIST_BATCH,
    data,
  };
};

export const fetchListRecommend = (data?: ListParamType) => ({
  type: FETCH_LIST_RECOMMEND,
  data,
});

export const fetchListRecommendCallback = data => ({
  type: FETCH_LIST_RECOMMEND_CALLBACK,
  data,
});

export const setBatchRequest = data => ({
  type: SET_BATCH_REQUEST,
  data,
});

export const setActiveFilterBarCode = data => ({
  type: SET_ACTIVE_FILTER_BAR_CODE,
  data,
});

export const getNoMoreToastText = (
  activeGroupName: string = '',
  toGroupName = '',
) => {
  const isListPage = CarABTesting.isListInPage();
  const fisrt = isListPage
    ? noMoreFilterToastFirstForPage
    : noMoreFilterToastFirst;
  const secondText = isListPage
    ? noMoreFilterToastSecondForPage
    : noMoreFilterToastSecond;
  const easyLifeText = isListPage
    ? noMoreFilterToastEasyLifeForPage
    : noMoreFilterToastEasyLife;
  const second =
    toGroupName === GroupBarEnums.easyLife ? easyLifeText : secondText;
  switch (activeGroupName) {
    case GroupBarEnums.all:
      return `${fisrt}${vehicleAllText}${second}`;
    case GroupBarEnums.hot:
    case GroupBarEnums.easyLife:
      return `${fisrt}${activeGroupName}${vehicleAllText}${second}`;
    case GroupBarEnums.economy:
    case GroupBarEnums.comfort:
    case GroupBarEnums.luxury:
      return `${fisrt}${activeGroupName}${vehicleText}${second}`;
    case GroupBarEnums.empty:
      return '';
    default:
      return `${fisrt}${activeGroupName}${second}`;
  }
};

const updateSelectedFilterPage = data => ({
  type: UPDATE_SELECTED_FILTER,
  data,
});

export const updateSelectedFilterListNoRefresh = data => ({
  type: UPDATE_SELECTED_FILTER_LISTNOREFRESH,
  data,
});

export const updateSelectedFilter = data => {
  if (CarABTesting.isListInPage()) {
    return updateSelectedFilterPage(data);
  }
  const productGroups = getAllProductGroups(data);
  const { filterLabels = [], isFilter } = data;
  // 如果是用户点击的筛选，非url传参，则禁用置顶功能
  if (isFilter) {
    ListReqAndResData.setData(listStatusKeyList.filterBrand, '');
    ListReqAndResData.setData(listStatusKeyList.filterVendor, '');
  }
  let { activeGroupId = '' } = data;
  if (activeGroupId) {
    const lastFilterLabel = filterLabels[filterLabels.length - 1] || {};
    const { code } = lastFilterLabel;
    const curGroup =
      productGroups.find(item => item.groupCode === activeGroupId) || {};
    const allGroup = productGroups.find(item => item.groupCode === 'all') || {};
    const easyGroup =
      productGroups.find(item => item.groupCode === 'easyLife') || {};
    const { groupName = '', productList: curProductList = [] } = curGroup;
    const { productList: allProductList = [] } = allGroup;
    const { productList: easyLifeProductList = [] } = easyGroup;

    const curCount = curProductList.length;
    const allCount = allProductList.length;
    const easyCount = easyLifeProductList.length;

    const isLastFilterEasyLife = code === SpecialFilterCode.EASYLIFE;
    if (allCount !== 0 && curCount <= 0) {
      if (isLastFilterEasyLife && easyCount > 0) {
        CarLog.LogCode({
          name: '点击_列表页_无结果弹窗',

          isLastFilterEasyLife,
        });
        Toast.show(getNoMoreToastText(groupName, GroupBarEnums.easyLife));
        activeGroupId = NavGroupCode.easyLife;
      } else if (activeGroupId !== NavGroupCode.all) {
        CarLog.LogCode({
          name: '点击_列表页_无结果弹窗',

          isLastFilterEasyLife,
        });
        Toast.show(getNoMoreToastText(groupName));
        activeGroupId = NavGroupCode.all;
      }
    }
    CarLog.LogTrace({
      key: LogKey.c_rn_car_trace_click,
      info: {
        name: `点击_列表页_快速筛选_${code}`,
        info: {
          selectedResult: curCount === 0 ? 0 : 1,
        },
      },
    });
  }

  return {
    type: UPDATE_SELECTED_FILTER,
    data,
    activeGroupId,
  };
};

export const deleteSelectedFilter = data => ({
  type: DELETE_SELECTED_FILTER,
  data,
});

export const clearSelectedFilter = (data?) => ({
  type: CLEAR_SELECTED_FILTER,
  data,
});

export const setLocationAndDatePopIsShow = data => ({
  type: SET_LOCATIONDATEPOP_VISIBLE,
  data,
});

export const setAgePickerIsShow = data => ({
  type: SET_AGEPICKER_VISIBLE,
  data,
});

export const setAgeTipPopIsShow = data => ({
  type: SET_AGETIPPOP_VISIBLE,
  data,
});

export const setProgressIsFinish = data => ({
  type: SET_PROGRESS_ISFINISH,
  data,
});

export const setFilterModalIsShow = data => ({
  type: SET_SORTANDFILTER_VISIBLE,
  data,
});

export const reset = () => ({
  type: RESET,
});

export const setScrollViewHeight = data => ({
  type: SET_SCROLL_VIEW_HEIGHT,
  data,
});

export const setDriverLicensePopData = data => ({
  type: SET_DRIVERLICENSE_POP_DATA,
  data,
});

export const setSaleOutList = data => ({
  type: SET_SALEOUT_LIST,
  data,
});

export const addSaleOutList = data => ({
  type: ADD_SALEOUT_LIST,
  data,
});

export const removeSaleOutList = data => ({
  type: REMOVE_SALEOUT_LIST,
  data,
});

export const addVehicleSaleOutList = data => ({
  type: ADD_VEHICLE_SALEOUT_LIST,
  data,
});

export const fetchLimitContent = (data: boolean) => ({
  type: FETCH_LIMIT_CONTENT,
  data,
});

export const setLimitRulePopVisible = data => ({
  type: LIMITRULE_POP_VISIBLE,
  data,
});

export const setVehPopData = data => ({
  type: SET_VEHPOP_DATA,
  data,
});

export const getC2BOrderStatus = data => ({
  type: GET_C2B_ORDERSTATUS,
  data,
});

export const fetchIsdIMUrl = () => ({
  type: FETCH_ISD_IMURL,
});

export const setTimeOutPopData = data => ({
  type: SET_TIMEOUT_POP_DATA,
  data,
});

export const setVendorListModalData = data => ({
  type: SET_VENDORLISTMODAL_DATA,
  data,
});

export const setShowFilteredProgress = data => ({
  type: SET_SHOW_FILTERED_PROGRESS,
  data,
});

export const setEasyLifePopVisible = data => ({
  type: SET_EASYLIFE_POP_VISIBLE,
  data,
});

export const setFilterNoResult = data => ({
  type: SET_FILTER_NO_RESULT,
  data,
});

export const updateFetchCacheStatus = (data: any) => ({
  type: UPDATE_FETCH_CACHE_STATUS,
  data,
});

export const setLimitTipLoadFinish = (data: any) => ({
  type: SET_LIMITTIP_LOADFINISH,
  data,
});

export const preListFetch = (data: any) => ({
  type: PRE_FETCH_LIST_BATCH,
  data,
});

export const setTotalPriceModalData = data => ({
  type: SET_TOTALPRICEMODAL_DATA,
  data,
});

export const setTipPopData = data => ({
  type: SET_TIPPOP_DATA,
  data,
});

export const fetchListPage = (data?: ListParamType) => ({
  type: FETCH_LIST_PAGE,
  data,
});

export const setIsShowGroupChangeToast = (data: boolean) => ({
  type: SET_IS_GROUP_CHANGE_TOAST,
  data,
});

export const setFilterStatus = data => ({
  type: SET_FILTER_STATUS,
  data,
});

export const setGroupStatus = data => ({
  type: SET_GROUP_STATUS,
  data,
});

export const setPageStatus = data => ({
  type: SET_PAGE_STATUS,
  data,
});

export const preListPageFetch = (data?: { filters: Array<string> }) => ({
  type: PRE_FETCH_LIST_PAGE,
  data,
});

export const setNextPageStatus = data => ({
  type: SET_NEXT_PAGE_STATUS,
  data,
});

export const setCacheExpireModalIsShow = data => ({
  type: SET_CACHEEXPIRE_MODAL_VISIBLE,
  data,
});

export const setNoMoreToastText = data => ({
  type: SET_NO_MORE_TOAST_TEXT,
  data,
});

// 添加当前车型组的pageNum
export const updatePageNum = data => ({
  type: UPDATE_PAGE_NUM,
  data,
});

export const updateStatusBeforeFilterSearch = data => ({
  type: UPDATE_STATUS_BEFORE_FILTER_SEARCH,
  data,
});

export const updateStatusBeforeGroupSearch = () => ({
  type: UPDATE_STATUS_BEFORE_GROUP_SEARCH,
});

export const updateStatusBeforeNextPageSearch = () => ({
  type: UPDATE_STATUS_BEFORE_NEXT_PAGE_SEARCH,
});

export const handleResGroupIdAndFilters = () => ({
  type: HANDLE_RES_GROUPID_FILTERS,
});

export const setPageResStatus = data => ({
  type: SET_PAGE_RES_STATUS,
  data,
});

export const setIsShowToast = data => ({
  type: SET_IS_SHOW_TOAST,
  data,
});

export const setPriceSummaryModal = (
  visible: boolean,
  data?: any,
  secretBoxPriceModalVisible?: boolean,
) => {
  return {
    type: SET_PRICE_SUMMARY_MODAL,
    data: {
      visible,
      data,
      secretBoxPriceModalVisible,
    },
  };
};

export const setIsMoreAge = data => ({
  type: SET_IS_MORE_AGE,
  data,
});

export const setSecretBoxModalData = data => ({
  type: SET_SECRET_BOX_MODAL_DATA,
  data,
});

export const listGoToBooking = data => ({
  type: LIST_GO_TO_BOOKING,
  data,
});

export const goToBookingEasyLife2024 = data => ({
  type: LIST_GO_TO_BOOKING_EASYLIFE,
  data,
});

export const queryPackageComparison = data => ({
  type: QUERY_PACKAGE_COMPARISON,
  data,
});

export const setQueryPackageComparisonData = (
  status: 'pending' | 'error' | 'success',
  response: any,
) => ({
  type: SET_QUERY_PACKAGE_COMPARISON,
  data: { status, response },
});

export const setLicenseModalData = data => ({
  type: SET_LICENSE_MODAL_DATA,
  data,
});

export const setSelfServiceBannerInfo = data => ({
  type: SET_SELF_SERVICE_BANNER_INFO,
  data,
});

export const setUserBrowsingHistory = data => ({
  type: SET_USER_BROWSING_HISTORY,
  data,
});

export const setOsdUserBrowsingHistory = data => ({
  type: SET_OSD_USER_BROWSING_HISTORY,
  data,
});

export const setUserBrowsingHistoryCallback = data => ({
  type: SET_USER_BROWSING_HISTORY_CALLBACK,
  data,
});

export const setOsdUserBrowsingHistoryCallback = data => ({
  type: SET_OSD_USER_BROWSING_HISTORY_CALLBACK,
  data,
});

export const resetUserBrowsingHistory = () => ({
  type: RESET_USER_BROWSING_HISTORY,
});

export const fetchEasyLife2024Tags = () => ({
  type: FETCH_EASYLIFE_2024_TAGS,
});

export const setEasyLife2024Tags = (
  status: 'pending' | 'error' | 'success',
  response: any,
) => ({
  type: SET_EASYLIFE_2024_TAGS,
  data: { status, response },
});

export const setRecommendType = (data?: any) => ({
  type: SET_RECOMMEND_TYPE,
  data,
});

export const checkOsdPoi = (data?: any) => ({
  type: CHECK_OSD_POI,
  data,
});
export const setCheckOsdPOICallback = data => ({
  type: CHECK_OSD_POI_CALLBACK,
  data,
});
export const removeSoldOutAll = () => ({
  type: REMOVE_SALEOUT_ALL,
});

export const setOsdUserBrowsed = data => ({
  type: SET_OSD_USER_BROWSED,
  data,
});

export const queryListIpollConfig = (data?: any) => ({
  type: QUERY_LIST_IPOLL_CONFIG,
  data,
});

export const setListIpollConfig = (data?: any) => ({
  type: SET_LIST_IPOLL_CONFIG,
  data,
});
export const setListSearchWords = data => ({
  type: SET_LIST_SEARCH_WORDS,
  data,
});
