import {
  get as lodashGet,
  pull as lodashPull,
  pullAll as lodashPullAll,
  uniqBy as lodashUniqBy,
} from 'lodash-es';
import Util from '@c2x/apis/Util';
import { xShowToast } from '@ctrip/xtaro';

import {
  takeEvery,
  select,
  put,
  fork,
  takeLatest,
  delay,
} from 'redux-saga/effects';
import uuid from 'uuid';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  FETCH_LIST_BATCH,
  SET_GROUPID,
  DELETE_SELECTED_FILTER,
  FETCH_LIMIT_CONTENT,
  GET_C2B_ORDERSTATUS,
  FETCH_ISD_IMURL,
  PRE_FETCH_LIST_BATCH,
  ADD_SALEOUT_LIST,
  FETCH_LIST_PAGE,
  FETCH_LIST_RECOMMEND,
  PRE_FETCH_LIST_PAGE,
  UPDATE_SELECTED_FILTER,
  CLEAR_SELECTED_FILTER,
  HANDLE_RES_GROUPID_FILTERS,
  ADD_VEHICLE_SALEOUT_LIST,
  LIST_GO_TO_BOOKING,
  LIST_GO_TO_BOOKING_EASYLIFE,
  QUERY_PACKAGE_COMPARISON,
  SET_USER_BROWSING_HISTORY,
  FETCH_EASYLIFE_2024_TAGS,
  CHECK_OSD_POI,
  QUERY_LIST_IPOLL_CONFIG,
  SET_OSD_USER_BROWSING_HISTORY,
} from './Types';
import {
  ApiResCode,
  PageName,
  Platform,
  ListConstants,
  LogKey,
  FetchListPageType,
  NavGroupCode,
  GroupBarEnums,
  LogKeyDev,
  HomeConst,
} from '../../Constants/Index';
import {
  ListReqAndResData,
  ListResSelectors,
  removeListStatusData,
  listStatusKeyList,
} from '../../Global/Cache/Index';
import {
  setStatus,
  setBatchRequest,
  reset,
  updateSelectedFilter,
  fetchLimitContent,
  fetchListBatchQuery,
  fetchIsdIMUrl,
  updateFetchCacheStatus,
  setLimitTipLoadFinish,
  setActiveGroupId,
  setPageStatus,
  setFilterStatus,
  setGroupStatus,
  preListPageFetch,
  setNextPageStatus,
  setCacheExpireModalIsShow,
  fetchListPage,
  updatePageNum,
  updateStatusBeforeFilterSearch,
  updateStatusBeforeNextPageSearch,
  updateStatusBeforeGroupSearch,
  setTimeOutPopData,
  setPageResStatus,
  setNoMoreToastText,
  getNoMoreToastText,
  fetchListRecommend,
  fetchListRecommendCallback,
  setIsMoreAge,
  updateSelectedFilterListNoRefresh,
  setSelfServiceBannerInfo,
  setQueryPackageComparisonData,
  setUserBrowsingHistoryCallback,
  resetUserBrowsingHistory,
  setEasyLife2024Tags,
  setCheckOsdPOICallback,
  setListIpollConfig,
  setListSearchWords,
  setOsdUserBrowsingHistoryCallback,
} from './Actions';
import {
  getPickUpCityId,
  getC2BLocationAndDate,
  getPickUpTime,
  getPickUpCityName,
} from '../LocationAndDate/Selectors';
import {
  getBatchesRequest,
  getSelectedFilters,
  getActiveGroupId,
  getFilterLabels,
  getSortAndFilterVisible,
  getFilterNoResult,
  getUserBrowsingHistories,
  getCurSearchWordObj,
  getOsdUserBrowsingHistories,
} from './Selectors';
import {
  CarFetch,
  User,
  CarFetchHelper,
  Utils,
  Hooks,
  CarABTesting,
  CarABConstants,
  CarLog,
  Channel,
  AppContext,
  CarServerABTesting,
  GetABCache,
} from '../../Util/Index';
import { listPagingAgent } from '../../Util/ListPagingAgent';
import {
  packageListReqParam,
  packageLimitReqParam,
  getAllCarsConfig,
  getListReqCount,
  packageListPageReqParam,
  getPageCacheKey,
  getListBatchGroups,
  appendResPatch,
  setResVehicleAndPriceBatch,
  upDateMergeVehicleExtendInfos,
} from './Mappers';
import {
  hasGroupCode,
  getNameByCode,
  getBaseResData,
  getProductGroupInfo,
  getProductGroupIndex,
  getVendorListBasePageParam,
} from '../../Global/Cache/ListResSelectors';
import * as UBTLog from './UBTLog';
import { setUserTraceQueryVid } from '../../AppLoad';
import { validateTimeIsOutIsd } from '../../Helpers/Index';
import { ListTexts as Texts } from '../../Constants/TextIndex';
import { ILableCode } from '../../Constants/CommonEnums';
import Enums from '../../ComponentBusiness/Common/src/Enums';
import { ActionType } from '../../Types/ActionType';
import { fetchRanking } from '../Home/Actions';
import {
  getHomeLocationDatePopVisible,
  getPreListFetchFilterItems,
} from '../Home/Selectors';
import { getRankingReqParentRequestId } from '../../Global/Cache/HomeSelectors';
import { getQConfig } from '../Common/Selectors';
import { getVehicleGroupByVehicleList, verifyPointInfo } from './Method';
import { requestInfoType } from '../../Util/CarFetchHelper';
import {
  queryVehicleDetailList,
  setIsSecretBoxListToBook,
  setModalStatus,
  setUniqueCode,
  setVehicleListRequestAndResponse,
} from '../VendorList/Actions';
import {
  getFirstScreenParamToBooking,
  getVehicleDetailListResponse,
  getVendorInfoByUniqueCode,
} from '../VendorList/Selectors';
import {
  getNationalChainTag,
  getSecretBoxFirstScreenParamToBooking,
  getVehicleInfoByCode,
} from './VehicleListMappers';
import { QueryProductsResponseType } from '../../Types/Dto/QueryProductsType';
import { SuggestionTypeNumber } from '../../Pages/List/Components/SearchCar/Type';
import {
  getListStatusData,
  setListStatusData,
} from '../../Global/Cache/ListReqAndResData';

function hasIsFirstSign(res) {
  return res?.productGroups?.some(group =>
    group?.productList?.some(product =>
      product?.vendorPriceList.some(vendor => vendor?.extMap?.isFirst === '1'),
    ),
  );
}

const verifyResponseIsValid = response => {
  const isSuccess = lodashGet(response, 'baseResponse.isSuccess');
  const resCode = lodashGet(response, 'baseResponse.code');
  // 列表页接口缓存会跳过不合法响应，境外列表页提升项目中两批数据都需要缓存，
  // 所以做特殊处理，对列表页资源不做code 限制，只做 isSuccess 判断
  return (
    isSuccess &&
    resCode === ApiResCode.ListResCode.C200 &&
    !hasIsFirstSign(response)
  );
};

const addListResultMark = (
  res,
  hasRetry?: boolean,
  isSuccess?: boolean,
  hasResult?: boolean,
) => {
  /* eslint-disable max-len */
  const curHasRetry =
    hasRetry !== undefined
      ? hasRetry
      : !!lodashGet(res, 'appResponseMap.hasRetry');
  const curIsSuccess =
    isSuccess !== undefined
      ? isSuccess
      : !!lodashGet(res, 'baseResponse.isSuccess');
  const curHasResult =
    hasResult !== undefined
      ? hasResult
      : !!lodashGet(res, 'baseResponse.hasResult');

  const result = res || {};
  result.appResponseMap = result.appResponseMap || {};
  result.appResponseMap.hasRetry = curHasRetry;
  result.appResponseMap.isSuccess = curIsSuccess;
  result.appResponseMap.hasResult = curHasResult;
  return result;
};

// saga改造
function* updateSelectedFilterByCodeGroup(filters, activeGroupId) {
  const state = yield select();
  let newSelectedFilters = getSelectedFilters(state);
  const curFilters = filters.map(m => ({ code: m, isSelected: true }));
  curFilters.forEach(filter => {
    let { code = '' } = filter;
    code = Hooks.mappingNewFilterCode(code);
    // 如果有车型组，则设置置顶筛选车型组
    if (activeGroupId) {
      ListReqAndResData.setData(listStatusKeyList.filterGroupId, activeGroupId);
    }
    const name = getNameByCode(code);
    if (name) {
      const item = {
        code,
        name: getNameByCode(code),
        isSelected: !filter.isSelected,
      };
      newSelectedFilters = Hooks.updateSelectedBuildFilterItem({
        item,
        newSelectedFilters,
      });
    }
  });
  yield put(
    updateSelectedFilter({
      bitsFilter: newSelectedFilters.bitsFilter,
      filterLabels: newSelectedFilters.filterLabels,
      activeGroupId,
      isHideProgress: true,
    }),
  );
}

// 分析响应数据, 触发新事件
function* queryProductsCallback({ isError, param, res, error }) {
  /** *
   * 响应数据放弃的场景：
   * 1、如果是预请求的数据，不使用
   * 2、正常请求中，发送多批数据时，如果不是当前请求的，不使用
   * 特殊场景：返回的是发布之前已经缓存好的数据(一种是没有uniqRequestKey,另一种是没有needCallBack的标记)可以直接使用，因为只会缓存200的有效数据
   */
  const resUniqRequestKey =
    lodashGet(res, 'appResponseMap.uniqRequestKey') || '';
  const removeMarkUniqRequestKey = lodashGet(
    resUniqRequestKey.split('/'),
    '[0]',
  );
  const needCallBackMark = lodashGet(resUniqRequestKey.split('/'), '[1]');
  let isRealFetch = false;
  let hasNeedCallbackMark = false;
  if (resUniqRequestKey && needCallBackMark) {
    hasNeedCallbackMark = true;
    isRealFetch = needCallBackMark === 'true';
  }

  // 预请求的数据，不使用
  if (hasNeedCallbackMark && !isRealFetch) {
    return;
  }

  // 不是当前真正要的请求，不使用
  if (
    resUniqRequestKey &&
    // @ts-ignore
    removeMarkUniqRequestKey !== global.ListUniqRequestKey
  ) {
    return;
  }

  // 海外车型重构,将vehicleKey赋值给vehicleCode，保证vehicleCode的唯一性
  const isRefactor = res?.extras?.isVehicle2 === '1';
  if (isRefactor) {
    res?.vehicleList.forEach(v => {
      // eslint-disable-next-line no-param-reassign
      v.vehicleCode = v.vehicleKey;
    });
    res?.productGroups.forEach(group => {
      group?.productList.forEach(product => {
        // eslint-disable-next-line no-param-reassign
        product.vehicleCode = product.vehicleKey;
      });
    });
  }

  const prePageResData = ListResSelectors.getBaseResData();

  // 已缓存了200，直接忽略当前响应
  const preCode = lodashGet(prePageResData, 'baseResponse.code');
  const preProductsCount =
    lodashGet(prePageResData, 'productGroups.length') || 0;
  const hasPreRecommendInfo = !!prePageResData?.recommendInfo?.promptTitle;
  // 首批已经标识无结果推荐
  const hasNoResultRecommendSign = !!prePageResData?.extras?.hasListSign;
  if (
    GetABCache.isOSDListAllRefresh() &&
    preCode === ApiResCode.ListResCode.C200 &&
    preProductsCount > 0
  ) {
    return;
  }

  const state = yield select();
  const resCode = lodashGet(res, 'baseResponse.code');
  const isSuccess = lodashGet(res, 'baseResponse.isSuccess');
  const productCount = lodashGet(res, 'productGroups.length') || 0;
  const hasRecommendInfo = !!res?.recommendInfo?.promptTitle;
  const curGroupId = getActiveGroupId(state);
  const isMoreAge = res?.isMoreAge;
  // log
  UBTLog.LogListEachTrace(isError, param, res, error);

  // resCode为200的场景下拆分成两个条件判断的原因是：
  // ①需满足当第一批201有数据，但第二批200没数据时，页面仍需正常展示的场景
  // ②需满足当第一批201无数据，第二批也无数据时，页面的无结果推荐需读接口返回的
  // --> 不赋值的场景为第一批有数据(或者有推荐)第二批无数据(或者无推荐)
  if (
    !(
      (preProductsCount > 0 && productCount === 0) ||
      (hasPreRecommendInfo && !hasRecommendInfo) ||
      // 已经标记无结果推荐，不需要赋值
      hasNoResultRecommendSign
    )
  ) {
    // 境外性能提升项目，判断是第二批数据做拼接
    if (!GetABCache.isOSDListAllRefresh()) {
      let resData: QueryProductsResponseType = res;
      // 设置资源返回批次数据
      resData = setResVehicleAndPriceBatch(resData);
      ListReqAndResData.setData(
        [`${ListReqAndResData.keyList.listProductRes}${resCode}`],
        resData,
      );
      const firstRes = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductRes201,
      );
      const secondRes = ListReqAndResData.getData(
        ListReqAndResData.keyList.listProductRes200,
      );

      // 1. 一批数据返回，正常渲染
      // 2. 二批数据也返回，做往后拼接渲染
      // 3. 如果二批数据先于一批返回，缓存数据，不做数据更新
      if (firstRes) {
        // 两批数据都已返回
        if (secondRes) {
          resData = appendResPatch(firstRes, secondRes);
        }
        ListReqAndResData.setData(
          ListReqAndResData.keyList.listProductRes,
          resData,
        );
      }
    } else {
      ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, res);
    }
    // 境外请求无结果推荐
    if (
      resCode === ApiResCode.ListResCode.C200 &&
      CarServerABTesting.isRecommend()
    ) {
      yield put(fetchListRecommend({ response: res, params: param }));
    }
  }

  const nowProductCount =
    ListResSelectors.getBaseResData()?.productGroups?.length || 0;

  // 是否第一批数据
  // 是否已完成
  // 是否已失败
  const batchesRequest = getBatchesRequest(state);
  let nextBatches = [...batchesRequest];
  const fetchGroup = nextBatches.find(f => f.vendorGroup === param.vendorGroup);
  if (!fetchGroup) {
    nextBatches.push({
      vendorGroup: param.vendorGroup,
      resCode,
      result: isSuccess ? 1 : -1,
    });
  }

  let progress = 0;
  if (resCode === ApiResCode.ListResCode.C200 && nowProductCount > 0) {
    progress = 1;
  } else {
    // 计算当前响应的总次数
    const totalCount = nextBatches.length;
    if (totalCount >= getListReqCount()) progress = 1;
    else if (nowProductCount > 0)
      progress = ListConstants.hasRequestCompleteProgress;
  }
  const isFail =
    progress === 1 && preProductsCount === 0 && nowProductCount === 0;

  if (progress === 1) {
    let initGId = getAllCarsConfig().groupCode;
    // 处理路由中带的筛选条件
    if (
      AppContext.RouterLoader.List &&
      !AppContext.RouterLoader.List.isDispatch
    ) {
      const { groupCode, filters } = AppContext.RouterLoader.List;
      if (groupCode && hasGroupCode(groupCode)) {
        initGId = groupCode;
      }
      if (filters && filters.length > 0) {
        yield updateSelectedFilterByCodeGroup(filters, initGId);
      }
      AppContext.setRouterListLoaderDispatch(true);
    }
    // 列表页提升项目要求二批数据返回不做车型组 Tab 初始化
    if (GetABCache.isOSDListAllRefresh() && curGroupId !== initGId) {
      yield put(setActiveGroupId({ activeGroupId: initGId }));
    }
  }

  yield put(
    setStatus({
      isFail,
      progress,
      isLoading: progress === 0,
      isError,
    }),
  );
  if (Utils.isCtripOsd()) {
    yield put(setIsMoreAge(isMoreAge));
  }

  if (nextBatches.length !== batchesRequest.length) {
    if (nextBatches.length >= getListReqCount()) nextBatches = [];
    yield put(setBatchRequest(nextBatches));
  }

  // 如果有res.extras?.isRecMulti字段则说明是调用新版无结果推荐接口，
  // 则需要将该埋点的触发时机放到无结果推荐接口里, 应为动态半径泛化算有结果，所以需要将埋点触发的时机放到调完推荐接口之后
  if (progress === 1 && !(res.extras?.isRecMulti === '1')) {
    UBTLog.LogListFinalTrace(
      param,
      res,
      res?.appResponseMap?.isFromCache,
      isError,
      null,
      error,
    );
  }
}

// 拼装请求参数 + 发送请求
function* queryProducts(data) {
  const { isNeedCallBack, uniqRequestKey } = data;
  const uniqRKWithMark = Utils.appendMarkToUniqRequestKey(
    uniqRequestKey,
    isNeedCallBack,
  );
  const curState = yield select();
  const param = packageListReqParam(
    curState,
    data,
    isNeedCallBack,
    uniqRKWithMark,
  );
  if (isNeedCallBack) {
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductReq, param);
  }
  const omit = ['now', 'requestId', 'appType', 'uniqRequestKey', 'baseRequest'];

  const verifyRequestParameters = () => {
    const { pickupPointInfo, returnPointInfo } = param || {};
    return verifyPointInfo(pickupPointInfo) && verifyPointInfo(returnPointInfo);
  };

  const parameter = CarFetchHelper.parameterBuilder({
    param,
    cachePolicy: {
      enableCache: true,
      isFromStorage: true,
      isLoadStorageThenRemove: true,
      omit,
    },
    verifyResponseIsValid,
    verifyRequestParameters,
  });

  let isError = false;
  let errorInfo = null;
  const res = yield CarFetchHelper.withRetry(CarFetch.getListProduct)(
    { maxCount: 1 },
    parameter,
  ).catch(error => {
    if (isNeedCallBack) {
      isError = true;
      errorInfo = error;
    }
  });

  if (isNeedCallBack) {
    const packageRes = isError
      ? addListResultMark({ error: errorInfo }, true, false, false)
      : addListResultMark(res);
    yield queryProductsCallback({
      isError,
      param,
      res: packageRes,
      error: errorInfo,
    });
  }
}

function* queryBatchProducts(isNeedCallBack) {
  if (isNeedCallBack) {
    yield put(reset());
  }
  const requestId = uuid();
  setUserTraceQueryVid();
  const batchGroups = getListBatchGroups();
  const data = {
    requestId,
    isNeedCallBack,
    vendorGroup: 0,
    uniqRequestKey: '',
  };
  if (isNeedCallBack) {
    // @ts-ignore
    global.ListUniqRequestKey = Utils.getUniqRequestKeyWithEnv(); // 生成唯一请求key
    // @ts-ignore
    data.uniqRequestKey = global.ListUniqRequestKey;
  }
  // 第一批
  yield fork(queryProducts, data);
  // 第二批
  if (batchGroups.length === 2) {
    yield fork(queryProducts, { ...data, vendorGroup: 1 });
  }
}

// 提前请求列表页数据
export function* apiListPreBatchQuery() {
  yield takeEvery(PRE_FETCH_LIST_BATCH, function* logic(action: ActionType) {
    try {
      const { prefetchType } = action.data || {};
      const { channelId } = AppContext.MarketInfo;
      let isPreFetch = true;

      yield delay(10);

      const state = yield select();
      const config = getQConfig(state);
      const { preFetchListChannelIds = [] } = config || {};

      // 根据配置的channelid来判断是否启用列表页预加载
      const allowedPreFetchsActions = [
        HomeConst.PRE_FETCH_TYPE.HomePreFetch,
        HomeConst.PRE_FETCH_TYPE.HomePreFetchAfterLogin,
      ];

      if (
        Utils.isCtripIsd() &&
        allowedPreFetchsActions.includes(prefetchType)
      ) {
        isPreFetch = preFetchListChannelIds?.includes(channelId);
      }
      if (isPreFetch) {
        if (CarABTesting.isListInPage()) {
          const filters = getPreListFetchFilterItems(state);
          yield put(preListPageFetch({ filters }));
        } else {
          yield queryBatchProducts(false);
        }
      }

      // 境外无限行
      if (Utils.isCtripIsd()) {
        yield put(fetchLimitContent(false));
      }
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_trace_prefetch,
        info: {
          channelId,
          preFetchListChannelIds,
          isPreFetch: isPreFetch ? '1' : '0',
          prefetchType,
          isIsd: Utils.isCtripIsd(),
          actionType: 'apiListPreBatchQuery',
        },
      });
    } catch (error) {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_trace_prefetch,
        info: {
          error,
          isIsd: Utils.isCtripIsd(),
          actionType: 'apiListPreBatchQuery_Error',
        },
      });
    }
  });
}

// 真正请求列表页
export function* apiListBatchQuery() {
  yield takeEvery(FETCH_LIST_BATCH, function* logic(action: ActionType) {
    const { isHitCache, isHitLimitCache } = action.data || {};
    if (Utils.isCtripIsd()) {
      if (!isHitLimitCache) {
        yield put(fetchLimitContent(true));
      }
      yield put(fetchIsdIMUrl());
    }
    if (GetABCache.isAiSort2()) {
      // AI排序实时正反馈处理：在FETCH_LIST_BATCH中处理
      const aiUpFeedBack = getListStatusData(listStatusKeyList.aiUpFeedBack);
      if (
        aiUpFeedBack &&
        aiUpFeedBack.aiUpFeedBackAction &&
        aiUpFeedBack.showRemoveCache
      ) {
        // 使前端缓存无效，确保后续请求获取最新数据
        AppContext.setUserFetchCacheId({
          actionType: 'aiUpFeedBackAction',
        });
        setListStatusData(listStatusKeyList.aiUpFeedBack, null);
      }
    }
    if (isHitCache) {
      yield put(
        updateFetchCacheStatus({
          isLoading: false,
          isFail: false,
          progress: 1,
          progressIsFinish: true,
        }),
      );
    } else {
      yield queryBatchProducts(true);
    }
  });
}

export function* deleteSelectedFilter() {
  yield takeEvery(DELETE_SELECTED_FILTER, function* logic(action: ActionType) {
    const data = action.data || {};
    const { deleteName } = data;
    let { deleteCode } = data;
    const state = yield select();
    const newSelectedFilters = [...state.List.selectedFilters.bitsFilter];
    const newSelectedLabels = [...state.List.selectedFilters.filterLabels];
    let newSelectedPrices = state.List.selectedFilters.priceFilter;
    const newSelctedComments = [...state.List.selectedFilters.commentFilter];

    const deleteLabels = newSelectedLabels.filter(
      label =>
        label &&
        (label.code === deleteCode ||
          (!!deleteName && label.name === deleteName)),
    );
    if (deleteLabels.length > 1) {
      deleteCode = deleteLabels.map(it => it.code);
    }

    deleteLabels.forEach(deleteLabel => {
      if (deleteCode && deleteCode.indexOf('Price') > -1) {
        newSelectedPrices = [];
        lodashPull(newSelectedLabels, deleteLabel);
        lodashPull(newSelectedFilters, deleteCode);
      } else {
        if (Array.isArray(deleteCode)) {
          lodashPullAll(newSelectedFilters, deleteCode);
        } else {
          lodashPull(newSelectedFilters, deleteCode);
        }
        lodashPull(newSelectedLabels, deleteLabel);
      }
    });
    yield put(
      updateSelectedFilter({
        priceFilter: newSelectedPrices,
        bitsFilter: newSelectedFilters,
        filterLabels: newSelectedLabels,
        commentFilter: newSelctedComments,
      }),
    );
  });
}

const validateCanSetLimitData = (state, res) => {
  const curCityId = getPickUpCityId(state);
  const curCityName = getPickUpCityName(state);
  const resCityId = res?.cityId;
  const resCityName = res?.cityName;
  return curCityId === resCityId && curCityName === resCityName;
};

export function* getLimitContent() {
  yield takeEvery(FETCH_LIMIT_CONTENT, function* logic(action: ActionType) {
    const state = yield select();
    const isUpdate = action.data;
    const param = packageLimitReqParam(state);

    const parameter = CarFetchHelper.parameterBuilder({
      param,
      cachePolicy: {
        enableCache: true,
        omit: ['baseRequest'],
      },
      verifyResponseIsValid: () => true,
      verifyResponseIsSuccess: res => !!res?.baseResponse?.isSuccess,
    });
    const res = yield CarFetch.getLimitContent(parameter).catch(() => {});
    const curState = yield select();
    if (isUpdate && validateCanSetLimitData(curState, res)) {
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listLimitRuleRes,
        res,
      );
      yield put(setLimitTipLoadFinish(true));
    }
  });
}

function* getC2BStatus() {
  yield takeLatest(GET_C2B_ORDERSTATUS, function* logic(action: ActionType) {
    let isLogin = yield User.isLogin();
    if (!isLogin) {
      isLogin = yield User.toLogin();
    }

    if (isLogin) {
      const res = yield CarFetch.getC2BStatus().catch(() => {});
      const callback = action.data;
      const curState = yield select();
      const isdLocationAndDate = getC2BLocationAndDate(curState);
      let url = '';
      let jumpCallback = () => {};
      const backPageName = PageName.List;

      if (res.status === 0) {
        const { pcid, pcname, ptime, rtime, poiinfo } = isdLocationAndDate;
        const param = {
          requireParam: {
            pcid,
            pcname,
            ptime,
            rtime,
            poiinfo,
          },
          backPageName,
        };
        url = Platform.CAR_CROSS_URL.C2B_REQUIREMENT.ISD;
        url += `&channelid=${AppContext.MarketInfo.channelId}
              &aid=${AppContext.MarketInfo.aId}&sid=${AppContext.MarketInfo.sId}
              &sparam=${encodeURIComponent(
                Util.base64Encode(JSON.stringify(param)),
              )}`;
        jumpCallback = () => {
          fetchListBatchQuery();
        };
      } else if (res.status === 1) {
        const param = {
          oid: res.orderId,
          backPageName,
        };
        url = Platform.CAR_CROSS_URL.C2B_FEEDBACK.ISD;
        url += `&channelid=${AppContext.MarketInfo.channelId}
              &aid=${AppContext.MarketInfo.aId}&sid=${AppContext.MarketInfo.sId}
              &sparam=${encodeURIComponent(
                Util.base64Encode(JSON.stringify(param)),
              )}`;
      } else {
        const param = {
          oid: res.orderId,
          backPageName,
        };
        url = Platform.CAR_CROSS_URL.ORDERDETAIL.ISD;
        url += `&channelid=${AppContext.MarketInfo.channelId}
              &aid=${AppContext.MarketInfo.aId}&sid=${AppContext.MarketInfo.sId}
              &sparam=${encodeURIComponent(
                Util.base64Encode(JSON.stringify(param)),
              )}`;
      }

      callback(url, jumpCallback);
    }
  });
}

export function* getIsdImURL() {
  yield takeEvery(FETCH_ISD_IMURL, function* logic() {
    const param = {
      pageId: Channel.getPageId().List.ID,
    };

    const parameter = CarFetchHelper.parameterBuilder({
      param,
      cachePolicy: { enableCache: true },
    });
    const res = yield CarFetch.getImUrl(parameter).catch(() => {});

    const imUrl = lodashGet(res, 'appUrl');
    AppContext.setISDIMurl(imUrl);
  });
}

export function* addSaleOutList() {
  yield takeEvery(
    [ADD_SALEOUT_LIST, ADD_VEHICLE_SALEOUT_LIST],
    function* logic() {
      yield AppContext.setUserFetchCacheId({
        actionType: 'addSaleOutList',
      });
    },
  );
}

// 列表页分页加载-预请求
export function* apiPreListPageQuery() {
  yield takeEvery(PRE_FETCH_LIST_PAGE, function* logic(action: ActionType) {
    setUserTraceQueryVid();
    const state = yield select();
    const param = packageListPageReqParam(state, {
      ...action.data,
      isPreFetch: true,
      type: FetchListPageType.Pre_Search,
    });
    // @ts-ignore
    const { filters } = action.data || {};

    listPagingAgent.precache(param);

    // 实际请求参数
    const listRealRequestId = AppContext.queryProductsRequestId;
    const rankingParentRequestId = getRankingReqParentRequestId();
    const isLocationPopVisible = getHomeLocationDatePopVisible(state);

    // 筛选项更改不刷新榜单
    // 搜索条件弹层打开时切换条件不请求榜单
    if (
      CarABConstants.isIsdHomeCombine() &&
      !filters &&
      // listRealRequestId !== rankingParentRequestId && // 重进首页 queryProducts命中缓存未发起榜单请求
      !isLocationPopVisible
    ) {
      yield put(
        fetchRanking({
          // 若已有真实请求(pending状态或者命中缓存)，则优先使用缓存的requestId请求
          parentRequestId: listRealRequestId || param?.requestId,
        }),
      );
    }
    // 记录预加载相同搜索条件，仅发起一次真实请求次数，实验后决定listRealRequestId !== rankingParentRequestId是否需要
    // 判断依据 listRealRequestId(本次预加载请求) === rankingParentRequestId(上一次预加载请求)
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_list_prefetch_requestid,
      info: {
        listRealRequestId,
        rankingParentRequestId,
        isSameRequestId: listRealRequestId === rankingParentRequestId,
        isLocationPopVisible,
      },
    });
  });
}

export const updateRes = (res, curActiveGroupId) => {
  const lastRes = getBaseResData();
  const newRes = { ...lastRes };
  const listNeedUpdateField = ApiResCode.ListNeedUpdateField.concat(
    ApiResCode.ListNeedUpdateField2,
  );
  listNeedUpdateField.forEach(field => {
    newRes[field] = lodashGet(res, field);
  });
  // 如果有筛选无结果推荐，将推荐内容追加到当前车型组下
  if (res?.filteredRecommendProducts?.length > 0) {
    const groupIdIndex = getProductGroupIndex(curActiveGroupId);
    if (groupIdIndex > -1) {
      const newProductList = res?.filteredRecommendProducts;
      let lastProductGroupInfo = newRes.productGroups[groupIdIndex];
      // 当前车型组无结果
      if (!(lastProductGroupInfo?.productList?.length > 0)) {
        lastProductGroupInfo = {
          ...lastProductGroupInfo,
          productList: newProductList,
        };
        newRes.productGroups[groupIdIndex] = lastProductGroupInfo;
        newRes.productGroupsHashCode = uuid();
      }
    }
  }
  ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, newRes);
};

// 在指定车型组中，追加productList(除对当前车型组数据进行追加外，其它需更新的字段，都需要更新)
const appendProductList = (groupId, res) => {
  const lastRes = getBaseResData();
  const newRes = { ...lastRes };
  newRes.productGroups = newRes.productGroups ? [...newRes.productGroups] : [];
  const newProductGroupInfo = getProductGroupInfo(groupId, res);
  if (newProductGroupInfo) {
    const groupIdIndex = getProductGroupIndex(groupId);
    if (groupIdIndex > -1) {
      const newProductList = lodashGet(newProductGroupInfo, 'productList');
      let lastProductGroupInfo = newRes.productGroups[groupIdIndex];
      lastProductGroupInfo = {
        ...lastProductGroupInfo,
        productList: lastProductGroupInfo?.productList?.concat(newProductList),
      };
      newRes.productGroups[groupIdIndex] = lastProductGroupInfo;
    }
  }
  ApiResCode.ListNeedUpdateField.forEach(field => {
    if (!ApiResCode.ListNextPageExpField.includes(field)) {
      newRes[field] = lodashGet(res, field);
    }
  });

  // 2022-2-14 报文精简，需对”storeList“、“vehicleList”进行拼接
  // 详情可查看http://conf.ctripcorp.com/pages/viewpage.action?pageId=919511187
  newRes.storeList = lodashUniqBy(
    lastRes.storeList?.concat(res?.storeList),
    'storeCode',
  );
  newRes.vehicleList = lodashUniqBy(
    lastRes.vehicleList?.concat(res?.vehicleList),
    'vehicleCode',
  );
  newRes.mergeVehicleExtendInfos = upDateMergeVehicleExtendInfos(lastRes, res);
  ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, newRes);
};

// 无少结果推荐接口，追加接口返到原有列表页数据中
export const appendRecommendList = res => {
  try {
    const lastRes = getBaseResData();
    const newRes = { ...lastRes };
    // 追加productGroups节点
    const {
      productGroups: originProductGroups = [],
      storeList: originStoreList = [],
      vehicleList: originVehicleList = [],
    } = newRes;
    const {
      productGroups: newProductGroups = [],
      storeList: newStoreList = [],
      vehicleList: newVehicleList = [],
      recommendProductInfo = [],
    } = res;
    const tempGroups = [...originProductGroups, ...newProductGroups];
    // 如果列表页无结果且推荐接口无结果，则展示推荐接口的无结果信息
    if (tempGroups.length <= 0 && res.recommendInfo) {
      newRes.recommendInfo = res.recommendInfo;
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listProductRes,
        newRes,
      );
      return;
    }
    const originAll =
      originProductGroups.find(item => item.groupCode === NavGroupCode.all) ||
      [];
    const recommendAll =
      newProductGroups.find(item => item.groupCode === NavGroupCode.all) || [];

    const groupInfo = {};
    const productGroups = [];
    // 将原有车型组进行分组，获取分组后的车型code集合
    let vehicleGroup = getVehicleGroupByVehicleList(null, newRes?.vehicleList);
    // 将所有车型进行分组，获取分组后的车型code集合
    vehicleGroup = getVehicleGroupByVehicleList(vehicleGroup, res?.vehicleList);

    tempGroups.forEach(item => {
      const newGroup = { ...item };
      const { groupCode, productList = [] } = newGroup;
      const curIndex = groupInfo[groupCode];
      if (!(curIndex >= 0)) {
        groupInfo[groupCode] = Object.keys(groupInfo).length;
        const originGroupProductList =
          originAll?.productList?.filter(oItem =>
            vehicleGroup[groupCode]?.includes(oItem.vehicleCode),
          ) || [];
        const appendGroupProductList =
          recommendAll?.productList?.filter(aItem =>
            vehicleGroup[groupCode]?.includes(aItem.vehicleCode),
          ) || [];
        // 追加的车型数据mapping推荐的修改时间，修改地点数据
        const mappingProduct = lodashUniqBy(
          productList.concat(originGroupProductList, appendGroupProductList),
          'vehicleCode',
        );
        const newProduct = [];
        mappingProduct.forEach(product => {
          const { recommendType } = product;
          const recommendInfo =
            recommendProductInfo.find(
              pItem => pItem.recommendType === recommendType,
            ) || {};
          const {
            recommendation,
            subStrategyType,
            availableLocation,
            latitude,
            longitude,
            cName,
            cid,
          } = recommendInfo;
          newProduct.push({
            subStrategyType,
            pickUpAvailableTime: recommendation?.pickUpAvailableTime,
            returnAvailableTime: recommendation?.returnAvailableTime,
            availableLocation,
            longitude,
            latitude,
            cid,
            cname: cName,
            ...product,
          });
        });
        productGroups.push(newGroup);
        productGroups[groupInfo[groupCode]].productList = newProduct;
        productGroups[groupInfo[groupCode]].hasResult = newProduct.length > 0;
      } else if (newGroup.dailyPrice < productGroups[curIndex].dailyPrice) {
        // 已存在车型组时，对比两个相同车型组的最低价
        productGroups[curIndex].dailyPrice = newGroup.dailyPrice;
      }
    });
    newRes.productGroups = productGroups;

    // storeCode追加
    newRes.storeList = lodashUniqBy(
      originStoreList.concat(newStoreList),
      'storeCode',
    );
    // vehicleList追加
    newRes.vehicleList = lodashUniqBy(
      originVehicleList.concat(newVehicleList),
      'vehicleCode',
    );
    newRes.mergeVehicleExtendInfos = upDateMergeVehicleExtendInfos(
      lastRes,
      res,
    );
    // 更新车型组缓存Key
    newRes.productGroupsHashCode = uuid();
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, newRes);
    // eslint-disable-next-line no-empty
  } catch (e) {}
};

// 处理拿到响应后，最新的车型组+筛选项处理
const getNewGroupIdAndFilters = curActiveGroupId => {
  let newGroupId = curActiveGroupId || 'all';
  const newFilterLabels = [];
  const newBitFilter = [];
  let filterNoResult;

  const param = ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductReq,
  );
  const res = ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductRes,
  );
  const resFilters = lodashGet(res, 'filters');
  const productGroupCodeUesd = lodashGet(res, 'productGroupCodeUesd');

  // 先处理响应返回的值
  if (productGroupCodeUesd && curActiveGroupId !== productGroupCodeUesd) {
    newGroupId = productGroupCodeUesd;
  }
  if (resFilters && resFilters.length > 0) {
    resFilters.forEach(code => {
      newFilterLabels.push({ code, name: getNameByCode(code) });
      newBitFilter.push(code);
    });
  }

  // 处理首页选中筛选项但响应中无筛选项的场景
  const paramFilters = lodashGet(param, 'filters') || [];
  ApiResCode.homeFilters.forEach(({ code, name }) => {
    if (paramFilters.includes(code) && !getNameByCode(code)) {
      newBitFilter.push(code);
      newFilterLabels.push({
        code,
        name,
      });
      filterNoResult = true;
    }
  });

  return {
    groupId: newGroupId,
    bitsFilter: newBitFilter,
    filterLabels: newFilterLabels,
    filterNoResult,
  };
};
interface PageResStatusType {
  activeGroupId?: string;
  selectedFilters?: any;
  filterNoResult?: boolean;
}

const getPageResStatus = state => {
  const curActiveGroupId = getActiveGroupId(state);
  const newStatus: PageResStatusType = {};
  const {
    groupId,
    bitsFilter: newBitFilter,
    filterLabels: newFilterLabels,
    filterNoResult,
  } = getNewGroupIdAndFilters(curActiveGroupId);
  if (groupId) {
    newStatus.activeGroupId = groupId;
  }

  if (newBitFilter.length > 0) {
    newStatus.selectedFilters = {
      ...getSelectedFilters(state),
      bitsFilter: newBitFilter,
      filterLabels: newFilterLabels,
    };
  }

  if (filterNoResult) {
    newStatus.filterNoResult = true;
  }

  return newStatus;
};

const showToast = (isFromSearch, isFromCache) => {
  if (isFromSearch && !isFromCache) {
    xShowToast({ title: '库存、报价可能发生变化，已重新加载', duration: 3000 });
  }
};

// 若是实时请求，则触发埋点
const handleLog = ({
  param,
  res,
  isFromCache,
  isError,
  isFromSearch,
  searchType,
  error,
}) => {
  if (isFromSearch && !isFromCache) {
    UBTLog.LogListFinalTrace(
      param,
      res,
      isFromCache,
      isError,
      searchType,
      error,
    );
  }
  // 列表页用户筛选变更搜索结果埋点
  if (searchType === FetchListPageType.Filter_Search) {
    UBTLog.LogListFilterSearchTrace(param, res);
  }
};

// 记录置顶参数
const recordTopParam = groupId => {
  const reqParam = ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductReq,
  );
  const tops = lodashGet(reqParam, 'tops');
  if (tops) {
    ListReqAndResData.setData(listStatusKeyList.routeTopParam, {
      groupId,
      tops,
    });
  }
};

// 筛选无结果后,车型组联动处理
function* filterNoResultHandle({
  activeGroupId,
  filterLabels,
  res,
  isRealSearch,
  filterModalIsShow,
  filterNoResult,
}) {
  // 首页送车上门，列表页无此筛选项的特殊处理
  if (filterNoResult && filterModalIsShow) {
    yield put(setNoMoreToastText(ListConstants.noMoreFilterText));
    return;
  }
  const productGroups = lodashGet(res, 'productGroups');
  let noMoreToastText = '';
  if (
    !filterNoResult &&
    activeGroupId &&
    productGroups &&
    !CarServerABTesting.isFilteredRecommendAb() // 如果是筛选无结果推荐B版，则不进行车型组联动处理
  ) {
    const lastFilterLabel = filterLabels[filterLabels.length - 1] || {};
    const { code } = lastFilterLabel;

    const curGroup =
      productGroups.find(item => item.groupCode === activeGroupId) || {};
    const allGroup =
      productGroups.find(item => item.groupCode === NavGroupCode.all) || {};
    const easyGroup =
      productGroups.find(item => item.groupCode === NavGroupCode.easyLife) ||
      {};
    const { groupName = '' } = curGroup;

    const allNoResult =
      !allGroup.hasResult && !isRealSearch && filterModalIsShow;
    if (allGroup.hasResult && !curGroup.hasResult) {
      const isLastFilterEasyLife =
        code === Enums.SpecialFilterCode.EASYLIFE ||
        code === Enums.SpecialFilterCode.EASYLIFE_NEW;
      let nextGroupId = '';
      if (
        isLastFilterEasyLife &&
        activeGroupId !== NavGroupCode.easyLife &&
        easyGroup.hasResult
      ) {
        if (!isRealSearch) {
          noMoreToastText = getNoMoreToastText(
            groupName,
            GroupBarEnums.easyLife,
          );
        }
        nextGroupId = NavGroupCode.easyLife;
        /* eslint-disable max-len */
      } else if (
        isLastFilterEasyLife &&
        activeGroupId !== NavGroupCode.easyLife &&
        !easyGroup.hasResult
      ) {
        if (!isRealSearch) {
          noMoreToastText = getNoMoreToastText(groupName);
        }
        nextGroupId = NavGroupCode.all;
      } else if (!isLastFilterEasyLife && activeGroupId !== NavGroupCode.all) {
        if (!isRealSearch) {
          noMoreToastText = getNoMoreToastText(groupName);
        }
        nextGroupId = NavGroupCode.all;
      }

      if (nextGroupId) {
        CarLog.LogCode({
          name: '点击_列表页_无结果弹窗',

          isLastFilterEasyLife,
        });
        yield put(setNoMoreToastText(noMoreToastText));
        yield put(setActiveGroupId({ activeGroupId: nextGroupId }));
        yield put(
          fetchListPage({ type: FetchListPageType.Vehcile_Group_Search }),
        );
      }
    } else if (allNoResult) {
      yield put(setNoMoreToastText(ListConstants.noMoreFilterText));
    }
  }
}

function* listPageQueryCallback(
  { type, isError, param, res, error, isAutoChange },
  callbackFun,
) {
  UBTLog.LogPriceExposureOfMarketing(res);
  AppContext.setRouterListLoaderDispatch(true);
  const isSuccess = !!lodashGet(res, 'baseResponse.isSuccess');
  const errorCode = lodashGet(res, 'baseResponse.errorCode');
  const state = yield select();
  const curActiveGroupId = getActiveGroupId(state);
  const isFilterFail =
    !isSuccess && errorCode !== ApiResCode.ListResErrorCode.E1000002;
  const isFromSearch = !!lodashGet(res, 'isFromSearch');
  const isFromCache = !!lodashGet(res, 'appResponseMap.isFromCache');
  const isRealSearch = isFromSearch && !isFromCache;
  const productCount = lodashGet(res, 'productGroups.length') || 0;
  const toastInfo = lodashGet(res, 'toastInfo');
  // 自助取还Banner信息
  const selfServiceBannerInfo =
    res?.promptInfos?.find(
      f => f?.type === ApiResCode.ListPromptType.SelfServiceBanner,
    ) || null;
  yield put(setSelfServiceBannerInfo(selfServiceBannerInfo));

  switch (type) {
    case FetchListPageType.Search:
    case FetchListPageType.Cache_Expire_Search:
      AppContext.setEasyLifeSwitch(res?.extras?.packageLevelSwitch);
      AppContext.setGoodsShelvesTwoSwitch(res?.extras?.goodsShelvesTwoSwitch);
      AppContext.setGoodsShelvesTwoABVersion(
        res?.extras?.goodsShelvesTwoABVersion,
      );
      AppContext.setIsdSearchCar(res?.extras?.searchSuggestSwitch);
      ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, res);
      // eslint-disable-next-line no-case-declarations
      const resStatus = getPageResStatus(state);
      yield put(
        setPageStatus({
          ...resStatus,
          isFail: productCount === 0,
          isLoading: false,
          progress: 1,
          progressIsFinish: true,
          isShowToast: true,
        }),
      );
      recordTopParam(lodashGet(resStatus, 'activeGroupId') || curActiveGroupId);
      UBTLog.LogListFinalTrace(param, res, isFromCache, isError, type, error);
      break;
    case FetchListPageType.Filter_Search:
    case FetchListPageType.Search_Word_Search:
      if (!isFilterFail) {
        updateRes(res, curActiveGroupId);
      }
      yield put(
        setFilterStatus({
          isFilterLoading: false,
          isFilterFail,
          isShowToast: isSuccess,
        }),
      );
      // 筛选无结果处理
      yield filterNoResultHandle({
        activeGroupId: curActiveGroupId,
        filterLabels: getFilterLabels(state),
        res,
        isRealSearch,
        filterModalIsShow: getSortAndFilterVisible(state),
        filterNoResult: getFilterNoResult(state),
      });
      showToast(isFromSearch, isFromCache);
      handleLog({
        param,
        res,
        isFromCache,
        isError,
        isFromSearch,
        searchType: type,
        error,
      });
      break;
    case FetchListPageType.Vehcile_Group_Search:
      if (!isFilterFail) {
        updateRes(res, curActiveGroupId);
      }
      yield put(
        setGroupStatus({
          isShowGroupChangeToast: isAutoChange,
          isGroupLoading: false,
          isGroupFail: isFilterFail,
        }),
      );
      showToast(isFromSearch, isFromCache);
      handleLog({
        param,
        res,
        isFromCache,
        isError,
        isFromSearch,
        searchType: type,
        error,
      });
      break;
    case FetchListPageType.Next_Page_Search:
      // 服务端通知缓存变了之后，弹窗提示
      if (errorCode === ApiResCode.ListResErrorCode.********) {
        yield put(setCacheExpireModalIsShow(true));
      } else {
        // 保留其他车型组的数据，在当前车型组后追加
        if (isSuccess) {
          if (res) {
            appendProductList(curActiveGroupId, res);
          }
        }
        yield put(
          setNextPageStatus({
            isNextPageLoading: false,
            isNextPageFail: !isSuccess,
          }),
        );
        // 失败情况下，pageNum需要还原
        if (!isSuccess) {
          yield put(updatePageNum({ type: 'minus' }));
        }
      }
      UBTLog.LogNextPagePerformance(res);
      break;
    default:
      break;
  }

  // 榜单车型带入列表页-异常场景
  if (toastInfo) {
    const { code, message } = toastInfo;
    CarLog.LogTrace({
      key: LogKey.c_car_trace_module_exposure,
      info: {
        // 40001-报价变化 40002-报价无库存，车型有库存 40003-报价无库存，车型无库存
        errorCode: code,
        name: Texts.errorToastLogName,
      },
    });
    xShowToast({ title: message, duration: 3000 });
  }
  UBTLog.LogListEachTrace(isError, param, res, error, type);
  BbkUtils.ensureFunctionCall(callbackFun);
}

const validateHasCacheData = state => {
  const groupParam = packageListPageReqParam(state, { pageNum: 1 });
  const pageCacheKey = listPagingAgent.getParamsUniqueKey(groupParam);
  const cacheData = listPagingAgent.loadSnapshot(pageCacheKey);
  const hasCaCheData = cacheData !== null;
  return hasCaCheData;
};

// 列表页分页加载
export function* apiListPageQuery() {
  yield takeEvery(FETCH_LIST_PAGE, function* logic(action: ActionType) {
    const state = yield select();
    const data = { ...action.data };
    const pTime = getPickUpTime(state);
    const isTimeOut = validateTimeIsOutIsd(pTime);
    if (isTimeOut) {
      yield put(
        setTimeOutPopData({
          visible: true,
          title: Texts.timeOutPopTitle,
          content: Texts.timeOutPopContent,
          rightText: Texts.timeOutPopConfirmText,
        }),
      );
      return;
    }
    const queryVidBefore = AppContext.UserTrace.queryVid;

    setUserTraceQueryVid();

    // 如果查询条件变更，清除浏览历史
    if (queryVidBefore !== AppContext.UserTrace.queryVid) {
      yield put(resetUserBrowsingHistory());
    }

    // 1、请求前的处理
    const {
      type,
      callbackFun,
      filters,
      isAutoChange,
      isNotHitCacheKeyElse,
      isWillMount,
    } = data;
    switch (type) {
      case FetchListPageType.Search:
      case FetchListPageType.Cache_Expire_Search:
        yield put(reset());
        ListReqAndResData.removeData();
        removeListStatusData();
        if (Utils.isCtripIsd()) {
          yield put(fetchLimitContent(true));
        }
        yield put(fetchIsdIMUrl());
        if (filters && filters?.length) {
          yield put(
            updateSelectedFilterListNoRefresh(
              Hooks.buildSelectedFiltersByCode(filters),
            ),
          );
        }
        break;
      case FetchListPageType.Filter_Search:
        yield put(
          updateStatusBeforeFilterSearch({
            hasCaCheData: validateHasCacheData(state),
          }),
        );
        ListReqAndResData.setData(listStatusKeyList.routeTopParam, null); // 更新筛选项后，置顶参数不保留
        break;
      case FetchListPageType.Search_Word_Search:
        yield put(
          updateStatusBeforeFilterSearch({
            hasCaCheData: validateHasCacheData(state),
          }),
        );
        ListReqAndResData.setData(listStatusKeyList.routeTopParam, null); // 更新筛选项后，置顶参数不保留
        break;
      case FetchListPageType.Vehcile_Group_Search:
        yield put(updateStatusBeforeGroupSearch());
        break;
      case FetchListPageType.Next_Page_Search:
        yield put(updateStatusBeforeNextPageSearch());
        break;
      default:
        break;
    }
    // 2、组装请求参数
    const curState = yield select();
    let param = packageListPageReqParam(curState, data);

    const verifyRequestParameters = () => {
      const { pickupPointInfo, returnPointInfo } = param || {};
      return (
        verifyPointInfo(pickupPointInfo) && verifyPointInfo(returnPointInfo)
      );
    };

    const verifyParametersCoincident = (resRequestInfo: requestInfoType) => {
      return Utils.isRequestParamCoincident(
        resRequestInfo,
        curState?.LocationAndDate,
      );
    };

    param = CarFetchHelper.parameterBuilder({
      param,
      verifyRequestParameters,
      verifyParametersCoincident,
    });
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductReq, param);
    // 3、发送请求
    const key = listPagingAgent.precache(param);
    const cache = listPagingAgent.loadWithProfile(key, param);
    // @ts-ignore
    global.ListUniqRequestKey = Utils.getUniqRequestKeyWithEnv(); // 生成唯一请求key
    let isError = false;
    let errorInfo = null;
    let res = null;
    if (cache && cache.promise) {
      res = yield cache.promise.catch(error => {
        isError = true;
        errorInfo = error;
      });
    } else {
      isError = true;
    }
    // 设置列表页第一页展示数据的requestId
    if (param?.pageNum === 1) {
      AppContext.setListRequestId(param?.requestId);
    }
    // 4、响应处理

    const newState = yield select();
    if (cache?.key === getPageCacheKey(newState).pageCacheKey) {
      const newRes = isError
        ? addListResultMark({ error: errorInfo }, true, false, false)
        : addListResultMark(res);
      if (type === FetchListPageType.Vehcile_Group_Search) {
        // 切换车型组如果命中缓存，isGroupLoading状态合并导致切换卡顿，延时设置数据防止状态合并
        yield delay(10);
      }
      yield listPageQueryCallback(
        { type, isError, param, res: newRes, error: errorInfo, isAutoChange },
        callbackFun,
      );
      // 若列表页的无少结果推荐服务端实验为B版，设置标识位，在下次渲染列表页时请求推荐接口
      if (CarServerABTesting.isRecommend()) {
        // 无结果不异步刷新
        if (res?.allVehicleCount > 0) {
          ListReqAndResData.setData(
            ListReqAndResData.keyList.listNeedRecommend,
            true,
          );
        } else {
          yield put(fetchListRecommend(res));
        }
      }
    } else {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_trace_list_not_hit_cacheKey,
        info: {
          cacheKey: cache?.key,
          ...getPageCacheKey(newState),
          isNotHitCacheKeyElse,
          type,
          errorInfo,
          isSuccess: !cache,
          isWillMount,
          value: isWillMount,
        },
      });
    }
  });
}

/**
 * 境外请求推荐列表页
 * @param data 参数透传, data.params 列表页请求参数, data.response 列表页响应
 * @returns
 */
function* queryOsdRecommendProducts(data) {
  const isCacheValid = result => {
    const baseResponse = result?.baseResponse || {};
    return (
      baseResponse.isSuccess &&
      baseResponse.hasResult &&
      baseResponse.code === '200'
    );
  };
  const omit = ['now', 'requestId', 'appType', 'uniqRequestKey', 'baseRequest'];

  const params = CarFetchHelper.parameterBuilder({
    param: data?.params,
    cachePolicy: {
      enableCache: true,
      isFromStorage: true,
      isLoadStorageThenRemove: true,
      omit,
    },
    verifyResponseIsValid: isCacheValid,
  });

  const productsRes = data?.response;

  // fix 添加sign验证，用于标识 queryProducts 接口响应后再查询推荐接口
  if (productsRes?.extras?.hasListSign) {
    if (!params.extraMaps) params.extraMaps = {};
    params.extraMaps.hasListSign = productsRes.extras.hasListSign;
  }

  // fix requestId：需要和queryProducts 同一个requestId
  params.requestId = productsRes?.baseResponse?.requestId;

  const result: any = yield CarFetch.getRecommendProducts(params).catch(
    () => {},
  );
  const res = { ...result, extras: productsRes.extras };
  try {
    const isSuccess = res?.baseResponse?.isSuccess;
    // 将补偿结果追加到内存中的列表页数据
    if (isSuccess) {
      ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, res);
    }
    let eventResult = false;
    // 如果存在recommendProductsList字段则为出境无结果新版推荐，否则为旧版无结果推荐
    // AB下线后直接将else里的代码删除即可
    if (res?.recommendProductsList) {
      const newRes = res?.recommendProductsList?.[0];
      const firstRecommendInfo = newRes?.recommendProductInfo;
      const firstRecommendation = firstRecommendInfo?.recommendation;
      yield put(
        fetchListRecommendCallback({
          isRecommendNoResult: !(newRes?.allVehicleCount > 0),
          recommendAllVehicleCount: newRes?.allVehicleCount,
          recommendAllVendorPriceCount: newRes?.allVendorPriceCount,
          recommendTitle: firstRecommendation?.tilte,
          recommendDesc: firstRecommendation?.subTitle,
          recommendTip: firstRecommendation?.recMessage,
          recommendButtonTitle: firstRecommendation?.recTitle,
          recommendType: firstRecommendInfo?.recommendType, // 车型推荐类型
          subStrategyType: firstRecommendInfo?.subStrategyType, // 推荐策略子类型(目前只有时间推荐有子类型)
          recUniqsign: newRes?.recUniqsign, // 推荐接口对应的缓存标识字段
          pickUpAvailableTime: firstRecommendation?.pickUpAvailableTime,
          returnAvailableTime: firstRecommendation?.returnAvailableTime,
          availableLocationCode: firstRecommendInfo?.availableLocationCode,
          availableLocation: firstRecommendInfo?.availableLocation,
          longitude: firstRecommendInfo?.longitude,
          latitude: firstRecommendInfo?.latitude,
          cid: firstRecommendInfo?.cid,
          noResultRecTip: res?.noResultRecTip, // 无结果聚合顶部文案
        }),
      );
      // 如果策略包含动态半径泛化，也就是rec4，算做是有结果，需要修改无结果埋点里的eventResult改为true
      res.recommendProductsList?.forEach((item: any) => {
        if (item?.recommendProductInfo?.recommendType === 'rec4') {
          eventResult = true;
        }
      });
      UBTLog.LogListFinalTrace(
        params,
        res,
        res?.appResponseMap?.isFromCache,
        false,
        null,
        undefined,
        eventResult,
      );
    } else {
      const firstRecommendInfo = res?.recommendProductInfo?.[0];
      const firstRecommendation = firstRecommendInfo?.recommendation;
      yield put(
        fetchListRecommendCallback({
          isRecommendNoResult: !(res?.allVehicleCount > 0),
          recommendAllVehicleCount: res?.allVehicleCount,
          recommendAllVendorPriceCount: res?.allVendorPriceCount,
          recommendTitle: firstRecommendation?.tilte,
          recommendDesc: firstRecommendation?.subTitle,
          recommendTip: firstRecommendation?.recMessage,
          recommendButtonTitle: firstRecommendation?.recTitle,
          recommendType: firstRecommendInfo?.recommendType, // 车型推荐类型
          subStrategyType: firstRecommendInfo?.subStrategyType, // 推荐策略子类型(目前只有时间推荐有子类型)
          recUniqsign: res?.recUniqsign, // 推荐接口对应的缓存标识字段
          pickUpAvailableTime: firstRecommendation?.pickUpAvailableTime,
          returnAvailableTime: firstRecommendation?.returnAvailableTime,
          availableLocationCode: firstRecommendInfo?.availableLocationCode,
          availableLocation: firstRecommendInfo?.availableLocation,
          longitude: firstRecommendInfo?.longitude,
          latitude: firstRecommendInfo?.latitude,
          cid: firstRecommendInfo?.cid,
        }),
      );
    }
  } catch (error) {
    yield put(
      fetchListRecommendCallback({
        isRecommendNoResult: true,
      }),
    );
  }
  return res;
}

// 列表页查询无少结果推荐
export function* apiListRecommend() {
  yield takeEvery(FETCH_LIST_RECOMMEND, function* logic(action: ActionType) {
    const { data } = action;
    const curState = yield select();
    if (Utils.isCtripOsd()) {
      yield queryOsdRecommendProducts(data);
      return;
    }
    const param = packageListPageReqParam(curState, {
      pageNum: 1,
    });
    const promise = listPagingAgent.precallback(data, param);
    if (promise) {
      try {
        const res = yield promise;
        const isSuccess = !!lodashGet(res, 'baseResponse.isSuccess');
        // 将补偿结果追加到内存中的列表页数据
        if (isSuccess) {
          appendRecommendList(res);
        }
        // 目前没有多种策略并存，所以取第一种策略作为推荐类型
        const firstRecommendInfo = res?.recommendProductInfo?.[0];
        const firstRecommendation = firstRecommendInfo?.recommendation;
        yield put(
          fetchListRecommendCallback({
            isRecommendNoResult: !(res?.allVehicleCount > 0),
            recommendAllVehicleCount: res?.allVehicleCount,
            recommendAllVendorPriceCount: res?.allVendorPriceCount,
            recommendTitle: firstRecommendation?.tilte,
            recommendDesc: firstRecommendation?.subTitle,
            recommendTip: firstRecommendation?.recTitle,
            recommendType: firstRecommendInfo?.recommendType, // 车型推荐类型
            subStrategyType: firstRecommendInfo?.subStrategyType, // 推荐策略子类型(目前只有时间推荐有子类型)
            recUniqsign: res?.recUniqsign, // 推荐接口对应的缓存标识字段
            noResultRecTip: res?.noResultRecTip, // 无结果聚合顶部文案
          }),
        );
      } catch (err) {
        yield put(
          fetchListRecommendCallback({
            isRecommendNoResult: true,
          }),
        );
      }
    }
  });
}

export function* updateGroupHandle() {
  yield takeEvery(SET_GROUPID, function* logic(action: ActionType) {
    if (CarABTesting.isListInPage()) {
      // 无少结果推荐时，车型组切换不查询列表页接口
      if (!CarServerABTesting.isRecommend()) {
        const state = yield select();
        const { isAutoChange, searchWord } = action?.data || {};
        // 点击车型组tab时，如果当前搜筛词是车型组，搜索词清空
        const { type: searchWordType } = getCurSearchWordObj(state) || {};
        if (!searchWord && searchWordType === SuggestionTypeNumber.modelGroup) {
          yield put(setListSearchWords({}));
        }
        yield put(
          fetchListPage({
            type: FetchListPageType.Vehcile_Group_Search,
            isAutoChange,
            searchWord,
          }),
        );
      }
    }
  });
}

export function* udpateSelectedFilterHandle() {
  yield takeEvery(
    [UPDATE_SELECTED_FILTER, CLEAR_SELECTED_FILTER],
    function* logic() {
      if (CarABTesting.isListInPage()) {
        yield put(
          fetchListPage({
            type: FetchListPageType.Filter_Search,
          }),
        );
      }
    },
  );
}

export function* handleResGroupAndFilters() {
  yield takeEvery(HANDLE_RES_GROUPID_FILTERS, function* logic() {
    const state = yield select();
    const resStatus = getPageResStatus(state);
    yield put(setPageResStatus(resStatus));
    const curActiveGroupId = getActiveGroupId(state);
    recordTopParam(lodashGet(resStatus, 'activeGroupId') || curActiveGroupId);
  });
}

// 处理列表页直接跳转填写页
export function* listGoToBooking() {
  yield takeLatest(LIST_GO_TO_BOOKING, function* logic(action: ActionType) {
    // reset状态
    yield put(setIsSecretBoxListToBook(false));
    const state = yield select();
    const pTime = getPickUpTime(state);
    const { uniqueCode, vehicleCode, from, vendorPriceInfo, productRef } =
      action.data;
    // 验证时间
    if (validateTimeIsOutIsd(pTime)) {
      yield put(
        setTimeOutPopData({
          visible: true,
        }),
      );
      return;
    }
    const isLogin = yield User.checkToLogin();
    if (!isLogin) return;
    let firstScreenParam;
    let curVendorInfo;
    if (from !== Enums.SecretBoxToBookType.SecretBoxModal) {
      const vehicleInfo = getVehicleInfoByCode(vehicleCode);
      const param = {
        ctripVehicleId: vehicleCode,
        productRef,
        groupCode: vehicleInfo?.groupCode, // 这里要传vehicleInfo里的groupCode
        ...getVendorListBasePageParam(),
      };
      yield put(
        queryVehicleDetailList({
          reqParam: param,
        }),
      );
      firstScreenParam = {
        vehicleInfo,
        ...getSecretBoxFirstScreenParamToBooking(vendorPriceInfo),
      };
      curVendorInfo = vendorPriceInfo;
      // 标识从首页直接跳转填写页（没有uniqueCode）
      yield put(setIsSecretBoxListToBook(true));
    } else {
      const curState = yield select();
      firstScreenParam = getFirstScreenParamToBooking(curState);
      yield put(setModalStatus({ uniqueCode }));
      curVendorInfo = getVendorInfoByUniqueCode(
        getVehicleDetailListResponse(state),
        uniqueCode,
      );
    }
    const reference = curVendorInfo?.reference || {};
    const isdParam = {
      vpid: firstScreenParam?.vehicleInfo?.vehicleCode,
      vehicleName: firstScreenParam?.vehicleInfo?.name,
      vendorid: reference?.vendorCode,
      csname: curVendorInfo?.vendorName,
      psid: reference?.pStoreCode,
      rsid: reference?.rStoreCode,
      groupId: curVendorInfo?.vehicleGroup,
    };
    const newReference = { ...reference, isdParam };
    // 不展示点评
    curVendorInfo.commentInfo = {};
    // 车型标签不展示
    firstScreenParam.allTags = { vehicleTagList: [] };
    // 取盲盒车型名称
    if (firstScreenParam?.vehicleInfo?.name) {
      firstScreenParam.vehicleInfo.name = curVendorInfo.decorateVehicleName;
    }
    // 跳转到填写页
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const isISDShelves3 = GetABCache.isISDShelves3();
    const pageToNavigate =
      isISDShelves2B && isISDShelves3 ? 'BookingIsd' : 'Booking';

    AppContext.PageInstance.push(pageToNavigate, {
      firstScreenParam,
      reference: newReference,
      vendorPriceInfo: curVendorInfo,
      isSecretBox: true,
    });

    yield delay(500);
    yield put(
      setModalStatus({
        priceDetailModalVisible: false,
        totalPriceModalVisible: false,
      }),
    );
  });
}

export function* goToBookingEasyLife2024() {
  yield takeLatest(
    LIST_GO_TO_BOOKING_EASYLIFE,
    function* logic(action: ActionType) {
      const state = yield select();
      const { vendorPriceList, productRef, vehicleIndex } = action.data;
      const pTime = getPickUpTime(state);
      if (validateTimeIsOutIsd(pTime)) {
        yield put(
          setTimeOutPopData({
            visible: true,
          }),
        );
        return;
      }
      const isLogin = User.isLoginSync();
      if (!isLogin) {
        const isLoginNow = yield User.toLogin();
        if (!isLoginNow) {
          return;
        }
      }
      const priceInfo = vendorPriceList?.[0] || {};

      const reference = priceInfo?.reference;

      const vehicleInfo = getVehicleInfoByCode(
        `${reference?.kVehicleId}`,
        undefined,
        productRef,
      );

      const firstScreenParam = {
        vehicleInfo,
        vendorInfo: {
          vendorName: priceInfo?.vendorName,
        },
        isSelect: priceInfo?.isSelect,
        isEasyLife: priceInfo?.easyLifeInfo?.isEasyLife,
        nationalChainTagTitle: getNationalChainTag(priceInfo?.allTags)?.title,
        allTags: {
          vehicleTagList: priceInfo?.allTags?.filter(
            flex => flex.groupId === 2,
          ),
        },
        isSelfService: !!priceInfo?.allTags?.find(
          item => item?.labelCode === ILableCode.SelfService,
        ),
      };

      const isdParam = {
        vpid: firstScreenParam?.vehicleInfo?.vehicleCode,
        vehicleName: firstScreenParam?.vehicleInfo?.name,
        vendorid: reference?.vendorCode,
        csname: priceInfo?.vendorName,
        psid: reference?.pStoreCode,
        rsid: reference?.rStoreCode,
        isEasyLife2024: reference?.packageLevel === ApiResCode.EasyLife2024Code,
      };

      const newReference = { ...reference, isdParam };

      const uniqueCode = uuid();
      const selectExtras = priceInfo?.isSelect
        ? {
            rSelect: '1',
          }
        : {};
      const vehicleListResponse = {
        specificProductGroups: {
          vendorPriceList: [{ ...priceInfo, uniqueCode }],
        },
        vehicleInfo,
        extras: selectExtras,
      };
      yield put(
        setVehicleListRequestAndResponse({
          response: vehicleListResponse,
          vehicleIndex,
        }),
      );
      yield put(setUniqueCode(uniqueCode));
      // 跳转到填写页
      const isISDShelves2B = CarServerABTesting.isISDShelves2B();
      const isISDShelves3 = GetABCache.isISDShelves3();
      const pageToNavigate =
        isISDShelves2B && isISDShelves3 ? 'BookingIsd' : 'Booking';

      AppContext.PageInstance.push(pageToNavigate, {
        firstScreenParam,
        reference: newReference,
        vendorPriceInfo: priceInfo,
      });
    },
  );
}

export function* queryPackageComparison() {
  yield takeLatest(
    QUERY_PACKAGE_COMPARISON,
    function* logic(action: ActionType) {
      const { packageComparison, reference } = action.data;
      if (!packageComparison || !reference) {
        CarLog.LogDevError({
          expPoint: 'queryPackageComparison',
          expMsg: !packageComparison ? '无 packageComparison' : '无 reference',
          requestId: ListResSelectors.getRealRequestId(),
          skuId: reference?.skuId,
        });
        return;
      }
      yield put(setQueryPackageComparisonData('pending', null));
      const { response } = yield CarFetch.queryPackageComparison({
        packageComparison,
        reference,
      }).catch(() => {});
      const isSuccess = response?.packageDetails?.length > 0;
      yield put(
        setQueryPackageComparisonData(
          isSuccess ? 'success' : 'error',
          response,
        ),
      );
    },
  );
}

export function* setUserBrowsingHistory() {
  yield takeLatest(
    SET_USER_BROWSING_HISTORY,
    function* logic(action: ActionType) {
      type SetUserBrowsingHistoryType = {
        vehicleCode: string;
        license: string;
      };
      const { vehicleCode, license }: SetUserBrowsingHistoryType = action.data;

      const key = `${vehicleCode}_${license}`;
      const state = yield select();
      const userBrowsingHistories: Map<string, object> =
        getUserBrowsingHistories(state);
      if (userBrowsingHistories.has(key)) {
        return;
      }

      const histories = new Map(userBrowsingHistories);
      histories.set(key, { vehicleCode, license });

      yield put(setUserBrowsingHistoryCallback(histories));
    },
  );
}

export function* setOsdUserBrowsingHistory() {
  yield takeLatest(
    SET_OSD_USER_BROWSING_HISTORY,
    function* logic(action: ActionType) {
      const { vehicleKey } = action.data;
      const state = yield select();
      const osdUserBrowsingHistories: Map<string, object> =
        getOsdUserBrowsingHistories(state);
      if (osdUserBrowsingHistories.has(vehicleKey)) {
        return;
      }
      const histories = new Map(osdUserBrowsingHistories);
      histories.set(vehicleKey, { vehicleKey });
      yield put(setOsdUserBrowsingHistoryCallback(histories));
    },
  );
}

export function* fetchEasyLife2024Tags() {
  yield takeLatest(FETCH_EASYLIFE_2024_TAGS, function* logic() {
    yield put(setEasyLife2024Tags('pending', null));
    const { response } = yield CarFetch.getEasyLifeTagInfoWithCatch({
      packageLevel: ApiResCode.EasyLife2024Code,
    });
    const isSuccess = response?.easyLifeTag?.length > 0;
    yield put(setEasyLife2024Tags(isSuccess ? 'success' : 'error', response));
  });
}

export function* checkOsdPoi() {
  yield takeLatest(CHECK_OSD_POI, function* logic(action: ActionType) {
    const {
      params,
      afterPoiInterceptCallback = () => {},
      setConfirmModalVisible,
    } = action.data;
    try {
      const response = yield CarFetch.checkPoi(params);
      if (response?.baseResponse?.isSuccess) {
        if (response?.checkPoiRes?.type) {
          yield put(setCheckOsdPOICallback(response?.checkPoiRes));
          setConfirmModalVisible(true);
          return;
        }
      }
      afterPoiInterceptCallback();
    } catch (error) {
      afterPoiInterceptCallback();
    }
  });
}

export function* queryListIpollConfig() {
  yield takeLatest(
    QUERY_LIST_IPOLL_CONFIG,
    function* logic(action: ActionType) {
      const { pageType } = action.data;
      const params = {
        pageType,
      };
      const response = yield CarFetch.getIpollConfig(params).catch(() => {});
      if (response?.baseResponse?.isSuccess) {
        if (response?.allConfigs) {
          yield put(setListIpollConfig(response.allConfigs));
        }
      }
    },
  );
}

export default [
  deleteSelectedFilter(),
  getLimitContent(),
  getC2BStatus(),
  getIsdImURL(),
  apiListPreBatchQuery(),
  apiListBatchQuery(),
  apiListPageQuery(),
  apiListRecommend(),
  apiPreListPageQuery(),
  updateGroupHandle(),
  udpateSelectedFilterHandle(),
  handleResGroupAndFilters(),
  addSaleOutList(),
  listGoToBooking(),
  goToBookingEasyLife2024(),
  queryPackageComparison(),
  setUserBrowsingHistory(),
  setOsdUserBrowsingHistory(),
  fetchEasyLife2024Tags(),
  checkOsdPoi(),
  queryListIpollConfig(),
];
