import { get as lodashGet } from 'lodash-es';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import memoize from 'memoize-one';
import { createSelector } from 'reselect';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import * as ProductSelectors from '../../Global/Cache/ProductSelectors';
import { getLocalDayJs } from '../../Components/Calendar/Method';
import Utils from '../../Util/Utils';
import { CarLog } from '../../Util/Index';
import { LogKeyDev } from '../../Constants/Index';
import { getQConfig } from '../Common/Selectors';
import { isValidRentalDate } from './Util';
import { SubPoiType } from '../../Types/Dto/GetAreaListType';

export const getLocationAndDate = state => state.LocationAndDate;

export const getRentalDate = state => getLocationAndDate(state).rentalDate;

export const getPickUpTime = state =>
  lodashGet(getRentalDate(state), 'pickUp.dateTime');

export const getFormatPickUpTime = createSelector([getPickUpTime], time =>
  dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
);

export const getDropOffTime = state =>
  lodashGet(getRentalDate(state), 'dropOff.dateTime');

export const getFormatDropOffTime = createSelector([getDropOffTime], time =>
  dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
);
// eslint-disable-next-line max-len
export const getProductDropOffTime = state =>
  lodashGet(getRentalDate(state), 'productDropOff.dateTime');

export const getIsPickupStation = state =>
  state.LocationAndDate.isPickupStation;

export const getIsDropOffStation = state =>
  state.LocationAndDate.isDropOffStation;

export const getRentalLocation = state => state.LocationAndDate?.rentalLocation;

export const getPickUpCityId = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.cid;

export const getPickUpProvinceName = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.province;

export const getSelectCityId = state => state.LocationAndDate.selectCityId;

export const getPickUpCityName = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.cname;

export const getPickUpCountry = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.country;

const getPickUpLocationType = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.type;

export const getPickUpLocationCode = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.id;

export const getPickUpLocationName = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.name;

const getPickUpLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.meetingPointId;

const getPickUpLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.subPoiType;

const getDropOffLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.meetingPointId;

const getDropOffLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.subPoiType;

// eslint-disable-next-line max-len
const getPickUpIsFromPosition = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.isFromPosition;

export const getPickUpLocationLat = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.lat;

export const getPickUpLocationLng = state =>
  state.LocationAndDate.rentalLocation?.pickUp?.area?.lng;

export const getDropOffCityId = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.cid;

export const getDropOffCityName = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.cname;

export const getDropOffCountry = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.country;

// eslint-disable-next-line max-len
const getDropOffLocationType = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.type;

export const getDropOffLocationCode = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.id;

// eslint-disable-next-line max-len
export const getDropOffLocationName = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.name;

// eslint-disable-next-line max-len
const getDropOffIsFromPosition = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.isFromPosition;

const getDropOffLocationLat = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.lat;

const getDropOffLocationLng = state =>
  state.LocationAndDate.rentalLocation?.dropOff?.area?.lng;

export const getIsShowDropOff = state =>
  state.LocationAndDate.rentalLocation.isShowDropOff;

export const getIsDifferentLocation = state =>
  !(
    getPickUpCityId(state) === getDropOffCityId(state) &&
    getPickUpLocationName(state) === getDropOffLocationName(state)
  );

export const getPositionInfo = state => state.LocationAndDate.position;

export const getAirPortTransferTip = state =>
  state.LocationAndDate?.airPortTransferTip;

const getPositionLocation = state =>
  lodashGet(getPositionInfo(state), 'positionLocation');

export const getPositionCityId = state =>
  lodashGet(getPositionLocation(state), 'cid');

export const getFormatRentalDate = createSelector(
  [getRentalDate],
  (rentalDate = {}) => {
    const formatTemp = 'YYYYMMDDHHmmss';
    const { pickUp = {}, dropOff = {} } = rentalDate;
    return {
      pickUp: {
        dateTime: dayjs(pickUp?.dateTime).format(formatTemp),
      },
      dropOff: {
        dateTime: dayjs(dropOff?.dateTime).format(formatTemp),
      },
    };
  },
);

export const getFormatLocationAndDate = createSelector(
  [getFormatRentalDate, getRentalLocation],
  (rentalDate, rentalLocation) => ({
    rentalDate,
    rentalLocation,
  }),
);

// eslint-disable-next-line max-len

export const getCarHeaderData = createSelector(
  [
    getPickUpTime,
    getDropOffTime,
    getPickUpLocationType,
    getPickUpLocationCode,
    getPickUpLocationName,
    getDropOffLocationType,
    getDropOffLocationCode,
    getDropOffLocationName,
  ],

  (
    ptime,
    rtime,
    pLocationType,
    pLocationCode,
    pLocationName,
    rLocationType,
    rLocationCode,
    rLocationName,
  ) => ({
    ptime,
    rtime,
    pickupLocation: {
      locationType: pLocationType,
      locationCode: pLocationCode,
      locationName: pLocationName,
    },
    returnLocation: {
      locationType: rLocationType,
      locationCode: rLocationCode,
      locationName: rLocationName,
    },
  }),
);

export const getProductCarHeaderData = createSelector(
  [getCarHeaderData, getProductDropOffTime],
  (data, productRtime) => ({
    ...data,
    rtime: productRtime,
  }),
);

export const getAggregationLocation = createSelector(
  [
    getPickUpLocationName,
    getPickUpLocationLng,
    getPickUpLocationLat,
    getDropOffLocationName,
    getDropOffLocationLng,
    getDropOffLocationLat,
  ],

  (
    pLocationName,
    pLongitude,
    pLatitude,
    rLocationName,
    rLongitude,
    rLatitude,
  ) => ({
    pickupPoiInfo: {
      locationName: pLocationName,
      longitude: pLongitude,
      latitude: pLatitude,
    },
    returnPoiInfo: {
      locationName: rLocationName,
      longitude: rLongitude,
      latitude: rLatitude,
    },
  }),
);

export const getExpandLocationAndDate = createSelector(
  [
    getPickUpCityId,
    getPickUpCityName,
    getPickUpLocationCode,
    getPickUpLocationName,
    getPickUpLocationLat,
    getPickUpLocationLng,
    getPickUpLocationType,
    getPickUpTime,
    getDropOffCityId,
    getDropOffCityName,
    getDropOffLocationCode,
    getDropOffLocationName,
    getDropOffLocationLat,
    getDropOffLocationLng,
    getDropOffLocationType,
    getDropOffTime,
  ],

  (
    pcid,
    pcname,
    plid,
    plname,
    plat,
    plng,
    pltype,
    ptime,
    rcid,
    rcname,
    rlid,
    rlname,
    rlat,
    rlng,
    rltype,
    rtime,
  ) => ({
    pcid,
    pcname,
    plid,
    plname,
    plat,
    plng,
    pltype,
    ptime: dayjs(ptime).format('YYYYMMDDHHmmss'),
    rcid,
    rcname,
    rlid,
    rlname,
    rlat,
    rlng,
    rltype,
    rtime: dayjs(rtime).format('YYYYMMDDHHmmss'),
  }),
);

// 获取C2B格式数据
export const getC2BLocationAndDate = createSelector(
  [
    getPickUpCityId,
    getPickUpCityName,
    getPickUpLocationName,
    getPickUpLocationLat,
    getPickUpLocationLng,
    getPickUpLocationType,
    getPickUpTime,
    getDropOffCityId,
    getDropOffCityName,
    getDropOffLocationName,
    getDropOffLocationLat,
    getDropOffLocationLng,
    getDropOffLocationType,
    getDropOffTime,
  ],

  (
    pcid,
    pcname,
    paddr,
    plat,
    plng,
    ptype,
    ptime,
    rcid,
    rcname,
    raddr,
    rlat,
    rlng,
    rtype,
    rtime,
  ) => ({
    pcid,
    pcname,
    poiinfo: {
      addr: paddr,
      lat: plat,
      lng: plng,
      desc: '',
      type: ptype,
    },
    ptime: dayjs(ptime).format('YYYY/MM/DD HH:mm:ss'),
    rcid,
    rcname,
    rpoiinfo: {
      addr: raddr,
      lat: rlat,
      lng: rlng,
      desc: '',
      type: rtype,
    },
    rtime: dayjs(rtime).format('YYYY/MM/DD HH:mm:ss'),
  }),
);

export const getPickUpCityData = createSelector(
  [
    getPickUpCityId,
    getPickUpCityName,
    getPickUpLocationLat,
    getPickUpLocationLng,
    getPickUpLocationCode,
    getPickUpLocationName,
    getPickUpLocationType,
    getPickUpIsFromPosition,
  ],

  // eslint-disable-next-line max-len
  (
    cityId,
    cityName,
    latitude,
    longtitude,
    locationCode,
    locationName,
    locationType,
    isFromPosition,
  ) => ({
    cityId,
    cityName,
    latitude,
    longtitude,
    locationCode,
    locationName: isFromPosition ? `${locationName}附近` : locationName,
    locationType,
  }),
);

export const getDropOffCityData = createSelector(
  [
    getDropOffCityId,
    getDropOffCityName,
    getDropOffLocationLat,
    getDropOffLocationLng,
    getDropOffLocationCode,
    getDropOffLocationName,
    getDropOffLocationType,
    getDropOffIsFromPosition,
  ],

  // eslint-disable-next-line max-len
  (
    cityId,
    cityName,
    latitude,
    longtitude,
    locationCode,
    locationName,
    locationType,
    isFromPosition,
  ) => ({
    cityId,
    cityName,
    latitude,
    longtitude,
    locationCode,
    locationName: isFromPosition ? `${locationName}附近` : locationName,
    locationType,
  }),
);

export const getTimeWarningFunc = (pickupTime, dropoffTime) => {
  const phour = dayjs(pickupTime).hour();
  const rhour = dayjs(dropoffTime).hour();
  const pminute = dayjs(pickupTime).minute();
  const rminute = dayjs(dropoffTime).minute();
  let dateWarning = '';

  if (
    (phour >= 21 && phour <= 24) ||
    (phour >= 0 && phour < 8) ||
    (phour === 8 && pminute === 0) ||
    (rhour >= 21 && rhour <= 24) ||
    (rhour >= 0 && rhour < 8) ||
    (rhour === 8 && rminute === 0)
  ) {
    dateWarning = '21点-次日8点取还车，可能收取夜间服务费';
  }

  return Utils.isCtripIsd() ? dateWarning : '';
};

export const getTimeWarning = state =>
  getTimeWarningFunc(getPickUpTime(state), getDropOffTime(state));

export const getNationalDayWarningFunc = (
  pickupTime,
  dropoffTime,
  nationalDayWarningConfig,
) => {
  const start = dayjs(nationalDayWarningConfig?.start).valueOf();
  const end = dayjs(nationalDayWarningConfig?.end).valueOf();
  const ptime = dayjs(pickupTime).valueOf();
  const rtime = dayjs(dropoffTime).valueOf();
  let nationalDayWarning = '';
  if (
    nationalDayWarningConfig?.title &&
    ((start <= ptime && ptime <= end) || (start <= rtime && rtime <= end))
  ) {
    nationalDayWarning = nationalDayWarningConfig?.title;
  }

  return Utils.isCtripIsd() ? nationalDayWarning : ''; // 传给native日历， 不能有null/undefined
};

export const getNationalDayWarning = createSelector(
  [getPickUpTime, getDropOffTime, getQConfig],
  (pickupTime, dropoffTime, remoteQConfig) =>
    getNationalDayWarningFunc(
      pickupTime,
      dropoffTime,
      remoteQConfig?.nationalDayWarning,
    ),
);

export const getTimeInsufficientWarningFunc = pickupTime => {
  const nowDate = getLocalDayJs();
  const pTime = pickupTime;
  const hour = dayjs(pTime).diff(nowDate, 'hours');
  let timeInsufficientWarning = '';
  if (hour < 4) {
    timeInsufficientWarning = '取车时间距当前时间较近，可能库存紧张';
  }
  return timeInsufficientWarning;
};

export const getTimeInsufficientWarning = state =>
  getTimeInsufficientWarningFunc(getPickUpTime(state));

export const getPointInfoParamsV2 = createSelector(
  [
    getPickUpCityId,
    getPickUpTime,
    getPickUpLocationCode,
    getPickUpLocationName,
    getPickUpLocationType,
    getPickUpLocationLat,
    getPickUpLocationLng,
    getDropOffCityId,
    getDropOffTime,
    getProductDropOffTime,
    getDropOffLocationCode,
    getDropOffLocationName,
    getDropOffLocationType,
    getDropOffLocationLat,
    getDropOffLocationLng,
    getPickUpLocationMeetingPointId,
    getPickUpLocationMeetingSubPoiType,
    getDropOffLocationMeetingPointId,
    getDropOffLocationMeetingSubPoiType,
  ],

  (
    pickUpCityId,
    pickUpTime,
    pickUpLocationCode,
    pickUpLocationName,
    pickUpLocationType,
    pickUpLocationLat,
    pickUpLocationLng,
    dropOffCityId,
    dropOffTime,
    productDropOffTime,
    dropOffLocationCode,
    dropOffLocationName,
    dropOffLocationType,
    dropOffLocationLat,
    dropOffLocationLng,
    pickUpMeetingPointId,
    pickUpMeetingSubPoiType,
    dropOffMeetingPointId,
    dropOffMeetingSubPoiType,
  ) =>
    memoize((isProduct = false) => {
      let fixDropOffTime = isProduct ? productDropOffTime : dropOffTime;
      // 如果还车时间跟取车时间校验无效，则取当前还车时间，因为当前dropOffTime一定大于取车时间
      const isDateValid = isValidRentalDate(
        pickUpTime,
        fixDropOffTime,
        true,
        true,
      );
      if (!isDateValid) {
        fixDropOffTime = dropOffTime;
      }
      return {
        pickupPointInfo: {
          cityId: pickUpCityId,
          date: dayjs(pickUpTime).format('YYYY-MM-DD HH:mm:ss'),
          locationCode: pickUpLocationCode,
          locationName: pickUpLocationName,
          locationType: pickUpLocationType,
          poi: {
            latitude: Number(pickUpLocationLat),
            longitude: Number(pickUpLocationLng),
            radius: 0,
          },
          pickupOnDoor: 0, // 国内免费上门取车 1 true 0 false
          dropOffOnDoor: 0, // 国内免费送车上门 1 true 0 false
          meetingPointId: pickUpMeetingPointId,
          subPoiType: pickUpMeetingSubPoiType,
        },
        returnPointInfo: {
          cityId: dropOffCityId,
          date: dayjs(fixDropOffTime).format('YYYY-MM-DD HH:mm:ss'),
          locationCode: dropOffLocationCode,
          locationName: dropOffLocationName,
          locationType: dropOffLocationType,
          poi: {
            latitude: Number(dropOffLocationLat),
            longitude: Number(dropOffLocationLng),
            radius: 0,
          },
          pickupOnDoor: 0, // 国内免费上门取车 1 true 0 false
          dropOffOnDoor: 0, // 国内免费送车上门 1 true 0 false
          meetingPointId: dropOffMeetingPointId,
          subPoiType: dropOffMeetingSubPoiType,
        },
      };
    }),
);

/**
 * @deprecated use getPointInfoParamsV2 instead
 * @param {boolean} isProduct isProduct
 * @return {any} func
 */
// eslint-disable-next-line max-len
export const getPointInfoParmas =
  (isProduct = false) =>
  state =>
    getPointInfoParamsV2(state)(isProduct);

export const getProductRentalLocationInfo = createSelector(
  [
    getPickUpLocationLat,
    getPickUpLocationLng,
    getPickUpLocationName,
    getDropOffLocationLat,
    getDropOffLocationLng,
    getDropOffLocationName,
    getPointInfoParamsV2,
  ],

  (
    pickupLat,
    pickupLng,
    pickupAddr,
    dropoffLat,
    dropoffLng,
    dropoffAddr,
    pointInfoParmas,
  ) => ({
    pickupStart: {
      lat: pickupLat,
      lng: pickupLng,
      addr: pickupAddr,
    },
    dropoffStart: {
      lat: dropoffLat,
      lng: dropoffLng,
      addr: dropoffAddr,
    },
    ...pointInfoParmas(true),
  }),
);

export const getDiffLocationWarning = state => {
  let diffLocationWarning = '';
  const pCityId = getPickUpCityId(state);
  const rCityId = getDropOffCityId(state);
  const pLocationName = getPickUpLocationName(state);
  const rLocationName = getDropOffLocationName(state);
  if (pCityId !== rCityId || pLocationName !== rLocationName) {
    diffLocationWarning = '可能产生异地还车费';
  }
  return diffLocationWarning;
};

export const getHistoryLogParam = createSelector(
  [
    getPickUpCityName,
    getPickUpLocationName,
    getDropOffCityName,
    getDropOffLocationName,
    getPickUpTime,
    getDropOffTime,
  ],

  (pcname, plname, rcname, rlname, ptime, rtime) => ({
    pcname,
    rcname,
    plname,
    rlname,
    ptime: dayjs(ptime).format('YYYY/MM/DD HH:mm:ss'),
    rtime: dayjs(rtime).format('YYYY/MM/DD HH:mm:ss'),
  }),
);

export const getRentalLocationAndDate = createSelector(
  [getRentalLocation, getRentalDate],
  (rentalLocation, rentalDate) => ({
    rentalLocation,
    rentalDate: {
      pickUp: {
        dateTime: dayjs(rentalDate?.pickUp?.dateTime).format('YYYYMMDDHHmmss'),
      },
      dropOff: {
        dateTime: dayjs(rentalDate?.dropOff?.dateTime).format('YYYYMMDDHHmmss'),
      },
    },
  }),
);

export const getIMLocationAndDate = createSelector(
  [
    getPickUpLocationName,
    getDropOffLocationName,
    getPickUpTime,
    getDropOffTime,
  ],

  (paddr, raddr, ptime, rtime) => ({
    pickupLocation: paddr,
    returnLocation: raddr,
    pickupDate: dayjs(ptime).format('YYYY-MM-DD HH:mm'),
    returnDate: dayjs(rtime).format('YYYY-MM-DD HH:mm'),
    days: Utils.isCtripIsd()
      ? BbkUtils.isd_dhm(ptime, rtime)
      : BbkUtils.getDayGap(ptime, rtime),
  }),
);

export const getIMVehicleAndVendorInfo = createSelector(
  [
    getIMLocationAndDate,
    ProductSelectors.getVehicleInfo,
    ProductSelectors.getVendorInfo,
    ProductSelectors.getIsSelected,
  ],

  (locationAndDate, vehicleInfo, vendorInfo, isSelected) => ({
    ...locationAndDate,
    vehicleName: vehicleInfo.name,
    // eslint-disable-next-line max-len
    vendorName: `${vendorInfo.vendorName ?? ''}${
      isSelected ? `(${'携程优选'})` : ''
    }`,

    vehicleCode: vendorInfo.vendorCode,
  }),
);

const dayMinutes = 1440; // 一天分钟数
export const getTenancyDays = createSelector(
  [getPickUpTime, getDropOffTime],
  (pTime, rTime) => {
    const minutes = dayjs(rTime).diff(pTime, 'minutes');
    const days = minutes > dayMinutes ? 1 : 0;
    return days;
  },
);

export const getRentCenterPoint = createSelector(
  [getRentalLocation],
  rentalLocation => {
    return (
      rentalLocation?.pickUp?.area?.subPoiType === SubPoiType.rentCenterPoint
    );
  },
);
