export enum TimeFormat {
  HHmm = 'YYYY-MM-DD HH:mm',
  HHmmss = 'YYYY-MM-DD HH:mm:ss',
}

export enum GuideTabType {
  Dropoff = 'dropoff',
  Pickup = 'pickup',
}

export enum ListType {
  Price = 1, // 1-低价榜、
  Hot = 2,
} // 2-热销榜

/**
 * 页面角色
 */
export enum PageRole {
  /**
   * 列表页
   */
  LIST,
  /**
   * 产品详情页
   */
  PRODUCT,
  /**
   * 填写页
   */
  BOOKING,
  /**
   * 订单详情页
   */
  ORDERDETAIL,
}

/**
 * 保单状态
 */
export enum InsuranceStatus {
  /**
   * 已支付
   */
  Payed = 63002,
  /**
   * 已出保
   */
  ComfirmIns = 63003,
  /**
   * 已退保
   */
  ReturnIng = 63004,
  /**
   * 已取消
   */
  Canceled = 63008,
  /**
   * 已成交
   */
  Comfirmed = 63009,
  /**
   * 未支付
   */
  UnPay = 63010,
  /**
   * 出保失败
   */
  Failed = 63011,
}

export enum DetailsItemLabelColorCode {
  Gray = 'gray',
  Disable = 'disable',
  Red = 'red',
  Green = 'green',
  Black = 'black',
  Blue = 'blue',
}

export enum InsuranceReminderLinkType {
  /**
   * 英文说明弹窗
   */
  EnglishModal = 3,
  /**
   * 保险详情弹窗理赔流程
   */
  ModalClaim = 4,
}

const getInsuranceGiveUpTip = name => {
  return `您已放弃“${name}”，不享有该保障`;
};
const getInsuranceCanceledTip = name => {
  return `您已取消“${name}”，不享有该保障`;
};
const getInsuranceReturnIngTip = name => {
  return `您已退保“${name}”，不享有该保障`;
};
const getInsuranceFaildTip = name => {
  return `“${name}“出保失败，不享有该保障`;
};

/**
 * 货架页保障Code
 */
export enum ShelvesSafeguardType {
  /**
   * 基础保障
   */
  Basic = '1002',
  /**
   * 优享保障
   */
  Premium = '2001',
  /**
   * 优享保障
   */
  PremiumPlus = '2011',
}

export const getInsuranceStatusTip = (giveUp, insuranceStatus, name) => {
  if (giveUp) {
    return getInsuranceGiveUpTip(name);
  }
  switch (insuranceStatus) {
    case InsuranceStatus.Canceled:
      return getInsuranceCanceledTip(name);
    case InsuranceStatus.ReturnIng:
      return getInsuranceReturnIngTip(name);
    case InsuranceStatus.Failed:
      return getInsuranceFaildTip(name);
    default:
      return '';
  }
};

/**
 * 护照类型
 */
export enum PassPortIdtype {
  /**
   * 大陆护照
   */
  CN = 'CN',
  /**
   * 香港护照
   */
  HK = 'HK',
  /**
   * 澳门护照
   */
  MO = 'MO',
  /**
   * 台湾护照
   */
  TW = 'TW',
  /**
   *  其他护照
   */
  OH = 'OH',
}

/**
 * 驾照是否推荐
 */
export enum DriverLicenseOptimalType {
  /**
   * 不推荐
   */
  NoRecommend = '0',
  /**
   * 推荐
   */
  Recommend = '1',
}

export enum LicenseModalDataType {
  /**
   * 一级页面弹窗链接及弹窗内容
   */
  First = 16,
  /**
   * 二级页面弹窗链接及弹窗内容
   */
  Second = 17,
}

export enum IEntranceName {
  Share = 'share',
}

export enum IShareType {
  WeixinFriend = 'WeixinFriend',
}

export enum ILableCode {
  SelfService = '3866',
  ETC = '4201',
  Limit = '3653',
  New = '3789',
}

export enum ISelfServiceSwitchType {
  Open = '1',
  Close = '0',
}

/**
 * 124472埋点类型
 */
export const enum PageSearchTraceType {
  /**
   * 首屏加载
   */
  AppLoad = 'AppLoad',
  /**
   * 行程卡回调
   */
  ItineraryCard = 'ItineraryCard',
  /**
   * PV埋点
   */
  PV = 'PV',
}

export const enum NetworkType {
  /**
   * 未知网络
   */
  Unknown = 'unknown',
  /**
   * 无网络
   */
  None = 'none',
  /**
   * WI-FI网络
   */
  WIFI = 'wifi',
  /**
   * 2G网络
   */
  SecondGeneration = '2g',
  /**
   * 3G网络
   */
  ThirdGeneration = '3g',
  /**
   * 4G网络
   */
  FourthGeneration = '4g',
}

export const enum VideoPlayerWindowMode {
  /**
   * 嵌入小屏状态
   */
  Embed = 'embed',
  /**
   * 全屏竖屏状态
   */
  Immersion = 'immersion',
  /**
   * 全屏横屏状态
   */
  Landscape = 'landscape',
}

export const enum VideoPlayerState {
  /**
   * 播放错误
   */
  Error = '-1',
  /**
   * 空闲
   */
  Idle = '0',
  /**
   * 播放中
   */
  Playing = '1',
  /**
   * 暂停
   */
  Pause = '2',
  /**
   * 缓冲中
   */
  Buffering = '3',
  /**
   * 播放完成
   */
  Completed = '4',
}

export const PauseState = [
  VideoPlayerState.Pause,
  VideoPlayerState.Idle,
  VideoPlayerState.Completed,
];

/**
 * 取车材料锚点定位
 */
export const enum PickUpMaterialsAnchor {
  ScrollView = 'ScrollView',
  /**
   * 默认详情
   */
  Detail = 'Detail',
  /**
   * 取车材料
   */
  PickUpMaterial = 'PickUpMaterial',
  /**
   * 护照
   */
  Identity = 'Identity',
  /**
   * 驾照
   */
  DriverLicense = 'DriverLicense',
  /**
   * 驾照补充说明
   */
  DriverLicenseNotes = 'DriverLicenseNotes',
  /**
   * 信用卡
   */
  CreditCard = 'CreditCard',
  /**
   * 其他材料
   */
  OtherMaterial = 'OtherMaterial',
}

export enum CountryId {
  Jappan = '78',
}

export enum APPSTATE {
  ACTIVE = 'active',
  BACKGROUND = 'background',
  INACTIVE = 'inactive',
}

export enum ToolBoxStyleType {
  BLACK = 1,
  WHITE = 3,
  blackAndMore = 2,
  whiteAndMore = 4,
}
