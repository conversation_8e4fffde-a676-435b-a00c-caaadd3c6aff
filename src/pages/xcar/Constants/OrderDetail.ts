import * as ImageUrl from './ImageUrl';
// 订单状态
export const ORDER_STATUS = {
  Unsubmitted: 0, // 待支付
  Processing: 1, // 确认中
  Confirmed: 2, // 已确认
  Completed: 3,
  Canceled: 4,
  Paying: 6, // 支付中
  Modifying: 7, // 修改中
  Modified: 8, // 已修改
};

// 保险状态
// 0未下单，1 待确认，2支付中，3已支付，4 不可加购， 5 支付失败 13已放弃
// 0,1,2,5还可以重新加购
export const INSURANCE_STATUS = {
  Unsubmitted: 0,
  ToConfirm: 1,
  Paying: 2,
  Payed: 3,
  CannotAdd: 4,
  PayFailure: 5,
  GiveUp: 13,
};

// 订单提示 tiptype
export const ORDER_ORDERTIPS = {
  ContinuePay: 1, // 待支付
  ConfirmDate: 2, // 确认中,修改确认中
  WarmTip: 3, // 已确认，已完成
  Paying: 4, // 支付中，修改支付中
  Canceled: 5, // 已取消
  Modify_ContinuePay: 6, // 修改待支付
  Modify_Confirm: 7, // 修改已确认
  Modify_Cancel: 8, // 修改后取消
  Modifying: 9, // 修改中
  Modified: 10, // 已修改
};

// 订单按钮
export const ORDER_BUTTON = {
  PayNow: 1, // 去支付
  CancelBooking: 2, // 取消订单
  Comments: 3,
  BookAgain: 4, // 再次预定
  PrintVoucher: 5,
  PrintInvoice: 6,
  ModifyOrder: 7,
  ModifyDetail: 8,
  ViewNewOrder: 9,
  ViewOldOrder: 10,
  Renew: 11, // 续租
  CancelModify: 12,
  AdvanceReturn: 18, // 提前还车
  SelfService: 19, // 自助取还
  Invoice: 20, // 发票
};

// 点评按钮
export enum ReviewButton {
  Regular = 0, // 点评
  Add = 1, // 追评
  View = 2, // 查看点评
  Unenable = 3, // 不能点评 -- 点评赚积分
}

// 订单认证入口状态
export const LICENSE_INFO_UNCOMPLETE = 'LICENSE_INFO_UNCOMPLETE'; // 未认证信息
export const LICENSE_INFO_AUTH = 'LICENSE_INFO_AUTH'; // 认证完成，需要授权(通过身份上传证件默认授权)
export const LICENSE_INFO_COMPLETE = 'LICENSE_INFO_COMPLETE'; // 完成信息录入

export const thirdpartyChannles = {
  holiday: '16401',
};

export const osdPages = {
  /**
   * 取车材料
   */
  OsdRentalCarMaterial: {
    EN: 'OsdRentalCarMaterial',
    NAME: '取车材料',
    ID: '10320670784',
  },
  /**
   * 提车凭证
   */
  OsdRentalCarVoucher: {
    EN: 'OsdRentalCarVoucher',
    NAME: '提车凭证',
    ID: '10320670785',
  },
  /**
   * 保险理赔
   */
  InsuranceInfo: { EN: 'InsuranceInfo', NAME: '保险理赔', ID: '10650007068' },
  /**
   * 取还车门店
   */
  StoreInfo: { EN: 'StoreInfo', NAME: '取还车门店', ID: '10320642945' },
  /**
   * 无忧组自驾锦囊
   */
  EasyLifeDriveKit: {
    EN: 'EasyLifeDriveKit',
    NAME: '无忧组自驾锦囊',
    ID: '10650012768',
  },
  /**
   * 缴费通道
   */
  PayChannel: { EN: 'PayChannel', NAME: '缴费通道', ID: 'PayChannel' },
};

export enum GuideTabType {
  Dropoff = 'dropoff',
  Pickup = 'pickup',
}

export enum SupplementTabType {
  All = 'all',
  Wait = 'waitPay',
}

export enum FreeDepositWayType {
  CreditRent = 1, // 信用租程信分
  OSDCreditRent = 100, // 信用租程信分
  Zhima = 2, // 信用租芝麻
  OSDZhiMa = 110, // 信用租程信分
  Vendor = 3, // 供应商无条件免押
  Sesame = 4, // 芝麻
}

// 订单免押租金支持类型
export enum FreeDepositType {
  Unsupport = 0, // 0不支持
  Both = 10, // 10押金双免
  Rent = 20, // 20免租车押金
  Illegal = 30, // 30免违章押金
  Auth = 100, // 100 通用免押类型，不区分单双免
}

// 免押方式展示类型字段（未免押）
export enum ShowDepositType {
  Normal = 0, // 根据支付方式正常展示
  CreditRent = 1, // 程信分免押
  Zhima = 2, // 芝麻
  PreAuth = 3, // 在线预授权
}

export enum DepositTipsDepositType {
  CreditRent = 1, // 程信分
  Zhima = 2, // 芝麻
  PreAuth = 3, // 在线预授权
  PayOnline = 4, // 在线付押金
}

// 当前免押类型（已免押）
export enum DepositStatus {
  Unsupport = 0, // 非免押订单
  CreditRent = 1, // 程信分
  Zhima = 2, // 芝麻
  Vendor = 3, // 供应商无条件免押
  PreAuth = 4, // 在线预授权
  PayOnline = 8, // 在线付
}

export enum SupplmentType {
  /*
   * 未知
   * */
  Other = 0,
  /*
   * 客服录入
   * */
  Customer = 1,
  /*
   * 车损
   * */
  VehicleDamage = 2,
  /*
   * 违章
   * */
  Violation = 3,
  /**
   * 押金
   */
  Deposit = 4,
}

/* 操作ID
 * 1-去支付
 * 2-取消
 * 3-去点评
 *
 * 4-再次预订
 * 5-打印提车单
 * 6-打印电子发票
 * 7-修改订单
 * 8-修改详情
 * 9-查看修改后订单
 * 10-查看修改前订单
 * 11-ISD：是否免违章押金 isFreePreccancy
 * 12-ISD:是否预授权 true-已授权 false-未授权
 * 13-ISD:是否无忧租
 * 14-ISD: 是否分期付款
 * 15-ISD: 是否自营供应商
 */
export const ORDER_BUTTON_NAME = {
  1: '去支付',
  2: '取消',
  3: '去点评',
  4: '再次预订',
  5: '打印提车单',
  6: '打印电子发票',
  7: '修改订单',
  8: '修改详情',
  9: '查看修改后订单',
  10: '查看修改前订单',
};

// pms 操作车辆

export const pmsOption = {
  CheckID: 1,
  CheckDrivingLicense: 2,
  PayDeposit: 3,
  CheckCar: 4,
};

export const NEWPIC_DOMAIN_ISD = 'https://pic.c-ctrip.com/car_isd/app';
export const PRE_SERVICE_URL =
  '/webapp/servicechatv2/?' +
  '&pageCode=cs_main&channel=RDHM&isPreSale=1&isHideNavBar=YES';
export const flaglogo = `${ImageUrl.CAR_APP_IMAGE_PATH}flaglogo.png`;
export const flagtousu = `${ImageUrl.CAR_APP_IMAGE_PATH}flagtousu.png`;
export const flagshensu = `${ImageUrl.CAR_APP_IMAGE_PATH}flagshensu.png`;
export const osdinvoice = 'https://m.ctrip.com/webapp/carhire/xsd/osdinvoice';

export const ANIMATE_DURATION = 300;
export const GUTTER = 10;

// 电话客服弹层入口（不同入口可能样式不同）
export enum CustomerPhoneModalType {
  Customer = 1, // 客服样式
  Phone = 2, // 纯电话样式
  ViolationPhone = 3, // 违章咨询
  FooterBar = 4, // 底部bar拨打
  Penalty = 5, // 违约金服务进度卡片
  HomeOrderCard = 6, // 首页订单卡片
}

// 取车材料子项类型
export enum PickUpMaterialsItemType {
  IdCard = 1, // 身份
  Driver = 2, // 驾照
  Payment = 3, // 支付方式
  ThirdPay = 4, // 支付宝微信
  CreditCard = 5, // 信用卡
  Marketing = 6, // 红色文案
}

// 若id为4，此字段值表示1-支付宝、2-微信、3、支付宝微信
export enum PickUpMaterialsPayType {
  Alipay = 1, // 支付宝
  WeChatPay = 2, // 微信
  All = 3,
}

export enum VendorIDType {
  directConnection = 30000, // 小于30000表示直连供应商
  yihai = 9787, // 一嗨
  wukong = 13034,
}

// 真实补足资金类型，0 没有，1 未解冻/未完结 2，已解冻，完结
export enum RealPayItemType {
  No = 0,
  NotEnd = 1,
  End = 2,
}

export enum OrderStatusCtrip {
  // 已提交
  COMMITTED = 'CAR_COMMITTED',
  // 处理中
  PROCESSING = 'CAR_PROCESSING',
  // 已确认
  CONFIRMED = 'CAR_CONFIRMED',
  // 取消中
  CANCELLING = 'CAR_CANCELLING',
  // 已取消
  CANCELLED = 'CAR_CANCELLED',
  // 已成交
  COMPLETED = 'CAR_COMPLETED',
  // 待支付
  WAITING_PAY = 'CAR_WAITING_PAY',
  // 修改中
  MODIFYING = 'CAR_MODIFYING',
  // 支付中
  PAYING = 'CAR_PAYING',
  // 用车中
  IN_SERVICE = 'CAR_IN_SERVICE',
}

export const ORDER_INTERVAL_TIME = 8000; // 轮询时间

// 操作Id 与订单详情页枚举ORDER_BUTTON保持一致
// 其他的界面操作定义在12以后
export enum DirectOpen {
  Link = 0, // 链接跳转
  ToPay = 1,
  CancelBooking = 2,
  ModifyOrder = 7,
  Renewal = 11,
  OrderDetail = 13,
  Contact = 14,
  PickUpMaterials = 15,
  SelfService = 19,
  // 取还指引页打电话弹窗
  CallPhone = 21,
  // 升级车行保障服务
  UpgradeCarService = 22,
  // 自助取还开锁/关锁
  Lock = 'lock',
  TravelLimit = 23, // 海外旅行限制模块
  FeeDetail = 'feeDetail', // 费用明细
  OsdInsurance = 'osdIns', // 出境保险
  CancelPolicy = 'cancelPolicy', // 取消政策
  CarDetail = 'carDetail', // 车辆详情
  ServiceClaimMore = 'serviceClaimMore', // 门店服务保障相关要求及须知
  OrderRefundDetail = 'orderRefundDetail', // 查看订单退款详情
  DepositDetail = 'depositDetail', // 查看押金明细
  LimitRule = 'limitRule', // 限行规则
  Reviews = 'reviews', // 门店信息弹层 点评tab
  Attention = 'attention', // 门店信息弹层 租车必读
  EasyLife = 'easyLife', // 门店信息弹层 无忧租弹层
  IsdGuarantee = 'isdGuarantee', // 国内加购人身及财物险
  IsdCarService = 'isdCarService', // 国内车行保障服务
  SupplementList = 'supplementList', // 车损记录违章记录
  DepositFree = 'depositFree', // 去免押弹窗
  AdvanceReturnRecord = 'advanceReturnRecord', // 提前还车记录
  renewListRecord = 'renewListRecord', // 续租记录
  AdvanceReturnApply = 18, // 提前还车申请
}

export enum DirectOpenSub {
  first = '0',
  second = '1',
  third = '2',
}

export const InsuranceAnchorCategory = {
  Insurance: '套餐构成',
  insuranceDetail: '保障详情',
  insuranceClaim: '理赔流程',
};

export enum StoreServiceType {
  // 站内取还
  PickUpInStation = '16',
}

// 自助取消违约金的状态
export enum CancelPenaltyStatus {
  // 已提交
  Submit = 0,
  // 已同意
  Agree = 1,
  // 已拒绝
  Refuse = 2,
}

export enum CreateInsOrderResCode {
  getPriceFail1 = '4005', // 未获取到价格
  getPriceFail2 = '4006', // 未获取到价格
  priceUnMatch = '4007', // 价格不一致
}

export enum CancelReasonCode {
  TripCancellation = 1, // 行程取消
  haveLowerPrice = 2, // 有更低价
  changeOrder = 3, // 修改订单/行程
  vehicleProblem = 4, // 车辆问题
  WithoutCertificate = 5, // 未带取车证件
  StoreNoCar = 7, // 门店告知无车
  Other = 9, // 其他
  unFreeDeposit = 10, // 订单未免押
  EpidemicControl = 11, // 疫情管控
  // osd
  OsdChangeOrder = 24, // 修改订单/行程
  OsdTripCancellation = 25, // 行程取消
  OsdHaveLowerPrice = 20, // 有更低价
  LicenseDocumentIssues = 26, // 驾照证件问题
  CreditCardProblems = 29, // 信用卡问题
  FewAddOnOptions = 33, // 可选附加产品少
  UnCrossTheBorder = 37, // 车辆不能过境
  OsdOther = 38, // 其他
  UnionPayDualCardsNotSupported = 30, // 不支持银联双币卡
  CardsKind = 31, // 卡种类
  goForLicense = 28, // 驾照翻译件问题
}
// 1-押金减免规则说明 2-补足资金退还说明
export enum LinkTypes {
  rule = 1,
  note = 2,
}

// type 0-押金政策 ，1-免押弹层
export enum DepositModalType {
  detail = 0,
  toAuthent = 1,
}
export enum VendorImType {
  Top = 1,
  Bottom = 2,
}

export enum PhoneModalFromWhere {
  homeOrderCard = 1,
  faqPage = 2,
  hasViolation = 3,
}

export enum OrderStatusTextLabelType {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  CANCEL = 'CANCEL',
}

export enum SelfServiceCode {
  TO_LOOK_FOR_CAR_PAGE = 1, // 跳转寻车页
  TO_AUTH_PAGE = 2, // 跳转取车认证页
  SHOW_TOAST = 3, // 不进入任何页面，直接文案提示
  TO_INSPECT_CAR_PAGE = 4, // 跳转验车中页面
  TO_CONTROL_CAR_PAGE = 5, // 跳转车辆控制页
  TO_DROP_OFF_COMPLETE_PAGE = 6, // 跳转还车完成页
  TO_DROP_OFF_UPLOAD_STATUS = 7, // 跳转还车图片上传页
  TO_LOOK_FOR_CAR_PAGE_AND_OPEN_INSPECT_POPUP = 8, // 跳转寻车页并打开合同签署弹层
  TO_CAR_CONDITION = 9, // 跳转车况页
  TO_RETURN_POINT = 10, // 去还车点
  TO_CAR_CONDITION_DROP_OFF = 11, // 跳转车况页锚定还车Tab
}

// 页面汇总: http://conf.ctripcorp.com/pages/viewpage.action?pageId=1957201288
export enum SelfServicePageName {
  LOOK_FOR_CAR_PAGE = 'seekVehicle', // 寻车页
  AUTH_PAGE = 'pickUpAuth', // 取车认证页
  INSPECT_CAR_PAGE = 'selfServicePickup', // 验车中页面
  CONTROL_CAR_PAGE = 'using', // 车辆控制页
  DROP_OFF_COMPLETE_PAGE = 'payStatus', // 还车完成页
  CAR_CONDITION = 'carCondition', // 车况查看页
  RETURN_POINT = 'returnPoint', // 去还车点
  DROP_OFF_UPLOAD_STATUS = 'dropoffUploadStatus', // 还车图片上传页
}

export enum OperationType {
  /**
   * 寻车
   */
  FIND = 1,
  /**
   * 开始验车——只验车，不取车
   */
  CHECK = 2,
  /**
   * 开始验车——验车，也取车（对应人脸识别之后）
   */
  AFTER_CHECK = 3,
  /**
   * 确认无误，完成验车
   */
  CHECK_FINISH = 4,
  /**
   * 车机操作
   */
  VEHICLE_CONTROL = 5,
  /**
   * 去还车
   */
  GO_RETURN = 6,
}

/**
 * 车机控制
 */
export enum VehicleControl {
  /**
   * 鸣笛
   */
  RING = 1,
  /**
   * 闪灯
   */
  LIGHT = 2,
  /**
   * 开锁
   */
  UNLOCK = 3,
  /**
   * 关锁
   */
  LOCK = 4,
}

/**
 * 车机锁定状态
 */
export enum ILockStatus {
  /**
   * 关闭
   */
  Locked = 0,
  /**
   * 打开
   */
  UnLocked = 1,
}
export enum FUELTYPE {
  diesel = '2', // 柴油
  gasoline = '3', // 汽油
  hybrid = '4', // 混动
  electric = '5', // 电动
  fuel = '2416', // 汽油或柴油
  unKnownFuel = '2423', // 未知燃油
}

// 信用租授权场景
export enum CreditAuthScene {
  DepositPaymentModalOSD = 'DepositPaymentModalOSD', // 境外免押提示弹层
  FreeDepositEntryOSD = 'FreeDepositEntryOSD', // 境外免押入口
}
