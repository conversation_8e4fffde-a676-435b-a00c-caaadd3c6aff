/* eslint-disable prettier/prettier */
/**
 * UI自动化TestID维护
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=426318044
 */

const UITestId = {
  // 新增通用组件
  car_testid_optimal_selection: 'car_testid_optimal_selection',
  car_testid_vendorheader: 'car_testid_vendorheader',
  car_testid_vendorheader_righticon: 'car_testid_vendorheader_righticon',
  car_testid_couponmodal_close_mask: 'car_testid_couponmodal_close_mask',
  car_testid_couponmodal_header_lefticon:
    'car_testid_couponmodal_header_lefticon',
  car_testid_couponmodal_paidcoupon: 'car_testid_couponmodal_paidcoupon',
  car_testid_couponmodal_coupon_item_detail:
    'car_testid_couponmodal_coupon_item_detail',
  car_testid_couponmodal_coupon_item_receive:
    'car_testid_couponmodal_coupon_item_receive',
  car_testid_pricedesc_totalprice: 'car_testid_pricedesc_totalprice',
  car_testid_pricedesc_help: 'car_testid_pricedesc_help',
  car_testid_manuallocating: 'car_testid_manuallocating',
  car_testid_searchpannel_switch: 'car_testid_searchpannel_switch',
  car_testid_advantage_item: 'car_testid_advantage_item',
  car_testid_business_license: 'car_testid_business_license',
  car_testid_multi_media_item: 'car_testid_multi_media_item',
  car_testid_multi_media_item_bg_button:
    'car_testid_multi_media_item_bg_button',
  car_testid_multi_media_item_video: 'car_testid_multi_media_item_video',
  car_testid_multi_media_item_mute: 'car_testid_multi_media_item_mute',
  car_testid_multi_media_item_play: 'car_testid_multi_media_item_play',
  car_testid_multi_media_item_picture: 'car_testid_multi_media_item_picture',
  car_testid_multi_media_item_vr: 'car_testid_multi_media_item_vr',
  car_testid_easylife_tag_list_modal_lefticon:
    'car_testid_easylife_tag_list_modal_lefticon',
  car_testid_easylife_tag_list_modal_closemask:
    'car_testid_easylife_tag_list_modal_closemask',
  car_testid_easylife_tag_list_modal_done:
    'car_testid_easylife_tag_list_modal_done',
  car_testid_easylife_tag_list_modal_retry:
    'car_testid_easylife_tag_list_modal_retry',
  car_testid_totalprice_modal_close_mask:
    'car_testid_totalprice_modal_close_mask',
  car_testid_totalprice_modal_continue: 'car_testid_totalprice_modal_continue',
  car_testid_comp_price_modal_close_mask:
    'car_testid_comp_price_modal_close_mask',
  car_testid_easylifecompare_retry: 'car_testid_easylifecompare_retry',
  car_testid_pricegroup_detail_title_item:
    'car_testid_pricegroup_detail_title_item',
  car_testid_calendarprice_modal_close_mask:
    'car_testid_calendarprice_modal_close_mask',
  car_testid_calendarprice_modal_comfirm:
    'car_testid_calendarprice_modal_comfirm',
  car_testid_calendarprice_modal_retry: 'car_testid_calendarprice_modal_retry',
  car_testid_calendarprice_modal_showall:
    'car_testid_calendarprice_modal_showall',
  car_testid_integralmodal_close_mask: 'car_testid_integralmodal_close_mask',
  car_testid_integralmodal_know: 'car_testid_integralmodal_know',
  car_testid_warningtips_modal_close_mask:
    'car_testid_warningtips_modal_close_mask',
  car_testid_cacheexpirepop_reload: 'car_testid_cacheexpirepop_reload',
  car_testid_upgrademodal_update: 'car_testid_upgrademodal_update',
  car_testid_listpoll_reload: 'car_testid_listpoll_reload',
  car_testid_loginguide_banner: 'car_testid_loginguide_banner',
  car_testid_loginguide_receive: 'car_testid_loginguide_receive',
  car_testid_optimizationstreng_detail: 'car_testid_optimizationstreng_detail',
  car_testid_optimizationstreng_modal_know:
    'car_testid_optimizationstreng_modal_know',
  car_testid_virtualnumberstore_modal_closemask:
    'car_testid_virtualnumberstore_modal_closemask',
  car_testid_virtualnumberstore_modal_contact_item:
    'car_testid_virtualnumberstore_modal_contact_item',
  car_testid_backtotop: 'car_testid_backtotop',
  car_testid_businesslicense_modal_header_lefticon:
    'car_testid_businesslicense_modal_header_lefticon',
  car_testid_businesslicense_modal_closemask:
    'car_testid_businesslicense_modal_closemask',
  car_testid_etcIntro_modal_closemask: 'car_testid_etcIntro_modal_closemask',
  car_testid_etcIntro_modal_header_lefticon:
    'car_testid_etcIntro_modal_header_lefticon',
  car_testid_etcIntro_modal_gotit: 'car_testid_etcIntro_modal_gotit',
  car_testid_call_modal_closemask: 'car_testid_call_modal_closemas',
  car_testid_call_modal_callnow: 'car_testid_call_modal_callnow',
  car_testid_call_modal_close: 'car_testid_call_modal_close',
  car_testid_guarantee_item: 'car_testid_guarantee_item',
  car_testid_guarantee_item_priceinfo: 'car_testid_guarantee_item_priceinfo',
  car_testid_pricechange_cross: 'car_testid_pricechange_cross',
  car_testid_cancelpenalty_modal_closemask:
    'car_testid_cancelpenalty_modal_closemask',
  car_testid_cancelpenalty_modal_lefticon:
    'car_testid_cancelpenalty_modal_lefticon',
  car_testid_cancelpenalty_modal_apply: 'car_testid_cancelpenalty_modal_apply',
  car_testid_pricedetail_modal_closemask:
    'car_testid_pricedetail_modal_closemask',
  car_testid_feedetail_modal_closemask: 'car_testid_feedetail_modal_closemask',
  car_testid_feedetail_modal_header_lefticon:
    'car_testid_feedetail_modal_header_lefticon',
  car_testid_feedetail_modal_fee_item_help:
    'car_testid_feedetail_modal_fee_item_help',
  car_testid_creditrentsesame_modal_closemask:
    'car_testid_creditrentsesame_modal_closemask',
  car_testid_creditrentsesame_modal_button_item:
    'car_testid_creditrentsesame_modal_button_item',
  car_testid_modifyorder_modal_closemask:
    'car_testid_modifyorder_modal_closemask',
  car_testid_modifyorder_modal_button_item:
    'car_testid_modifyorder_modal_button_item',
  car_testid_freedepositrule_modal_closemask:
    'car_testid_freedepositrule_modal_closemask',
  car_testid_freedepositrule_modal_content_item:
    'car_testid_freedepositrule_modal_content_item',
  car_testid_freedepositrule_modal_footer_button:
    'car_testid_freedepositrule_modal_footer_button',
  car_testid_cancelzhima_modal_closemask:
    'car_testid_cancelzhima_modal_closemask',
  car_testid_cancelzhima_modal_button_left:
    'car_testid_cancelzhima_modal_button_left',
  car_testid_cancelzhima_modal_button_right:
    'car_testid_cancelzhima_modal_button_right',
  car_testid_price_alert_confirm: 'car_testid_price_alert_confirm',
  car_testid_price_alert_cancel: 'car_testid_price_alert_cancel',
  car_testid_pickupdowngrade_pop_closemask:
    'car_testid_pickupdowngrade_pop_closemask',
  car_testid_pickupdowngrade_pop_lefticon:
    'car_testid_pickupdowngrade_pop_lefticon',
  car_testid_pickupdowngrade_pop_button_item:
    'car_testid_pickupdowngrade_pop_button_item',
  car_testid_createins_modal_closemask: 'car_testid_createins_modal_closemask',
  car_testid_createins_modal_header_lefticon:
    'car_testid_createins_modal_header_lefticon',
  car_testid_insfailed_modal_closemask: 'car_testid_insfailed_modal_closemask',
  car_testid_insfailed_modal_pay: 'car_testid_insfailed_modal_pay',
  car_testid_insfailed_modal_back: 'car_testid_insfailed_modal_back',
  car_testid_depositratedescription_modal_closemask:
    'car_testid_depositratedescription_modal_closemask',
  car_testid_depositratedescription_modal_header_lefticon:
    'car_testid_depositratedescription_modal_header_lefticon',
  car_testid_flightdelayrules_modal_closemask:
    'car_testid_flightdelayrules_modal_closemask',
  car_testid_flightdelayrules_modal_header_lefticon:
    'car_testid_flightdelayrules_modal_header_lefticon',
  car_testid_restassured_banner: 'car_testid_restassured_banner',
  car_testid_vendorlog_optimize: 'car_testid_vendorlog_optimize',
  car_testid_vendorheader_comment: 'car_testid_vendorheader_comment',
  car_testid_sesamebar: 'car_testid_sesamebar',
  car_testid_sesamebar_radio_wrap: 'car_testid_sesamebar_radio_wrap',
  car_testid_sesamebar_radio: 'car_testid_sesamebar_radio',
  car_testid_sesamebar_righticon: 'car_testid_sesamebar_righticon',
  car_testid_insurancemorebox_checkbox_item:
    'car_testid_insurancemorebox_checkbox_item',
  car_testid_travellimit_item: 'car_testid_travellimit_item',
  car_testid_countrieslist_modal_closemask:
    'car_testid_countrieslist_modal_closemask',
  car_testid_countrieslist_modal_crossplace_item:
    'car_testid_countrieslist_modal_crossplace_item',
  car_testid_carpolicy: 'car_testid_carpolicy',
  car_testid_carpolicy_policytab_item: 'car_testid_carpolicy_policytab_item',
  car_testid_optimize_modal_closemask: 'car_testid_optimize_modal_closemask',
  car_testid_idtype_modal_closemask: 'car_testid_idtype_modal_closemask',
  car_testid_idtype_modal_id_item: 'car_testid_idtype_modal_id_item',
  car_testid_creditrent_modal_closemask:
    'car_testid_creditrent_modal_closemask',
  car_testid_creditrent_modal_gotit: 'car_testid_creditrent_modal_gotit',
  car_testid_insuranceslider_item: 'car_testid_insuranceslider_item',
  car_testid_order_confirm_modal_closemask:
    'car_testid_order_confirm_modal_closemask',
  car_testid_rentalcarsdate_picker_closemask:
    'car_testid_rentalcarsdate_picker_closemask',
  car_testid_rentalcarsdate_picker_header_lefticon:
    'car_testid_rentalcarsdate_picker_header_lefticon',
  car_testid_rentalcarsdate_picker_header_righticon:
    'car_testid_rentalcarsdate_picker_header_righticon',
  car_testid_selfservice_assistant_link:
    'car_testid_selfservice_assistant_link',
  car_testid_policytips_item: 'car_testid_policytips_item',
  car_testid_morecards: 'car_testid_morecards',
  car_testid_tipscard_addmicrogroup: 'car_testid_tipscard_addmicrogroup',
  car_testid_historymessageentry: 'car_testid_historymessageentry',
  car_testid_flightdelayruleentry: 'car_testid_flightdelayruleentry',
  car_testid_tip_warning_refresh: 'car_testid_tip_warning_refresh',
  car_testid_tip_warning_detail: 'car_testid_tip_warning_detail',
  car_testid_footermenus_modal_closemask:
    'car_testid_footermenus_modal_closemask',
  car_testid_footermenus_modal_menu_item:
    'car_testid_footermenus_modal_menu_item',
  car_testid_footermenus_modal_cancel: 'car_testid_footermenus_modal_cancel',
  car_testid_carassistant_button_item: 'car_testid_carassistant_button_item',
  car_testid_carassistant_link_item: 'car_testid_carassistant_link_item',
  car_testid_carassistant_more: 'car_testid_carassistant_more',
  car_testid_carassistant_modal_closemask:
    'car_testid_carassistant_modal_closemask',
  car_testid_carassistant_modal_done: 'car_testid_carassistant_modal_done',
  car_testid_privilegetab: 'car_testid_privilegetab',
  car_testid_vehicleandvendorinfo_returntip:
    'car_testid_vehicleandvendorinfo_returntip',
  car_testid_vehicleandvendorinfo_location_pickup:
    'car_testid_vehicleandvendorinfo_location_pickup',
  car_testid_vehicleandvendorinfo_location_dropoff:
    'car_testid_vehicleandvendorinfo_location_dropoff',
  car_testid_vehicleandvendorinfo_vendor:
    'car_testid_vehicleandvendorinfo_vendor',
  car_testid_vehicleandvendorinfo_limit:
    'car_testid_vehicleandvendorinfo_limit',
  car_testid_vendortag_item: 'car_testid_vendortag_item',
  car_testid_carrentalmustread_policy: 'car_testid_carrentalmustread_policy',
  car_testid_carrentalmustread_accident:
    'car_testid_carrentalmustread_accident',
  car_testid_carservice_upgrade: 'car_testid_carservice_upgrade',
  car_testid_buyinsurance_item: 'car_testid_buyinsurance_item',
  car_testid_buyinsurance_item_button: 'car_testid_buyinsurance_item_button',
  car_testid_countrieslistfooter_clear: 'car_testid_countrieslistfooter_clear',
  car_testid_countrieslistfooter_confirm:
    'car_testid_countrieslistfooter_confirm',
  car_testid_gpdr_license: 'car_testid_gpdr_license',
  car_testid_optimizationstrengthen_modal_close:
    'car_testid_optimizationstrengthen_modal_close',
  car_testid_insurancedetail_modal_closemask:
    'car_testid_insurancedetail_modal_closemask',
  car_testid_sesameguider_modal_closemask:
    'car_testid_sesameguider_modal_closemask',
  car_testid_giveup_modal_closemask: 'car_testid_giveup_modal_closemask',
  car_testid_giveup_modal_gotit: 'car_testid_giveup_modal_gotit',
  car_testid_auth_modal_closemask: 'car_testid_auth_modal_closemask',
  car_testid_auth_modal_explain_link_item_help:
    'car_testid_auth_modal_explain_link_item_help',
  car_testid_auth_modal_submit: 'car_testid_auth_modal_submit',
  car_testid_auth_modal_giveup: 'car_testid_auth_modal_giveup',
  car_testid_progressstage_item_button: 'car_testid_progressstage_item_button',
  car_testid_ordermaterials_rightbutton_item:
    'car_testid_ordermaterials_rightbutton_item',
  car_testid_ordermaterials_note_item: 'car_testid_ordermaterials_note_item',
  car_testid_limitrulespop_closemask: 'car_testid_limitrulespop_closemask',
  car_testid_limitrulespop_header_lefticon:
    'car_testid_limitrulespop_header_lefticon',
  car_testid_cardialog_closemask: 'car_testid_cardialog_closemask',
  car_testid_sesamerepeatorder_dialog_cancel:
    'car_testid_sesamerepeatorder_dialog_cancel',
  car_testid_sesamerepeatorder_dialog_confirm:
    'car_testid_sesamerepeatorder_dialog_confirm',
  car_testid_buyInsconfirm_dialog_confirm:
    'car_testid_buyInsconfirm_dialog_confirm',
  car_testid_carservice_modal_closemask:
    'car_testid_carservice_modal_closemask',
  car_testid_carservice_modal_header_lefticon:
    'car_testid_carservice_modal_header_lefticon',
  car_testid_carservice_modal_footer_button:
    'car_testid_carservice_modal_footer_button',
  car_testid_carservicedetail_modal_closemask:
    'car_testid_carservicedetail_modal_closemask',
  car_testid_continuepayfail_dialog_cancel:
    'car_testid_continuepayfail_dialog_cancel',
  car_testid_continuepayfail_dialog_confirm:
    'car_testid_continuepayfail_dialog_confirm',
  car_testid_advance_return_record_modal_closemask:
    'car_testid_advance_return_record_modal_closemask',
  car_testid_advance_return_record_modal_header_lefticon:
    'car_testid_advance_return_record_modal_header_lefticon',
  car_testid_advance_return_record_modal_showfeemodal:
    'car_testid_advance_return_record_modal_showfeemodal',
  car_testid_advance_return_record_modal_cancel:
    'car_testid_advance_return_record_modal_cancel',
  car_testid_advance_fee_detail_modal_closemask:
    'car_testid_advance_fee_detail_modal_closemask',
  car_testid_advance_fee_detail_modal_header_lefticon:
    'car_testid_advance_fee_detail_modal_header_lefticon',
  car_testid_advance_fee_detail_modal_footer:
    'car_testid_advance_fee_detail_modal_footer',
  car_testid_feetitleexplain_modal_closemask:
    'car_testid_feetitleexplain_modal_closemask',
  car_testid_feetitleexplain_modal_view:
    'car_testid_feetitleexplain_modal_view',
  car_testid_materials_modal_closemask: 'car_testid_materials_modal_closemask',
  car_testid_materials_modal_title_voucher:
    'car_testid_materials_modal_title_voucher',
  car_testid_materials_modal_driverlicense_item:
    'car_testid_materials_modal_driverlicense_item',
  car_testid_etcusehelper_modal_closemask:
    'car_testid_etcusehelper_modal_closemask',
  car_testid_etcusehelper_modal_header_lefticon:
    'car_testid_etcusehelper_modal_header_lefticon',
  car_testid_etcusehelper_modal_nav_item:
    'car_testid_etcusehelper_modal_nav_item',
  car_testid_etcusehelper_modal_gotit: 'car_testid_etcusehelper_modal_gotit',
  car_testid_claimprocess_modal_closemask:
    'car_testid_claimprocess_modal_closemask',
  car_testid_claimprocess_modal_header_lefticon:
    'car_testid_claimprocess_modal_header_lefticon',
  car_testid_vehicleusenotes_modal_closemask:
    'car_testid_vehicleusenotes_modal_closemask',
  car_testid_vehicleusenotes_modal_header_lefticon:
    'car_testid_vehicleusenotes_modal_header_lefticon',
  car_testid_vehicleusenotes_modal_note_item:
    'car_testid_vehicleusenotes_modal_note_item',
  car_testid_vehicleusenotes_modal_note_item_link:
    'car_testid_vehicleusenotes_modal_note_item_link',
  car_testid_vehicleusenotes_modal_gotit:
    'car_testid_vehicleusenotes_modal_gotit',
  car_testid_orderpickUpmaterials_modal_closemask:
    'car_testid_orderpickUpmaterials_modal_closemask',
  car_testid_orderpickUpmaterials_modal_header_lefticon:
    'car_testid_orderpickUpmaterials_modal_header_lefticon',
  car_testid_orderpickUpmaterials_modal_gotit:
    'car_testid_orderpickUpmaterials_modal_gotit',
  car_testid_distanceinvalidate_modal_closemask:
    'car_testid_distanceinvalidate_modal_closemask',
  car_testid_distanceinvalidate_modal_contract:
    'car_testid_distanceinvalidate_modal_contract',
  car_testid_distanceinvalidate_modal_gotit:
    'car_testid_distanceinvalidate_modal_gotit',
  car_testid_applypenaltyconfirm_modal_closemask:
    'car_testid_applypenaltyconfirm_modal_closemask',
  car_testid_applypenaltyconfirm_modal_button_item:
    'car_testid_applypenaltyconfirm_modal_button_item',
  car_testid_supplement_modal_done: 'car_testid_supplement_modal_done',
  car_testid_supplement_vehicledamage_item:
    'car_testid_supplement_vehicledamage_item',
  car_testid_supplement_footer_button: 'car_testid_supplement_footer_button',
  car_testid_limittextbar: 'car_testid_limittextbar',
  car_testid_queryfailed_button: 'car_testid_queryfailed_button',
  car_testid_renewsingledate_header_righticon:
    'car_testid_renewsingledate_header_righticon',
  car_testid_renewsingledate_closemask: 'car_testid_renewsingledate_closemask',
  car_testid_renewsingledate_header_lefticon:
    'car_testid_renewsingledate_header_lefticon',
  car_testid_rerentinsurance_header: 'car_testid_rerentinsurance_header',
  car_testid_rerentinsurance_checkbox: 'car_testid_rerentinsurance_checkbox',
  car_testid_rerentinsurance_radio: 'car_testid_rerentinsurance_radio',
  car_testid_rerentfooter_save: 'car_testid_rerentfooter_save',
  car_testid_rerentfooter_cancel: 'car_testid_rerentfooter_cancel',
  car_testid_rerentfooter_continue: 'car_testid_rerentfooter_continue',
  car_testid_rerentfooter_retry: 'car_testid_rerentfooter_retry',
  car_testid_renewLayer_closemask: 'car_testid_renewLayer_closemask',
  car_testid_renewLayer_header_lefticon:
    'car_testid_renewLayer_header_lefticon',
  car_testid_renewLayer_cancel: 'car_testid_renewLayer_cancel',
  car_testid_singledate_modal_closemask:
    'car_testid_singledate_modal_closemask',
  car_testid_singledate_modal_header_lefticon:
    'car_testid_singledate_modal_header_lefticon',
  car_testid_singledate_modal_header_righticon:
    'car_testid_singledate_modal_header_righticon',
  car_testid_discount: 'car_testid_discount',
  car_testid_membercoupon_receive: 'car_testid_membercoupon_receive',
  car_testid_membercoupon_use: 'car_testid_membercoupon_use',
  car_testid_membercoupon_check: 'car_testid_membercoupon_check',

  // 城市页索引
  car_testid_page_location_anchor_side: 'car_testid_page_location_anchor_side',
  // 区域切换城市按钮
  car_testid_page_location_go_city: 'car_testid_page_location_go_city',
  car_testid_page_location_SubAreaButton:
    'car_testid_page_location_SubAreaButton',
  car_testid_search_result_item: 'car_testid_search_result_item',
  // 城市页新增
  car_testid_page_location_header_goback:
    'car_testid_page_location_header_goback',
  car_testid_page_location_searchinput_input:
    'car_testid_page_location_searchinput_input',
  car_testid_page_location_searchinput_clear:
    'car_testid_page_location_searchinput_clear',
  car_testid_page_location_searchinput_go_area:
    'car_testid_page_location_searchinput_go_area',
  car_testid_page_location_searchinput_cancel:
    'car_testid_page_location_searchinput_cancel',
  car_testid_page_location_error_refresh:
    'car_testid_page_location_error_refresh',
  car_testid_page_location_city_tab_item:
    'car_testid_page_location_city_tab_item',
  car_testid_page_location_city_clearhistory:
    'car_testid_page_location_city_clearhistory',
  car_testid_page_location_city_anchor: 'car_testid_page_location_city_anchor',
  car_testid_page_location_city_item: 'car_testid_page_location_city_item',
  car_testid_page_location_local_position:
    'car_testid_page_location_local_position',
  car_testid_page_location_local_refresh:
    'car_testid_page_location_local_refresh',
  car_testid_page_location_area_history:
    'car_testid_page_location_area_history',
  car_testid_page_location_area_item: 'car_testid_page_location_area_item',
  car_testid_page_location_area_more: 'car_testid_page_location_area_more',
  car_testid_page_location_area_recommend_item:
    'car_testid_page_location_area_recommend_item',
  car_testid_page_location_anchor_side_item:
    'car_testid_page_location_anchor_side_item',

  // 列表页头部
  car_testid_page_list_header: 'car_testid_page_list_header',
  // 列表页搜索弹层
  car_testid_page_list_search_pannel_modal:
    'car_testid_page_list_search_pannel_modal',
  // 列表页搜索弹层 - 蒙层关闭按钮
  car_testid_page_list_search_pannel_close_mask:
    'car_testid_page_list_search_pannel_close_mask',
  // 列表页搜索弹层时间组件
  car_testid_page_list_search_pannel_timeline:
    'car_testid_page_list_search_pannel_timeline',
  // 列表也搜索弹层地区组件-取车组件
  car_testid_page_list_search_pannel_locationItem_pickup:
    'car_testid_page_list_search_pannel_locationItem_pickup',
  // 列表也搜索弹层地区组件-取车组件
  car_testid_page_list_search_pannel_locationItem_dropoff:
    'car_testid_page_list_search_pannel_locationItem_dropoff',
  // 列表页车型列表组件
  car_testid_page_list_vehicleList: 'car_testid_page_list_vehicleList',
  // 列表页车型列表合并车型组车型——供应商报价组件
  car_testid_page_list_vehicleList_group_vendoritem:
    'car_testid_page_list_vehicleList_group_vendoritem',
  // 列表页优惠券入口
  car_testid_page_list_coupon_entry: 'car_testid_page_list_coupon_entry',
  // 列表页唐图模块
  car_testid_page_list_tangram_entry: 'car_testid_page_list_tangram_entry',
  // 自助取还中插Banner模块
  car_testid_page_list_self_service_entry:
    'car_testid_page_list_self_service_entry',
  // 自助取还中插Banner模块
  car_testid_page_list_easylife_entry: 'car_testid_page_list_easylife_entry',
  car_testid_page_list_filter_bar: 'car_testid_page_list_filter_bar',
  // 列表页底部回显筛选项
  car_testid_page_list_selected_filter_items:
    'car_testid_page_list_selected_filter_items',
  // 筛选弹窗底部清除按钮
  car_testid_page_list_filter_modal_clear_btn:
    'car_testid_page_list_filter_modal_clear_btn',
  car_testid_page_list_filter_modal_confirm_btn:
    'car_testid_page_list_filter_modal_confirm_btn',
  car_testid_page_list_selected_filter_item:
    'car_testid_page_list_selected_filter_item',
  car_testid_page_list_vehicle_bar: 'car_testid_page_list_vehicle_bar',
  car_testid_page_list_vehicle_sku: 'car_testid_page_list_vehicle_sku',
  car_testid_page_list_vehicle_osd: 'car_testid_page_list_vehicle_osd',
  car_testid_page_list_vendor_list_enter_osd:
    'car_testid_page_list_vendor_list_enter_osd',
  car_testid_page_list_vendor_list_modal_osd:
    'car_testid_page_list_vendor_list_modal_osd',
  car_testid_page_list_vendor_list_modal_boot_osd:
    'car_testid_page_list_vendor_list_modal_boot_osd',
  car_testid_page_list_vendor_list_modal_item_osd:
    'car_testid_page_list_vendor_list_modal_item_osd',
  car_testid_page_list_tiplist: 'car_testid_page_list_tiplist',
  car_testid_page_list_limitTip: 'car_testid_page_list_limitTip',
  // 列表页排序弹窗
  car_testid_page_list_filter_sort_modal:
    'car_testid_page_list_filter_sort_modal',
  // 列表页新增
  car_testid_page_list_searchconditiontip_close:
    'car_testid_page_list_searchconditiontip_close',
  car_testid_page_list_header_goback: 'car_testid_page_list_header_goback',
  car_testid_page_list_header_modify: 'car_testid_page_list_header_modify',
  car_testid_page_list_filter_bar_item: 'car_testid_page_list_filter_bar_item',
  car_testid_page_list_quick_filter_bar_item:
    'car_testid_page_list_quick_filter_bar_item',
  car_testid_page_list_tip_coronavirus_righticon:
    'car_testid_page_list_tip_coronavirus_righticon',
  car_testid_page_list_tip_coronavirus_detail:
    'car_testid_page_list_tip_coronavirus_detail',
  car_testid_page_list_tip_limit_detail:
    'car_testid_page_list_tip_limit_detail',
  car_testid_page_list_tip_banner_item: 'car_testid_page_list_tip_banner_item',
  car_testid_page_list_tip_banner_close:
    'car_testid_page_list_tip_banner_close',
  car_testid_page_list_tip_license_righticon:
    'car_testid_page_list_tip_license_righticon',
  car_testid_page_list_tip_promotionfilter_detail:
    'car_testid_page_list_tip_promotionfilter_detail',
  car_testid_page_list_tip_platformsecurityandcoupon_detail:
    'car_testid_page_list_tip_platformsecurityandcoupon_detail',
  car_testid_page_list_tip_coupon_entry_detail:
    'car_testid_page_list_tip_coupon_entry_detail',
  car_testid_page_list_tip_searchlessresult_modify:
    'car_testid_page_list_tip_searchlessresult_modify',
  car_testid_page_list_tip_noresultcompensation:
    'car_testid_page_list_tip_noresultcompensation',
  car_testid_page_list_nomatch_recommend:
    'car_testid_page_list_nomatch_recommend',
  car_testid_page_list_nomatch_recommend_righticon:
    'car_testid_page_list_nomatch_recommend_righticon',
  car_testid_page_list_nomatch_modifyrentaldate:
    'car_testid_page_list_nomatch_modifyrentaldate',
  car_testid_page_list_nomatch_operate: 'car_testid_page_list_nomatch_operate',
  car_testid_page_list_vehiclelist_login:
    'car_testid_page_list_vehiclelist_login',
  car_testid_page_list_vehiclelist_agemodify:
    'car_testid_page_list_vehiclelist_agemodify',
  car_testid_page_list_vehiclelist_activityenterce:
    'car_testid_page_list_vehiclelist_activityenterce',
  car_testid_page_list_vehiclelist_easylifeentrance:
    'car_testid_page_list_vehiclelist_easylifeentrance',
  car_testid_page_list_vehiclelist_carcenterenter_look:
    'car_testid_page_list_vehiclelist_carcenterenter_look',
  car_testid_page_list_vehiclelist_feedBackenterance:
    'car_testid_page_list_vehiclelist_feedBackenterance',
  car_testid_page_list_vehiclelist_yunnanbanner:
    'car_testid_page_list_vehiclelist_yunnanbanner',
  car_testid_page_list_agemodal_item: 'car_testid_page_list_agemodal_item',
  car_testid_page_list_agemodal_close_mask:
    'car_testid_page_list_agemodal_close_mask',
  car_testid_page_list_selectedfilter_clear:
    'car_testid_page_list_selectedfilter_clear',
  car_testid_page_list_selectedfilter_item:
    'car_testid_page_list_selectedfilter_item',
  car_testid_page_list_vehicle_item: 'car_testid_page_list_vehicle_item',
  car_testid_page_list_vehicle_item_badge:
    'car_testid_page_list_vehicle_item_badge',
  car_testid_page_list_vehicle_item_image:
    'car_testid_page_list_vehicle_item_image',
  car_testid_page_list_vehicle_item_info:
    'car_testid_page_list_vehicle_item_info',
  car_testid_page_list_vehicle_item_easylife_totalprice:
    'car_testid_page_list_vehicle_item_easylife_totalprice',
  car_testid_page_list_vehicle_item_easylife_book:
    'car_testid_page_list_vehicle_item_easylife_book',
  car_testid_page_list_vehicle_name_similar:
    'car_testid_page_list_vehicle_name_similar',
  car_testid_page_list_vehicle_desc_item:
    'car_testid_page_list_vehicle_desc_item',
  car_testid_page_list_vehicle_totalprice:
    'car_testid_page_list_vehicle_totalprice',
  car_testid_page_list_vehiclelist_easylife_banner:
    'car_testid_page_list_vehiclelist_easylife_banner',
  car_testid_page_list_vehiclelist_footer_next:
    'car_testid_page_list_vehiclelist_footer_next',
  car_testid_page_list_vendor_item_marketlabel:
    'car_testid_page_list_vendor_item_marketlabel',
  car_testid_page_list_vendor_item_priceinfo:
    'car_testid_page_list_vendor_item_priceinfo',
  car_testid_page_list_vendor_item_priceinfo_price:
    'car_testid_page_list_vendor_item_priceinfo_price',
  car_testid_page_list_searchnoresult: 'car_testid_page_list_searchnoresult',
  car_testid_page_list_filtermodal_close_mask:
    'car_testid_page_list_filtermodal_close_mask',
  car_testid_page_list_filtermodal_sort_item:
    'car_testid_page_list_filtermodal_sort_item',
  car_testid_page_list_filtermodal_sort_item_choose:
    'car_testid_page_list_filtermodal_sort_item_choose',
  car_testid_page_list_filtermodal_nav_item:
    'car_testid_page_list_filtermodal_nav_item',
  car_testid_page_list_filtermodal_filter_item_header_clear:
    'car_testid_page_list_filtermodal_filter_item_header_clear',
  car_testid_page_list_filtermodal_filter_item_header_more:
    'car_testid_page_list_filtermodal_filter_item_header_more',
  car_testid_page_list_vendorlistmodal_close_mask:
    'car_testid_page_list_vendorlistmodal_close_mask',
  car_testid_page_list_vendorlistmodal_gotop:
    'car_testid_page_list_vendorlistmodal_gotop',
  car_testid_page_list_secretboxmodal_close_mask:
    'car_testid_page_list_secretboxmodal_close_mask',
  car_testid_page_list_secretboxmodal_item:
    'car_testid_page_list_secretboxmodal_item',
  car_testid_page_list_secretboxmodal_item_totalprice:
    'car_testid_page_list_secretboxmodal_item_totalprice',
  car_testid_page_list_secretboxmodal_item_book:
    'car_testid_page_list_secretboxmodal_item_book',
  car_testid_page_list_search_pannel_close:
    'car_testid_page_list_search_pannel_close',
  car_testid_page_list_search_pannel_pickup_city:
    'car_testid_page_list_search_pannel_pickup_city',
  car_testid_page_list_search_pannel_pickup_area:
    'car_testid_page_list_search_pannel_pickup_area',
  car_testid_page_list_search_pannel_dropoff_city:
    'car_testid_page_list_search_pannel_dropoff_city',
  car_testid_page_list_search_pannel_dropoff_area:
    'car_testid_page_list_search_pannel_dropoff_area',
  car_testid_page_list_search_pannel_ptime:
    'car_testid_page_list_search_pannel_ptime',
  car_testid_page_list_search_pannel_rtime:
    'car_testid_page_list_search_pannel_rtime',
  car_testid_page_list_search_pannel_timiegap:
    'car_testid_page_list_search_pannel_timiegap',
  car_testid_page_list_search_pannel_agetip_close:
    'car_testid_page_list_search_pannel_agetip_close',
  car_testid_page_list_search_pannel_agetip_done:
    'car_testid_page_list_search_pannel_agetip_done',
  car_testid_page_list_search_pannel_agetip_cancel:
    'car_testid_page_list_search_pannel_agetip_cancel',
  car_testid_page_list_driverlicensepop_confirm:
    'car_testid_page_list_driverlicensepop_confirm',
  car_testid_page_list_driverlicensepop_cancel:
    'car_testid_page_list_driverlicensepop_cancel',
  car_testid_page_list_timeoutpop_confirm:
    'car_testid_page_list_timeoutpop_confirm',
  car_testid_page_list_limitrulespop_done:
    'car_testid_page_list_limitrulespop_done',
  car_testid_page_list_limitrulespop_gomap:
    'car_testid_page_list_limitrulespop_gomap',
  car_testid_page_list_vehiclemodal_close_mask:
    'car_testid_page_list_vehiclemodal_close_mask',
  car_testid_page_list_vehiclemodal_lefticon:
    'car_testid_page_list_vehiclemodal_lefticon',

  // 详情页租车券包-展示券列表
  car_testid_page_vendorList_show_coupon_modal:
    'car_testid_page_vendorList_show_coupon_modal',
  // 详情页租车券包-展示券列表
  car_testid_page_vendorList_coupon_modal:
    'car_testid_page_vendorList_coupon_modal',
  // 详情页租车券包-展示券列表-全部领取按钮
  car_testid_page_vendorList_coupon_modal_footer_button:
    'car_testid_page_vendorList_coupon_modal_footer_button',
  // 详情页-限行信息
  car_testid_page_vendorList_limit_tip: 'car_testid_page_vendorList_limit_tip',
  // 详情页-安心订 放心行模块
  car_testid_page_vendorList_relieved_booking:
    'car_testid_page_vendorList_relieved_booking',
  // 详情页-ScrollView
  car_testid_page_vendorList_ScrollView:
    'car_testid_page_vendorList_ScrollView',
  // 详情页-车型图片
  car_testid_page_vendorList_VehicleImage:
    'car_testid_page_vendorList_VehicleImage',
  // 详情页-页面回退按钮
  car_testid_page_vendorList_PageBack: 'car_testid_page_vendorList_PageBack',
  car_testid_page_vendorList_carInfo: 'car_testid_page_vendorList_carInfo',
  car_testid_page_vendorList_carInfoModal:
    'car_testid_page_vendorList_carInfoModal',
  car_testid_page_vendorList_carInfoModal_image:
    'car_testid_page_vendorList_carInfoModal_image',
  car_testid_page_vendorList_carInfoModal_config:
    'car_testid_page_vendorList_carInfoModal_config',
  car_testid_page_vendorList_carInfo_newEnergyVeh_btn:
    'car_testid_page_vendorList_carInfo_newEnergyVeh_btn',
  // 详情页-取还车时间回显区-展示逻辑
  car_testid_page_vendorList_date_ptime:
    'car_testid_page_vendorList_date_ptime',
  car_testid_page_vendorList_date_rtime:
    'car_testid_page_vendorList_date_rtime',
  // 国内产品详情页头部图片的子分类
  car_testid_page_vendorlist_header_image_bannersub:
    'car_testid_page_vendorlist_header_image_bannersub',
  // 国内产品详情页新增
  car_testid_page_vendorlist_header_goback:
    'car_testid_page_vendorlist_header_goback',
  car_testid_page_vendorlist_header_location:
    'car_testid_page_vendorlist_header_location',
  car_testid_page_vendorlist_images_mute:
    'car_testid_page_vendorlist_vehicleimages_mute',
  car_testid_page_vendorlist_images_album:
    'car_testid_page_vendorlist_vehicleimages_album',
  car_testid_page_vendorlist_images_pre:
    'car_testid_page_vendorlist_images_pre',
  car_testid_page_vendorlist_images_next:
    'car_testid_page_vendorlist_images_next',
  car_testid_page_vendorlist_marketing_banner:
    'car_testid_page_vendorlist_marketing_banner',
  car_testid_page_vendorlist_limit_detail:
    'car_testid_page_vendorlist_limit_detail',
  car_testid_page_vendorlist_specific_header:
    'car_testid_page_vendorlist_specific_header',
  car_testid_page_vendorlist_specific_nomatch_retry:
    'car_testid_page_vendorlist_specific_nomatch_retry',
  car_testid_page_vendorlist_specific_more:
    'car_testid_page_vendorlist_specific_more',
  car_testid_page_vendorlist_specific_gotop:
    'car_testid_page_vendorlist_specific_gotop',
  car_testid_page_vendorlist_product_modal_close_mask:
    'car_testid_page_vendorlist_product_modal_close_mask',
  car_testid_page_vendorlist_product_modal_header_lefticon:
    'car_testid_page_vendorlist_product_modal_header_lefticon',
  car_testid_page_vendorlist_product_modal_header_phone:
    'car_testid_page_vendorlist_product_modal_header_phone',
  car_testid_page_vendorlist_product_modal_retry:
    'car_testid_page_vendorlist_product_modal_retry',
  car_testid_page_vendorlist_product_modal_video:
    'car_testid_page_vendorlist_product_modal_video',
  car_testid_page_vendorlist_product_modal_gomap:
    'car_testid_page_vendorlist_product_modal_gomap',
  car_testid_page_vendorlist_product_modal_pickup_address:
    'car_testid_page_vendorlist_product_modal_pickup_address',
  car_testid_page_vendorlist_product_modal_pickup_center:
    'car_testid_page_vendorlist_product_modal_pickup_center',
  car_testid_page_vendorlist_product_modal_dropoff_address:
    'car_testid_page_vendorlist_product_modal_dropoff_address',
  car_testid_page_vendorlist_product_modal_dropoff_center:
    'car_testid_page_vendorlist_product_modal_dropoff_center',
  car_testid_page_vendorlist_product_modal_reviews_item:
    'car_testid_page_vendorlist_product_modal_reviews_item',
  car_testid_page_vendorlist_product_modal_reviews_item_more:
    'car_testid_page_vendorlist_product_modal_reviews_item_more',
  car_testid_page_vendorlist_product_modal_policy_item:
    'car_testid_page_vendorlist_product_modal_policy_item',
  car_testid_page_vendorlist_product_modal_license:
    'car_testid_page_vendorlist_product_modal_license',
  car_testid_page_vendorlist_product_modal_footer_phone:
    'car_testid_page_vendorlist_product_modal_footer_phone',
  car_testid_page_vendorlist_product_modal_footer_bar:
    'car_testid_page_vendorlist_product_modal_footer_bar',
  car_testid_page_vendorlist_product_modal_footer_button:
    'car_testid_page_vendorlist_product_modal_footer_button',
  car_testid_page_vendorlist_store_modal_closemask:
    'car_testid_page_vendorlist_store_modal_closemask',
  car_testid_page_vendorlist_store_modal_lefticon:
    'car_testid_page_vendorlist_store_modal_lefticon',
  car_testid_page_vendorlist_store_modal_know:
    'car_testid_page_vendorlist_store_modal_know',
  car_testid_page_vendorlist_vehicle_modal_closemask:
    'car_testid_page_vendorlist_vehicle_modal_closemask',
  car_testid_page_vendorlist_vehicle_modal_vehiclebase_item_help:
    'car_testid_page_vendorlist_vehicle_modal_vehiclebase_item_help',
  car_testid_page_vendorlist_vehicle_modal_vehicleboot_hide:
    'car_testid_page_vendorlist_vehicle_modal_vehicleboot_hide',
  car_testid_page_vendorlist_vehicle_modal_footer_button:
    'car_testid_page_vendorlist_vehicle_modal_footer_button',
  car_testid_page_vendorlist_fueltype_modal_closemask:
    'car_testid_page_vendorlist_fueltype_modal_closemask',
  car_testid_page_vendorlist_fueltype_modal_header_lefticon:
    'car_testid_page_vendorlist_fueltype_modal_header_lefticon',
  car_testid_page_vendorlist_fueltype_modal_gotit:
    'car_testid_page_vendorlist_fueltype_modal_gotit',
  car_testid_page_vendorlist_nolimit_modal_closemask:
    'car_testid_page_vendorlist_nolimit_modal_closemask',
  car_testid_page_vendorlist_lessdetail_modal_closemask:
    'car_testid_page_vendorlist_lessdetail_modal_closemask',
  car_testid_page_vendorlist_lessdetail_modal_lefticon:
    'car_testid_page_vendorlist_lessdetail_modal_lefticon',
  car_testid_page_vendorlist_lessdetail_modal_agree:
    'car_testid_page_vendorlist_lessdetail_modal_agree',
  car_testid_page_vendorlist_virtualnumber_dialog_confirm:
    'car_testid_page_vendorlist_virtualnumber_dialog_confirm',
  car_testid_page_vendorlist_virtualnumber_dialog_cancel:
    'car_testid_page_vendorlist_virtualnumber_dialog_cancel',

  car_testid_page_guide_store_call: 'car_testid_page_guide_store_call',
  car_testid_page_guide_map_walk_tab: 'car_testid_page_guide_map_walk_tab',
  car_testid_page_guide_map_driver_tab: 'car_testid_page_guide_map_driver_tab',
  car_testid_page_guide_map_location: 'car_testid_page_guide_map_location',
  // 取还指引页新增
  car_testid_page_guide_closemask: 'car_testid_page_guide_closemask',
  car_testid_page_guide_mask: 'car_testid_page_guide_mask',
  car_testid_page_guide_header_lefticon:
    'car_testid_page_guide_header_lefticon',
  car_testid_page_guide_nav_item: 'car_testid_page_guide_nav_item',
  car_testid_page_guide_toggleicon: 'car_testid_page_guide_toggleicon',
  car_testid_page_guide_store_phone: 'car_testid_page_guide_store_phone',
  car_testid_page_guide_store_copy: 'car_testid_page_guide_store_copy',
  car_testid_page_guide_store_lookvideo:
    'car_testid_page_guide_store_lookvideo',
  car_testid_page_guide_phone_modal_closemask:
    'car_testid_page_guide_phone_modal_closemask',
  car_testid_page_guide_phone_modal_cancel:
    'car_testid_page_guide_phone_modal_cancel',
  car_testid_page_guide_phone_modal_phone_item:
    'car_testid_page_guide_phone_modal_phone_item',
  car_testid_page_guide_parking_modal_closemask:
    'car_testid_page_guide_parking_modal_closemask',
  car_testid_page_guide_parking_modal_header_lefticon:
    'car_testid_page_guide_parking_modal_header_lefticon',
  car_testid_page_guide_parking_modal_image_item:
    'car_testid_page_guide_parking_modal_image_item',

  // 优惠券页面新增
  car_testid_page_coupon_header_lefticon:
    'car_testid_page_coupon_header_lefticon',
  car_testid_page_coupon_tab_item: 'car_testid_page_coupon_tab_item',
  car_testid_page_coupon_codeinput: 'car_testid_page_coupon_codeinput',
  car_testid_page_coupon_exchange: 'car_testid_page_coupon_exchange',
  car_testid_page_coupon_empty_selection:
    'car_testid_page_coupon_empty_selection',
  car_testid_page_coupon_item: 'car_testid_page_coupon_item',
  car_testid_page_coupon_item_toggle: 'car_testid_page_coupon_item_toggle',
  car_testid_page_coupon_item_radio: 'car_testid_page_coupon_item_radio',
  car_testid_page_coupon_detail_modal: 'car_testid_page_coupon_detail_modal',

  car_testid_page_booking: 'car_testid_page_booking',
  car_testid_page_booking_vehicleLocation:
    'car_testid_page_booking_vehicleLocation',
  // 填写页-押金方式及说明-产品支持免押-提示文案
  car_testid_page_booking_deposit_title:
    'car_testid_page_booking_deposit_title',
  car_testid_page_booking_deposit_subtitle:
    'car_testid_page_booking_deposit_subtitle',
  // 填写页-押金方式及说明-产品支付免押-勾选放弃
  car_testid_page_booking_deposit_check_give_up:
    'car_testid_page_booking_deposit_check_give_up',
  // 填写页-个人信息说明弹窗
  car_testid_page_booking_approve_explain_modal:
    'car_testid_page_booking_approve_explain_modal',
  // 填写页- 车行险模块 - 标题
  car_testid_comp_booking_ins_title: 'car_testid_comp_booking_ins_title',
  // 填写页- 车行险模块 - 内容
  car_testid_comp_booking_ins_content: 'car_testid_comp_booking_ins_content',
  // 填写页- 车行险模块 - 内容 - 表头选项
  car_testid_comp_booking_ins_content_headitem:
    'car_testid_comp_booking_ins_content_headitem',
  // 填写页- 车行险模块 - 服务分块（基础、优享、尊享）
  car_testid_comp_booking_ins_content_item:
    'car_testid_comp_booking_ins_content_item',
  // 填写页- 车行险模块 - 选择按钮
  car_testid_comp_booking_ins_content_item_btn:
    'car_testid_comp_booking_ins_content_item_btn',
  // 填写页- 车行险模块 - 查看详情
  car_testid_comp_booking_ins_content_btn:
    'car_testid_comp_booking_ins_content_btn',
  // 填写页- 车行险详情模块 - tab（基础、优享、尊享）
  car_testid_comp_booking_ins_detail_tab:
    'car_testid_comp_booking_ins_detail_tab',
  // 填写页- 车行险模块 - 查看理赔相关政策
  car_testid_comp_booking_ins_tip_btn: 'car_testid_comp_booking_ins_tip_btn',
  // 填写页优惠券押金模块优惠券入口
  car_testid_comp_booking_NewCouponAndDeposit_coupon:
    'car_testid_comp_booking_NewCouponAndDeposit_coupon',
  // 填写页优惠券押金模块优惠券弹窗
  car_testid_comp_booking_couponModal: 'car_testid_comp_booking_couponModal',
  // 填写页优惠券押金模块优惠券弹窗可用优惠券
  car_testid_comp_booking_couponModal_usable_item:
    'car_testid_comp_booking_couponModal_usable_item',
  // 填写页-门店政策跳转-点击【门店政策】
  car_testid_page_booking_store_policy: 'car_testid_page_booking_store_policy',
  // 填写页-预定条款跳转-点击【预定条款】
  car_testid_page_booking_isd_agreement_title:
    'car_testid_page_booking_isd_agreement_title',
  // 填写页-个人信息说明跳转-点击【个人信息说明】
  car_testid_page_booking_approve_explain:
    'car_testid_page_booking_approve_explain',
  // 填写页-程信分免押服务协议跳转-点击【程信分免押服务协议】
  car_testid_page_booking_cheng_xin_fen_agreement:
    'car_testid_page_booking_cheng_xin_fen_agreement',
  car_testid_comp_booking_vehicle_info: 'car_testid_comp_booking_vehicle_info',
  car_testid_comp_booking_vehicle_modal:
    'car_testid_comp_booking_vehicle_modal',
  // 填写页激励信息模块
  car_testid_comp_booking_advantageDom: 'car_testid_comp_booking_advantageDom',
  // 填写页优惠券押金模块
  car_testid_comp_booking_NewCouponAndDeposit:
    'car_testid_comp_booking_NewCouponAndDeposit',
  // 填写页优惠券押金模块活动
  car_testid_comp_booking_NewCouponAndDeposit_activity:
    'car_testid_comp_booking_NewCouponAndDeposit_activity',
  car_testid_comp_booking_new_couponAndActivity:
    'car_testid_comp_booking_new_couponAndActivity',
  // 填写页新增
  car_testid_page_booking_header_lefticon:
    'car_testid_page_booking_header_lefticon',
  car_testid_page_booking_header_right: 'car_testid_page_booking_header_right',
  car_testid_page_booking_header_storepolicy:
    'car_testid_page_booking_header_storepolicy',
  car_testid_page_booking_retry: 'car_testid_page_booking_retry',
  car_testid_page_booking_vehicle_tags: 'car_testid_page_booking_vehicle_tags',
  car_testid_page_booking_vehicle_limitrules:
    'car_testid_page_booking_vehicle_limitrules',
  car_testid_page_booking_form_diver_help:
    'car_testid_page_booking_form_diver_help',
  car_testid_page_booking_form_credit_tip:
    'car_testid_page_booking_form_credit_tip',
  car_testid_page_booking_form_recommend_driver_item:
    'car_testid_page_booking_form_recommend_driver_item',
  car_testid_page_booking_form_recommend_driver_more:
    'car_testid_page_booking_form_recommend_driver_more',
  car_testid_page_booking_form_driver: 'car_testid_page_booking_form_driver',
  car_testid_page_booking_form_age_input:
    'car_testid_page_booking_form_age_input',
  car_testid_page_booking_form_mobile_input:
    'car_testid_page_booking_form_mobile_input',
  car_testid_page_booking_form_mobile_input_areacode:
    'car_testid_page_booking_form_mobile_input_areacode',
  car_testid_page_booking_form_idtype_input:
    'car_testid_page_booking_form_idtype_input',
  car_testid_page_booking_form_flightnumber_input:
    'car_testid_page_booking_form_flightnumber_input',
  car_testid_page_booking_form_flightnumber_info:
    'car_testid_page_booking_form_flightnumber_info',
  car_testid_page_booking_form_input_item:
    'car_testid_page_booking_form_input_item',
  car_testid_page_booking_discount_toggle:
    'car_testid_page_booking_discount_toggle',
  car_testid_page_booking_discount_activity:
    'car_testid_page_booking_discount_activity',
  car_testid_page_booking_deposit_help: 'car_testid_page_booking_deposit_help',
  car_testid_page_booking_deposit_select_driver:
    'car_testid_page_booking_deposit_select_driver',
  car_testid_page_booking_deposit_method_item:
    'car_testid_page_booking_deposit_method_item',
  car_testid_page_booking_deposit_method_help:
    'car_testid_page_booking_deposit_method_help',
  car_testid_page_booking_deposit_description_help:
    'car_testid_page_booking_deposit_description_help',
  car_testid_page_booking_carservice_single:
    'car_testid_page_booking_carservice_single',
  car_testid_page_booking_carservice_item:
    'car_testid_page_booking_carservice_item',
  car_testid_page_booking_carservice_item_checkbox:
    'car_testid_page_booking_carservice_item_checkbox',
  car_testid_page_booking_carservice_table_cell_item:
    'car_testid_page_booking_carservice_table_cell_item',
  car_testid_page_booking_extras: 'car_testid_page_booking_extras',
  car_testid_page_booking_extras_item_checkbox:
    'car_testid_page_booking_extras_item_checkbox',
  car_testid_page_booking_gpdr_termsbar_checkbox:
    'car_testid_page_booking_gpdr_termsbar_checkbox',
  car_testid_page_booking_rebooktip_penalty:
    'car_testid_page_booking_rebooktip_penalty',
  car_testid_page_booking_footer_button:
    'car_testid_page_booking_footer_button',
  car_testid_page_booking_footer_bar: 'car_testid_page_booking_footer_bar',
  car_testid_page_booking_footer_checkbar_checkbox:
    'car_testid_page_booking_footer_checkbar_checkbox',
  car_testid_page_booking_footer_checkbar_agree:
    'car_testid_page_booking_footer_checkbar_agree',
  car_testid_page_booking_footer_checkbar_auth:
    'car_testid_page_booking_footer_checkbar_auth',
  car_testid_page_booking_modals_freezedeposit_closemask:
    'car_testid_page_booking_modals_freezedeposit_closemask',
  car_testid_page_booking_modals_freezedeposit_lefticon:
    'car_testid_page_booking_modals_freezedeposit_lefticon',
  car_testid_page_booking_modals_supplier_closemask:
    'car_testid_page_booking_modals_supplier_closemask',
  car_testid_page_booking_modals_supplier_lefticon:
    'car_testid_page_booking_modals_supplier_lefticon',
  car_testid_page_booking_modals_ctripcredit_closemask:
    'car_testid_page_booking_modals_ctripcredit_closemask',
  car_testid_page_booking_modals_ctripcredit_lefticon:
    'car_testid_page_booking_modals_ctripcredit_lefticon',
  car_testid_page_booking_modals_couponprevalidation_confirm:
    'car_testid_page_booking_modals_couponprevalidation_confirm',
  car_testid_page_booking_modals_couponprevalidation_cancel:
    'car_testid_page_booking_modals_couponprevalidation_cancel',
  car_testid_page_booking_pricediff_modal_closemask:
    'car_testid_page_booking_modals_pricediff_modal_closemask',
  car_testid_page_booking_pricediff_modal_lefticon:
    'car_testid_page_booking_modals_pricediff_modal_lefticon',
  car_testid_page_booking_pricediff_modal_button_item:
    'car_testid_page_booking_modals_pricediff_modal_button_item',
  car_testid_page_booking_coupon_modal_closemask:
    'car_testid_page_booking_coupon_modal_closemask',
  car_testid_page_booking_coupon_modal_header_lefticon:
    'car_testid_page_booking_coupon_modal_header_lefticon',
  car_testid_page_booking_coupon_modal_tab_item:
    'car_testid_page_booking_coupon_modal_tab_item',
  car_testid_page_booking_coupon_modal_coupon_item:
    'car_testid_page_booking_coupon_modal_coupon_item',
  car_testid_page_booking_coupon_modal_coupon_item_checkbox:
    'car_testid_page_booking_coupon_modal_coupon_item_checkbox',
  car_testid_page_booking_applypenalty_modal_closemask:
    'car_testid_page_booking_applypenalty_modal_closemask',
  car_testid_page_booking_applypenalty_modal_mask:
    'car_testid_page_booking_applypenalty_modal_mask',
  car_testid_page_booking_applypenalty_modal_help:
    'car_testid_page_booking_applypenalty_modal_help',
  car_testid_page_booking_applypenalty_modal_input:
    'car_testid_page_booking_applypenalty_modal_input',
  car_testid_page_booking_applypenalty_modal_apply:
    'car_testid_page_booking_applypenalty_modal_apply',
  car_testid_page_booking_personalauthcheck_modal_closemask:
    'car_testid_page_booking_personalauthcheck_modal_closemask',
  car_testid_page_booking_personalauthcheck_modal_agree:
    'car_testid_page_booking_personalauthcheck_modal_agree',
  car_testid_page_booking_approveexplain_modal_closemask:
    'car_testid_page_booking_approveexplain_modal_closemask',
  car_testid_page_booking_approveexplain_modal_agree:
    'car_testid_page_booking_approveexplain_modal_agree',
  car_testid_page_booking_activity_modal_closemask:
    'car_testid_page_booking_activity_modal_closemask',
  car_testid_page_booking_serviceclaimmore_modal_closemask:
    'car_testid_page_booking_serviceclaimmore_modal_closemask',
  car_testid_page_booking_serviceclaimmore_modal_nav_item:
    'car_testid_page_booking_serviceclaimmore_modal_nav_item',
  car_testid_page_booking_serviceclaimmore_modal_gotit:
    'car_testid_page_booking_serviceclaimmore_modal_gotit',
  car_testid_page_booking_carservicedetail_modal_nav_item:
    'car_testid_page_booking_carservicedetail_modal_nav_item',
  car_testid_page_booking_carservicedetail_modal_footer_button:
    'car_testid_page_booking_carservicedetail_modal_footer_button',
  car_testid_page_booking_driverLicense_modal_closemask:
    'car_testid_page_booking_driverLicense_modal_closemask',
  car_testid_page_booking_driverLicense_modal_license_item:
    'car_testid_page_booking_driverLicense_modal_license_item',
  car_testid_page_booking_confirm_modal_closemask:
    'car_testid_page_booking_confirm_modal_closemask',
  car_testid_page_booking_confirm_modal_confirm:
    'car_testid_page_booking_confirm_modal_confirm',
  car_testid_page_booking_confirm_modal_cancel:
    'car_testid_page_booking_confirm_modal_cancel',

  car_testid_page_product_header_osd: 'car_testid_page_product_header_osd',
  car_testid_page_product_image_header_osd:
    'car_testid_page_product_image_header_osd',
  car_testid_page_product_page_osd: 'car_testid_page_product_page_osd',
  car_testid_page_product_vehicle_osd: 'car_testid_page_product_vehicle_osd',
  car_testid_page_product_vendor_osd: 'car_testid_page_product_vendor_osd',
  car_testid_page_product_vendor_package_osd:
    'car_testid_page_product_vendor_package_osd',
  car_testid_page_product_localtion_detail_osd:
    'car_testid_page_product_localtion_detail_osd',
  car_testid_page_product_vendor_comment_osd:
    'car_testid_page_product_vendor_comment_osd',
  car_testid_page_product_PickupMaterials:
    'car_testid_page_product_PickupMaterials',
  car_testid_page_product_extrasInfo: 'car_testid_page_product_extrasInfo',
  // 海外产品详情页保险模块
  car_testid_page_product_insurancebox: 'car_testid_page_product_insurancebox',
  // 海外产品详情页保险模块套餐选项
  car_testid_page_product_insurancebox_item:
    'car_testid_page_product_insurancebox_item',
  // 海外产品详情页保险模块详情按钮
  car_testid_page_product_insurancebox_detail:
    'car_testid_page_product_insurancebox_detail',
  // 海外产品详情页保险详情弹窗
  car_testid_page_product_insurancesuits_modal:
    'car_testid_page_product_insurancesuits_modal',
  // 海外产品详情页保险详情弹窗投保须知按钮
  car_testid_page_product_insurancesuits_modal_mustread:
    'car_testid_page_product_insurancesuits_modal_mustread',
  // 海外产品详情页保险详情弹窗保险条款按钮
  car_testid_page_product_insurancesuits_modal_noticerule:
    'car_testid_page_product_insurancesuits_modal_noticerule',
  // 海外产品详情页保险详情弹窗保障详情水平滑动Item按钮
  car_testid_page_product_insurancesuits_modal_detail_horizontal:
    'car_testid_page_product_insurancesuits_modal_detail_horizontal',
  // 海外产品详情页保险详情弹窗分类水平滑动Item按钮
  car_testid_page_product_insurancesuits_modal_category_horizontal:
    'car_testid_page_product_insurancesuits_modal_category_horizontal',
  // 海外产品详情页保险详情弹窗投保须知弹窗
  car_testid_page_product_insurancesuits_modal_mustread_modal:
    'car_testid_page_product_insurancesuits_modal_mustread_modal',
  // 海外产品详情页保险详情弹窗查看保障详情按钮
  car_testid_page_product_insurancesuits_modal_anchordetail:
    'car_testid_page_product_insurancesuits_modal_anchordetail',
  // 海外产品详情页保险详情弹窗什么是起赔额按钮
  car_testid_page_product_insurancesuits_modal_excess_btn:
    'car_testid_page_product_insurancesuits_modal_excess_btn',
  // 海外产品详情页什么是起赔额弹窗
  car_testid_page_product_excessintroduce_modal:
    'car_testid_page_product_excessintroduce_modal',
  // 海外产品详情页套餐比较弹窗
  car_testid_page_product_insurancecompare_modal:
    'car_testid_page_product_insurancecompare_modal',
  // 海外产品详情页套餐售卖弹窗
  car_testid_page_product_insuranceselling_modal:
    'car_testid_page_product_insuranceselling_modal',
  // 海外产品详情页套餐售卖弹窗关闭蒙层
  car_testid_page_product_insuranceselling_close_mask:
    'car_testid_page_product_insuranceselling_close_mask',
  // 海外产品详情页套餐比较弹窗保险套餐选项
  car_testid_page_product_insurancecompare_modal_item:
    'car_testid_page_product_insurancecompare_modal_item',
  car_testid_page_product_materials_detail:
    'car_testid_page_product_materials_detail',
  car_testid_page_product_page_timeLine:
    'car_testid_page_product_page_timeLine',
  car_testid_page_product_page_location:
    'car_testid_page_product_page_location',
  car_testid_page_product_image_header_osd_wrap:
    'car_testid_page_product_image_header_osd_wrap',
  // 产品详情页新增
  car_testid_page_product_header_lefticon:
    'car_testid_page_product_header_lefticon',
  car_testid_page_product_header_serviceicon:
    'car_testid_page_product_header_serviceicon',
  car_testid_page_product_header_package_tab_item:
    'car_testid_page_product_header_package_tab_item',
  car_testid_page_product_header_section_tab_item:
    'car_testid_page_product_header_section_tab_item',
  car_testid_page_product_nomatch_button_item:
    'car_testid_page_product_nomatch_button_item',
  car_testid_page_product_pricealert_confirm:
    'car_testid_page_product_pricealert_confirm',
  car_testid_page_product_pricealert_cancel:
    'car_testid_page_product_pricealert_cancel',
  car_testid_page_product_depositfreefervice:
    'car_testid_page_product_depositfreefervice',
  car_testid_page_product_rentallocation_pickup:
    'car_testid_page_product_rentallocation_pickup',
  car_testid_page_product_rentallocation_dropoff:
    'car_testid_page_product_rentallocation_dropoff',
  car_testid_page_product_insurance_compare:
    'car_testid_page_product_insurance_compare',
  car_testid_page_product_insurance_item:
    'car_testid_page_product_insurance_item',
  car_testid_page_product_insurance_item_detail:
    'car_testid_page_product_insurance_item_detail',
  car_testid_page_product_insurance_item_reminder_item:
    'car_testid_page_product_insurance_item_reminder_item',
  car_testid_page_product_insuranceselling_modal_notneed:
    'car_testid_page_product_insuranceselling_modal_notneed',
  car_testid_page_product_insuranceselling_modal_add:
    'car_testid_page_product_insuranceselling_modal_add',
  car_testid_page_product_vehicelmodal_closemask:
    'car_testid_page_product_vehicelmodal_closemask',
  car_testid_page_product_insurancecompare_modal_closemask:
    'car_testid_page_product_insurancecompare_modal_closemask',
  car_testid_page_product_insurancecompare_modal_header_lefticon:
    'car_testid_page_product_insurancecompare_modal_header_lefticon',
  car_testid_page_product_insurancecompare_modal_header_detail:
    'car_testid_page_product_insurancecompare_modal_header_detail',
  car_testid_page_product_insurancecompare_modal_confirm:
    'car_testid_page_product_insurancecompare_modal_confirm',
  car_testid_page_product_insurancesuits_modal_closemask:
    'car_testid_page_product_insurancesuits_modal_closemask',
  car_testid_page_product_insurancesuits_modal_header_lefticon:
    'car_testid_page_product_insurancesuits_modal_header_lefticon',
  car_testid_page_product_insurancesuits_modal_package_choose:
    'car_testid_page_product_insurancesuits_modal_package_choose',
  car_testid_page_product_excessintroduce_modal_closemask:
    'car_testid_page_product_excessintroduce_modal_closemask',
  car_testid_page_product_mustread_modal_closemask:
    'car_testid_page_product_mustread_modal_closemask',
  car_testid_page_product_extras_modal_closemask:
    'car_testid_page_product_extras_modal_closemask',
  car_testid_page_product_morepackageintro_modal_closemask:
    'car_testid_page_product_morepackageintro_modal_closemask',
  car_testid_page_product_morepackageintro_modal_header_lefticon:
    'car_testid_page_product_morepackageintro_modal_header_lefticon',
  car_testid_page_product_morepackageintro_modal_gotit:
    'car_testid_page_product_morepackageintro_modal_gotit',

  // 证照页面新增
  car_testid_page_license_header_lefticon:
    'car_testid_page_license_header_lefticon',
  car_testid_page_license_item: 'car_testid_page_license_item',

  car_testid_page_driverlist: 'car_testid_page_driverlist',
  // 驾驶员列表页内容
  car_testid_page_driverlist_content: 'car_testid_page_driverlist_content',
  // 驾驶员列表页驾驶员Item
  car_testid_page_driverlist_item: 'car_testid_page_driverlist_item',
  // 新增驾驶员列表页
  car_testid_page_driverlist_header_lefticon:
    'car_testid_page_driverlist_header_lefticon',
  car_testid_page_driverlist_add: 'car_testid_page_driverlist_add',
  car_testid_page_driverlist_driver_item_delete:
    'car_testid_page_driverlist_driver_item_delete',
  car_testid_page_driverlist_driver_item:
    'car_testid_page_driverlist_driver_item',
  car_testid_page_driverlist_driver_item_checkbox:
    'car_testid_page_driverlist_driver_item_checkbox',
  car_testid_page_driverlist_driver_item_edit:
    'car_testid_page_driverlist_driver_item_edit',
  car_testid_page_driverlist_driver_item_delete_dialog_confirm:
    'car_testid_page_driverlist_driver_item_delete_dialog_confirm',
  car_testid_page_driverlist_driver_item_delete_dialog_cancel:
    'car_testid_page_driverlist_driver_item_delete_dialog_cancel',

  // 驾驶员编辑页面填写规则弹窗
  car_testid_page_driveredit_rule: 'car_testid_page_driveredit_rule',
  // 新增驾驶员编辑页
  car_testid_page_driveredit_rule_modal_closemask:
    'car_testid_page_driveredit_rule_modal_closemask',
  car_testid_page_driveredit_rule_modal_header_lefticon:
    'car_testid_page_driveredit_rule_modal_header_lefticon',
  car_testid_page_driveredit_showrule: 'car_testid_page_driveredit_showrule',
  car_testid_page_driveredit_input_fullname:
    'car_testid_page_driveredit_input_fullname',
  car_testid_page_driveredit_input_lastname:
    'car_testid_page_driveredit_input_lastname',
  car_testid_page_driveredit_input_firstname:
    'car_testid_page_driveredit_input_firstname',
  car_testid_page_driveredit_certificate_item:
    'car_testid_page_driveredit_certificate_item',
  car_testid_page_driveredit_input_certificate:
    'car_testid_page_driveredit_input_certificate',
  car_testid_page_driveredit_input_birth:
    'car_testid_page_driveredit_input_birth',
  car_testid_page_driveredit_input_nationality:
    'car_testid_page_driveredit_input_nationality',
  car_testid_page_driveredit_input_mobile:
    'car_testid_page_driveredit_input_mobile',
  car_testid_page_driveredit_input_areacode:
    'car_testid_page_driveredit_input_areacode',
  car_testid_page_driveredit_agreeterms:
    'car_testid_page_driveredit_agreeterms',

  car_osd_orderdetail_price_modal: 'car_osd_orderdetail_price_modal',
  car_testid_page_order_continue_pay: 'car_testid_page_order_continue_pay',
  car_testid_page_order_price_detail: 'car_testid_page_order_price_detail',
  // 订详操作栏按钮
  car_testid_page_order_op_btn: 'car_testid_page_order_op_btn',
  // 订详消息入口卡片入口
  car_testid_comp_orderDetail_message_card:
    'car_testid_comp_orderDetail_message_card',
  // 订详咨询进度弹窗
  car_testid_comp_order_ServiceProgressModal:
    'car_testid_comp_order_ServiceProgressModal',
  // 订详消息助手
  car_testid_page_order_status_round_btn:
    'car_testid_page_order_status_round_btn',
  // 履约可视化订详消息助手
  car_testid_page_order_status_message_assistant:
    'car_testid_page_order_status_message_assistant',
  // 订详未加购保险描述
  car_testid_order_detail_insurance_desc:
    'car_testid_order_detail_insurance_desc',
  car_testid_comp_order_priceDetail_point:
    'car_testid_comp_order_priceDetail_point',
  car_testid_comp_order_priceDetail_tip:
    'car_testid_comp_order_priceDetail_tip',
  car_testid_order_detail_orderIdCopyBtn:
    'car_testid_order_detail_orderIdCopyBtn',
  car_testid_comp_orderDetail_refund_entry:
    'car_testid_comp_orderDetail_refund_entry',
  car_testid_comp_orderDetail_isd_refund_modal:
    'car_testid_comp_orderDetail_isd_refund_modal',
  // 订详页
  car_testid_page_order_detail_deposit_block:
    'car_testid_page_order_detail_deposit_block',
  car_testid_page_order_detail_deposit_detail_modal:
    'car_testid_page_order_detail_deposit_detail_modal',
  car_testid_page_order_detail_invoice: 'car_testid_page_order_detail_invoice',
  car_testid_page_order_detail_renew_days:
    'car_testid_page_order_detail_renew_days',
  car_testid_page_order_detail_renew_day_plus:
    'car_testid_page_order_detail_renew_day_plus',
  car_testid_page_order_detail_renew_day_minus:
    'car_testid_page_order_detail_renew_day_minus',
  car_testid_page_order_detail_violation_damage_entry:
    'car_testid_page_order_detail_violation_damage_entry',
  car_testid_page_order_detail_upgrade_module:
    'car_testid_page_order_detail_upgrade_module',
  car_testid_page_order_detail_upgrade_button:
    'car_testid_page_order_detail_upgrade_button',
  car_testid_page_order_detail_upgrade_single:
    'car_testid_page_order_detail_upgrade_single',
  car_testid_page_order_detail_car_service_modal:
    'car_testid_page_order_detail_car_service_modal',
  car_testid_page_order_detail_upgrade_various:
    'car_testid_page_order_detail_upgrade_various',
  car_testid_page_order_detail_car_service:
    'car_testid_page_order_detail_car_service',
  car_testid_page_order_detail_car_service_detail:
    'car_testid_page_order_detail_car_service_detail',
  car_testid_page_order_detail_car_service_detail_modal:
    'car_testid_page_order_detail_car_service_detail_modal',
  car_testid_page_order_detail_car_service_tips:
    'car_testid_page_order_detail_car_service_tips',
  car_testid_page_order_detail_car_service_claim_more:
    'car_testid_page_order_detail_car_service_claim_more',
  car_testid_page_order_detail_pick_return_map:
    'car_testid_page_order_detail_pick_return_map',
  car_testid_page_order_detail_car_detail_btn:
    'car_testid_page_order_detail_car_detail_btn',
  car_testid_comp_orderDetail_goCredentialEntry:
    'car_testid_comp_orderDetail_goCredentialEntry',
  car_testid_page_order_confirm_modal_button:
    'car_testid_page_order_confirm_modal_button',
  car_testid_order_confirm_modal: 'car_testid_order_confirm_modal',
  car_testid_comp_orderDetail_optionButtons:
    'car_testid_comp_orderDetail_optionButtons',
  // 海外订单详情页英文说明弹窗
  car_testid_page_order_detail_insurance_reminder_english_modal:
    'car_testid_page_order_detail_insurance_reminder_english_modal',
  // 海外产品详订单详情页保险模块查看电子保单按钮
  car_testid_page_order_detail_insurancebox_item_electronicdetail:
    'car_testid_page_order_detail_insurancebox_item_electronicdetail',
  // 订详页面标题
  car_testid_page_order_detail_page_header:
    'car_testid_page_order_detail_page_header',
  c_testid_orderDetail_tipsCardOsd: 'c_testid_orderDetail_tipsCardOsd',
  c_testid_orderDetail_tipsCardOsd_title:
    'c_testid_orderDetail_tipsCardOsd_title',
  c_testid_orderDetail_tipsCardOsd_btns:
    'c_testid_orderDetail_tipsCardOsd_btns',
  c_testid_orderDetail_tipsCardOsd_microEnterPrise:
    'c_testid_orderDetail_tipsCardOsd_microEnterPrise',
  c_testid_orderDetail_invoice_button: 'c_testid_orderDetail_invoice_button',
  // 订单详情页新增
  car_testid_page_order_header_lefticon:
    'car_testid_page_order_header_lefticon',
  car_testid_page_order_header_couponicon:
    'car_testid_page_order_header_couponicon',
  car_testid_page_order_header_serviceicon:
    'car_testid_page_order_header_serviceicon',
  car_testid_page_order_retry: 'car_testid_page_order_retry',
  car_testid_page_order_status_modifytip:
    'car_testid_page_order_status_modifytip',
  car_testid_page_order_status_button_item:
    'car_testid_page_order_status_button_item',
  car_testid_page_order_status_assistant_item:
    'car_testid_page_order_status_assistant_item',
  car_testid_page_order_status_assistant_item_url:
    'car_testid_page_order_status_assistant_item_url',
  car_testid_page_order_status_refundcard_help:
    'car_testid_page_order_status_refundcard_help',
  car_testid_page_order_status_refundcard_penaltydesc:
    'car_testid_page_order_status_refundcard_penaltydesc',
  car_testid_page_order_status_commentcard:
    'car_testid_page_order_status_commentcard',
  car_testid_page_order_status_commentcard_icon:
    'car_testid_page_order_status_commentcard_icon',
  car_testid_page_order_status_storemessage_entry:
    'car_testid_page_order_status_storemessage_entry',
  car_testid_page_order_status_contracttracker_progress_item:
    'car_testid_page_order_status_contracttracker_progress_item',
  car_testid_page_order_status_contracttracker_progress_item_auth:
    'car_testid_page_order_status_contracttracker_progress_item_auth',
  car_testid_page_order_status_contracttracker_progress_item_rightbutton:
    'car_testid_page_order_status_contracttracker_progress_item_rightbutton',
  car_testid_page_order_status_contracttracker_progress_item_guidebar:
    'car_testid_page_order_status_contracttracker_progress_item_guidebar',
  car_testid_page_order_status_contracttracker_progress_item_actionBar:
    'car_testid_page_order_status_contracttracker_progress_item_actionBar',
  car_testid_page_order_status_contracttracker_progress_item_renewcard:
    'car_testid_page_order_status_contracttracker_progress_item_renewcard',
  car_testid_page_order_status_contracttracker_progress_item_renewcard_minus:
    'car_testid_page_order_status_contracttracker_progress_item_renewcard_minus',
  car_testid_page_order_status_contracttracker_progress_item_renewcard_plus:
    'car_testid_page_order_status_contracttracker_progress_item_renewcard_plus',
  car_testid_page_order_status_showconsultprogress:
    'car_testid_page_order_status_showconsultprogress',
  car_testid_page_order_status_showcancelpenaltymodal:
    'car_testid_page_order_status_showcancelpenaltymodal',
  car_testid_page_order_amount_showcancelpolicymodal:
    'car_testid_page_order_amount_showcancelpolicymodal',
  car_testid_page_order_amount_showrenewtipsmodal:
    'car_testid_page_order_amount_showrenewtipsmodal',
  car_testid_page_order_amount_cashreturn:
    'car_testid_page_order_amount_cashreturn',
  car_testid_page_order_depositblock: 'car_testid_page_order_depositblock',
  car_testid_page_order_depositblock_desc_help:
    'car_testid_page_order_depositblock_desc_help',
  car_testid_page_order_depositblock_freedepositentry:
    'car_testid_page_order_depositblock_freedepositentry',
  car_testid_page_order_depositblock_freedepositentry_button:
    'car_testid_page_order_depositblock_freedepositentry_button',
  car_testid_page_order_replenish_block:
    'car_testid_page_order_replenish_block',
  car_testid_page_order_replenish_block_button:
    'car_testid_page_order_replenish_block_button',
  car_testid_page_order_renewalentry: 'car_testid_page_order_renewalentry',
  car_testid_page_order_supplemententry:
    'car_testid_page_order_supplemententry',
  car_testid_page_order_selfservice_button:
    'car_testid_page_order_selfservice_button',
  car_testid_page_order_invoice_button: 'car_testid_page_order_invoice_button',
  car_testid_page_order_vehicle_guideenter:
    'car_testid_page_order_vehicle_guideenter',
  car_testid_page_order_vehicle_showlabelmodal:
    'car_testid_page_order_vehicle_showlabelmodal',
  car_testid_page_order_vehicle_limittip:
    'car_testid_page_order_vehicle_limittip',
  car_testid_page_order_vendor: 'car_testid_page_order_vendor',
  car_testid_page_order_vendor_limit: 'car_testid_page_order_vendor_limit',
  car_testid_page_order_vendor_etcentry:
    'car_testid_page_order_vendor_etcentry',
  car_testid_page_order_pickreturntab_pickup:
    'car_testid_page_order_pickreturntab_pickup',
  car_testid_page_order_pickreturntab_dropoff:
    'car_testid_page_order_pickreturntab_dropoff',
  car_testid_page_order_activitydetailentry:
    'car_testid_page_order_activitydetailentry',
  car_testid_page_order_driver_addmoredriver:
    'car_testid_page_order_driver_addmoredriver',
  car_testid_page_order_driver_desc: 'car_testid_page_order_driver_desc',
  car_testid_page_order_customerinfo_help:
    'car_testid_page_order_customerinfo_help',
  car_testid_page_order_lessenter: 'car_testid_page_order_lessenter',
  car_testid_page_order_change_layer_closemask:
    'car_testid_page_order_change_layer_closemask',
  car_testid_page_order_change_layer_done:
    'car_testid_page_order_change_layer_done',
  car_testid_page_order_change_layer_button_item:
    'car_testid_page_order_change_layer_button_item',
  car_testid_page_order_layerwithbutton_closemask:
    'car_testid_page_order_layerwithbutton_closemask',
  car_testid_page_order_layerwithbutton_done:
    'car_testid_page_order_layerwithbutton_done',
  car_testid_page_order_layerwithbutton_input:
    'car_testid_page_order_layerwithbutton_input',
  car_testid_page_order_layerwithbutton_button:
    'car_testid_page_order_layerwithbutton_button',
  car_testid_page_order_depositdetail_modal_closemask:
    'car_testid_page_order_depositdetail_modal_closemask',
  car_testid_page_order_depositdetail_modal_header_lefticon:
    'car_testid_page_order_depositdetail_modal_header_lefticon',
  car_testid_page_order_depositdetail_modal_intro:
    'car_testid_page_order_depositdetail_modal_intro',
  car_testid_page_order_depositdetail_modal_freeservice:
    'car_testid_page_order_depositdetail_modal_freeservice',
  car_testid_page_order_depositdetail_modal_tipline_item:
    'car_testid_page_order_depositdetail_modal_tipline_item',
  car_testid_page_order_depositdetail_modal_preauth:
    'car_testid_page_order_depositdetail_modal_preauth',
  car_testid_page_order_depositdetail_modal_rateintroduce:
    'car_testid_page_order_depositdetail_modal_rateintroduce',
  car_testid_page_order_depositdetail_modal_progress_item:
    'car_testid_page_order_depositdetail_modal_progress_item',
  car_testid_page_order_depositdetail_modal_deductionfail:
    'car_testid_page_order_depositdetail_modal_deductionfail',
  car_testid_page_order_depositdetail_modal_highlightlink:
    'car_testid_page_order_depositdetail_modal_highlightlink',
  car_testid_page_order_depositintroduce_modal_closemask:
    'car_testid_page_order_depositintroduce_modal_closemask',
  car_testid_page_order_depositintroduce_modal_done:
    'car_testid_page_order_depositintroduce_modal_done',
  car_testid_page_order_depositrateintroduce_modal_closemask:
    'car_testid_page_order_depositrateintroduce_modal_closemask',
  car_testid_page_order_depositrateintroduce_modal_done:
    'car_testid_page_order_depositrateintroduce_modal_done',
  car_testid_page_order_voc_modal_closemask:
    'car_testid_page_order_voc_modal_closemask',
  car_testid_page_order_voc_modal_closeicon:
    'car_testid_page_order_voc_modal_closeicon',
  car_testid_page_order_voc_modal_button_item:
    'car_testid_page_order_voc_modal_button_item',
  car_testid_page_order_cancelpolicy_modal_closemask:
    'car_testid_page_order_cancelpolicy_modal_closemask',
  car_testid_page_order_cancelpolicy_modal_header_lefticon:
    'car_testid_page_order_cancelpolicy_modal_header_lefticon',
  car_testid_page_order_cancelpolicy_modal_footer_button:
    'car_testid_page_order_cancelpolicy_modal_footer_button',
  car_testid_page_order_renewtips_modal_closemask:
    'car_testid_page_order_renewtips_modal_closemask',
  car_testid_page_order_renewtips_modal_header_lefticon:
    'car_testid_page_order_renewtips_modal_header_lefticon',
  car_testid_page_order_renewtips_modal_more:
    'car_testid_page_order_renewtips_modal_more',
  car_testid_page_order_renewtips_modal_cancel:
    'car_testid_page_order_renewtips_modal_cancel',
  car_testid_page_order_refunddetail_modal_closemask:
    'car_testid_page_order_refunddetail_modal_closemask',
  car_testid_page_order_refunddetail_modal_header_lefticon:
    'car_testid_page_order_refunddetail_modal_header_lefticon',
  car_testid_page_order_serviceprogress_modal_closemask:
    'car_testid_page_order_serviceprogress_modal_closemask',
  car_testid_page_order_serviceprogress_modal_closeicon:
    'car_testid_page_order_serviceprogress_modal_closeicon',
  car_testid_page_order_serviceprogress_modal_urgeservice:
    'car_testid_page_order_serviceprogress_modal_urgeservice',
  car_testid_page_order_cancelpenaltyinfo_modal_closemask:
    'car_testid_page_order_cancelpenaltyinfo_modal_closemask',
  car_testid_page_order_cancelpenaltyinfo_modal_closeicon:
    'car_testid_page_order_cancelpenaltyinfo_modal_closeicon',
  car_testid_page_order_buttonsbar_person_call:
    'car_testid_page_order_buttonsbar_person_call',
  car_testid_page_order_buttonsbar_showphonemodal:
    'car_testid_page_order_buttonsbar_showphonemodal',
  car_testid_page_order_cardetail_modal_closemask:
    'car_testid_page_order_cardetail_modal_closemask',
  car_testid_page_order_cardetail_modal_header_lefticon:
    'car_testid_page_order_cardetail_modal_header_lefticon',
  car_testid_page_order_carLabels_modal_closemask:
    'car_testid_page_order_carLabels_modal_closemask',
  car_testid_page_order_customerphone_modal_closemask:
    'car_testid_page_order_customerphone_modal_closemask',
  car_testid_page_order_customerphone_modal_header_lefticon:
    'car_testid_page_order_customerphone_modal_header_lefticon',
  car_testid_page_order_customerphone_modal_im:
    'car_testid_page_order_customerphone_modal_im',
  car_testid_page_order_customerphone_modal_personphone:
    'car_testid_page_order_customerphone_modal_personphone',
  car_testid_page_order_customerphone_modal_tell_item:
    'car_testid_page_order_customerphone_modal_tell_item',
  car_testid_page_order_customerphone_modal_ctripservice:
    'car_testid_page_order_customerphone_modal_ctripservice',
  car_testid_page_order_pickupmaterials_modal_closemask:
    'car_testid_page_order_pickupmaterials_modal_closemask',
  car_testid_page_order_pickupmaterials_modal_header_lefticon:
    'car_testid_page_order_pickupmaterials_modal_header_lefticon',
  car_testid_page_order_modifyorderwarn_modal_closemask:
    'car_testid_page_order_modifyorderwarn_modal_closemask',
  car_testid_page_order_modifyorderwarn_modal_header_lefticon:
    'car_testid_page_order_modifyorderwarn_modal_header_lefticon',
  car_testid_page_order_modifyorderwarn_modal_leftbutton:
    'car_testid_page_order_modifyorderwarn_modal_leftbutton',
  car_testid_page_order_modifyorderwarn_modal_rightbutton:
    'car_testid_page_order_modifyorderwarn_modal_rightbutton',
  car_testid_page_order_reviewunopened_modal_closemask:
    'car_testid_page_order_reviewunopened_modal_closemask',
  car_testid_page_order_reviewunopened_modal_header_lefticon:
    'car_testid_page_order_reviewunopened_modal_header_lefticon',
  car_testid_page_order_detail_insurance_reminder_english_modal_closemask:
    'car_testid_page_order_detail_insurance_reminder_english_modal_closemask',
  car_testid_page_order_surveyentry_yes:
    'car_testid_page_order_surveyentry_yes',
  car_testid_page_order_surveyentry_no: 'car_testid_page_order_surveyentry_no',
  car_testid_page_order_surveymodal_answer:
    'car_testid_page_order_surveymodal_answer',
  car_testid_page_order_surveymodal_iknow:
    'car_testid_page_order_surveymodal_iknow',

  car_testid_page_online_auth_button: 'car_testid_page_online_auth_button',
  // 证件认证页面新增
  car_testid_page_online_auth_header_lefticon:
    'car_testid_page_online_auth_header_lefticon',
  car_testid_page_online_auth_card_item_retry:
    'car_testid_page_online_auth_card_item_retry',
  car_testid_page_online_auth_card_item_face_image:
    'car_testid_page_online_auth_card_item_face_image',
  car_testid_page_online_auth_card_item_verifyresult_item:
    'car_testid_page_online_auth_card_item_verifyresult_item',
  car_testid_page_online_auth_card_item_carda:
    'car_testid_page_online_auth_card_item_carda',
  car_testid_page_online_auth_card_item_cardb:
    'car_testid_page_online_auth_card_item_cardb',
  car_testid_page_online_footer_radio: 'car_testid_page_online_footer_radio',
  car_testid_page_online_footer_tip_link:
    'car_testid_page_online_footer_tip_link',
  car_testid_page_online_footer_askstore:
    'car_testid_page_online_footer_askstore',
  car_testid_page_online_finishbar_radio:
    'car_testid_page_online_finishbar_radio',
  car_testid_page_online_finishbar_switchdefault:
    'car_testid_page_online_finishbar_switchdefault',
  car_testid_page_online_finishbar_cancel:
    'car_testid_page_online_finishbar_cancel',
  car_testid_page_online_finishbar_choose:
    'car_testid_page_online_finishbar_choose',
  car_testid_page_online_authswitch_modal_closemask:
    'car_testid_page_online_authswitch_modal_closemask',
  car_testid_page_online_authswitch_modal_header_lefticon:
    'car_testid_page_online_authswitch_modal_header_lefticon',
  car_testid_page_online_lisenceauthtip_modal_closemask:
    'car_testid_page_online_lisenceauthtip_modal_closemask',
  car_testid_page_online_lisenceauthtip_modal_gotit:
    'car_testid_page_online_lisenceauthtip_modal_gotit',
  car_testid_page_online_personalauthorize_modal_closemask:
    'car_testid_page_online_personalauthorize_modal_closemask',
  car_testid_page_online_personalauthorize_modal_header_lefticon:
    'car_testid_page_online_personalauthorize_modal_header_lefticon',
  car_testid_page_online_personalauthorize_modal_confirm:
    'car_testid_page_online_personalauthorize_modal_confirm',
  car_testid_page_online_personalauthorize_modal_auth_link:
    'car_testid_page_online_personalauthorize_modal_auth_link',

  // 新增取车政策页
  car_testid_page_policy_closemask: 'car_testid_page_policy_closemask',

  // 新增附加产品页
  car_testid_page_extras_closemask: 'car_testid_page_extras_closemask',
  car_testid_page_extras_continue: 'car_testid_page_extras_continue',

  // 新增标签页
  car_testid_page_extras_header_lefticon:
    'car_testid_page_extras_header_lefticon',
  car_testid_page_extras_package_item: 'car_testid_page_extras_package_item',

  // 新增合同页
  car_testid_page_credentials_header_lefticon:
    'car_testid_page_credentials_header_lefticon',
  car_testid_page_credentials_signbutton:
    'car_testid_page_credentials_signbutton',

  // 新增订单取消页
  car_testid_page_cancel_closemask: 'car_testid_page_cancel_closemask',
  car_testid_page_cancel_header_lefticon:
    'car_testid_page_cancel_header_lefticon',
  car_testid_page_cancel_showcancelpolicy:
    'car_testid_page_cancel_showcancelpolicy',
  car_testid_page_cancel_reason_item: 'car_testid_page_cancel_reason_item',
  car_testid_page_cancel_reason_input: 'car_testid_page_cancel_reason_input',
  car_testid_page_cancel_penaly_apply: 'car_testid_page_cancel_penaly_apply',

  // 新增退款详情页
  car_testid_page_refund_detail_closemask:
    'car_testid_page_refund_detail_closemask',
  car_testid_page_refund_detail_header_lefticon:
    'car_testid_page_refund_detail_header_lefticon',

  car_testid_page_supplementlist_carviolationrules:
    'car_testid_page_supplementlist_carviolationrules',
  car_testid_page_supplementlist_header_tab:
    'car_testid_page_supplementlist_header_tab',
  car_testid_page_supplementlist_cardamages:
    'car_testid_page_supplementlist_cardamages',
  // 新增续租详情页
  car_testid_page_supplementlist_header_lefticon:
    'car_testid_page_supplementlist_header_lefticon',
  car_testid_page_supplementlist_nav_item:
    'car_testid_page_supplementlist_nav_item',
  car_testid_page_supplementlist_showphone_modal:
    'car_testid_page_supplementlist_showphone_modal',
  car_testid_page_supplementlist_violationrule_detail:
    'car_testid_page_supplementlist_violationrule_detail',
  car_testid_page_supplementlist_violationdesc:
    'car_testid_page_supplementlist_violationdesc',
  car_testid_page_supplementlist_vehicleviolation:
    'car_testid_page_supplementlist_vehicleviolation',
  car_testid_page_supplementlist_vehicledamage:
    'car_testid_page_supplementlist_vehicledamage',

  // 新增续租列表
  car_testid_page_renewlist_header_lefticon:
    'car_testid_page_renewlist_header_lefticon',
  car_testid_page_renewlist_renew_item: 'car_testid_page_renewlist_renew_item',
  car_testid_page_renewlist_renew_item_button_item:
    'car_testid_page_renewlist_renew_item_button_item',

  // 新增违章详情页
  car_testid_page_violationdetail_header_lefticon:
    'car_testid_page_violationdetail_header_lefticon',
  car_testid_page_violationdetail_image_item:
    'car_testid_page_violationdetail_image_item',

  // 新增车损详情页
  car_testid_page_damagedetail_header_lefticon:
    'car_testid_page_damagedetail_header_lefticon',
  car_testid_page_damagedetail_govehicledamageprove:
    'car_testid_page_damagedetail_govehicledamageprove',
  car_testid_page_damagedetail_video_item_play:
    'car_testid_page_damagedetail_video_item_play',
  car_testid_page_damagedetail_image_item_browser:
    'car_testid_page_damagedetail_image_item_browser',
  car_testid_page_damagedetail_more: 'car_testid_page_damagedetail_more',
  car_testid_page_damagedetail_showrateintroduce_modal:
    'car_testid_page_damagedetail_showrateintroduce_modal',

  // 新增补款页面
  car_testid_page_supplement_closemask: 'car_testid_page_supplement_closemask',
  car_testid_page_supplement_header_lefticon:
    'car_testid_page_supplement_header_lefticon',
  car_testid_page_supplement_nav_item: 'car_testid_page_supplement_nav_item',
  car_testid_page_supplement_card_item: 'car_testid_page_supplement_card_item',
  car_testid_page_supplement_card_item_button_wrap:
    'car_testid_page_supplement_card_item_button_wrap',
  car_testid_page_supplement_card_item_button_item:
    'car_testid_page_supplement_card_item_button_item',

  // 新增续租页面
  car_testid_page_rerent_header_lefticon:
    'car_testid_page_rerent_header_lefticon',
  car_testid_page_rerent_status_showrefunddetail:
    'car_testid_page_rerent_status_showrefunddetail',

  // 新增提前还车页面
  car_testid_page_advancereturn_header_lefticon:
    'car_testid_page_advancereturn_header_lefticon',
  car_testid_page_advancereturn_priceresult:
    'car_testid_page_advancereturn_priceresult',
  car_testid_page_advancereturn_nomatch_reload:
    'car_testid_page_advancereturn_nomatch_reload',
  car_testid_page_advancereturn_footer_cancel:
    'car_testid_page_advancereturn_footer_cancel',
  car_testid_page_advancereturn_footer_back:
    'car_testid_page_advancereturn_footer_back',
  car_testid_page_advancereturn_footer_apply:
    'car_testid_page_advancereturn_footer_apply',

  // 新增保险订单详情页面
  car_testid_page_insuranceorderdetail_header_lefticon:
    'car_testid_page_insuranceorderdetail_header_lefticon',
  car_testid_page_insuranceorderdetail_header_righticon:
    'car_testid_page_insuranceorderdetail_header_righticon',
  car_testid_page_insuranceorderdetail_button_item:
    'car_testid_page_insuranceorderdetail_button_item',
  car_testid_page_insuranceorderdetail_reload:
    'car_testid_page_insuranceorderdetail_reload',
  car_testid_page_insuranceorderdetail_gotop:
    'car_testid_page_insuranceorderdetail_gotop',

  // 新增订单违章规则页面
  car_testid_page_ordervialationrule_header_lefticon:
    'car_testid_page_ordervialationrule_header_lefticon',

  // 新增修改订单页面
  car_testid_page_modifyorder_reload: 'car_testid_page_modifyorder_reload',
  car_testid_page_modifyorder_header_lefticon:
    'car_testid_page_modifyorder_header_lefticon',
  car_testid_page_modifyorder_pickup_city:
    'car_testid_page_modifyorder_pickup_city',
  car_testid_page_modifyorder_dropoff_city:
    'car_testid_page_modifyorder_dropoff_city',
  car_testid_page_modifyorder_pickup_area:
    'car_testid_page_modifyorder_pickup_area',
  car_testid_page_modifyorder_dropoff_area:
    'car_testid_page_modifyorder_dropoff_area',
  car_testid_page_modifyorder_pickup_time:
    'car_testid_page_modifyorder_pickup_time',
  car_testid_page_modifyorder_dropoff_time:
    'car_testid_page_modifyorder_dropoff_time',
  car_testid_page_modifyorder_rebook: 'car_testid_page_modifyorder_rebook',
  car_testid_page_modifyorder_footer_button:
    'car_testid_page_modifyorder_footer_button',

  // 新增订单修改确认页面
  car_testid_page_modifyorderconfirm_header_lefticon:
    'car_testid_page_modifyorderconfirm_header_lefticon',
  car_testid_page_modifyorderconfirm_insurancesitem:
    'car_testid_page_modifyorderconfirm_insurancesitem',
  car_testid_page_modifyorderconfirm_footer_button:
    'car_testid_page_modifyorderconfirm_footer_button',

  // 新增重新预定首页
  car_testid_page_rebookhome_header_lefticon:
    'car_testid_page_rebookhome_header_lefticon',

  // 新增租车中心页面
  car_testid_page_carrentalcenter_header_lefticon:
    'car_testid_page_carrentalcenter_header_lefticon',
  car_testid_page_carrentalcenter_guide:
    'car_testid_page_carrentalcenter_guide',
  car_testid_page_carrentalcenter_footer_button:
    'car_testid_page_carrentalcenter_footer_button',

  // 新增预定协议页面
  car_testid_page_isdagreement_header_lefticon:
    'car_testid_page_isdagreement_header_lefticon',

  // 新增消息助手页面
  car_testid_page_messageassistant_header_lefticon:
    'car_testid_page_messageassistant_header_lefticon',

  // 新增用车指南页面
  car_testid_page_instructions_header_lefticon:
    'car_testid_page_instructions_header_lefticon',
  car_testid_page_instructions_listitem:
    'car_testid_page_instructions_listitem',
  car_testid_page_instructions_listitem_hightext:
    'car_testid_page_instructions_listitem_hightext',
  car_testid_page_instructions_listitem_map:
    'car_testid_page_instructions_listitem_map',
  car_testid_page_instructions_listitem_cardentry:
    'car_testid_page_instructions_listitem_cardentry',

  // 新增会员详情页
  car_testid_page_memberdetail_header_lefticon:
    'car_testid_page_memberdetail_header_lefticon',
  car_testid_page_memberdetail_footer_button:
    'car_testid_page_memberdetail_footer_button',
  car_testid_page_memberdetail_membertab_item:
    'car_testid_page_memberdetail_membertab_item',

  // 新增会员积分页面
  car_testid_page_memberpoints_header_lefticon:
    'car_testid_page_memberpoints_header_lefticon',

  // 新增车损证明页面
  car_testid_page_vehicledamageprove_header_lefticon:
    'car_testid_page_vehicledamageprove_header_lefticon',
  car_testid_page_vehicledamageprove_damageitem:
    'car_testid_page_vehicledamageprove_damageitem',
  car_testid_page_vehicledamageprove_photobrowser:
    'car_testid_page_vehicledamageprove_photobrowser',

  // 新增推荐车型页面
  car_testid_page_recommendvehicle_header_lefticon:
    'car_testid_page_recommendvehicle_header_lefticon',
  car_testid_page_recommendvehicle_minisearch_edit:
    'car_testid_page_recommendvehicle_minisearch_edit',
  car_testid_page_recommendvehicle_retry:
    'car_testid_page_recommendvehicle_retry',
  car_testid_page_recommendvehicle_golist:
    'car_testid_page_recommendvehicle_golist',

  // 新增限行地图页面
  car_testid_page_limitmap_header_lefticon:
    'car_testid_page_limitmap_header_lefticon',

  // 新增押金费用页面
  car_testid_page_depositfree_header_lefticon:
    'car_testid_page_depositfree_header_lefticon',

  // 新增相册页面
  car_testid_page_album_header_lefticon:
    'car_testid_page_album_header_lefticon',
  car_testid_page_album_menu_item: 'car_testid_page_album_menu_item',
  car_testid_page_album_menu_item_more: 'car_testid_page_album_menu_item_more',

  car_osd_pick_return_content: 'car_osd_pick_return_content',
  car_osd_pick_return_item: 'car_osd_pick_return_item',
  car_testid_page_home_content_wrap: 'car_testid_page_home_content_wrap',
  car_testid_page_home_pickup_city: 'car_testid_page_home_pickup_city',
  car_testid_page_home_pickup_address: 'car_testid_page_home_pickup_address',
  car_testid_page_home_return_city: 'car_testid_page_home_return_city',
  car_testid_page_home_return_address: 'car_testid_page_home_return_address',
  car_testid_page_home_offsite_switch: 'car_testid_page_home_offsite_switch',
  car_testid_page_home_pickup_time: 'car_testid_page_home_pickup_time',
  car_testid_page_home_return_time: 'car_testid_page_home_return_time',
  car_testid_page_home_site_take: 'car_testid_page_home_site_take',
  car_testid_page_home_pickup_ondoor: 'car_testid_page_home_pickup_ondoor',
  car_testid_page_home_search_btn: 'car_testid_page_home_search_btn',
  car_testid_comp_vendor_headpic: 'car_testid_comp_vendor_headpic',
  car_testid_page_vendor_detail_btn: 'car_testid_page_vendor_detail_btn',
  car_testid_page_vendor_card: 'car_testid_page_vendor_card',
  car_testid_comp_bookbar_price: 'car_testid_comp_bookbar_price',
  car_testid_page_deposit_item: 'car_testid_page_deposit_item',
  car_testid_page_area_history: 'car_testid_page_area_history',
  car_testid_page_area_recommend: 'car_testid_page_area_recommend',
  car_testid_comp_filter_modal_item: 'car_testid_comp_filter_modal_item',
  // 车型卡片图片
  car_testid_comp_vehicle_car_image: 'car_testid_comp_vehicle_car_image',
  // 车型卡片名称
  car_testid_comp_vehicle_name: 'car_testid_comp_vehicle_name',
  // 车型卡片车型描述
  car_testid_comp_vehicle_desc: 'car_testid_comp_vehicle_desc',
  // 车型卡片标签
  car_testid_comp_vehicle_tag: 'car_testid_comp_vehicle_tag',
  // 价格问号
  car_testid_comp_vehicle_price_help: 'car_testid_comp_vehicle_price_help',
  // 车型日价
  car_testid_comp_vehicle_day_price: 'car_testid_comp_vehicle_day_price',
  // 车型总价
  car_testid_comp_vehicle_total_price: 'car_testid_comp_vehicle_total_price',
  // 价格弹层日价
  car_testid_comp_price_modal_day_price:
    'car_testid_comp_price_modal_day_price',
  // 价格弹层总价
  car_testid_comp_price_modal_total_price:
    'car_testid_comp_price_modal_total_price',
  // 价格弹层
  car_testid_comp_price_modal: 'car_testid_comp_price_modal',
  // 快筛bar
  car_testid_comp_quick_filter_bar: 'car_testid_comp_quick_filter_bar',
  // 价格弹层下一步
  car_testid_comp_price_modal_next: 'car_testid_comp_price_modal_next',
  // 详情页车型名
  car_testid_comp_head_vehicle_name: 'car_testid_comp_head_vehicle_name',
  // 详情页供应商名
  car_testid_comp_vendor_name: 'car_testid_comp_vendor_name',
  // 详情页点评
  car_testid_comp_vendor_comment: 'car_testid_comp_vendor_comment',
  // 供应商取还车信息
  car_testid_comp_vendor_pick_drop_desc:
    'car_testid_comp_vendor_pick_drop_desc',
  // 供应商日价
  car_testid_comp_vendor_day_price: 'car_testid_comp_vendor_day_price',
  // 供应商总价
  car_testid_comp_vendor_total_price: 'car_testid_comp_vendor_total_price',
  // 供应商价格明细问号
  car_testid_comp_vendor_price_help: 'car_testid_comp_vendor_price_help',
  // 填写页底部Bar
  car_testid_comp_book_bar_total_price: 'car_testid_comp_book_bar_total_price',
  // 首页非融合首页BannerA
  car_testid_page_home_banner_first_line_right_B:
    'car_testid_page_home_banner_first_line_right_B',

  // 首页周租入口
  car_testid_page_home_week_rent: 'car_testid_page_home_week_rent',
  // 首页月租入口
  car_testid_page_home_month_rent: 'car_testid_page_home_month_rent',
  // 会员权益
  car_testid_page_home_membership_rights:
    'car_testid_page_home_membership_rights',
  car_testid_page_home_member_grow_up: 'car_testid_page_home_member_grow_up',
  car_testid_page_home_member_my_point: 'car_testid_page_home_member_my_point',
  car_testid_page_home_member_title: 'car_testid_page_home_member_title',
  car_testid_page_home_member_to_view: 'car_testid_page_home_member_to_view',
  car_testid_page_home_member_desc: 'car_testid_page_home_member_desc',
  car_testid_page_home_member_welfare_title:
    'car_testid_page_home_member_welfare_title',
  car_testid_page_home_member_go_rent: 'car_testid_page_home_member_go_rent',
  car_testid_page_home_member_go_back: 'car_testid_page_home_member_go_back',
  car_testid_page_home_member_page_title:
    'car_testid_page_home_member_page_title',
  car_testid_page_home_member_level: 'car_testid_page_home_member_level',
  car_testid_page_home_member_level_url:
    'car_testid_page_home_member_level_url',
  car_testid_page_home_member_welfare_item:
    'car_testid_page_home_member_welfare_item',
  car_testid_page_home_member_rights_text_1:
    'car_testid_page_home_member_rights_text_1',
  car_testid_page_home_member_rights_text_2:
    'car_testid_page_home_member_rights_text_2',
  car_testid_page_home_member_rights_title_1:
    'car_testid_page_home_member_rights_title_1',
  car_testid_page_home_member_rights_title_2:
    'car_testid_page_home_member_rights_title_2',
  car_testid_page_home_member_rights_title_3:
    'car_testid_page_home_member_rights_title_3',
  car_testid_page_home_member_rights_icon:
    'car_testid_page_home_member_rights_icon',

  // 首页行程卡
  car_testid_page_itinerary_card: 'car_testid_page_itinerary_card',
  car_testid_page_itinerary_card_osd: 'car_testid_page_itinerary_card_osd',
  // 首页行程卡
  car_testid_page_itinerary_card_item: 'car_testid_page_itinerary_card_item',
  // 首页订单卡片
  car_testid_page_home_order_card: 'car_testid_page_home_order_card',
  // 首页订单卡片按钮组
  car_testid_page_home_order_card_btn: 'car_testid_page_home_order_card_btn',
  // 首页订单卡片地图
  car_testid_home_order_card_btn_guide: 'car_testid_home_order_card_btn_guide',
  // 首页新手指南入口
  car_testid_page_home_guide_rent: 'car_testid_page_home_guide_rent',
  // 榜单tab
  car_testid_page_home_ranking_tab: 'car_testid_page_home_ranking_tab',
  // 首页券包入口
  car_testid_page_home_coupon_entry: 'car_testid_page_home_coupon_entry',
  // 首页券包入口标题图片
  car_testid_page_home_coupon_entry_title_image:
    'car_testid_page_home_coupon_entry_title',
  // 首页券包item
  car_testid_page_home_coupon_item: 'car_testid_page_home_coupon_item',
  // 首页券包显示更多按钮
  car_testid_page_home_coupon_show_more_btn:
    'car_testid_page_home_coupon_show_more_btn',
  // 首页券包已领取
  car_testid_page_home_coupon_status2: 'car_testid_page_home_coupon_status2',
  // 详情页- 报价列表 - 订按钮
  car_testid_comp_vendor_list_book: 'car_testid_comp_vendor_list_book',
  // 详情页- 总价弹层
  car_testid_comp_vendor_total_price_pop:
    'car_testid_comp_vendor_total_price_pop',
  // 详情页- 供应商弹层Tab
  car_testid_comp_vendor_modal_tab: 'car_testid_comp_vendor_modal_tab',
  // 详情页- 供应商弹层标题logo
  car_testid_comp_vendor_modal_title_logo:
    'car_testid_comp_vendor_modal_title_logo',
  // 详情页- 供应商弹层标题
  car_testid_comp_vendor_modal_title: 'car_testid_comp_vendor_modal_title',
  // 详情页- 供应商弹层地址
  car_testid_comp_vendor_modal_address: 'car_testid_comp_vendor_modal_address',
  // 详情页- 供应商弹层车型标签
  car_testid_comp_vendor_modal_tags: 'car_testid_comp_vendor_modal_tags',
  // 详情页- 供应商弹层门店实拍图
  car_testid_comp_vendor_modal_vehicle_pic:
    'car_testid_comp_vendor_modal_vehicle_pic',
  // 详情页- 供应商弹层评分
  car_testid_comp_vendor_modal_score: 'car_testid_comp_vendor_modal_score',
  // 详情页- 供应商弹层 - BookBar
  car_testid_comp_vendor_modal_book_bar:
    'car_testid_comp_vendor_modal_book_bar',
  // 详情页- 供应商弹层 - 更多政策按钮
  car_testid_comp_vendor_modal_more_policy_btn:
    'car_testid_comp_vendor_modal_more_policy_btn',
  // 详情页- 供应商弹层 - 门店政策页面
  car_testid_comp_vendor_modal_more_policy_wrap:
    'car_testid_comp_vendor_modal_more_policy_wrap',
  // 详情页- 供应商弹层 - 标题弹层关闭按钮
  car_testid_comp_vendor_modal_title_close_mask:
    'car_testid_comp_vendor_modal_title_close_mask',
  // 详情页- 供应商弹层 - 取还车地图
  car_testid_comp_vendor_modal_location_map:
    'car_testid_comp_vendor_modal_location_map',
  // 详情页- 供应商弹层 - 无忧租入口
  car_testid_comp_vendor_modal_easy_life_entry:
    'car_testid_comp_vendor_modal_easy_life_entry',
  // 详情页- 供应商弹层 - 查看全部评价
  car_testid_comp_vendor_modal_reviews_all_btn:
    'car_testid_comp_vendor_modal_reviews_all_btn',
  // 详情页- 供应商弹层 - 更多政策
  car_testid_comp_vendor_modal_more_policy:
    'car_testid_comp_vendor_modal_more_policy',
  // 详情页- 保障服务 - 保险
  car_testid_page_insurance_item: 'car_testid_page_insurance_item',
  // 详情页- 保障服务 - 星级分数展示
  car_testid_page_insurance_score_osd: 'car_testid_page_insurance_score_osd',
  // 详情页- 保障服务 - 套餐名称
  car_testid_page_insurance_name_osd: 'car_testid_page_insurance_name_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐构成
  car_testid_page_insurance_compose_osd:
    'car_testid_page_insurance_compose_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐构成图片
  car_testid_page_insurance_support_img_osd:
    'car_testid_page_insurance_support_img_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐
  car_testid_page_insurance_nav_osd: 'car_testid_page_insurance_nav_osd',
  // 详情页 - 保险套餐详情 - 弹层关闭按钮
  car_testid_page_insurance_modal_close_icon:
    'car_testid_page_insurance_modal_close_icon',
  // 详情页 - 保险套餐详情 - 保障详情
  car_testid_page_insurance_modal_detail:
    'car_testid_page_insurance_modal_detail',
  // 详情页- 供应商弹层 - 查看更多
  car_testid_comp_vendor_modal_image_all_btn:
    'car_testid_comp_vendor_modal_image_all_btn',
  // 详情页- 供应商弹层 - 优质门店 - 营业执照
  car_testid_comp_vendor_modal_store_license:
    'car_testid_comp_vendor_modal_store_license',
  // 列表页-租车中心-筛选按钮
  car_testid_page_rent_center_filter_btn:
    'car_testid_page_rent_center_filter_btn',
  // 列表页-租车中心-banner 点击
  car_testid_page_rent_center_banner: 'car_testid_page_rent_center_banner',
  // 门店政策页面返回按钮
  car_testid_comp_policy_back_btn: 'car_testid_comp_policy_back_btn',

  car_testid_page_order_detail_deposit_detail_modal_header:
    'car_testid_page_order_detail_deposit_detail_modal_header',
  car_testid_page_order_detail_damage_content:
    'car_testid_page_order_detail_damage_content',
  car_testid_page_supplementlist_car_deposit:
    'car_testid_page_supplementlist_car_deposit',
  car_testid_comp_Credentia_CredentiaItem:
    'car_testid_comp_Credentia_CredentiaItem',
  car_testid_list_filterListWithNav: 'car_testid_list_filterListWithNav',
  car_testid_page_address_copy: 'car_testid_page_address_copy',
  car_testid_page_doorAddress_copy: 'car_testid_page_doorAddress_copy',
  // 海外人数选中组件
  car_testid_page_home_num_select: 'car_testid_page_home_num_select',
  // 海外年龄选中组件
  car_testid_page_home_age_select: 'car_testid_page_home_age_select',
  // // 海外年龄弹窗组件
  // car_testid_page_home_age_modal: 'car_testid_page_home_age_modal',
  car_testid_advance_return_record_btn: 'car_testid_advance_return_record_btn',
  car_testid_advance_return_record_modal:
    'car_testid_advance_return_record_modal',
  car_testid_list_osd_carVehicleList: 'car_testid_list_osd_carVehicleList',
  car_testid_advance_return_time_modify_item:
    'car_testid_advance_return_time_modify_item',
  car_testid_list_VehicleGroupBar: 'car_testid_list_VehicleGroupBar',
  car_testid_product_mustread_more_policy:
    'car_testid_product_mustread_more_policy',
  car_testid_product_mustread_materials:
    'car_testid_product_mustread_materials',
  car_testid_advance_fee_detail_modal: 'car_testid_advance_fee_detail_modal',
  car_testid_advance_return_time_pick_modal:
    'car_testid_advance_return_time_pick_modal',
  car_testid_IdtypeModal: 'car_testid_IdtypeModal',
  car_testid_extrasModal_extrasItem: 'car_testid_extrasModal_extrasItem',
  car_testid_renew_card_minus: 'car_testid_renew_card_minus',
  car_testid_renew_card_plus: 'car_testid_renew_card_plus',
  car_testid_policy_cancel_noshow: 'car_testid_policy_cancel_noshow',
  car_testid_comp_vendor_modal_more_policy_collapse:
    'car_testid_comp_vendor_modal_more_policy_collapse',
  car_testid_page_materials: 'car_testid_page_materials',
  car_testid_instructions_tab: 'car_testid_instructions_tab',

  car_testid_book_osd_driver: 'car_testid_book_osd_driver',
  car_testid_comp_bookbar_button: 'car_testid_comp_bookbar_button',
  car_testid_comp_driver_edit_close: 'car_testid_comp_driver_edit_close',
  car_testid_comp_coupon_item: 'car_testid_comp_coupon_item',

  // 国内相册页面分组
  car_testid_page_album_media_group_item:
    'car_testid_page_album_media_group_item',
  // 国内相册页面底bar
  car_testid_page_album_footer: 'car_testid_page_album_footer',
  // 国内相册页menu
  car_testid_page_album_menu: 'car_testid_page_album_menu',
  // 自助取还
  // 订详页面标题
  car_testid_page_order_detail_page_header_left_icon:
    'car_testid_page_order_detail_page_header_left_icon',
  car_testid_vehicleBootModal: 'car_testid_vehicleBootModal',
  car_testid_page_etc_helper_modal: 'car_testid_page_etc_helper_modal',
  c_testid_product_more_package_detail: 'c_testid_product_more_package_detail',
  c_testid_orderDetail_vehicle_damage_prove:
    'c_testid_orderDetail_vehicle_damage_prove',
  c_testid_orderDetail_vehicle_damage_prove_image_wrap:
    'c_testid_orderDetail_vehicle_damage_prove_image_wrap',
  c_testid_orderDetail_vehicle_damage_prove_image_item:
    'c_testid_orderDetail_vehicle_damage_prove_image_item',
  c_testid_orderDetail_vehicle_damage_item:
    'c_testid_orderDetail_vehicle_damage_item',
  c_testid_orderDetail_vehicle_damage_image_view_more:
    'c_testid_orderDetail_vehicle_damage_image_view_more',
  c_testid_orderDetail_vehicle_damage_fee_help_icon:
    'c_testid_orderDetail_vehicle_damage_fee_help_icon',
  c_testid_orderDetail_deposit_detail_modal_help_icon:
    'c_testid_orderDetail_deposit_detail_modal_help_icon',
  c_testid_depositRateIntroduceModal: 'c_testid_depositRateIntroduceModal',
  c_testid_orderDetail_damageProveHeader:
    'c_testid_orderDetail_damageProveHeader',
  c_testid_orderDetail_depositProgressContent:
    'c_testid_orderDetail_depositProgressContent',
  // 自助取还标签
  c_testid_selfservice_label: 'c_testid_selfservice_label',
  c_testid_travelLimitItem: 'c_testid_travelLimitItem',
  c_testid_countriesListModal: 'c_testid_countriesListModal',
  c_testid_countriesListFooter: 'c_testid_countriesListFooter',
  c_testid_guide_content_item: 'c_testid_guide_content_item',
  car_testid_page_vendorlist_marketbanner:
    'car_testid_page_vendorlist_marketbanner',
  car_testid_page_vendorlist_productconfirm_modal:
    'car_testid_page_vendorlist_productconfirm_modal',
  car_testid_selfserviceslogan: 'car_testid_selfserviceslogan',
  c_testid_pickDropModal_content: 'c_testid_pickDropModal_content',
  c_testid_pickDropModal_backIcon: 'c_testid_pickDropModal_backIcon',
  c_testid_LocalContactsModal: 'c_testid_LocalContactsModal',
  c_testid_ContactDoorStoreModal: 'c_testid_ContactDoorStoreModal',
  c_testid_pickDropStore: 'c_testid_pickDropStore',
};

export default UITestId;
