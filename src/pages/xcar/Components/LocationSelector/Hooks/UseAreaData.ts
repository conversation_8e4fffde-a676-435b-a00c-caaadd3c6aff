import { useEffect, useState, useMemo } from 'react';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import {
  GetAreaListResponseType,
  Area,
  RecomdCityInfo,
} from '../../../Types/Dto/GetAreaListType';
import { useAreaHistory } from './UseHistory';
import {
  AreaSectionType,
  AppType,
  AreaType,
  NoResultType,
  OnPageReadyType,
} from '../Types';
import Texts from '../Texts';

function useGetAreaData(
  area: (cId: string, isDomestic: boolean) => Promise<GetAreaListResponseType>,
  onPageReady: OnPageReadyType,
  cityId: string,
  isDomestic: boolean,
) {
  const [appResponseMap, setAppResponseMap] = useState(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isFail, setIsFail] = useState<boolean>(false);
  const [areaList, setAreaList] = useState<Area[]>([]);
  const [recommendCityInfo, setRecommendCityInfo] = useState<RecomdCityInfo>(
    {},
  );
  const [refreshKey, setRefreshKey] = useState(false);
  const [isSearchInputFocus, setIsSearchInputFocus] = useState(false);
  const refresh = useMemoizedFn(() => {
    setRefreshKey(!refreshKey);
  });
  useEffect(() => {
    setIsLoading(true);
    setIsFail(false);
    setAreaList([]);
    setIsSearchInputFocus(false);
    area(cityId, isDomestic)
      .then(res => {
        setAppResponseMap(res.appResponseMap);
        setRecommendCityInfo(res.recomdCityInfo);
        setAreaList(res.areaList);
        setIsLoading(false);
        setIsSearchInputFocus(
          res?.extraMaps?.noPoiABTestRes === 'B' &&
            !(res?.areaList?.length > 0),
        );
      })
      .catch(() => {
        setIsFail(true);
        setIsLoading(false);
      });
  }, [cityId, isDomestic, refreshKey, area]);

  useEffect(() => {
    if (!isLoading && appResponseMap) {
      onPageReady(appResponseMap);
    }
  }, [onPageReady, isLoading, appResponseMap]);

  return {
    isLoading,
    isFail,
    refresh,
    areaList,
    recommendCityInfo,
    isSearchInputFocus,
  };
}

// AreaView SectionList sections
const locationData = [
  {
    key: AreaSectionType.location,
    text: Texts.currentLocationText,
    data: [{ key: AreaSectionType.location }],
  },
];

export function useComposeAreaSection(
  list: Area[],
  recommendCityInfo: RecomdCityInfo,
  appType: AppType,
  cityId: string,
  locationCityId: string,
  mainPageSearchFrom?: string, // 用作埋点，区分来自首页哪个模块
) {
  const { history, clearHistory } = useAreaHistory(
    appType,
    cityId,
    mainPageSearchFrom,
  );
  const sections = useMemo(() => {
    const historyData = history?.length
      ? [
          {
            key: AreaSectionType.history,
            text: Texts.recentSearches,
            data: [{ key: AreaSectionType.history, history }],
          },
        ]
      : [];
    const footerData = recommendCityInfo?.recomdLocations?.length
      ? [
          {
            key: AreaSectionType.recommend,
            recommendTips: recommendCityInfo.recomdTips,
            recommendPercent: recommendCityInfo.recomdPercent,
            recommendLocations: recommendCityInfo.recomdLocations,
            data: [{ key: AreaSectionType.recommend }],
          },
        ]
      : !list?.length && !history?.length
        ? []
        : [
            {
              key: AreaSectionType.footer,
              text: Texts.haveShownWholeContent,
              data: [{ key: AreaSectionType.footer }],
            },
          ];

    const areaData = list?.length
      ? list?.map(v => {
          const { type, name, areaInfoList } = v;
          const showNum = v.showNum * 2;
          let key;
          switch (type) {
            case AreaType.AirPort:
              key = areaInfoList?.[0]?.isDomestic
                ? AreaSectionType.airportIsd
                : AreaSectionType.airport;
              break;
            case AreaType.TrainStation:
              key = AreaSectionType.trainStation;
              break;
            case AreaType.BusStation:
              key = AreaSectionType.busStation;
              break;
            default:
              key = AreaSectionType.area;
          }
          return {
            key,
            type,
            name,
            showNum,
            data: [{ type, key, areaInfoList }],
          };
        })
      : !history?.length && recommendCityInfo?.recomdLocations?.length
        ? [
            {
              key: AreaSectionType.noResult,
              type: NoResultType.Half,
              data: [{ key: AreaSectionType.noResult }],
            },
          ]
        : [];
    return [
      ...(!!locationCityId && cityId === locationCityId ? locationData : []),
      ...historyData,
      ...areaData,
      ...footerData,
    ];
  }, [list, history, cityId, locationCityId, recommendCityInfo]);
  return { sections, history, clearHistory };
}

export default useGetAreaData;
