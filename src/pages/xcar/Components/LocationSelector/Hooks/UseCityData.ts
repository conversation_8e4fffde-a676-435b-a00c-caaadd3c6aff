import { chunk as lodashChunk } from 'lodash-es';
import { useEffect, useState, useMemo, useRef } from 'react';

import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import {
  GetCityResponseType,
  CityList,
} from '../../../Types/Dto/GetCityListType';
import { CarLog, AppContext, CarStorage } from '../../../Util/Index';
import LogKeyDev from '../../../Constants/LogKeyDev';
import { APP_TYPE } from '../../../Constants/Platform';
import { AppType, CitySectionType, hotPrefix, OnPageReadyType } from '../Types';
import Texts from '../Texts';
import useCityHistory from './UseHistory';
import { cityLimit, locationResStorageKey } from '../Utils/Config';

const getCityStorage = () => {
  const isdKey = locationResStorageKey.ISD;
  const osdKey = locationResStorageKey.OSD;
  const isdResult = CarStorage.loadSyncV2(isdKey);
  const osdResult = CarStorage.loadSyncV2(osdKey);
  try {
    const isdCitys: GetCityResponseType = JSON.parse(isdResult);
    const osdCitys: GetCityResponseType = JSON.parse(osdResult);
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_location_getCityStorage,
      info: {
        isCacheValid: !!isdCitys?.cityList && !!osdCitys?.cityList,
        eventResult: true,
      },
    });
    return [isdCitys, osdCitys];
  } catch (e) {
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_location_getCityStorage,
      info: {
        eventResult: false,
        extraData: {
          isdResult,
          osdResult,
        },
      },
    });
    return null;
  }
};
export function useLazyInitialNumToRender(appType?: AppType): number {
  const max = 20;
  const [initialNumToRender, setInitialNumToRender] = useState(
    appType === AppType.ISD ? 8 : max,
  );
  const timer = useRef<any>(null);
  useEffect(() => {
    timer.current = setTimeout(() => {
      setInitialNumToRender(max);
    }, 1000);
    return () => {
      clearTimeout(timer.current);
    };
  }, []);
  return initialNumToRender;
}

export function useLazyRenderNum(appType: AppType): number {
  const [initialRenderNum, setInitialRenderNum] = useState(
    appType === AppType.ISD ? 4 : 6,
  );
  const timer = useRef<any>(null);
  useEffect(() => {
    timer.current = setTimeout(() => {
      setInitialRenderNum(-1);
    }, 500);
    return () => {
      clearTimeout(timer.current);
    };
  }, []);
  return initialRenderNum;
}

function useGetCityData(
  city: () => {
    isd: Promise<GetCityResponseType>;
    osd: Promise<GetCityResponseType>;
  },
  onPageReady: OnPageReadyType,
) {
  const [appResponseMap, setAppResponseMap] = useState(null);
  const [isdCityList, setIsdCityList] = useState<CityList[]>([]);
  const [osdCityList, setOsdCityList] = useState<CityList[]>([]);
  const [isCityListLoading, setIsCityListLoading] = useState(true);
  const [isIsdCityListFail, setIsIsdCityListFail] = useState(false);
  const [isOsdCityListFail, setIsOsdCityListFail] = useState(false);
  const [refreshKey, setRefreshKey] = useState(false);
  const cityListRefresh = useMemoizedFn(() => {
    setRefreshKey(!refreshKey);
  });
  useEffect(() => {
    const { isd, osd } = city();
    setIsCityListLoading(true);
    setIsIsdCityListFail(false);
    setIsOsdCityListFail(false);
    const [isdStorage, osdStorage] = getCityStorage?.() || [];
    const hasCityStorage =
      !!isdStorage?.cityList?.length && !!osdStorage?.cityList?.length;
    if (hasCityStorage) {
      setIsCityListLoading(false);
      setAppResponseMap(isdStorage.appResponseMap);
      setIsdCityList(isdStorage.cityList);
      setOsdCityList(osdStorage.cityList);
    }

    Promise.all([isd, osd])
      .then(([isdCity, osdCity]) => {
        setAppResponseMap(isdCity?.appResponseMap || osdCity?.appResponseMap);
        if (isdCity?.cityList?.length) {
          setIsdCityList(isdCity.cityList);
          CarStorage.save(locationResStorageKey.ISD, isdCity, '30d');
          // cityInfo节点缺失，或者节点存在但没有数据
          if (
            isdCity?.cityList?.some(
              item => !item?.cityInfo || item?.cityInfo?.length === 0,
            )
          ) {
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_location_city_list_data_missing,
              info: {
                fetchType: APP_TYPE.ISD_C_APP,
                appType: AppContext.CarEnv.appType,
              },
            });
          }
        } else if (!hasCityStorage) {
          setIsIsdCityListFail(true);
        }
        if (osdCity?.cityList?.length) {
          CarStorage.save(locationResStorageKey.OSD, osdCity, '30d');
          setOsdCityList(osdCity.cityList);
          // cityInfo节点缺失，或者节点存在但没有数据
          if (
            osdCity?.cityList?.some(
              item => !item?.cityInfo || item?.cityInfo?.length === 0,
            )
          ) {
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_location_city_list_data_missing,
              info: {
                fetchType: APP_TYPE.OSD_C_APP,
                appType: AppContext.CarEnv.appType,
              },
            });
          }
        } else if (!hasCityStorage) {
          setIsOsdCityListFail(true);
        }
        setIsCityListLoading(false);
      })
      .catch(() => {
        if (!hasCityStorage) {
          setIsIsdCityListFail(true);
          setIsOsdCityListFail(true);
        }
        setIsCityListLoading(false);
      });
  }, [city, refreshKey]);

  useEffect(() => {
    if (!isCityListLoading && appResponseMap) {
      onPageReady(appResponseMap);
    }
  }, [onPageReady, isCityListLoading, appResponseMap]);
  return {
    isdCityList,
    osdCityList,
    isCityListLoading,
    isIsdCityListFail,
    isOsdCityListFail,
    cityListRefresh,
  };
}

const specialNames = {
  [CitySectionType.hot]: Texts.hotCities,
  [CitySectionType.location]: Texts.location,
};

const domesticConfig = {
  [AppType.ISD]: true,
  [AppType.OSD]: false,
};

export function useComposeCitySection(
  list: CityList[],
  appType: AppType,
  locationIsDomestic: boolean,
  isAutonomyProvince: boolean, // 是否是港澳台自治区
  isHideAutonomy?: boolean, // 是否隐藏港澳台自治区 同时隐藏港澳台的定位数据，因为港澳台定位数据会在国内tab以及海外tab下都展示
  mainPageSearchFrom?: string, // 用作埋点，区分来自首页哪个模块
  ilocationCityId?: number,
) {
  const { history, clearHistory } = useCityHistory(
    appType,
    isHideAutonomy,
    mainPageSearchFrom,
  );

  const sections = useMemo(() => {
    const cityData = list
      .filter(v => !v.key.startsWith(hotPrefix))
      .map(v => {
        const { bgImage, cityInfo, key, text } = v;
        return {
          bgImage,
          // sectionList必须命名为“data”
          data: lodashChunk(
            cityInfo.filter(s => !!s.name),
            cityLimit[appType],
          ),
          key,
          text,
        };
      });
    const isIsd = appType === AppType.ISD;
    const otherSectionData = [];
    /**
     * 城市页定位模块展示的条件
     * 1、拿到定位数据后，国内tab下展示国内的定位(除港澳台外，港澳台国内境外tab下都展示)，境外tab下展示境外的定位
     * 2、如果是港澳台自治区，且没有屏蔽港澳台自治区，则定位数据展示在国内tab及海外tab下
     */

    if (
      !!Number(ilocationCityId) &&
       (locationIsDomestic === domesticConfig[appType] ||
         (isAutonomyProvince && !isHideAutonomy))
    ) {
      otherSectionData.push({
        key: CitySectionType.location,
        text: Texts.currentLocationText,
        data: [{ key: CitySectionType.location }],
      });
    }
    if (history?.length) {
      otherSectionData.push({
        key: CitySectionType.history,
        text: Texts.historyCities,
        data: [history.map(item => ({ ...item, isHistory: true }))],
      });
    }
    const hots = list.filter(v => v.key.startsWith(hotPrefix));
    if (hots?.length) {
      otherSectionData.push({
        key: CitySectionType.hot,
        text: Texts.hotCities,
        data: [{ key: CitySectionType.hot, hots }],
      });
    }
    if (isIsd) {
      otherSectionData.push({
        key: CitySectionType.anchor,
        text: Texts.alphabeticalIndex,
        data: [
          cityData.map(v => {
            const { key, text } = v;
            return { key, text };
          }),
        ],
      });
    }
    return [...otherSectionData, ...cityData];
  }, [
    list,
    appType,
    history,
    locationIsDomestic,
    isAutonomyProvince,
    isHideAutonomy,
  ]);

  const anchors = useMemo(() => {
    return sections
      .filter(v => v.key !== CitySectionType.anchor && !!v.text)
      .map(v => {
        const { key } = v;
        let { text } = v;
        if (specialNames[key]) {
          text = specialNames[key];
        }
        return { key, text: text.substring(0, 2) };
      });
  }, [sections]);
  return { sections, anchors, history, clearHistory };
}
export default useGetCityData;
