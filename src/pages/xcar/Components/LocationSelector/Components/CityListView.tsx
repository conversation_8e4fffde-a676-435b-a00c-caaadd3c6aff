import SegmentedControl from '@c2x/components/SegmentedControl';
import StyleSheet from '@c2x/apis/StyleSheet';
import SectionList from '@c2x/components/SectionList';
import Keyboard from '@c2x/apis/Keyboard';
import React, { memo, useRef, useEffect, useMemo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import memoize from 'memoize-one';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import useLazyLoad from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './cityListViewC2xStyles.module.scss';
import { CityList, CityInfo } from '../../../Types/Dto/GetCityListType';
import { AreaInfo } from '../../../Types/Dto/GetAreaListType';
import { SectionHeader, ItemGrouped, ClearHistory, HotCities } from './Items';
import AnchorSide, { AnchorMiddle } from './Anchor';
import Location from './LocationComponent';
import EnvControlTab from './EnvControlTab';
import {
  useComposeCitySection,
  useLazyInitialNumToRender,
  useLazyRenderNum,
} from '../Hooks/UseCityData';
import useGetLocationInfo from '../Hooks/UseLocationData';
import { AppType, CitySectionType } from '../Types';
import { cityLimit } from '../Utils/Config';
import { PageType } from '../../../ComponentBusiness/SkeletonLoading';
import ErrorLoading from './ErrorLoading';
import Texts from '../Texts';
import { FilterPoiType } from '../../../Util/AppContext';
import { Utils } from '../../../Util/Index';

const { getPixel, fixIOSOffsetBottom, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  sectionList: {
    paddingLeft: getPixel(16),
    paddingRight: getPixel(40),
    flex: 1,
  },
  footer: {
    height: fixIOSOffsetBottom(),
  },
  historyTopGap: {
    paddingTop: getPixel(24),
  },
  location: {
    paddingTop: getPixel(12),
  },
  locationGap: {
    marginBottom: getPixel(22),
  },
});

const stylesByCitySectionType = {
  [CitySectionType.location]: styles.location,
  [CitySectionType.history]: styles.historyTopGap,
};
interface ICityListView {
  isLoading: boolean;
  isFail: boolean;
  list: CityList[];
  appType: AppType;
  cityId: string;
  tabLabel: string;
  areaName: string;
  isHideAutonomy: boolean;
  handleCityPress: (data: CityInfo) => void;
  onPressLocation: (data: { area: AreaInfo; city: CityInfo }) => void;
  refresh: () => void;
  mainPageSearchFrom?: string;
}
const CityListView: React.FC<ICityListView> = ({
  isLoading,
  isFail,
  refresh,
  list,
  cityId,
  appType,
  isHideAutonomy,
  handleCityPress,
  areaName,
  onPressLocation,
  mainPageSearchFrom,
}) => {
  const initialNumToRender = useLazyInitialNumToRender(appType);
  const isLazyLoad = useLazyLoad(50);
  const scroll = useRef(null);
  const {
     locationIsDomestic,
     isAutonomyProvince,
     locationCityId: ilocationCityId,
   } = useGetLocationInfo();
  const { sections, anchors, history, clearHistory } = useComposeCitySection(
    list,
    appType,
    locationIsDomestic,
    isAutonomyProvince,
    isHideAutonomy,
    mainPageSearchFrom,
    ilocationCityId,
  );

  // 首屏渲染数量
  const initialRenderNum = useLazyRenderNum(appType);

  // SectionList 渲染数据
  const renderSections = useMemo(() => {
    if (initialRenderNum > 0) {
      return sections?.slice(0, initialRenderNum);
    }
    return sections;
  }, [initialRenderNum, sections]);

  // 点击的锚点 Index
  const activeAnchorIndex = useRef(0);

  // 锚点是否可直接点击
  const anchorTouchAble = useLazyLoad(1200);

  const renderSectionHeader = useMemoizedFn(({ section }) => {
    return section.text && section.key !== CitySectionType.hot ? (
      <SectionHeader
        style={stylesByCitySectionType[section.key]}
        text={section.text}
        renderRight={
          section.key === CitySectionType.history && (
            <ClearHistory handlePress={clearHistory} />
          )
        }
      />
    ) : null;
  });

  const scrollToIndexFailed = useMemoizedFn(() => {
    const wait = new Promise(resolve => setTimeout(resolve, 1000));
    wait.then(() => {
      if (
        scroll.current?.scrollToLocation &&
        activeAnchorIndex?.current !== undefined &&
        initialRenderNum < 0
      ) {
        scroll.current.scrollToLocation({
          sectionIndex: activeAnchorIndex?.current,
          itemIndex: 1,
        });
      }
    });
  });

  const handleAnchorPress = useMemoizedFn(data => {
    const sectionIndex = sections.findIndex(v => v?.key === data?.key);
    activeAnchorIndex.current = sectionIndex;
    if (initialRenderNum > 0 || !anchorTouchAble) {
      scrollToIndexFailed();
      return;
    }
    scroll.current.recordInteraction();
    scroll.current.scrollToLocation({ sectionIndex, itemIndex: 1 });
  });

  const renderItemMemoize = memoize((item, section) => {
    switch (section.key) {
      case CitySectionType.location:
        return (
          <Location
            areaName={areaName}
            style={styles.locationGap}
            onPressItem={onPressLocation}
          />
        );

      case CitySectionType.hot:
        return (
          <HotCities
            hots={item.hots}
            handleCityPress={handleCityPress}
            cityId={cityId}
            mainPageSearchFrom={mainPageSearchFrom}
            appType={appType}
          />
        );

      case CitySectionType.anchor:
        return <AnchorMiddle list={item} handleItemPress={handleAnchorPress} />;
      default:
        return (
          <ItemGrouped
            testID={`car_testid_search_result_item_${section.text}`}
            item={item}
            itemLength={cityLimit[appType]}
            handleCityPress={handleCityPress}
            cityId={cityId}
            continentId={section.key.startsWith('Continent') ? section.key : ''}
            mainPageSearchFrom={mainPageSearchFrom}
            appType={appType}
          />
        );
    }
  });

  const renderItem = useMemoizedFn(({ item, section }) =>
    renderItemMemoize(item, section),
  );

  const keyExtractor = useMemoizedFn((item: any) => {
    if (Array.isArray(item)) {
      return item.reduce((m, v) => `${m}_${v.id || v.key}`, '');
    }
    return item?.key;
  });

  const isShowList = !(isLoading || isFail) || history.length > 0;
  return !isShowList ? (
    <ErrorLoading
      hasError={isFail}
      pageName={PageType.LocationCity}
      refresh={refresh}
    />
  ) : (
    <View className={c2xStyles.wrapper}>
      {isLazyLoad && (
        <SectionList
          ref={scroll}
          style={styles.sectionList}
          onScrollBeginDrag={Keyboard.dismiss}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="always"
          initialNumToRender={initialNumToRender}
          windowSize={21}
          sections={renderSections}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          renderSectionHeader={renderSectionHeader}
          ListFooterComponent={<View style={styles.footer} />}
          stickySectionHeadersEnabled={true}
          onScrollToIndexFailed={scrollToIndexFailed}
        />
      )}
      <AnchorSide
        list={anchors}
        handleItemPress={handleAnchorPress}
        mainPageSearchFrom={mainPageSearchFrom}
        appType={appType}
      />
    </View>
  );
};
const CityListMemo = memo(CityListView);
interface ICityView {
  city: CityInfo;
  areaName: string;
  appType: AppType;
  handleLocationPress: (data: { area: AreaInfo; city: CityInfo }) => void;
  handleCityPress: (city: CityInfo) => void;
  handleTabChange: (data: { i: string }) => void;
  filterPoi?: FilterPoiType;
  isHideAutonomy?: boolean;
  isdCityList?: CityList[];
  osdCityList?: CityList[];
  isIsdCityListFail?: boolean;
  isOsdCityListFail?: boolean;
  isCityListLoading?: boolean;
  cityListRefresh?: () => void;
  mainPageSearchFrom?: string;
}

const CityView: React.FC<ICityView> = ({
  city,
  areaName,
  filterPoi,
  isHideAutonomy,
  handleLocationPress,
  handleCityPress,
  handleTabChange,
  isdCityList,
  osdCityList,
  isIsdCityListFail,
  isOsdCityListFail,
  isCityListLoading,
  cityListRefresh,
  mainPageSearchFrom,
}) => {
  // 根据 filterPoi 屏蔽城市 Tab
  const isShowTab = filterPoi === FilterPoiType.Default;
  const isShowISDCity = filterPoi !== FilterPoiType.ISD;
  const isShowOSDCity = filterPoi !== FilterPoiType.OSD;
  const initialPage = Utils.isCtripOsd() && isShowTab ? 1 : 0;
  const timer = useRef(null);
  const segmentedControl = useRef(null);
  useEffect(() => {
    // 安卓初始化 延迟 200ms 执行 goToPage
    if (isAndroid && segmentedControl?.current) {
      timer.current = setTimeout(() => {
        segmentedControl?.current?.goToPage(initialPage);
      }, 200);
    }
    // 组件卸载清除定时器
    return () => {
      if (isAndroid && timer?.current) {
        clearTimeout(timer?.current);
      }
    };
  }, [initialPage]);

  const handleTabChangeFix = useMemoizedFn(param => {
    handleTabChange(param);
    // tab发生变化清除定时器
    if (isAndroid && timer?.current) {
      clearTimeout(timer?.current);
    }
  });

  return (
    <SegmentedControl
      renderTabBar={p =>
        isShowTab ? (
          <EnvControlTab props={p} />
        ) : (
          <View className={c2xStyles.tabEmptyStyle} />
        )
      }
      tabBarTextStyle={font.areaLabel}
      tabBarUnderlineColor={color.blueBase}
      tabBarActiveTextColor={color.blueBase}
      tabBarInactiveTextColor={color.blueGrayBase}
      initialPage={initialPage}
      onChangeTab={handleTabChangeFix}
      ref={ref => {
        segmentedControl.current = ref;
      }}
    >
      {isShowISDCity && (
        <CityListMemo
          isFail={isIsdCityListFail}
          isHideAutonomy={isHideAutonomy}
          isLoading={isCityListLoading}
          refresh={cityListRefresh}
          list={isdCityList}
          appType={AppType.ISD}
          cityId={city?.id}
          handleCityPress={handleCityPress}
          tabLabel={Texts.isdTab}
          areaName={areaName}
          onPressLocation={handleLocationPress}
          mainPageSearchFrom={mainPageSearchFrom}
        />
      )}

      {isShowOSDCity && (
        <CityListMemo
          isFail={isOsdCityListFail}
          isHideAutonomy={isHideAutonomy}
          isLoading={isCityListLoading}
          refresh={cityListRefresh}
          list={osdCityList}
          appType={AppType.OSD}
          cityId={city?.id}
          handleCityPress={handleCityPress}
          tabLabel={Texts.osdTab}
          areaName={areaName}
          onPressLocation={handleLocationPress}
          mainPageSearchFrom={mainPageSearchFrom}
        />
      )}
    </SegmentedControl>
  );
};

CityView.defaultProps = {
  filterPoi: FilterPoiType.Default,
  isHideAutonomy: false,
};

export default memo(CityView);
