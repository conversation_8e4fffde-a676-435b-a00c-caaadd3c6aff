@import '../../../Common/src/Tokens/tokens/color.scss';

.main {
  flex-direction: row;
}
.successWrap {
  flex-direction: column;
  position: relative;
}
.wrap {
  flex-direction: row;
  align-items: flex-start;
}
.addressLightIcon {
  color: $fontPrimary;
  margin-right: 10px;
  line-height: 40px;
  font-size: 32px;
}
.titleStyle {
  font-size: 32px;
  color: $blueGrayBase;
  font-weight: bold;
  line-height: 42px;
  max-width: 75%; /* 占据容器的70宽度 */
  overflow: hidden; /* 隐藏溢出部分 */
}
.rightIconStyle {
  width: 40px;
  height: 40px;
  margin-left: 16px;
  background-color: $blueBgSecondary;
  border-radius: 50px;
  border-width: 1px;
  border-color: $grayPlaceholder;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
}
.refreshIcon {
  color: $blueBase;
  font-size: 28px;
}
.address {
  padding-left: 40px;
  padding-top: 12px;
}
.addressTex {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Medium;
  color: $fontSubDark;
  max-width: 75%; /* 占据容器的70宽度 */
  overflow: hidden; /* 隐藏溢出部分 */
}
.tick {
  position: absolute;
  right: 20px;
  bottom: 32px;
  height: 32px;
  width: 32px;
  margin-left: 8px;
  flex-direction: row;
  align-items: center;
}
.tickStyle {
  font-size: 32px;
  line-height: 32px;
  color: $blueBase;
}
.leftIconStyle {
  color: $blueBase;
  margin-right: 10px;
  line-height: 40px;
  font-size: 32px;
}
.closedContentWrap {
  align-items: center;
}
.wrapper {
  margin-left: 8px;
  margin-right: 8px;
  background-color: $white;
  min-height: 88px;
}
