import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import React, { useState, useEffect, CSSProperties } from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text/src/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, font, layout, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './collapseC2xStyles.module.scss';

interface ICollapse {
  title: string;
  children: React.ReactNode;
  expand?: boolean;
  onPress?: (open: boolean) => void;
  titleStyle?: CSSProperties;
  iconStyle?: CSSProperties;
  headStyle?: CSSProperties;
  testID?: string;
  contentTestID?: string;
  titleKeyword?: string;
  titleKeywordStyle?: CSSProperties;
}

export const Collapse: React.FC<ICollapse> = ({
  title,
  children,
  expand = false,
  onPress = () => {},
  titleStyle,
  iconStyle,
  testID,
  contentTestID,
  headStyle,
  titleKeyword,
  titleKeywordStyle,
}) => {
  const [open, setOpen] = useState(expand);

  const toggleCollapsed = () => {
    setOpen(!open);
    if (onPress) {
      onPress(!open);
    }
  };

  useEffect(() => {
    setOpen(expand);
  }, [expand]);
  let titleWithKeyword;
  if (!!titleKeyword && title?.includes(titleKeyword)) {
    titleWithKeyword = title?.split(titleKeyword).map((part, index) => {
      return (
        <Text key={`${part}`}>
          {part}
          {index < title.split(titleKeyword).length - 1 && (
            <Text
              style={xMergeStyles([font.title2MediumStyle, titleKeywordStyle])}
            >
              {titleKeyword}
            </Text>
          )}
        </Text>
      );
    });
  }
  return (
    <View style={layout.justifyStart}>
      <Touchable
        onPress={toggleCollapsed}
        testID={testID}
        className={c2xStyles.headTitle}
        style={headStyle}
      >
        <Text
          style={xMergeStyles([
            font.title2MediumStyle,
            { color: color.fontPrimary },
            titleStyle,
          ])}
        >
          {!!titleKeyword && title?.includes(titleKeyword)
            ? titleWithKeyword
            : title}
          <View>
            <Text type="icon" className={c2xStyles.icon} style={iconStyle}>
              {open ? icon.arrowUp : icon.arrowDown}
            </Text>
          </View>
        </Text>
      </Touchable>
      {open ? <View testID={contentTestID}>{children}</View> : null}
    </View>
  );
};

export default Collapse;
