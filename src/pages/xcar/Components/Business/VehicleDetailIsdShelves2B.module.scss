@import '../../Common/src/Tokens/tokens/color.scss';

.pickAndDropWayBlock {
  padding-left: 32px;
  padding-top: 32px;
  padding-bottom: 32px;
}

.vehicleDetailNewBVerBlockWrap {
  padding-left: 24px;
  padding-right: 24px;
}

.vehicleDetailNewBVerBlockInnerWrap {
  background-color: $white;
  border-radius: 12px;
  padding-left: 34px;
  padding-bottom: 28px;
  padding-right: 24px;
}

.blockTitleIconWrap {
  width: 32px;
  height: 42px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.blockTitle {
  font-size: 30px;
  line-height: 42px;
  font-weight: 500;
  color: $C_111111;
}

.blockLeft {
  margin-right: 16px;
}

.cancelBlockRight {
  flex: 1;
}

.blockTitleIcon {
  width: 32px;
  height: 32px;
}

.negaMb2 {
  margin-bottom: -2px;
}
.pickAndDropWayInner {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 12px;
}

.pickAndDropWayInnerRight {
  flex: 1;
}

.flex1 {
  flex: 1;
}

.pickAndDropWayInnerLeft {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
}

.lineWrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin-top: 14px;
}

.imageWrap {
  flex-direction: row;
}

.verticalLine {
  width: 2px;
  background-color: $C_e9edf1;
  margin: 4px 0;
}

.pickDropWayLabelWrap {
  border-radius: 4px;
  border: 1px;
  border-color: $C_e9edf1;
}

.pickDropWayLabel {
  font-size: 22px;
  line-height: 30px;
  padding: 0px 6px;
}
.pickupWayText {
  font-size: 26px;
  font-weight: normal;
}

.ml6 {
  margin-left: 6px;
}

.pickupWay {
  margin-bottom: 4px;
}

.pickupDropAdress {
  font-size: 26px;
  line-height: 48px;
  font-weight: 500;
  max-width: 470px;
}

.imgScrollWrap {
  overflow: hidden;
  margin: 18px -24px 12px 0;
  padding-right: 32px;
}

.showMoreImage {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 116px;
  background-color: $white;
  margin-left: 12px;
}

.moreIcon {
  width: 34px;
  height: 34px;
  margin-right: 13px;
}

.showMoreTextWrapper {
  width: 26px;
}
.showMoreImageTxt {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  color: $C_111111;
}

.cancelRuleTitle {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  color: $recommendProposeBg;
}
.cancelRuleNotice {
  font-size: 22px;
  line-height: 34px;
  font-weight: normal;
  color: $grayBase;
  margin-top: 12px;
}
.blockSection {
  display: flex;
  flex-direction: row;
  padding-top: 40px;
  margin-bottom: -6px;
}

.lablesWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 12px;
  flex-wrap: wrap;
}

.serviceWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 12px;
  flex-wrap: wrap;
}

.serviceItemWrap {
  flex-direction: row;
  align-items: center;
}

.lablesItem {
  padding: 4px 10px;
  background-color: $C_F5F8FA;
  margin-right: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.lablesItemText {
  font-size: 24px;
  line-height: 36px;
  color: $C_333333;
}

.serviceItemText {
  font-size: 26px;
  color: $C_111111;
}

.serviceItemSplit {
  width: 2px;
  height: 22px;
  background-color: $R_17_17_17_0_10;
  margin: 0 12px;
}

.optiLable {
  padding: 5px 10px;
  background-color: $C_FFF6EA;
  border-radius: 4px;
  margin-right: 12px;
  min-height: 40px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.optiLableText {
  font-size: 24px;
  line-height: 34px;
  color: $C_602a12;
}

.vendorName {
  font-size: 26px;
  color: $C_111111;
  margin-right: 14px;
}

.guideIconBlock {
  position: absolute;
  width: 174px;
  height: 148px;
  z-index: -1;
  top: -88px;
  right: -40px;
}

.guideIconBlockBackgroundImageBtn {
  position: absolute;
  width: 174px;
  height: 154px;
  top: -56px;
  right: -12px;
}

.noGuideStepGuideIconBlockBackgroundImageBtn {
  position: absolute;
  width: 174px;
  height: 154px;
  top: -56px;
  right: -12px;
}

.guideIconBlockBackgroundImage {
  position: absolute;
  width: 174px;
  height: 154px;
  top: -56px;
  right: -12px;
  z-index: -1;
}

.noGuideStepGuideIconBlockBackgroundImage {
  position: absolute;
  width: 174px;
  height: 154px;
  top: -56px;
  right: -12px;
  z-index: -1;
}
.guideIconWrap {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 52px;
  right: 52px;
}

.guideIcon {
  width: 80px;
  height: 80px;
}

.guideText {
  font-size: 22px;
}

.nationalChainTag {
  background-color: $C_EFF4F8;
  height: 44px;
  border-radius: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin-right: 12px;
}

.nationalChainTagText {
  font-size: 24px;
  line-height: 32px;
  color: $C_333333;
}

.dropWayWrap {
  margin-top: 22px;
}

.negativeML8 {
  margin-left: -8px;
}

.youxuanIcon {
  width: 28px;
  height: 28px;
}

.cSCard {
  padding-top: 28px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding-bottom: 24px;
  top: 24px;
}
.carAgeDescription {
  font-size: 26px;
  color: $C_111111;
  line-height: 48px;
  margin-top: 2px;
}

.cSLogoWrap {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 17px;
}
.cSLogo {
  width: 174px;
  height: 40px;
}

.cSItemWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: 35px;
  margin-bottom: 16px;
}

.cSItemIcon {
  width: 32px;
  height: 32px;
  margin-right: 17px;
}

.cSItemTitle {
  font-size: 26px;
  color: $C_FFF0DC;
  line-height: 38px;
}

.cSItemSubtitle {
  font-size: 26px;
  color: $C_FFF0DC;
  line-height: 38px;
}

.cSProgressBar {
  width: 176px;
  height: 16px;
  margin-left: 8px;
  margin-right: 8px;
}

.cSItemContent {
  font-size: 26px;
  color: $C_FFF0DC;
  font-family: "PingFangSC-Regular";
  margin-left: 20px;
}

.title {
  font-size: 30px;
  line-height: 42px;
  font-weight: medium;
  font-family: TripNumber-Bold;
  color: $C_111111;
  margin-top: 3px;
}

.header {
  padding-top: 0,
}

.verticalLineGap {
  width: 2px;
  height: 22px;
  background-color: $C_111111;
  margin: 0 10px;
}

.pt24 {
  padding-top: 24px;
}

.arrowRight {
  font-size: 26px;
  margin-top: 10px;
  color: $C_111111;
}

.assArrowRight {
  position: absolute;
  font-size: 26px;
  right: 0;
  margin-top: 17px;
  color: $C_111111;
}

.busLicenseWrap {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.skeleton {
  position: relative;
  width: 100%;
  height: 220px;
  top: 24px;
}

.promptInfoItemNew {
  margin-top: 16px;
}

.row {
  flex-direction: row;
  align-items: center;
}

.promptInfoTextNew {
  font-size: 26px;
  line-height: 48px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blackBase;
}

.promptInfoTip {
  font-size: 22px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-top: 8px;
  color: $grayBase;
}