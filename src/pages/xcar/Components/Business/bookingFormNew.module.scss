@import '../../Common/src/Tokens/tokens/color.scss';
.block {
  margin-left: 32px;
  margin-right: 32px;
}

.licenseTipWrapper {
  padding-top: 24px;
  padding-bottom: 28px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 2px;
}

.lisenceTipIcon {
  font-size: 30px;
  color: $addMoreDriver;
  margin-right: 17px;
  margin-top: 4px;
}

.licenseTipContent {
  color: $addMoreDriver;
  font-size: 26px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
  flex: 1;
}

.priceWrap {
  flex-direction: row;
  align-items: center;
  padding-left: 10px;
  justify-content: flex-end;
  text-align: left;
}

.questionIcon {
  color: $addMoreDriver;
  font-size: 24px;
}

.noDriverContainer {
  padding-top: 24px;
  padding-bottom: 32px;
}

.addDriverButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: $grayBg;
  border-radius: 8px;
  padding: 28px 0;
  width: 100%;
}

.addDriverIcon {
  width: 34px;
  height: 34px;
  margin-right: 10px;
}

.addDriverText {
  font-size: 30px;
  color: $recommendProposeBg;
  font-family: PingFangSC-Medium;
  margin-top: 3px;
}

.loading {
  width: 693px;
  height: 255px;
}
