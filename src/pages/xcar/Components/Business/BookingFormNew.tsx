import { xMergeStyles, XView as View, XImage as Image } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet'; /* eslint-disable @typescript-eslint/no-use-before-define */
import React, { useState, useEffect, memo } from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { layout, font, icon, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './bookingFormNew.module.scss';
import { getProductTraceData } from '../../State/Product/Method';
import { RecommendedDriversV2 } from '../../ComponentBusiness/RecommendedDrivers';
import Form, { DriverNew, IInputType } from '../../ComponentBusiness/BookForm';
import Block from '../../ComponentBusiness/DetailsBlock';
import { UITestID, ImageUrl } from '../../Constants/Index';
import { CarLog, Utils } from '../../Util/Index';
import { getNewBookingBlockStyle } from '../../Pages/Booking/Component/SwitchVersion';
import { IBookFrom } from './Types';
import SkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  wrapper: {
    marginTop: 0,
    paddingTop: getPixel(40),
    paddingLeft: 0,
    paddingRight: 0,
    backgroundColor: color.white,
  },
  driverInstruct: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginRight: getPixel(32),
    marginBottom: getPixel(8),
  },
  bookingOptimizationWrapper: {
    paddingTop: getPixel(32),
  },
  bookingOptimizationDriverTitle: {
    ...font.title4BoldStyle,
    color: color.recommendProposeBg,
  },
  bookingOptimizationAddMoreDriver: {
    ...font.dialogSubTitle,
    color: color.addMoreDriver,
    marginRight: getPixel(9),
  },
  container: {
    backgroundColor: color.bookingOptimizationGrayBg,
    paddingTop: getPixel(16),
  },
  line: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: color.bookingOptimizationGrayBg,
  },
  loadingBg: {
    backgroundColor: color.white,
    marginBottom: getPixel(16),
    marginTop: getPixel(18),
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    borderRadius: getPixel(12),
  },
});

const MOBILE_PHONE = IInputType.mobilePhone;
const FLIGHT_NUMBER = IInputType.flightNumber;

const getSequences = (isFlightInfo: boolean) => {
  if (Utils.isCtripIsd()) {
    return isFlightInfo ? [MOBILE_PHONE, FLIGHT_NUMBER] : [MOBILE_PHONE];
  }
  return null;
};

const areaC = {
  get showName() {
    return '中国大陆';
  },
  countryCode: '86',
  countryName: 'CN',
};

const BookForm: React.FC<IBookFrom> = ({
  recommendedPassengers = [],
  driverInfo,
  changeFormData,
  onPressDriver,
  onPresssMore,
  selectDriver,
  onPressPickUpMaterials,
  age,
  setAge,
  passenger,
  maxDriverAge,
  minDriverAge,
  driverFeeDesc,
  flightErrorTip,
  isEasyLife,
  flightInfoTip,
  isPressFlightInfo,
  onPressFlightDelayRules,
  testID,
  noAnyPassenger,
  driverPickUpMaterialDesc,
  passengerIDCard,
  passengerError,
  isShowLoading,
  isAirportStore,
  validateFlightNo,
  setFlightErrorTip,
  onPressLocalContacts,
  hasLocalContacts,
  localContactsInputFocus,
  changeLocalContactsData,
  localContactsData,
  showFlightNo,
  needFlightNo,
}) => {
  const logInfo = getProductTraceData();
  const [currentCountry] = useState(areaC);
  useEffect(() => {
    changeFormData([
      { type: 'areaCode', value: areaC.countryCode, error: false },
    ]);
  }, [changeFormData]);

  const sequences = getSequences(!!flightInfoTip);
  const RecommendedDriversComponent = RecommendedDriversV2;
  return (
    <View style={Utils.isCtripIsd() && styles.container}>
      <Block
        renderTitle={<Title onPressPickUpMaterials={onPressPickUpMaterials} />}
        titleStyle={{ paddingBottom: 0, paddingLeft: getPixel(32) }}
        style={xMergeStyles([
          styles.wrapper,
          getNewBookingBlockStyle(),
          Utils.isCtripIsd() && styles.bookingOptimizationWrapper,
        ])}
        testID={CarLog.LogExposure({
          name: '曝光_填写页_驾驶员信息',

          info: {
            ...logInfo,
            nationality: passenger?.nationality,
            driverAge: passenger?.age,
            hasFrequentTravelerInfo: noAnyPassenger ? '0' : '1',
          },
        })}
      >
        {isShowLoading ? (
          <Image
            src={`${ImageUrl.DIMG04_PATH}1tg0512000moo1rnq6E80.png`}
            className={c2xStyles.loading}
          />
        ) : (
          <>
            <RecommendedDriversComponent
              passengers={recommendedPassengers}
              passenger={passenger}
              onPressPassenger={selectDriver}
              onPressMore={onPresssMore}
            />

            <View testID={testID} className={c2xStyles.block}>
              {noAnyPassenger ? (
                <View className={c2xStyles.noDriverContainer}>
                  <Touchable
                    debounce={true}
                    onPress={onPressDriver}
                    className={c2xStyles.addDriverButton}
                  >
                    <Image
                      src={`${ImageUrl.DIMG04_PATH}1tg0s12000monznk97C5C.png`}
                      className={c2xStyles.addDriverIcon}
                    />
                    <Text className={c2xStyles.addDriverText}>新增驾驶员</Text>
                  </Touchable>
                </View>
              ) : (
                <Form
                  needFlightNo={needFlightNo}
                  showFlightNo={showFlightNo}
                  maxDriverAge={maxDriverAge}
                  minDriverAge={minDriverAge}
                  ageInfoTip={driverFeeDesc}
                  driverInfo={[
                    { value: age, type: 'age', error: false },
                    ...driverInfo,
                  ]}
                  changeFormData={changeFormData}
                  changeLocalContactsData={changeLocalContactsData}
                  localContactsData={localContactsData}
                  passengerInfo={passenger}
                  flightErrorTip={flightErrorTip}
                  currentCountry={currentCountry}
                  isWechat={isEasyLife}
                  flightInfoTip={flightInfoTip}
                  sequences={sequences}
                  hasLocalContacts={hasLocalContacts}
                  isIsd={Utils.isCtripIsd()}
                  isNewStyle={Utils.isCtripIsd()}
                  renderPassenger={
                    <DriverNew
                      isPassengerError={passengerError}
                      passenger={passenger}
                      passengerIDCard={passengerIDCard}
                      noAnyPassenger={noAnyPassenger}
                      isLoading={isShowLoading}
                      onPress={onPressDriver}
                    />
                  }
                  handleAgeChange={(currentAge: string) => {
                    CarLog.LogCode({
                      name: '点击_填写页_驾驶员信息_年龄切换成功',

                      age: currentAge,
                    });
                    setAge(currentAge);
                  }}
                  onPressAreaCode={() => {
                    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_选择区号' });
                  }}
                  onPressContact={() => {
                    CarLog.LogCode({
                      name: '点击_填写页_驾驶员信息_选择联系人',
                    });
                  }}
                  onPressAge={() => {
                    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_年龄' });
                  }}
                  onPressIdType={() => {
                    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_证件选择' });
                  }}
                  onPressFlightInfo={() => {
                    if (onPressFlightDelayRules) {
                      onPressFlightDelayRules();
                    }
                  }}
                  isPressFlightInfo={isPressFlightInfo}
                  isAirportStore={isAirportStore}
                  validateFlightNo={validateFlightNo}
                  setFlightErrorTip={setFlightErrorTip}
                  onPressLocalContacts={onPressLocalContacts}
                  localContactsInputFocus={localContactsInputFocus}
                />
              )}
              {!noAnyPassenger && (
                <DriverLisenceTip list={driverPickUpMaterialDesc} />
              )}
            </View>
          </>
        )}
      </Block>
    </View>
  );
};

const DriverLisenceTip = ({ list }: { list?: string[] }) => {
  if (!list?.length || !list?.[0]?.length) return null;
  return (
    <View className={c2xStyles.licenseTipWrapper} style={styles.line}>
      <Text type="icon" className={c2xStyles.lisenceTipIcon}>
        {icon.circleI}
      </Text>
      <View className={c2xStyles.licenseTipContent}>{list[0]}</View>
    </View>
  );
};

interface ITitle {
  onPressPickUpMaterials: () => void;
}

const Title: React.FC<ITitle> = ({ onPressPickUpMaterials }) => {
  return (
    <View style={layout.betweenHorizontal}>
      <View
        style={xMergeStyles([
          layout.flexRow,
          layout.flex1,
          styles.driverInstruct,
        ])}
      >
        <Text fontWeight="medium" style={styles.bookingOptimizationDriverTitle}>
          驾驶员
        </Text>
        <Touchable
          testID={UITestID.car_testid_page_booking_form_diver_help}
          onPress={onPressPickUpMaterials}
        >
          <View className={c2xStyles.priceWrap}>
            <Text style={styles.bookingOptimizationAddMoreDriver}>
              取车证件要求
            </Text>
            <Text type="icon" className={c2xStyles.questionIcon}>
              {icon.circleQuestion}
            </Text>
          </View>
        </Touchable>
      </View>
    </View>
  );
};

export default memo(BookForm);
