import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './nationalChainTagGray.module.scss';

interface INationalChainTag {
  tagTitle: string;
}

// 全国连锁标签
const NationalChainTagGray: React.FC<INationalChainTag> = memo(
  ({ tagTitle }) => {
    if (!tagTitle) return null;
    return (
      <View className={c2xStyles.nationalChainWrap}>
        <Text className={c2xStyles.nationalChainText}>{tagTitle}</Text>
      </View>
    );
  },
);

export default NationalChainTagGray;
