/* eslint-disable no-underscore-dangle */
/* eslint-disable class-methods-use-this */
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import Dimensions from '@c2x/apis/Dimensions';
import CRNPage, { IBasePageProps } from '@c2x/components/Page';
import Device from '@c2x/apis/Device';
import React from 'react';
import {
  xRouter,
  xNavigateBack,
  xGetCurrentPages,
  xGetCurrentInstance,
  XStatusBar as StatusBar,
  xUbt as Log,
  xRedirectTo,
} from '@ctrip/xtaro';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Page from '@ctrip/xtaro/BasePage';
import {
  sharkFailKeys,
  clearSharkFailKeys,
} from '@ctrip/rn_com_car/dist/src/Shark/src/Index';
import {
  AppContext,
  CarLog,
  Utils,
  User,
  Channel,
  EventHelper,
} from '../../Util/Index';
import { LogKey, LogKeyDev } from '../../Constants/Index';
import { ReduxData } from '../../Global/Cache/Index';
import { getStore, lazyLoadReduxStore } from '../../State/StoreRef';
import PagePerfomance, { PerformanceType } from './PagePerfomance';
import ViewPort from './CViewPort';
import RouterMapping from '../../Constants/RouterMapping';
import ErrorKey from '../../Constants/ErrorKey';
import { PageSearchTraceType } from '../../Constants/CommonEnums';
import CarDevHubTrace from '../../Util/DevHubTrace';
import { logBasicInfo } from '../../Util/AppBaseInfoMap';
import { setPushData } from '../../Util/xTaroTools';
import isDev from '../../Util/platformSwith';

import FloatFromRight from './switchPlatform';

let pushTimer = null;

export interface IStateType {
  lang?: string;
  messages?: any;
}

export interface ICPageProps extends IBasePageProps {
  performanceMonitor: PerformanceType;
  page: any;
}

export interface IExtrasTraceInfo {
  orderId?: number;
  extraData?: Object;
}

export default class CPage<
  P extends IBasePageProps,
  S extends IStateType,
> extends Page<P, S> {
  pageAppearCount: number = 0; // 标识是否首次加载页面，首次加载时是0

  isPageReady: boolean = false; // 页面可交互标识

  isPageAppear: boolean = true;

  curPageValue: any = null;

  performanceMonitor: PerformanceType;

  sceneConfig: any = null;

  needLogPage: boolean = false; // immediatelyResetRouteStack 需要手动发送 PV

  // @ts-ignore
  timerList: Array<NodeJS.Timeout | number>;

  /**
   * 是否是初始化页面 用于页面渲染未完成时禁用侧滑
   */
  isInitialPage: boolean = true;

  /**
   * 是否页面展示时刷新禁用侧滑状态
   */
  isResetEnableDragByAppear: boolean = false;

  /**
   * 当前页面禁用侧滑状态，true表示当前页面可以侧滑
   */
  isEnableDrag: boolean = true;

  __crnDimensionChangeEvent = null;

  // 是否触发navigator reset事件
  isImmediatelyResetRouteStack: boolean = false;

  // 页面url添加默认值
  url = '';

  static enableNativeDragBack: () => void;

  static disableNativeDragBack: () => void;

  constructor(prop: P) {
    super(prop);
    this.performanceMonitor = PagePerfomance(this);
    this.curPageValue = getStore().getState();
    this.timerList = [];
    AppContext.setPageInstance(this);
    this.__crnDimensionChangeEvent = DeviceEventEmitter.addListener(
      'didUpdateDimensions',
      function (e) {
        if (e && e.fromCRN) {
          this.onWindowSizeChanged(Dimensions.get('window'));
        }
      }.bind(this),
    );
  }

  UNSAFE_componentWillMount() {
    this.performanceMonitor.firstRenderStart = new Date();
    this.sendPagePV();
  }

  clearTimer() {
    // @ts-ignore
    this.timerList.map(item => clearTimeout(item));
  }

  // @ts-ignore
  addTimer(timerId: NodeJS.Timeout | number) {
    this.timerList.push(timerId);
  }

  getPageId() {
    return '';
  }

  getPageParamInfo() {
    return {};
  }

  // 是否是首次打开webview
  isFirstPage = () => {
    const routes = xGetCurrentPages() || [];
    return !routes.length;
  };

  setEnableDragBack = (enable: boolean = true) => {
    try {
      // 设置侧滑需在当前页面，否则设置页面展示时刷新禁用侧滑状态位
      if (!this.isPageAppear) {
        this.isResetEnableDragByAppear = true;
        // 设置当前禁用侧滑状态，用于页面展示时刷新禁用侧滑状态
        this.isEnableDrag = true;
        return;
      }
      // 这个判断防止setEnableDragBack无限制调用造成死循环
      if (this.isImmediatelyResetRouteStack) {
        return;
      }
      this.isEnableDrag = enable;
      // @ts-ignore
      const { navigation = {} } = xGetCurrentInstance().app;
      const { navigator = {} } = navigation;
      const routes = navigation.getCurrentRoutes?.() || [];
      // 通过当前的路由数量来判断禁用Native测滑还是webView测试
      // 两个侧滑禁用有冲突，不能同时执行
      if (routes.length > 1) {
        const currentRoute = routes[routes.length - 1] || {};
        const { sceneConfig } = currentRoute;
        const { gestures } = sceneConfig || {};
        if (!this.sceneConfig) {
          this.sceneConfig = sceneConfig || FloatFromRight;
        }
        if (!gestures && enable) {
          currentRoute.sceneConfig = { ...this.sceneConfig };
        } else if (gestures && !enable) {
          currentRoute.sceneConfig = { ...this.sceneConfig, gestures: null };
        }
        // xTaro 这个方法调用完之后会触发一次componentDidShow进而触发pageDidAppear
        navigator.immediatelyResetRouteStack(navigation.getCurrentRoutes());
        this.isImmediatelyResetRouteStack = true;
        setTimeout(() => {
          this.isImmediatelyResetRouteStack = false;
        }, 0);
      } else if (enable) {
        CRNPage.enableNativeDragBack();
      } else {
        CRNPage.disableNativeDragBack();
      }
    } catch (error) {
      CarLog.LogError(ErrorKey.e_cpage_set_enable_drag_back, { error });
    }
  };

  enableDragBack = () => {
    this.setEnableDragBack();
  };

  disableDragBack = () => {
    this.setEnableDragBack(false);
  };

  // 初始化页面完成时开启侧滑
  initialPageEnableDragBack = () => {
    if (this.isInitialPage) {
      this.isInitialPage = false;
      this.enableDragBack();
    }
  };

  // 初始化页面未完成时禁用侧滑
  initialPageDisableDragBack = () => {
    if (this.isInitialPage) {
      this.disableDragBack();
    }
  };

  createPageName = (pageName = '') => {
    return `${pageName}${dayjs().format('YYYYMMDDHHmmssSSS')}`;
  };

  onPageReadyAfter() {
    // 子页面实现此方法，页面可操作后调用
  }

  onPageReady(appResponseMap) {
    // 子页面实现此方法，页面可操作时调用
    // 网络耗时需要传入CarFetch返回的参数appResponseMap
    // 此方法可重复调用，只有第一次生效
    this.performanceMonitor.responseMap = appResponseMap;
    this.performanceMonitor.interactiveStart = new Date();
    // @ts-ignore
    this.performanceMonitor.submit();
    this.performanceMonitor.contentShowStart =
      this.performanceMonitor.interactiveStart;
    // @ts-ignore
    this.performanceMonitor.submitTTI();
    if (!this.isPageReady) {
      this.isPageReady = true;
      this.onPageReadyAfter();
    }
  }

  // 子页面实现此方法，页面可追加其它reducer、logic、sharkMessage文件时调用
  lazyLoadOtherModules() {
    lazyLoadReduxStore();
  }

  lazyComponentDidMount() {
    // 子页面实现此方法，
    // 当页面进入时有复杂View需要渲染时，使用其优化页面切换速度
  }

  getPVOption() {
    if (!isDev) {
      return {
        ...logBasicInfo(),
      };
    }
    return {};
  }

  sendPagePV() {
    const pvInfo = this.getPageParamInfo ? this.getPageParamInfo() : {};
    CarLog.LogTrace({
      key: LogKey.c_car_page_search,
      info: {
        pageId: this.getPageId(),
        // @ts-ignore 解决埋点漂移
        targetPageRef: this.targetPageRef,
        ...pvInfo,
        traceType: PageSearchTraceType.PV,
      },
    });
  }

  componentDidShow() {
    this.pageDidAppear();
  }

  pageDidAppear() {
    // 由于crn064版本第一次初始化时不会调用，crn070版本每次显示都会执行，包括初始化，所以加一个标记表示第一次执行
    AppContext.setPageInstance(this);
    this.pageAppearCount += 1;
    this.performanceMonitor.pageLastActiveTime = new Date();
    this.isPageAppear = true;
    Device.setStatusBarStyle('darkContent');
    if (this.isResetEnableDragByAppear) {
      this.isResetEnableDragByAppear = false;
      this.setEnableDragBack(this.isEnableDrag);
    }
  }

  onDidFocus(route) {
    this.logPage(route);
  }

  componentDidHide() {
    this.pageDidDisappear();
  }

  pageDidDisappear(extrasTraceInfo?: IExtrasTraceInfo) {
    EventHelper.removeAllLazyDispatchListener();
    const { orderId = '', extraData } = extrasTraceInfo || {};
    this.isPageAppear = false;
    if (this.performanceMonitor.pageLastActiveTime) {
      // 2021-06-01 对于停留时长埋点做一个负数过滤处理，如果是负数的，则不埋，且新增一个开发埋点，来记录出现负数的情况
      const stayTime = this.performanceMonitor.getPageActiveTime();
      const traceFun = stayTime > 0 ? CarLog.LogTrace : CarLog.LogTraceDev;
      const key =
        stayTime > 0
          ? LogKey.c_car_trace_page_active_time
          : LogKeyDev.c_car_trace_abnormal_stay_time;
      traceFun({
        key,
        info: {
          pageId: this.getPageId(),
          value: stayTime,
          orderId,
          extrasTraceInfo: extraData || {},
          // 2020-09-03 先注释,因为会引发数据太大导致埋点上传失败的问题
          // reduxActions: JSON.stringify(ReduxData.getActions()),
        },
      });
    }
    this.performanceMonitor.pageLastActiveTime = null;
    ReduxData.removeActions();
    try {
      const curSharkFailKeys = sharkFailKeys.filter(Boolean);
      if (curSharkFailKeys?.length > 0) {
        // @ts-ignore
        const sharkFailKeyStr = curSharkFailKeys.toString();
        clearSharkFailKeys();
        CarLog.LogTrace({
          key: LogKey.c_car_trace_app_shark_default,
          info: {
            pageId: this.getPageId(),
            sharkFailKeys: sharkFailKeyStr,
          },
        });
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  push(name: string, ...args: any) {
    if (AppContext.enablePush) {
      AppContext.setEnablePush(false);
      // 定时器
      pushTimer = setTimeout(() => {
        AppContext.setEnablePush(true);
      }, 1000); // 一秒内不能重复点击

      const pushData = args[0] || {};
      setPushData(name, pushData);
      const url = RouterMapping[name];
      if (url) {
        xRouter.navigateTo({ url: `/${url}?urlPageName=${name}` });
      } else {
        console.error('这个页面没有注册吧？');
      }
    }
  }

  pop(name?: string) {
    const routes = xGetCurrentPages() || [];
    if (name) {
      const curIndex = routes.length - 1;
      // @ts-ignore
      const popIndex = routes.findIndex(item => item.__pageName__ === name);
      if (popIndex >= 0) {
        const delta = curIndex - popIndex;
        if (delta > 0) {
          xNavigateBack({ delta });
        } else {
          xNavigateBack();
        }
      } else {
        xNavigateBack();
      }
    } else {
      xNavigateBack();
    }
  }

  popHome() {
    const routes = xGetCurrentPages() || [];
    const homePage = routes.find(
      // @ts-ignore
      item => item.__pageName__ === Channel.getPageId().Home.EN,
    );
    if (AppContext.isHomeCombine && routes.length > 1) {
      // TODO JENNY 需要测试
      CRNPage.backToLast({ animated: true });
    } else if (homePage) {
      this.pop(Channel.getPageId().Home.EN);
    } else {
      this.pop();
    }
  }

  replace(name: string, ...args: any) {
    const pushData = args[0] || {};
    setPushData(name, pushData);
    const url = RouterMapping[name];
    xRedirectTo({ url: `/${url}?urlPageName=${name}` });
  }

  componentDidMount() {
    setTimeout(() => {
      // 上报页面信息到devhub
      CarDevHubTrace.logPageInfo({
        otherInfo: this.getPageParamInfo?.(),
      });
    }, 1000); // 增加延迟防止埋点漂移
    this.performanceMonitor.firstRenderEnd = new Date();
    setTimeout(() => {
      this.lazyComponentDidMount();
    });
    if (Utils.isAndroid) {
      // 安卓沉浸式状态栏
      StatusBar.setBackgroundColor(color.transparent, false);
      StatusBar.setTranslucent(true);
      StatusBar.setBarStyle('dark-content');
    }
  }

  // 页面登入
  login = (
    success: () => void = Utils.noop,
    faild: () => void = Utils.noop,
  ) => {
    User.isLogin()
      .then(userInfo => {
        if (userInfo) {
          success();
        } else {
          User.toLogin()
            .then(res => {
              if (res) {
                success();
              } else {
                faild();
              }
            })
            .catch(() => {
              faild();
            });
        }
      })
      .catch(() => {
        faild();
      });
  };

  componentWillUnmount() {
    this.clearTimer();
    clearTimeout(pushTimer);
    AppContext.setEnablePush(true);
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_page_componentWillUnmount,
      info: {},
    });
    if (this.__crnDimensionChangeEvent) {
      this.__crnDimensionChangeEvent.remove();
    }
  }

  onWindowSizeChanged() {
    // DO SIZE UPDATE
  }

  logPage(route) {
    if (!this.needLogPage) return;
    this.needLogPage = false; // 设置是否需要手动发送埋点标志位为false, 防止重复手动发送
    const pvOption = this.getPVOption ? this.getPVOption() : {};
    // @ts-ignore
    const orderId = pvOption.orderid || '';

    let params = '';
    if (route?.params) {
      try {
        params = `&params=${encodeURIComponent(JSON.stringify(route.params))}`;
        // eslint-disable-next-line no-empty
      } catch (err) {}
    }
    let pageName = '';
    if (route?.name) {
      pageName = `&pageName=${route.name}`;
    }

    try {
      // @ts-ignore
      Log.logSendPV(String(this.getPageId()), {
        ...pvOption,
        // @ts-ignore 解决埋点漂移
        targetPageRef: this.targetPageRef,
        // @ts-ignore Page constructor 时赋值
        url: (this.url + pageName + params).substr(0, 2048),
        orderID: orderId,
      });
    } catch (err) {
      if (isDev) {
        throw new Error(err);
      }
    }
  }

  render() {
    // @ts-ignore
    return <ViewPort>{this.renderPage()}</ViewPort>;
  }

  renderPage() {
    // 子页面实现此方法，代替render
  }
}
