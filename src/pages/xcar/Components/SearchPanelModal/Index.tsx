import React, { Component } from 'react';
import { xRouter } from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { NewSearchPanelModal } from '../../ComponentBusiness/SearchPanelModal';
import ConfirmModal from '../../ComponentBusiness/OrderConfirmModal/Index';
import {
  CarLog,
  Utils,
  CarStorage,
  CarABTesting,
  EventHelper,
} from '../../Util/Index';
import {
  EventName,
  Platform,
  StorageKey,
  PageName,
  LogKey,
  ApiResCode,
} from '../../Constants/Index';
import AppContext from '../../Util/AppContext';
import Channel from '../../Util/Channel';
import { setFromPage } from '../../Global/Cache/ListReqAndResData';
import { ListResSelectors } from '../../Global/Cache/Index';
import { setCarAppEnv } from '../../AppEnvironment';
import {
  isDataChangeEnvironment,
  getNextLocation,
} from '../../State/__Environment/DataUtils';
import { getLocalDayJs } from '../Calendar/Method';
import { composeCityArea2RentalLocation } from '../../State/LocationAndDate/Mappers';
import { IPropsType, IStateType } from './Types';
import { isListInPage } from '../../Util/ABTesting';
import isDev from '../../Util/platformSwith';
import { getIsAiSortTag } from '../../Global/Cache/ListResSelectors';

export enum LocationDatePopTypes {
  other = 0, // 其他
  fromIsdRankingHead = 1, // 榜单头部唤起
  fromIsdRankingResult = 2, // 榜单结果唤起
  fromVendorListPage = 3, // 产品详情页唤起
  fromOsdModifyOrder = 4,
} // 出境修改订单

class SearchPanelModal extends Component<IPropsType, IStateType> {
  /* eslint-disable*/
  static defaultProps = {
    backPageName: PageName.List,
    vehicleCode: '',
    enterPosition: 0,
  };

  backPTime: Date;

  backRTime: Date;

  backUpRentalLocation: any;

  backUpAge: number | string;

  backUpAdultNumber: number;

  backUpChildNumber: number;

  backUpRecCityId: number; // 还原的信息流城市ID

  hasBackUp: boolean;

  backAppEnv: string;

  historyIsdCityInfo: any;

  historyOsdCityInfo: any;

  isModalClickDisabled: boolean = false;

  constructor(props) {
    super(props);
    this.state = {
      confirmModalVisible: false,
    };
    if (props.searchPanelModalRefFn) {
      props.searchPanelModalRefFn(this);
    }
    this.onSelectedLocation = this.onSelectedLocation.bind(this);
  }

  shouldComponentUpdate(next) {
    const pageId = AppContext.PageInstance.getPageId();
    const currentPageId = next.currentPageId;
    let fromListToHome;
    //当从列表页返回到首页的时候也需要更新搜索框的状态
    if (
      next.currentPageId === Channel.getPageId().Home.ID &&
      pageId === Channel.getPageId().List.ID
    ) {
      fromListToHome = true;
    }
    const update =
      pageId === currentPageId ||
      pageId === Channel.getPageId().Location.ID ||
      !pageId ||
      fromListToHome;
    return !!update;
  }

  componentWillUnmount() {
    if (!isDev) {
      AppContext.setListPageIsEffect(true);
    }
    if (this.props.backPageName === PageName.List) {
      AppContext.setRouterListLoaderDispatch(true);
    }
  }

  setConfirmModalVisible = (visible: boolean) => {
    this.setState({
      confirmModalVisible: visible,
    });
  };

  handleBackUpData = () => {
    const {
      ptime,
      rtime,
      rentalLocation,
      age,
      adultSelectNum,
      childSelectNum,
      recCityId,
    } = this.props;
    if (!this.hasBackUp) {
      this.hasBackUp = true;
      this.backPTime = ptime;
      this.backRTime = rtime;
      this.backUpRentalLocation = rentalLocation;
      this.backUpRecCityId = recCityId;
      this.backUpAge = age;
      this.backUpAdultNumber = adultSelectNum;
      this.backUpChildNumber = childSelectNum;
      this.backAppEnv = AppContext.CarEnv.appType;
    }
  };

  logCode = (
    data: {
      pageId: string;
      logInfo: { name: string; [propName: string]: any };
    }[],
  ) => {
    const { currentPageId } = this.props;
    data.forEach(item => {
      if (currentPageId === item.pageId) {
        CarLog.LogCode({
          ...item.logInfo,
        });
      }
    });
  };

  onCancel = () => {
    if (this.isModalClickDisabled) return;
    AppContext.setListPageIsEffect(true);
    const { recCityId, setStreamRecCityId } = this.props;
    this.props.setLocationAndDatePopIsShow({ visible: false });
    // reset reducer
    const resetRentalDate = { pickup: this.backPTime, dropoff: this.backRTime };
    const resetNum = {
      adultSelectNum: this.backUpAdultNumber,
      childSelectNum: this.backUpChildNumber,
    };
    if (this.backAppEnv && this.backAppEnv !== AppContext.CarEnv.appType) {
      setCarAppEnv({ carEnv: { appType: this.backAppEnv } });
    }
    if (this.backUpRentalLocation) {
      EventHelper.sendEvent(EventName.ApptypeChange, {
        apptype: AppContext.CarEnv.appType,
      });
      this.props.setLocationInfo(
        Object.assign({}, this.backUpRentalLocation, {
          notDisPatchPreFetch: true,
        }),
      );
    }
    // 首页搜索弹窗取消，还原信息流组件的推荐城市ID，例如榜单的取车城市修改
    if (this.backUpRecCityId !== recCityId) {
      setStreamRecCityId({ recCityId: this.backUpRecCityId });
    }
    this.props.setDateInfo(
      Object.assign({}, resetRentalDate, { notDisPatchPreFetch: true }),
    );
    this.props.setAge(
      Object.assign({}, { age: this.backUpAge }, { notDisPatchPreFetch: true }),
    );
    this.props.setAgeAdultAndChildNum(
      Object.assign({}, resetNum, { notDisPatchPreFetch: true }),
    );
    CarLog.LogCode({ name: '点击_列表页_关闭修改取还车信息弹层' });
    this.hasBackUp = false;
    if (this.props.onCancelCallback) {
      this.props.onCancelCallback();
    }
  };

  addMark = appType => {
    if (
      this.props.currentPageId === Channel.getPageId().List.ID &&
      appType !== AppContext.CarEnv.appType
    ) {
      // 修复国内切境外再切国内页面展示异常
      if (isListInPage()) {
        AppContext.setListPageIsEffect(appType === Platform.APP_TYPE.ISD_C_APP);
      } else {
        AppContext.setListPageIsEffect(appType === Platform.APP_TYPE.OSD_C_APP);
      }
    }
  };

  updateLocationInfo = (rentalLocation, isDataChangeEnv?: any) => {
    const { pickType, showDropOff, setLocationInfo } = this.props;
    const isHasInfoFlow = CarABTesting.isInfoFlow();
    const locationInfo = {
      ...getNextLocation({
        isChangeEnv: isDataChangeEnv,
        isShowDropOff: showDropOff,
        pickType,
        rentalLocation,
      }),
      isHasInfoFlow,
    };
    if (isDataChangeEnv) {
      if (pickType === 'dropOff') {
        if (!showDropOff) {
          setLocationInfo({
            isShowDropOff: true,
            pickUp: locationInfo.pickUp,
            isHasInfoFlow,
          });
        } else {
          setLocationInfo(locationInfo);
        }
        Toast.show('暂时没有异门店取还车资源哦，建议您修改取还车条件');
      } else {
        setLocationInfo(locationInfo);
      }
    } else {
      setLocationInfo(locationInfo);
    }
    if (AppContext.CallCityAreaPageBackPageName) {
      EventHelper.sendEvent('car_changeRentalLocation_seo', {
        ...rentalLocation,
      });
      // CPage.popToPage(AppContext.CallCityAreaPageBackPageName);
    }
    return locationInfo;
  };
  setAreaAppType = (appType, notDisPatch = false) => {
    this.addMark(appType);
    setCarAppEnv({ carEnv: { appType }, notDisPatch });
  };
  onSelectedLocation = data => {
    const rentalLocation = composeCityArea2RentalLocation(data);
    const isDataChangeEnv = isDataChangeEnvironment(
      this.props.rentalLocation,
      rentalLocation,
    );
    if (isDataChangeEnv) {
      const appType = Utils.getNewAppType(rentalLocation.isDomestic);
      this.setAreaAppType(appType);
      EventHelper.sendEvent(EventName.ApptypeChange, { apptype: appType });
    }
    setTimeout(() => {
      this.updateLocationInfo(rentalLocation, isDataChangeEnv);
    });
  };

  handlePressCityLog = (isPickUp: boolean) => {
    this.logCode([
      {
        pageId: Channel.getPageId().List.ID,
        logInfo: {
          name: isPickUp
            ? '点击_列表页_修改取还车信息弹层_修改取车城市信息'
            : '点击_列表页_修改取还车信息弹层_修改还车城市信息',
        },
      },
      this.fromVendorListPageLog(
        isPickUp
          ? '点击_产品详情页_修改取还车信息弹层_修改取车城市信息'
          : '点击_产品详情页_修改取还车信息弹层_修改还车城市信息',
      ),
      {
        pageId: Channel.getPageId().Order.ID,
        logInfo: { name: '点击_修改订单弹框_取还车城市' },
      },
      {
        pageId: Channel.getPageId().OrderCancel.ID,
        logInfo: { name: '点击_修改订单弹框_取还车城市' },
      },
    ]);
  };

  handlePressLocationLog = (isPickUp: boolean) => {
    this.logCode([
      {
        pageId: Channel.getPageId().List.ID,
        logInfo: {
          name: isPickUp
            ? '点击_列表页_修改取还车信息弹层_修改取车地点信息'
            : '点击_列表页_修改取还车信息弹层_修改还车地点信息',
        },
      },
      this.fromVendorListPageLog(
        isPickUp
          ? '点击_产品详情页_修改取还车信息弹层_修改取车地点信息'
          : '点击_产品详情页_修改取还车信息弹层_修改还车地点信息',
      ),
      {
        pageId: Channel.getPageId().Order.ID,
        logInfo: { name: '点击_修改订单弹框_取还车地区' },
      },
      {
        pageId: Channel.getPageId().OrderCancel.ID,
        logInfo: { name: '点击_修改订单弹框_取还车地区' },
      },
    ]);
  };

  fromVendorListPageLog = enName => {
    const { vehicleCode, enterPosition } = this.props;
    return {
      pageId: Channel.getPageId().VendorList.ID,
      logInfo: {
        name: enName,
        info: {
          vehicleCode,
          enterPosition,
        },
      },
    };
  };
  onAgeChange = data => {
    this.props.setAge({ age: data });
    CarLog.LogCode({ name: '点击_列表页_修改取还车信息弹层_修改年龄_确认' });
  };

  onAgeCancel = () => {
    CarLog.LogCode({ name: '点击_列表页_修改取还车信息弹层_修改年龄_取消' });
  };
  handleTimepassed = (ptime, rtime) => {
    const curMinutes = Math.ceil(parseInt(dayjs().format('mm')) / 15) * 15;
    const timeDiff =
      Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
    const newRentalDate = {
      pickup: dayjs().add(4, 'hours').minute(curMinutes),
      dropoff: dayjs()
        .add(4, 'hours')
        .minute(curMinutes + timeDiff),
    };
    this.props.setDateInfo(newRentalDate);
    Toast.show('取车时间已过当前时间，已为您修改取车时间', 2);
  };
  timeChange = data => {
    const ptime = data.ptime;
    const rtime = data.rtime;
    const newRentalDate = { pickup: ptime, dropoff: rtime };
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else {
      this.props.setDateInfo(newRentalDate);
    }
  };
  onTimeChange = data => {
    this.timeChange(data);
    CarLog.LogCode({ name: '点击_列表页_修改取还车信息弹层_修改时间_确认' });
  };

  onTimeCancel = () => {
    CarLog.LogCode({ name: '点击_列表页_修改取还车信息弹层_修改时间_取消' });
  };

  onPressSearch = () => {
    this.isModalClickDisabled = true;
    // 用户可能点击查询按钮，极短时间内又点击蒙层，触发取消事件。这里给查询按钮加个500ms的锁
    setTimeout(() => {
      this.isModalClickDisabled = false;
    }, 500);
    const isAiSortTag = getIsAiSortTag();
    if (Utils.isCtripIsd() && isAiSortTag) {
      // 清除前端缓存，确保后续请求获取最新数据
      AppContext.setUserFetchCacheId();
    }
    AppContext.setListPageIsEffect(true);
    const {
      ptime,
      rtime,
      rentalLocation,
      age,
      adultSelectNum,
      childSelectNum,
      backPageName,
      openList,
      isPickupStation,
      isDropOffStation,
      locationDatePopType,
      airPortTransferTip,
      showAirPortTransferTip,
      fetchVehicleDetailList,
      isDifferentLocation,
      recommendType,
      subStrategyType,
      pickupCityId,
      isRentCenterPoint,
      orderId,
      pressSearchCallback,
      removeSoldOutAll,
    } = this.props;

    const isFromOsdModifyOrder =
      locationDatePopType === LocationDatePopTypes.fromOsdModifyOrder;
    if (isFromOsdModifyOrder) {
      // 出境修改订单，客户端路由跳转境外列表页
      const baseUrl = Platform.CAR_CROSS_URL.MARKETLIST.OSD;
      const data = {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(ptime).format('YYYYMMDDHHmmss'),
          },
          dropOff: {
            dateTime: dayjs(rtime).format('YYYYMMDDHHmmss'),
          },
        },
        rentalLocation,
        age,
        adultSelectNum,
        childSelectNum,
      };
      const curPageId = AppContext.PageInstance.getPageId();
      const url = `${baseUrl}&data=${encodeURIComponent(
        JSON.stringify(data),
      )}&osdOriginOrderId=${orderId}&from=${curPageId}`;
      xRouter.navigateTo({ url: url });
      if (pressSearchCallback && typeof pressSearchCallback === 'function') {
        setTimeout(() => {
          BbkUtils.ensureFunctionCall(pressSearchCallback);
        }, 500);
      }
      return;
    }

    const isFromIsdRanking =
      [
        LocationDatePopTypes.fromIsdRankingHead,
        LocationDatePopTypes.fromIsdRankingResult,
      ].includes(locationDatePopType) && Utils.isCtripIsd();
    const isFromVendorListPage =
      locationDatePopType === LocationDatePopTypes.fromVendorListPage;
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else if (airPortTransferTip?.isShow) {
      showAirPortTransferTip();
    } else {
      // 埋点先行，否则实验数据丢失
      const recommendEventResult = ListResSelectors.getRecommendEventResult();
      CarLog.LogCode({
        name: '点击_列表页_修改取还车信息弹层_重新搜索',
        info: {
          ispickupStation: isPickupStation,
          isdropoffStation: isDropOffStation,
          recommendEventResult,
          isDifferentLocation,
          recommendType,
          recommendTimeType: subStrategyType,
          pickupCityId,
          locationDatePopType,
        },
      });
      BbkUtils.ensureFunctionCall(this.props.pressSearchCallback);
      setFromPage(AppContext.PageInstance.getPageId());
      if (isFromVendorListPage && fetchVehicleDetailList) {
        // 如果是再货架页点击搜索条件的查询按钮，则需要重置售罄缓存
        removeSoldOutAll();
        fetchVehicleDetailList({
          deleteUniqSign: true,
        });
        this.props.setLocationAndDatePopIsShow({ visible: false });
      } else {
        if (isRentCenterPoint) {
          this.props.fetchList({
            filters: [ApiResCode.ITEM_CODE.RentalStoreEnter],
          });
        } else {
          this.props.fetchList();
        }
      }
      this.hasBackUp = false;

      const crossData = {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(ptime).format('YYYYMMDDHHmmss'),
          },
          dropOff: {
            dateTime: dayjs(rtime).format('YYYYMMDDHHmmss'),
          },
        },
        rentalLocation,
        isShowDropOff: rentalLocation.isShowDropOff,
        age,
        adultSelectNum,
        childSelectNum,
        notDisPatchPreFetch: true,
      };
      CarStorage.saveNoLocal(
        Utils.isCtripIsd()
          ? StorageKey.CAR_CROSS_DATA_ISD
          : StorageKey.CAR_CROSS_DATA,
        JSON.stringify(crossData),
      );

      EventHelper.sendEvent(
        Utils.isCtripIsd()
          ? EventName.changeRentalISDLocation
          : EventName.changeRentalDate,
        crossData,
      );

      // 榜单唤起弹层，点击查询不跳转列表页
      if (!isFromIsdRanking) {
        if (openList) {
          openList();
        } else if (backPageName.indexOf(PageName.Home) > -1) {
          AppContext.PageInstance.push(Channel.getPageId().List.EN, {
            fromurl: PageName.Home,
          });
        }
      } else {
        // 榜单唤起弹层，点击查询埋点
        CarLog.LogCode({
          name: '点击_车型榜单_修改取还车信息_查询按钮',

          buttonFrom: locationDatePopType,
        });
      }
    }
  };

  onPressSearchAfterPoiIntercept = () => {
    const { checkOsdPoi, pickupCityId, dropOffCityId, qConfig, ptime, rtime } =
      this.props;
    try {
      // 点击列表页搜索框的查询按钮时，将列表页的时间同步到首页
      // 因为可能列表页的时间不是用户手动修改的，造成没有发送同步时间事件
      if (AppContext.isListCombineEventSwitch) {
        const nextPtime = dayjs(ptime).format('YYYY-MM-DD HH:mm:ss');
        const nextRtime = dayjs(rtime).format('YYYY-MM-DD HH:mm:ss');
        EventHelper.sendEvent(EventName.changeListRentalDate, {
          pickup: nextPtime,
          dropoff: nextRtime,
          appType: AppContext.CarEnv.appType,
          notDisPatchPreFetch: true,
        });
      }
      if (Utils.isCtripOsd() && qConfig?.poiSearchIntercept) {
        checkOsdPoi({
          params: { pickUpCityId: pickupCityId, returnCityId: dropOffCityId },
          afterPoiInterceptCallback: this.onPressSearch,
          setConfirmModalVisible: this.setConfirmModalVisible,
        });
      } else {
        this.onPressSearch();
      }
    } catch (error) {
      this.onPressSearch();
    }
  };

  onPressAgeSelect = () => {
    if (
      this.props.locationDatePopType === LocationDatePopTypes.fromOsdModifyOrder
    ) {
      CarLog.LogCode({ name: '点击_修改订单弹框_年龄' });
      return;
    }
    CarLog.LogCode({ name: '点击_列表页_修改取还车信息弹层_修改年龄' });
  };

  onIsShowDropOffChange = isShowDropOff => {
    this.props.setLocationInfo({
      isShowDropOff,
      dropOff: this.props.rentalLocation.pickUp,
      notDisPatchPreFetch: true,
    });
    this.logCode([
      {
        pageId: Channel.getPageId().List.ID,
        logInfo: {
          name: '点击_列表页_修改取还车信息弹层_异地还车按钮',
          isShowDropOff,
        },
      },
      this.fromVendorListPageLog(
        '点击_产品详情页_修改取还车信息弹层_异地还车按钮',
      ),
    ]);
  };

  onPressPickUpDate = () => {
    this.logCode([
      {
        pageId: Channel.getPageId().Home.ID,
        logInfo: { name: '点击_首页_首页二屏置顶titlebar_取车时间' },
      },
      {
        pageId: Channel.getPageId().List.ID,
        logInfo: { name: '点击_列表页_列表页二屏置顶titlebar_取车时间' },
      },
      this.fromVendorListPageLog(
        '点击_产品详情页_修改取还车信息弹层_修改取车时间',
      ),
      {
        pageId: Channel.getPageId().Order.ID,
        logInfo: { name: '点击_修改订单弹框_时间组' },
      },
      {
        pageId: Channel.getPageId().OrderCancel.ID,
        logInfo: { name: '点击_修改订单弹框_时间组' },
      },
    ]);
  };

  onPressDropOffDate = () => {
    this.logCode([
      {
        pageId: Channel.getPageId().Home.ID,
        logInfo: { name: '点击_首页_首页二屏置顶titlebar_还车时间' },
      },
      {
        pageId: Channel.getPageId().List.ID,
        logInfo: { name: '点击_列表页_列表页二屏置顶titlebar_还车时间' },
      },
      this.fromVendorListPageLog(
        '点击_产品详情页_修改取还车信息弹层_修改还车时间',
      ),
      {
        pageId: Channel.getPageId().Order.ID,
        logInfo: { name: '点击_修改订单弹框_时间组' },
      },
      {
        pageId: Channel.getPageId().OrderCancel.ID,
        logInfo: { name: '点击_修改订单弹框_时间组' },
      },
    ]);
  };

  onPressRentalTime = () => {
    const { currentPageId } = this.props;

    if (Utils.isCtripIsd()) {
      if (currentPageId === Channel.getPageId().List.ID) {
        CarLog.LogCode({ name: '点击_国内列表页_列表页二屏置顶titlebar_租期' });
      } else {
        CarLog.LogCode({ name: '点击_国内产品详情页_修改取还车信息弹层_租期' });
      }
    } else {
      CarLog.LogCode({ name: '点击_出境列表页_列表页二屏置顶titlebar_租期' });
    }
  };

  onPressAgeTip = () => {
    const { setAgeTipPopIsShow } = this.props;
    if (!setAgeTipPopIsShow) return;
    setAgeTipPopIsShow({ visible: false });
    CarLog.LogCode({ name: '点击_列表页_打开年龄说明弹层' });
  };

  onAgeTipClose = () => {
    const { setAgeTipPopIsShow } = this.props;
    if (!setAgeTipPopIsShow) return;
    setAgeTipPopIsShow({ visible: true });
    CarLog.LogCode({ name: '点击_列表页_关闭年龄说明弹层' });
  };

  onPressNumberSelect = () => {
    CarLog.LogCode({ name: '点击_列表页_搜索框_人数' });
  };

  onNumberConfirm = data => {
    this.props.setAgeAdultAndChildNum(data);
    CarLog.LogCode({ name: '点击_选择出行人数弹层_确定' });
  };

  onNumberCancel = () => {
    CarLog.LogCode({ name: '点击_选择出行人数弹层_取消' });
  };

  getConfirmModalBtns = () => {
    const { checkPoiRes } = this.props;
    return [
      {
        name: checkPoiRes?.confirmSearchBtn,
        onPress: () => {
          this.setConfirmModalVisible(false);
          this.onPressSearch();
          CarLog.LogCode({
            key: LogKey.CLICK_KEY,
            name: '点击_POI搜索_阻断弹窗_继续搜索按钮',
            info: {
              popType: checkPoiRes?.type,
            },
          });
        },
      },
      {
        name: checkPoiRes?.confirmModifyBtn,
        isPrimary: true,
        onPress: () => {
          this.setConfirmModalVisible(false);
          CarLog.LogCode({
            key: LogKey.CLICK_KEY,
            name: '点击_POI搜索_阻断弹窗_修改按钮',
            info: {
              popType: checkPoiRes?.type,
            },
          });
        },
      },
    ];
  };

  render() {
    const { checkPoiRes } = this.props;
    if (this.props.visible) {
      this.handleBackUpData();
    }
    return (
      <>
        <NewSearchPanelModal
          onCancel={this.onCancel}
          onPressCity={this.handlePressCityLog}
          onPressLocation={this.handlePressLocationLog}
          onPressPickUpDate={this.onPressPickUpDate}
          onPressDropOffDate={this.onPressDropOffDate}
          onPressRentalTime={this.onPressRentalTime}
          onAgeChange={this.onAgeChange}
          onAgeCancel={this.onAgeCancel}
          onTimeChange={this.onTimeChange}
          onPressSearch={this.onPressSearchAfterPoiIntercept}
          onPressAgeSelect={this.onPressAgeSelect}
          onIsShowDropOffChange={this.onIsShowDropOffChange}
          onPressAgeTip={this.onPressAgeTip}
          onAgeTipClose={this.onAgeTipClose}
          onPressNumberSelect={this.onPressNumberSelect}
          onNumberConfirm={this.onNumberConfirm}
          onNumberCancel={this.onNumberCancel}
          handleAreaPress={this.onSelectedLocation}
          {...this.props}
        />
        <ConfirmModal
          visible={this.state.confirmModalVisible}
          title={checkPoiRes?.confirmTitle}
          contentText={checkPoiRes?.confirmContent}
          exposeTestID={CarLog.LogExposure({
            name: '曝光_POI搜索_阻断弹窗',
            info: {
              popType: checkPoiRes?.type,
            },
          })}
          btns={this.getConfirmModalBtns()}
        />
      </>
    );
  }
}

export default SearchPanelModal;
