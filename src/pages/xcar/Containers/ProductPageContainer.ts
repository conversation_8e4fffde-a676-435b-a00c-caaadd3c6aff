import connect from '../WithRedux';
import {
  getProductMorePackageTraceData,
  getCurPackageIsEasyLife,
  getCurCtripInsurance,
  getImageLogInfo,
  getIsKlb,
  getIsOpenInsuranceSellingModal,
  getExtraProductTraceData,
  getIpollLogData,
} from '../State/Product/Mappers';

import Product from '../Pages/Product/Index';
import { AppContext, Utils, Channel, CarABTesting } from '../Util/Index';
import {
  getProductCarHeaderData,
  getPickUpTime,
  getProductDropOffTime,
  getIsShowDropOff,
  getProductRentalLocationInfo,
  getIsDifferentLocation,
  getPickUpLocationName,
  getDropOffLocationName,
  getExpandLocationAndDate,
} from '../State/LocationAndDate/Selectors';
import { getCountryId } from '../State/CountryInfo/Selectors';
import {
  setLocationInfo,
  setProductDropOffDate,
  restPrevDataInfo,
  restPrevLocationInfo,
} from '../State/LocationAndDate/Actions';
import { fetchListWarningInfo } from '../State/Common/Actions';
import { isDebugMode } from '../State/Debug/Selectors';

import {
  getProductState,
  getEasyLifePopVisible,
  getCurInsPackageId,
  getCurPackageId,
  getDepositRateDescriptionModalVisible,
  getIsShowTravelLimit,
  getInsuranceCompareModalVisible,
  getInsuranceSellingModalVisible,
} from '../State/Product/Selectors';
import {
  queryProduct,
  selectPackage,
  changeSelectInsurance,
  setSelectedIdType,
  reset,
  queryPriceInfo,
  setEasyLifePopVisible,
  showPriceConfirm,
  validateIsDownGrade,
  setDepositRateDescriptionModalVisible,
  setInsuranceCompareModalVisible,
  setInsuranceSellingModalVisible,
  queryProductCallBack,
  queryEquipmentInfo,
} from '../State/Product/Actions';
import { onAuthentication, initSesameAuthState } from '../State/Sesame/Actions';
import {
  getProductWaringInfo,
  getFetchWarningInfoLoading,
} from '../State/Common/Selectors';
import { getAuthenStatusTicket } from '../State/Sesame/Selectors';
import { fetchApiGuide } from '../State/Guide/Actions';
import { getIsSupportSesame, getSesameBarTexts } from '../State/Sesame/Mappers';
import { SesamePageLocation } from '../State/Sesame/Types';
import {
  addSaleOutList,
  setLicenseModalData,
  setOsdUserBrowsingHistory,
} from '../State/List/Actions';
import { clear as clearDriverList } from '../State/DriverList/Actions';

import {
  getProductReq,
  getPriceChange,
  getPriceUuid,
  getPickupStoreInfo,
  getDownGradeInfo,
  getNeedDownGrade,
  getProductPriceChangeInfo,
  getEmptyGuideInfo,
  getPriceTimerRes,
} from '../Global/Cache/ProductSelectors';
import {
  getRebookParams,
  getRebookParamsOsd,
} from '../State/ModifyOrder/CommonSelector';
import { getImAddress } from '../Global/IM/IMCommon';
import {
  setListStatusData,
  listStatusKeyList,
  setFromPage,
} from '../Global/Cache/ListReqAndResData';
import {
  isPickPointFn,
  getRestAssuredTag,
  getFuelDesc,
} from '../State/Product/BbkMapper';
import { getRecommendType } from '../State/List/Selectors';
import {
  changePayMode,
  queryOsdModifyOrderNote,
} from '../State/Booking/Actions';
import { getIsVehicle2 } from '../Global/Cache/ListResSelectors';

const mapStateToProps = state => {
  const pickupStoreInfo = getPickupStoreInfo();
  const curInsPackageId = getCurInsPackageId(state);
  const curPackageId = getCurPackageId(state);
  const priceChangeInfo = getProductPriceChangeInfo();
  return {
    isDebugMode: isDebugMode(state),
    ptime: getPickUpTime(state),
    rtime: getProductDropOffTime(state),
    pickUpLocationName: getPickUpLocationName(state),
    dropOffLocationName: getDropOffLocationName(state),
    authenStatusTicket: getAuthenStatusTicket(state),
    ...getProductState(state),
    isSupportSesame: getIsSupportSesame(),
    sesameBarTexts: getSesameBarTexts(SesamePageLocation.detailBar),
    easyLifePopVisible: getEasyLifePopVisible(state),
    isCreditRent: CarABTesting.isCreditRent(),
    productReq: getProductReq(),
    productRentalLocationInfo: getProductRentalLocationInfo(state),
    curCtripInsurance: getCurCtripInsurance(getCurInsPackageId(state)),
    headerData: getProductCarHeaderData(state),
    currency: AppContext.LanguageInfo.currency,
    bizType: Utils.getSideToolBizType(),
    toolBoxCustomerJumpUrl: getImAddress({
      pageId: Channel.getPageId().Product.ID,
      isPreSale: 1,
    }),
    isShowDropOff: getIsShowDropOff(state),
    isDifferentLocation: getIsDifferentLocation(state),
    countryId: getCountryId(state),
    isEasyLife: getCurPackageIsEasyLife(),
    curInsPackageId,
    tripH5LocationDate: null,
    expandLocationAndDate: getExpandLocationAndDate(state),
    productWaringInfo: getProductWaringInfo(state),
    priceChange: getPriceChange(),
    priceUuid: getPriceUuid(),
    imageLogInfo: getImageLogInfo(state),
    pickupStoreInfo,
    isPickPoint: isPickPointFn(pickupStoreInfo, true),
    isRebook: !!getRebookParams(state)?.vehicleId,
    restAssuredTag: getRestAssuredTag(curInsPackageId, curPackageId),
    priceChangeInfo,
    needDownGrade: getNeedDownGrade(),
    downGradeInfo: getDownGradeInfo(),
    fetchWarningInfoLoading: getFetchWarningInfoLoading(state),
    depositRateDescriptionModalVisible:
      getDepositRateDescriptionModalVisible(state),
    isShowTravelLimit: getIsShowTravelLimit(),
    emptyGuideInfo: getEmptyGuideInfo(),
    isKlb: getIsKlb(state),
    insuranceCompareModalVisible: getInsuranceCompareModalVisible(state),
    insuranceSellingModalVisible: getInsuranceSellingModalVisible(state),
    isOpenInsuranceSellingModal: getIsOpenInsuranceSellingModal(state),
    recommendType: getRecommendType(state),
    morePackageLogBaseInfo: getProductMorePackageTraceData(state),
    extraProductLogInfo: getExtraProductTraceData(state),
    isRefactor: getIsVehicle2(),
    priceTimerRes: getPriceTimerRes(),
    isRebookOsd: !!getRebookParamsOsd(state)?.ctripOrderId,
    ipollLogData: getIpollLogData(state),
    fuelModalData: getFuelDesc(state),
  };
};

const mapDispatchToProps = (dispatch, props) => ({
  setLocationInfo: data => {
    dispatch(setLocationInfo(data));
  },
  queryProduct: data => {
    dispatch(queryProduct(data));
  },
  selectPackage: data => {
    dispatch(selectPackage(data));
  },
  changeSelectInsurance: data => {
    dispatch(changeSelectInsurance(data));
  },
  setSelectedIdType: data => {
    dispatch(setSelectedIdType(data));
  },
  onAuthentication: data => {
    dispatch(onAuthentication(data));
  },
  fetchApiGuide: data => {
    dispatch(fetchApiGuide(data));
  },
  setEasyLifePopVisible: data => {
    dispatch(setEasyLifePopVisible(data));
  },
  setProductDropOffDate: data => {
    dispatch(setProductDropOffDate(data));
  },
  reset: () => {
    dispatch(reset());
    dispatch(clearDriverList());
  },
  initSesameAuthState: data => {
    dispatch(initSesameAuthState(data));
  },
  queryPriceInfo: data => {
    dispatch(queryPriceInfo(data));
  },
  addSaleOutList: data => {
    dispatch(addSaleOutList(data));
  },
  showPriceConfirmAction: data => {
    dispatch(showPriceConfirm(data));
  },
  refreshList: () => {
    // 添加在详情页登录的标记
    setListStatusData(listStatusKeyList.loginFromProduct, true);
    setFromPage(AppContext.PageInstance.getPageId());
    // 清空列表页缓存
    AppContext.setUserFetchCacheId({
      actionType: 'productRefreshList',
    });
  },
  onErrorRefreshList: () => {
    setListStatusData(listStatusKeyList.refreshFromOSDProductPage, true);
    setFromPage(AppContext.PageInstance.getPageId());
    // 清空列表页缓存
    AppContext.setUserFetchCacheId({
      actionType: 'refreshFromOSDProductPage',
    });
  },
  onErrorShowLocationAndDatePop: () => {
    const { reference } = props || {};
    setListStatusData(listStatusKeyList.downGradeFromProduct, {
      vehicleId: reference?.vehicleCode,
      vendorId: reference?.vendorCode,
    });
    setFromPage(AppContext.PageInstance.getPageId());
    // 清空列表页缓存
    AppContext.setUserFetchCacheId({
      actionType: 'downGradeFromProduct',
    });
  },
  getListWarningInfo: data => dispatch(fetchListWarningInfo(data)),
  validateIsDownGrade: data => dispatch(validateIsDownGrade(data)),
  closeDepositRateDescriptionModal: () =>
    dispatch(setDepositRateDescriptionModalVisible(false)),
  setLicenseModalData: data => dispatch(setLicenseModalData(data)),
  closeInsuranceCompareModal: () =>
    dispatch(setInsuranceCompareModalVisible(false)),
  openInsuranceCompareModal: () =>
    dispatch(setInsuranceCompareModalVisible(true)),
  openInsuranceSellingModal: () =>
    dispatch(setInsuranceSellingModalVisible(true)),
  closeInsuranceSellingModal: () =>
    dispatch(setInsuranceSellingModalVisible(false)),
  restPrevDataInfo: data => dispatch(restPrevDataInfo(data)),
  restPrevLocationInfo: data => dispatch(restPrevLocationInfo(data)),
  queryProductCallBack: data => dispatch(queryProductCallBack(data)),
  changePayMode: data => dispatch(changePayMode(data)),
  queryEquipmentInfo: data =>
    dispatch(queryEquipmentInfo({ productReqParams: data })),
  queryOsdModifyOrderNote: () => dispatch(queryOsdModifyOrderNote()),
  setOsdUserBrowsingHistory: data => dispatch(setOsdUserBrowsingHistory(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(Product);
