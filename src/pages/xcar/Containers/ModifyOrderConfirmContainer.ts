import connect from '../WithRedux';
import ModifyOrderConfirm from '../Pages/ModifyOrderConfirm/Index';
import {
  getPaymentParams,
  getCreateInsModalVisible,
  getInsFailedModalVisible,
  getSelectedInsuranceId,
  getModifyOrderModalProps,
  getFeeInfo,
  getIsSubmitting,
  getIsLoading,
  getPayTimeOutModalVisible,
  getPageInfo,
} from '../State/ModifyOrderConfirm/Selector';
import { getOrderId, getIsCancelOrder } from '../State/OrderDetail/Selectors';
import {
  resetConfirmData,
  setCreateInsModalVisible,
  setInsFailedModalVisible,
  onPressSubmit,
  modifyOrderToPay,
  setPayTimeOutModalVisible,
} from '../State/ModifyOrderConfirm/Actions';
import { createModifyOrder } from '../State/ModifyOrder/Actions';
import { setCouponPreValidationModalVisible } from '../State/Coupon/Actions';

const mapStateToProps = state => ({
  payParams: getPaymentParams(state),
  orderId: getOrderId(state),
  createInsModalVisible: getCreateInsModalVisible(state),
  insFailedModalVisible: getInsFailedModalVisible(state),
  selectedInsuranceId: getSelectedInsuranceId(state),
  modifyOrderModalProps: getModifyOrderModalProps(state),
  feeInfo: getFeeInfo(state),
  isSubmitting: getIsSubmitting(state),
  isLoading: getIsLoading(state),
  payTimeOutModalVisible: getPayTimeOutModalVisible(state),
  pageInfo: getPageInfo(state),
  isCancelOrder: getIsCancelOrder(state),
});

const mapDispatchToProps = dispatch => ({
  createModifyOrder: orderId => dispatch(createModifyOrder(orderId)),
  resetConfirmData: () => dispatch(resetConfirmData()),
  setCreateInsModalVisible: (visible: boolean) =>
    dispatch(setCreateInsModalVisible(visible)),
  setInsFailedModalVisible: (visible: boolean) =>
    dispatch(setInsFailedModalVisible(visible)),
  onPressSubmit: (data?: any) => dispatch(onPressSubmit(data)),
  modifyOrderToPay: orderId => dispatch(modifyOrderToPay(orderId)),
  setPayTimeOutModalVisible: (visible: boolean) =>
    dispatch(setPayTimeOutModalVisible(visible)),
  setCouponPreValidationModalVisible: (visible, content) =>
    dispatch(setCouponPreValidationModalVisible(visible, content)),
});

function renderCheckInfo() {
  return { top: 0.05, bottom: 0.1 };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  renderCheckInfo,
)(ModifyOrderConfirm);
