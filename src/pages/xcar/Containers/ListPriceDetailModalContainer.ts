import { connect } from 'react-redux';
import {
  listGoToBooking,
  setPriceSummaryModal,
  setSecretBoxModalData,
  queryPackageComparison,
  goToBookingEasyLife2024,
  setUserBrowsingHistory,
} from '../State/List/Actions';
import {
  getPriceSummaryModalVisible,
  getSaleOutList,
  getVehicleSoldOutList,
} from '../State/List/Selectors';
import {
  packagePriceSummaryModalData,
  getRequestQuery,
} from '../State/List/Mappers';
import PriceDetailModal from '../ComponentBusiness/PriceDetailModal/Index';
import { CarLog, AppContext, Channel } from '../Util/Index';

import { getVendorListFirstScreenParam } from '../State/List/VehicleListMappers';
import Enums from '../ComponentBusiness/Common/src/Enums';
import { queryVehicleDetailList } from '../State/VendorList/Actions';

const mapStateToProps = state => ({
  packageComparisonData: state.List?.packageComparisonData,
  visible: getPriceSummaryModalVisible(state),
  requestQuery: getRequestQuery(state), // 日历价查询请求参数
  soldOutList: getSaleOutList(state),
  vehicleSoldOutList: getVehicleSoldOutList(state),
  ...packagePriceSummaryModalData(state),
});

const mapDispatchToProps = dispatch => ({
  onClose: () => {
    dispatch(setPriceSummaryModal(false));
    CarLog.LogCode({ name: '点击_列表页_车型总价提示弹层_关闭' });
  },
  onContinue: (
    vendorListPageParam,
    vehicleIndex,
    priceListLen,
    vehicleList,
    type?,
    section?,
  ) => {
    dispatch(setPriceSummaryModal(false));
    if (type === Enums.TotalPriceModalType.SecretBox) {
      dispatch(
        queryVehicleDetailList({
          reqParam: vendorListPageParam,
        }),
      );
      dispatch(
        setSecretBoxModalData({
          visible: true,
          data: section,
        }),
      );
    } else {
      AppContext.PageInstance.push(Channel.getPageId().VendorList.EN, {
        pageParam: vendorListPageParam,
        vehicleIndex,
        vendorListFirstScreenParam: getVendorListFirstScreenParam(
          vendorListPageParam?.ctripVehicleId,
        ),
        priceListLen,
      });
      CarLog.LogCode({ name: '点击_列表页_车型总价提示弹层_下一步' });

      // 添加用户浏览记录
      setTimeout(() => {
        dispatch(
          setUserBrowsingHistory({
            vehicleCode: section?.vehicleCode,
            license: section?.productRef?.license,
          }),
        );
      }, 300);
    }
  },
  onContinueSecretBoxBook: (uniqueCode, vehicleCode?, vendorPriceInfo?) => {
    const data = {
      uniqueCode,
      vehicleCode,
      vendorPriceInfo,
      from: Enums.SecretBoxToBookType.SecretBoxEntry,
    };
    dispatch(setPriceSummaryModal(false));
    dispatch(listGoToBooking(data));
  },
  goToBookingEasyLife2024: (data, section) => {
    dispatch(goToBookingEasyLife2024(data));

    // 添加用户浏览记录
    dispatch(
      setUserBrowsingHistory({
        vehicleCode: section?.vehicleCode,
        license: section?.productRef?.license,
      }),
    );
  },
  onRetryPackageComparison: data => {
    dispatch(queryPackageComparison(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(PriceDetailModal);
