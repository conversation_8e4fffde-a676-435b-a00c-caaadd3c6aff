import connect from '../WithRedux';
import ModifyOrder from '../Pages/ModifyOrder/Index';
import {
  setInitalData,
  createModifyOrder,
  reBooking,
  clear,
  setModifyOrderWarnModalVisible,
} from '../State/ModifyOrder/Actions';
import {
  getModifyCancelRule,
  getOrderModalsVisible,
  getVehicleInfo,
  getModifyInfoDto,
  getOrderBaseInfo,
  getIsCancelOrder,
} from '../State/OrderDetail/Selectors';
import {
  getModifyStatus,
  getIsLoading,
  getIsFail,
  getIsMaskLoading,
  getModifyOrderSuccess,
  getModifyOrderWarnModalVisible,
} from '../State/ModifyOrder/Selector';
import { setOrderModalsVisible } from '../State/OrderDetail/Actions';
import { getDriversMap } from '../State/Booking/Selectors';

const mapStateToProps = state => ({
  vehicleInfo: getVehicleInfo(state),
  modifyCancelRule: getModifyCancelRule(state),
  modifyStatus: getModifyStatus(state),
  isLoading: getIsLoading(state),
  isFail: getIsFail(state),
  isMaskLoading: getIsMaskLoading(state),
  modalsVisible: getOrderModalsVisible(state),
  driversMap: getDriversMap(state),
  modifyOrderSuccess: getModifyOrderSuccess(state),
  modifyOrderWarnModalVisible: getModifyOrderWarnModalVisible(state),
  disablePickDropInfo: getModifyInfoDto(state)?.modificationType === '1',
  orderCtripStatus: getOrderBaseInfo(state)?.orderCtripStatus,
  isCancelOrder: getIsCancelOrder(state),
});

const mapDispatchToProps = dispatch => ({
  initData: (orderId: number) => {
    dispatch(setInitalData(orderId));
  },
  reBooking: (isInfoFromOrder?: boolean) => {
    dispatch(reBooking(isInfoFromOrder));
  },
  createModifyOrder: (orderId: number) => {
    dispatch(createModifyOrder(orderId));
  },
  setOrderModalsVisible: (data: any) => {
    dispatch(setOrderModalsVisible(data));
  },
  clear: () => dispatch(clear()),
  setModifyOrderWarnModalVisible,
});

export default connect(mapStateToProps, mapDispatchToProps)(ModifyOrder);
