import { connect } from 'react-redux';
import Vendor from '../Pages/List/Components/Vendor';
import {
  getFormatLocationAndDate,
  getExpandLocationAndDate,
  getIsDifferentLocation,
  getDropOffLocationCode,
  getPickUpLocationCode,
  getFormatPickUpTime,
  getFormatDropOffTime,
  getPickUpTime,
  getDropOffTime,
} from '../State/LocationAndDate/Selectors';
import { getAge, getPopulation } from '../State/DriverAgeAndNumber/Selectors';
import { setDateInfo, setLocationInfo } from '../State/LocationAndDate/Actions';
import {
  getPriceSoldOutTextMap,
  getRecommendType,
  getRentCenter,
  getSelectedFilters,
  getRecommendPickReturnInfo,
  getOsdUserBrowsed,
} from '../State/List/Selectors';
import {
  getLicenseInfo,
  getStoreList,
  getEasyLifeTagList,
  getPHub,
  getRHub,
} from '../Global/Cache/ListResSelectors';
import {
  setDriverLicensePopData,
  setTimeOutPopData,
  setVendorListModalData,
  setTotalPriceModalData,
  setEasyLifePopVisible,
  setPriceSummaryModal,
  setOsdUserBrowsed,
  setOsdUserBrowsingHistory,
} from '../State/List/Actions';
import { queryProduct } from '../State/Product/Actions';

const mapStateToProps = state => ({
  locationAndDate: getFormatLocationAndDate(state),
  expandLocationAndDate: getExpandLocationAndDate(state),
  age: getAge(state),
  licenseInfo: getLicenseInfo(),
  rentCenter: getRentCenter(state),
  storeList: getStoreList(),
  easyLifeTagList: getEasyLifeTagList(),
  selectedFilters: getSelectedFilters(state),
  pHub: getPHub(),
  rHub: getRHub(),
  priceSoldOutTextMap: getPriceSoldOutTextMap(state),
  isDifferentLocation: getIsDifferentLocation(state),
  recommendType: getRecommendType(state),
  pickupDateTime: getFormatPickUpTime(state),
  dropOffDateTime: getFormatDropOffTime(state),
  pickupLocationCode: getPickUpLocationCode(state),
  dropOffLocationCode: getDropOffLocationCode(state),
  population: getPopulation(state),
  recommendPickReturnInfo: getRecommendPickReturnInfo(state),
  pTime: getPickUpTime(state),
  rTime: getDropOffTime(state),
  osdUserBrowsed: getOsdUserBrowsed(state),
});

const mapDispatchToProps = dispatch => ({
  setDriverLicensePopData: data => {
    dispatch(setDriverLicensePopData(data));
  },
  setTimeOutPopData: data => {
    dispatch(setTimeOutPopData(data));
  },
  setDateInfo: data => {
    dispatch(setDateInfo(data));
  },
  setLocationInfo: data => {
    dispatch(setLocationInfo(data));
  },
  setVendorListModalData: data => dispatch(setVendorListModalData(data)),
  setTotalPriceModalData: data => dispatch(setTotalPriceModalData(data)),
  setPriceSummaryModal: data =>
    dispatch(setPriceSummaryModal(data.visible, data.data)),
  setEasyLifePopVisible: data => dispatch(setEasyLifePopVisible(data)),
  queryProduct: data => {
    dispatch(queryProduct(data));
  },
  setOsdUserBrowsed: data => {
    dispatch(setOsdUserBrowsed(data));
  },
  setOsdUserBrowsingHistory: data => {
    dispatch(setOsdUserBrowsingHistory(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(Vendor);
