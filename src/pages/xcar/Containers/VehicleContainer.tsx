import { connect } from 'react-redux';
import { Vehicle } from '../Pages/List/Components/OSD/Vehicle';
import { getAge, getPopulation } from '../State/DriverAgeAndNumber/Selectors';
import {
  getRentCenter,
  getSaleOutList,
  getSelectedFilters,
  getProgressIsFinish,
  getRecommendType,
  getSubStrategyType,
  getRecommendPickReturnInfo,
  getOsdUserBrowsed,
  getVehicleModalIpollDataOSD,
  getIpollLogData,
  getOsdUserBrowsingHistories,
} from '../State/List/Selectors';
import {
  getLicenseInfo,
  getStoreList,
  getVehicleList,
  getRecommendProductsList,
  getBaseResData,
} from '../Global/Cache/ListResSelectors';
import {
  setTimeOutPopData,
  setTipPopData,
  setTotalPriceModalData,
  fetchListRecommendCallback,
  setRecommendType,
} from '../State/List/Actions';
import {
  getDropOffLocationCode,
  getFormatDropOffTime,
  getFormatPickUpTime,
  getIsDifferentLocation,
  getPickUpLocationCode,
} from '../State/LocationAndDate/Selectors';

const mapStateToProps = state => ({
  age: getAge(state),
  licenseInfo: getLicenseInfo(),
  rentCenter: getRentCenter(state),
  storeList: getStoreList(),
  vehicleList: getVehicleList(),
  soldOutList: getSaleOutList(state),
  selectedFilters: getSelectedFilters(state),
  progressIsFinish: getProgressIsFinish(state),
  isDifferentLocation: getIsDifferentLocation(state),
  recommendType: getRecommendType(state),
  subStrategyType: getSubStrategyType(state),
  pickupDateTime: getFormatPickUpTime(state),
  dropOffDateTime: getFormatDropOffTime(state),
  pickupLocationCode: getPickUpLocationCode(state),
  dropOffLocationCode: getDropOffLocationCode(state),
  population: getPopulation(state),
  recommendPickReturnInfo: getRecommendPickReturnInfo(state),
  recommendProductsList: getRecommendProductsList(),
  listData: getBaseResData(),
  osdUserBrowsed: getOsdUserBrowsed(state),
  osdVehicleListIpollData: getVehicleModalIpollDataOSD(state),
  ipollLogData: getIpollLogData(state),
  osdUserBrowsingHistories: getOsdUserBrowsingHistories(state),
});

const mapDispatchToProps = dispatch => ({
  setTimeOutPopData: data => {
    dispatch(setTimeOutPopData(data));
  },
  setTipPopData: data => dispatch(setTipPopData(data)),
  setTotalPriceModalData: data => dispatch(setTotalPriceModalData(data)),
  fetchListRecommendCallback: data => {
    dispatch(fetchListRecommendCallback(data));
  },
  setRecommendType: (data: any) => {
    dispatch(setRecommendType(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(Vehicle);
