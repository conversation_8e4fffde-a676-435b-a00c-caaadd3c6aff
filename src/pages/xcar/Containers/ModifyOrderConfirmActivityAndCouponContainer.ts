import { connect } from 'react-redux';
import ActivityAndCoupon from '../Pages/ModifyOrderConfirm/Components/ActivityAndCoupon';
import {
  getActivityDetail,
  getCouponList,
  getExplain,
  getEnablePressCoupon,
} from '../State/ModifyOrderConfirm/Selector';

const mapStateToProps = state => ({
  activityDetails: getActivityDetail(state) || {},
  couponList: getCouponList(state) || {},
  explain: getExplain(state),
  enablePressCoupon: getEnablePressCoupon(state),
});

export default connect(mapStateToProps)(ActivityAndCoupon);
