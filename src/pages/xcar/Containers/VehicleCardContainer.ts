import { connect } from 'react-redux';
import { VehicleCard } from '../Pages/List/Components/VehicleCard';
import { getAge } from '../State/DriverAgeAndNumber/Selectors';
import {
  getRentCenter,
  getSaleOutList,
  getSelectedFilters,
  getProgressIsFinish,
  getVehicleSoldOutList,
  getRecUniqsign,
  getUserBrowsingHistories,
} from '../State/List/Selectors';
import {
  getLicenseInfo,
  isDiffLocation,
  getIsNewSearchNoResult,
  getRecommendEventResult,
  getIsFilteredRecommend,
} from '../Global/Cache/ListResSelectors';
import { setDateInfo, setLocationInfo } from '../State/LocationAndDate/Actions';
import {
  setTimeOutPopData,
  setTipPopData,
  setTotalPriceModalData,
  setPriceSummaryModal,
  setSecretBoxModalData,
  listGoToBooking,
  goToBookingEasyLife2024,
  queryPackageComparison,
  setUserBrowsingHistory,
} from '../State/List/Actions';
import {
  getPickUpTime,
  getRentalLocation,
  getPickUpCityId,
  getIsDifferentLocation,
  getDropOffCityId,
} from '../State/LocationAndDate/Selectors';
import { CarABTesting } from '../Util/Index';
import {
  queryVehicleDetailList,
  setFristScreenParam,
} from '../State/VendorList/Actions';

const mapStateToProps = state => ({
  age: getAge(state),
  cityId: getPickUpCityId(state),
  rentalLocation: getRentalLocation(state),
  licenseInfo: getLicenseInfo(),
  rentCenter: getRentCenter(state),
  soldOutList: getSaleOutList(state),
  vehicleSoldOutList: getVehicleSoldOutList(state),
  selectedFilters: getSelectedFilters(state),
  progressIsFinish: getProgressIsFinish(state),
  isDiffLocation: isDiffLocation(),
  pTime: getPickUpTime(state),
  abVersion: CarABTesting.packageAbversion(),
  isNewSearchNoResult: getIsNewSearchNoResult(),
  recommendEventResult: getRecommendEventResult(),
  pickupCityId: getPickUpCityId(state),
  dropOffCityId: getDropOffCityId(state),
  isDifferentLocation: getIsDifferentLocation(state),
  isFilteredRecommend: getIsFilteredRecommend(),
  recUniqsign: getRecUniqsign(state),
  userBrowsingHistories: getUserBrowsingHistories(state),
});

const mapDispatchToProps = dispatch => ({
  setDateInfo: data => {
    dispatch(setDateInfo(data));
  },
  setLocationInfo: data => {
    dispatch(setLocationInfo(data));
  },
  setTimeOutPopData: data => {
    dispatch(setTimeOutPopData(data));
  },
  setTipPopData: data => dispatch(setTipPopData(data)),
  setTotalPriceModalData: data => dispatch(setTotalPriceModalData(data)),
  setPriceSummaryModal: data =>
    dispatch(setPriceSummaryModal(data.visible, data.data)),
  setSecretBoxModalData: data => dispatch(setSecretBoxModalData(data)),
  listGoToBooking: data => dispatch(listGoToBooking(data)),
  queryVehicleDetailList: data => dispatch(queryVehicleDetailList(data)),
  goToBookingEasyLife2024: data => dispatch(goToBookingEasyLife2024(data)),
  queryPackageComparison: data => dispatch(queryPackageComparison(data)),
  setUserBrowsingHistory: (data: {
    activeGroupId: string;
    vehicleCode: string;
    license: string;
  }) => {
    setTimeout(() => {
      dispatch(setUserBrowsingHistory(data));
    }, 300);
  },
  setVendorListFirstScreenParam: data => dispatch(setFristScreenParam(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(VehicleCard);
