import connect from '../WithRedux';
import OrderDetail from '../Pages/OrderDetail/Index';
import {
  fetchOrder2,
  showInsDetailModal,
  setModifyFlightNoModalVisible,
  setIsdOrderChangeModalVisible,
  showFeeDeduction,
  setLabelsModalVisible,
  setCarDetailModalVisible,
  setEasyLifeTagModalVisible,
  fetchQueryCancelFee,
  setFetchDone,
  reset,
  setPhoneModalVisible,
  setPersonPhoneModalVisible,
  goIsdInsurancePayment,
  setOrderModalsVisible,
  isShowRenewStatusByStorage,
  saveRenewalOrderStatus,
  queryOrderStatus,
  payCountDownTimeOut,
  setStorageCardsTitle,
  setPriceDetailModalVisible,
  setOrderDetailConfirmModalVisible,
  ctripContinuePay,
  setTravelLimitSelectedResult,
  setDepositDetailModalVisible,
  setLimitRulePopVisible,
  createPreFetch,
  setFlightDelayRulesModalVisible,
  queryOsdModifyOrderNote,
  setLocationAndDatePopIsShow,
} from '../State/OrderDetail/Actions';
import {
  queryQuestionnaire,
  saveQuestionnaire,
  setVocModalVisible,
} from '../State/Voc/Actions';
import {
  queryServiceProgress,
  urgeServiceProgress,
} from '../State/Service/Actions';
import { isDebugMode } from '../State/Debug/Selectors';
import { getAuthenStatusTicket } from '../State/Sesame/Selectors';

import {
  selectHasUserAuthInfo,
  selectIdCardResult,
  selectDriverResult,
  selectLisenceAuthStatus,
  selectOrderBaseInfo,
  selectHasUserInfoItem,
} from '../State/OnlineAuth/Selectors';

import {
  getIsdChangeOrderModalVisible,
  getFeeDeductionVisible,
  getModifyFlightNoModalVisible,
  BbkInsuranceDetailProps,
  getFetchDone,
  getPhoneModalVisible,
  getPersonPhoneModalVisible,
  getPhoneModalType,
  getOrderId,
  getRealFirstLoadSucTime,
  getCarRentalMustRead,
  getOrderModalsVisible,
  getRestAssuredTag,
  getLabelsModalVisible,
  getCarDetailModalVisible,
  getEasyLifeTagModalVisible,
  getCarLabelsInfo,
  getSafeRent,
  getEasyLifeTags,
  getQueryOrderApiStatus,
  getIsdInvoice,
  getCustomerInfo,
  getCancelRuleInfo,
  getOrderBaseInfo,
  getExtendedInfo,
  getIpollInfo,
  getOrderPriceInfo,
  getVendorInfo,
  getAppResponseMap,
  getFreezeDepositExplain,
  getVehicleInfo,
  getPriceDetailModalVisible,
  getEhiModifyOrderModalVisible,
  getRefundPenaltyInfo,
  getStorageCardsTitle,
  getNextStorageCardsTitle,
  getStoreAttendant,
  getOrderDetailConfirmModalVisible,
  getIsHideCancelButton,
  getNewOrderInsAndXRes,
  getOperationButtons,
  getDepositPaymentModalAutoShow,
  getOrderEhiFreeDepositVisible,
  getQueryOrderAllDataSuccess,
  getFullSearchNum,
  getAdvanceReturnFeeInfo,
  getAdvanceReturnRecord,
  getAdvanceReturnFeeInfoVendor,
  getFeeDetailData,
  getOrderDetailResponse,
  getIsShowTravelLimit,
  getCrossPolicy,
  getIsNewCancelRule,
  getIsSelfService,
  getDepositDetailModalVisible,
  getLimitRulePopVisible,
  getOrderDataByPhone,
  getAdditionalDriverDesc,
  getIsEasyLife2024,
  getClaimProcessModalData,
  getVehicleUseNotesModalData,
  getIsShowFulfillCard,
  getFulfillmentData,
  getcarAssistantSummaryV2,
  getCountryId,
  getVendorId,
  getIsOsdShowBottomButtons,
  getLocationDatePopVisible,
  getIsNewOsdModifyOrder,
  getOsdModifyOrderNote,
  getIsOsdModifyNewOrder,
  getPickUpInDoor,
  getOrderTraceData,
  getOrderDetailIsISDShelves2B,
} from '../State/OrderDetail/Selectors';
import { selectSupportInfo } from '../State/OnlineAuth/CommonSelectors';
import { getQuestionnaires, getVocModalVisible } from '../State/Voc/Selectors';
import { AppContext, Utils } from '../Util/Index';
import {
  getOrderWaringInfo,
  getFetchWarningInfoLoading,
  getQConfig,
  getOrderPhoneNumberList,
  getPhoneSurveyShowCount,
} from '../State/Common/Selectors';
import {
  getServiceProgressList,
  getUrgeServiceIds,
  getServiceIds,
} from '../State/Service/Selectors';

import {
  getpolicyList,
  getRenewTipLogData,
  mapIsuranceBox,
  mapIsuranceBoxOsd,
} from '../State/OrderDetail/Mappers';
import {
  fetchListWarningInfo,
  queryOrderNumber,
  setPhoneSurveyShowCount,
  setStoreSurveyCommit,
} from '../State/Common/Actions';
import { queryCommentSummary } from '../State/ProductConfirm/Actions';
import handleOrderDetailGoToIns from '../State/OrderDetail/InsHandle';
import { EventName } from '../Constants/Index';
import {
  setAdvanceApplySign,
  clearCache,
} from '../State/AdvanceReturn/Actions';
import { ISelectedItem } from '../ComponentBusiness/TravelLimit/src/Types';

const mapStateToProps = (state, props) => ({
  config: getQConfig(state),
  driverInfo: getCustomerInfo(state),
  cancelRuleInfo: getCancelRuleInfo(state),
  orderBaseInfo: getOrderBaseInfo(state),
  extendedInfo: getExtendedInfo(state),
  ipollInfo: getIpollInfo(state),
  orderPriceInfo: getOrderPriceInfo(state),
  vendorInfo: getVendorInfo(state),
  appResponseMap: getAppResponseMap(state),
  orderId:
    props.orderID ||
    props.orderId ||
    AppContext.UrlQuery?.orderId ||
    getOrderId(state),
  orderStatus: selectOrderBaseInfo(state).orderStatus,
  orderStatusCtrip: selectOrderBaseInfo(state).orderStatusCtrip,
  orderStatusDesc: selectOrderBaseInfo(state).orderStatusDesc,
  lisenceAuthStatus: selectLisenceAuthStatus(state),
  hasUserAuthInfo: selectHasUserAuthInfo(state),
  hasUserInfoItem: selectHasUserInfoItem(state),
  idCardResult: selectIdCardResult(state),
  driverResult: selectDriverResult(state),
  isDebugMode: isDebugMode(state),
  fetchDone: getFetchDone(state),
  orderIsdModalVisible: getIsdChangeOrderModalVisible(state),
  feeDeductionVisible: getFeeDeductionVisible(state),
  modifyFlightNoModalVisible: getModifyFlightNoModalVisible(state),
  BbkInsuranceDetailProps: BbkInsuranceDetailProps(state),
  authenStatusTicket: getAuthenStatusTicket(state),
  realFirstLoadSucTime: getRealFirstLoadSucTime(state),
  phoneModalVisible: getPhoneModalVisible(state),
  personPhoneModalVisible: getPersonPhoneModalVisible(state),
  phoneModalType: getPhoneModalType(state),
  carRentalMustReadData: getCarRentalMustRead(state),
  modalsVisible: getOrderModalsVisible(state),
  orderWaringInfo: getOrderWaringInfo(state),
  restAssuredTag: getRestAssuredTag(state),
  labelsModalVisible: getLabelsModalVisible(state),
  carDetailModalVisible: getCarDetailModalVisible(state),
  easyLifeTagModalVisible: getEasyLifeTagModalVisible(state),
  carTags: getCarLabelsInfo(state),
  safeRent: getSafeRent(state),
  easyLifeTags: getEasyLifeTags(state),
  queryOrderApiStatus: getQueryOrderApiStatus(state),
  modifyOrderWarnModalVisible: state.ModifyOrder.modifyOrderWarnModalVisible,
  invoiceDesc: getIsdInvoice(state)?.invoiceDesc || Utils.EmptyObj,
  renewTipLogData: getRenewTipLogData(state),
  questionnaires: getQuestionnaires(state),
  vocModalVisible: getVocModalVisible(state),
  freezeDepositExplain: getFreezeDepositExplain(state),
  vehicleInfo: getVehicleInfo(state),
  callBarDesc: getExtendedInfo(state)?.callBarDesc,
  directOpen: AppContext?.UrlQuery?.directOpen,
  directOpenSub: AppContext?.UrlQuery?.directOpenSub,
  link: AppContext?.UrlQuery?.link,
  priceDetailModalVisible: getPriceDetailModalVisible(state),
  ehiModifyOrderModalVisible: getEhiModifyOrderModalVisible(state),
  urgeServiceIds: getUrgeServiceIds(state),
  serviceProgressList: getServiceProgressList(state),
  serviceIds: getServiceIds(state),
  refundPenaltyInfo: getRefundPenaltyInfo(state),
  supportInfo: selectSupportInfo(state),
  storageCardsTitle: getStorageCardsTitle(state),
  nextStorageCardsTitle: getNextStorageCardsTitle(state),
  fetchWarningInfoLoading: getFetchWarningInfoLoading(state),
  storeAttendant: getStoreAttendant(state),
  orderDetailConfirmModalVisible: getOrderDetailConfirmModalVisible(state),
  isHideCancelButton: getIsHideCancelButton(state),
  newOrderInsAndXRes: getNewOrderInsAndXRes(state),
  operationButtons: getOperationButtons(state),
  depositPaymentModalAutoShow: getDepositPaymentModalAutoShow(state),
  orderEhiFreeDepositVisible: getOrderEhiFreeDepositVisible(state),
  finalQueryIsFinish: getQueryOrderAllDataSuccess(state),
  fullSearchNum: getFullSearchNum(state),
  advanceReturnFeeInfo: getAdvanceReturnFeeInfo(state),
  advanceReturnFeeInfoByVendor: getAdvanceReturnFeeInfoVendor(state),
  advanceRecord: getAdvanceReturnRecord(state),
  orderPriceInfoFee: getFeeDetailData(state),
  orderDetailResponse: getOrderDetailResponse(state),
  isuranceBox: Utils.isCtripOsd()
    ? mapIsuranceBoxOsd(state)
    : mapIsuranceBox(state),
  isShowTravelLimit: getIsShowTravelLimit(state),
  crossPolicy: getCrossPolicy(state),
  isNewCancelRule: getIsNewCancelRule(state),
  isSelfService: getIsSelfService(state),
  depositDetailModalVisible: getDepositDetailModalVisible(state),
  limitPopVisible: getLimitRulePopVisible(state),
  isOrderDataByPhone: getOrderDataByPhone(state),
  addInstructData: getAdditionalDriverDesc(state),
  isEasyLife2024: getIsEasyLife2024(state),
  claimProcessModalData: getClaimProcessModalData(state),
  vehicleUseNotesInfo: getVehicleUseNotesModalData(state),
  policyList: getpolicyList(state),
  isShowFulfillmentCard: getIsShowFulfillCard(state),
  isOsdShowBottomButtons: getIsOsdShowBottomButtons(),
  fulfillmentData: getFulfillmentData(state),
  carAssistantSummaryV2: getcarAssistantSummaryV2(state),
  countryId: getCountryId(state),
  vendorId: getVendorId(state),
  phoneNumberData: getOrderPhoneNumberList(state),
  locationDatePopVisible: getLocationDatePopVisible(state),
  isNewOsdModifyOrder: getIsNewOsdModifyOrder(state),
  osdModifyOrderNote: getOsdModifyOrderNote(state),
  isOsdModifyNewOrder: getIsOsdModifyNewOrder(state),
  isPickUpInDoor: getPickUpInDoor(state),
  logBaseInfo: getOrderTraceData(state),
  isShelves2: getOrderDetailIsISDShelves2B(state),
  phoneSurveyShowCount: getPhoneSurveyShowCount(state),
});
const mapDispatchToProps = dispatch => ({
  fetchOrder2: (data?: any, interval?: boolean) =>
    dispatch(fetchOrder2(data, interval)),
  fetchQueryCancelFee: data => dispatch(fetchQueryCancelFee(data)),
  setModifyFlightNoModalVisible: data =>
    dispatch(setModifyFlightNoModalVisible(data)),
  showInsDetailModal: data => dispatch(showInsDetailModal(data)),
  setIsdOrderChangeModalVisible: data =>
    dispatch(setIsdOrderChangeModalVisible(data)),
  showFeeDeduction: data => dispatch(showFeeDeduction(data)),
  setLabelsModalVisible: data => dispatch(setLabelsModalVisible(data)),
  setCarDetailModalVisible: data => dispatch(setCarDetailModalVisible(data)),
  setEasyLifeTagModalVisible: data =>
    dispatch(setEasyLifeTagModalVisible(data)),
  setFetchDone: data => dispatch(setFetchDone(data)),
  reset: () => dispatch(reset()),
  setPhoneModalVisible: (data?: any) => dispatch(setPhoneModalVisible(data)),
  setPersonPhoneModalVisible: (data?: any) =>
    dispatch(setPersonPhoneModalVisible(data)),
  goIsdInsurancePayment: data => dispatch(goIsdInsurancePayment(data)),
  setOrderModalsVisible: data => dispatch(setOrderModalsVisible(data)),
  isShowRenewStatusByStorage: (data?: any) =>
    dispatch(isShowRenewStatusByStorage(data)),
  saveRenewalOrderStatus: (data?: any) =>
    dispatch(saveRenewalOrderStatus(data)),
  queryOrderStatus: (data?: any) => dispatch(queryOrderStatus(data)),
  payCountDownTimeOut: data => dispatch(payCountDownTimeOut(data)),
  queryQuestionnaire: data => dispatch(queryQuestionnaire(data)),
  saveQuestionnaire: data => dispatch(saveQuestionnaire(data)),
  setVocModalVisible: data => dispatch(setVocModalVisible(data)),
  setPriceDetailModalVisible: data =>
    dispatch(setPriceDetailModalVisible(data)),
  queryServiceProgress: data => dispatch(queryServiceProgress(data)),
  urgeServiceProgress: data => dispatch(urgeServiceProgress(data)),
  setStorageCardsTitle: data => dispatch(setStorageCardsTitle(data)),
  getListWarningInfo: data => dispatch(fetchListWarningInfo(data)),
  openOrderDetailConfirmModal: () => {
    dispatch(setOrderDetailConfirmModalVisible(true));
  },
  closeOrderDetailConfirmModal: () => {
    dispatch(setOrderDetailConfirmModalVisible(false));
  },
  queryCommentSummary: () => {
    dispatch(queryCommentSummary({ isOrderDetail: true }));
  },
  ctripContinuePay: data => {
    const goToInsFun = addInsParams => {
      handleOrderDetailGoToIns({
        eventName: EventName.insConfirmBackToOrderDetail,
        addInsParams,
        dispatch,
      });
    };
    dispatch(ctripContinuePay(data, goToInsFun));
  },
  setAdvanceApplySign: data => dispatch(setAdvanceApplySign(data)),
  clearAdvanceCache: () => dispatch(clearCache()),
  setTravelLimitSelectedResult: (data: ISelectedItem[]) =>
    dispatch(setTravelLimitSelectedResult(data)),
  setDepositDetailModalVisible: data =>
    dispatch(setDepositDetailModalVisible(data)),
  setLimitRulePopVisible: data => dispatch(setLimitRulePopVisible(data)),
  createPreFetch: data => dispatch(createPreFetch(data)),
  setFlightDelayRulesModalVisible: data =>
    dispatch(setFlightDelayRulesModalVisible(data)),
  queryOrderNumber: data => dispatch(queryOrderNumber(data)),
  queryOsdModifyOrderNote: () => dispatch(queryOsdModifyOrderNote()),
  setLocationAndDatePopIsShow: data => {
    dispatch(setLocationAndDatePopIsShow(data));
  },
  setPhoneSurveyShowCount: data => {
    dispatch(setPhoneSurveyShowCount(data));
  },
  setStoreSurveyCommit: data => {
    dispatch(setStoreSurveyCommit(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(OrderDetail);
