/**
 * RGBA颜色
 */
export const R_0_0_0_0_0 = 'rgba(0, 0, 0, 0)';
export const R_0_0_0_0_7 = 'rgba(0, 0, 0, 0.7)';
export const R_0_0_0_0_8 = 'rgba(0, 0, 0, 0.8)';
export const R_0_0_0_0_3 = 'rgba(0, 0, 0, 0.3)';
export const R_0_0_0_0_65 = 'rgba(0, 0, 0, 0.65)';
export const R_0_0_0_0_6 = 'rgba(0, 0, 0, 0.60)';
export const R_0_0_0_0_5 = 'rgba(0, 0, 0, 0.50)';
export const R_255_255_255_0_4 = 'rgba(255, 255, 255, 0.40)';
export const R_255_255_255_0 = 'rgba(255, 255, 255, 0)';
export const R_255_119_0 = 'rgb(255, 119, 0)';
export const R_161_221_255_0_22 = 'rgba(161,221,255,0.22)';
export const R_104_185_255 = 'rgb(104,185,255)';
export const R_17_223_119 = 'rgb(17, 223, 119)';
export const R_0_222_174 = 'rgb(0, 222, 174)';
export const R_255_150_48 = 'rgb(255, 150, 48)';
export const R_255_32_75 = 'rgb(255, 32, 75)';
export const R_0_0_0_0_08 = 'rgba(0, 0, 0, 0.08)';
export const R_223_248_240_0_7 = 'rgba(223, 248, 240, 0.7)';
export const R_255_165_10 = 'rgb(255, 165, 10)';
export const R_255_237_232 = 'rgb(255, 237, 232)';
export const R_70_115_190_0_25 = 'rgba(70, 115, 190, 0.25)';
export const R_255_200_153_0_25 = 'rgba(70, 115, 190, 0.25)';
export const R_151_151_151_0_35 = 'rgba(151, 151, 151, 0.35)';
export const R_0_102_246_1 = 'rgba(0, 102, 246,1)';
export const R_0_136_246_1 = 'rgba(0, 136, 246, 1)';

export const R_245_249_253_1 = 'rgba(245, 249, 253, 1)';
export const R_255_255_255_0_89 = 'rgba(255, 255, 255, 0.89)';
export const R_245_247_250_0 = 'rgba(245, 247, 250, 0)';

export const C_transparent = 'transparent';

/**
 * http://cds.ued.ctripcorp.com/?cat=144
 */
export const C_FFF1EB = '#FFF1EB';
export const C_FFE9E7 = '#FFE9E7';
export const C_fafcff = '#fafcff';
export const C_f2f9ff = '#f2f9ff';

export const C_006ff6 = '#006ff6';
export const C_F5F9FF = '#F5F9FF';
export const C_00b87a = '#00b87a';
export const C_01AE73 = '#01AE73';
export const C_fff8f2 = '#fff8f2';
export const C_ff6600 = '#ff6600';
export const C_FF732F = '#FF732F';
export const C_FF6F00 = '#FF6F00';
export const C_FF6501 = '#FF6501';
export const C_F5190A = '#F5190A';
export const C_808A92 = '#808A92';
export const C_F51A0B = '#F51A0B';
export const C_753400 = '#753400';
export const C_333333 = '#333333';
export const C_666 = '#666';
export const C_999 = '#999';
export const C_aaa = '#aaa';
export const C_ccc = '#ccc';
export const C_eee = '#eee';
export const C_DADFE6 = '#DADFE6';
export const C_EFF1F6 = '#EFF1F6';
export const C_D8D8D8 = '#D8D8D8';
export const C_F1F5F8 = '#F1F5F8';
export const C_FEFEFF = '#FEFEFF';
export const C_111111 = '#111111';
export const C_888888 = '#888888';
export const C_eef1f6 = '#eef1f6';
export const C_f8fafd = '#f8fafd';
export const C_F2F8FE = '#F2F8FE';
export const C_f5f6fa = '#f5f6fa';

export const C_EEEEEE = '#EEEEEE';
export const C_FFF = '#fff';
export const C_000 = '#000';
export const C_00a7fa = '#00a7fa';
export const C_0076f5 = '#0076f5';
export const C_ff9d0a = '#ff9d0a';
export const C_E7F3FF = '#E7F3FF';
export const C_F9F9F9 = '#F9F9F9';
export const C_EDF2F8 = '#EDF2F8';
export const C_F5F8FA = '#F5F8FA';
export const C_979797 = '#979797';
export const C_555555 = '#555555';
export const C_F8FBFF = '#F8FBFF';
export const C_F1F7FF = '#F1F7FF';
export const C_FF5500 = '#FF5500';
export const C_888 = '#888';
export const C_F0F0F0 = '#F0F0F0';
export const C_F5F7FA = '#F5F7FA';
export const C_E0F0FD = '#E0F0FD';
export const C_5D6F96 = '#5D6F96';
export const C_D5DAE1 = '#D5DAE1';
export const C_2aa1ff = '#2aa1ff';
export const C_E5E5E5 = '#e5e5e5';
export const C_EBF4FF = '#ebf4ff';
export const C_272727 = '#272727';
export const C_FF6D2C = '#FF6D2C';
export const C_008CFF = '#008CFF';
export const C_0070FD = '#0070FD';
export const C_fd7300 = '#fd7300';
export const C_ff822d = '#ff822d';
export const C_EBF5FF = '#EBF5FF';
export const C_D4EAFF = '#D4EAFF';
export const C_EFF4F8 = '#EFF4F8';
export const C_0F4999 = '#0F4999';
export const C_FFC899 = '#FFC899';
export const C_F24C3D = '#f24c3d';
export const C_93BCF5 = '#93bcf5';
export const C_0019C3 = '#0019C3';
export const C_00A7FA = '#00A7FA';
export const C_FAFCFE = '#FAFCFE';
export const C_FFF0DC = '#FFF0DC';
export const C_843A00 = '#843A00';
export const C_FFF5E5 = '#FFF5E5';
export const C_FFF3D8 = '#FFF3D8';
export const C_FFE6BC = '#FFE6BC';
export const C_F5F6FA = '#F5F6FA';

export const white = '#fff';
export const textWhite = '#fff';
export const black = '#000';
export const transparent = 'transparent';
export const transparentBase = 'transparent';
export const blackTransparent = 'rgba(0, 0, 0, 0.7)';
export const modalShadow = 'rgba(0, 0, 0, 0.7)';
export const cardShadow = 'rgba(0, 0, 0, 0.14)';
export const orderCardStatusBlueShadow = '#007FE9';
export const bottomBarShadow = '#C4C4C4';

// 品牌色、标准色
export const blueBase = '#0086f6';
export const greenBase = '#00b87a';
export const orangeBase = '#ff7700';
export const redBase = '#F5190A';
export const grayBase = '#999';
export const blueGrayBase = '#111111';
export const lightRedBase = '#FF481D';
export const blueDeepBase = '#006ff6';
// 单色图标
export const blueIcon = '#2598f8';
export const greenIcon = '#24c28d';
export const orangeIcon = '#ff8b26';
export const redIcon = '#f63a2d';
export const blueGrayIcon = '#999';
// 边框色
export const blueBorder = '#bfe0fc';
export const greenBorder = '#bfeddd';
export const orangeBorder = '#ffdcbf';
export const redBorder = '#fcc5c2';
export const grayBorder = C_eee;
export const grayDescLine = '#bbb';
export const darkGrayBorder = '#ccc';

// 浅色衬底
export const blueBg = '#f3f8fe';
export const blueBgSecondary = '#E6F3FE';
export const greenBg = '#f2fbf7';
export const orangeBg = '#fff8f2';
export const orangeBgLight = '#fff8f2';

export const redBg = '#fef2f1';
export const grayBg = '#f4f4f4';
export const grayBgSecondary = '#f8f8f8';
export const blueGrayBg = '#eef1f6';
export const tableBg = '#F2F8FE';
export const grayPlaceholder = '#EEEEEE';
export const tableGrayBg = '#f5f6fa';
export const blackBase = '#111111';
export const ipxGragBg = '#f4f4f4';
export const lightRedBg = '#FFEEE7';
export const redLightBg = '#FEE8E6';

// 点击色
export const blueClick = '#007fe8';

// 价格
export const orangePrice = '#ff6600';
export const orangePrice2 = '#FF9500';
export const priceColor = '#ff6600';

// 字体
export const fontPrimary = '#111111';
export const fontSecondary = '#666';
export const fontSubDark = '#999';
export const fontSubLight = '#ddd';
export const fontGrayBlue = '#aaa';

export const fontBlueDark = '#99aeca';
export const fontGrayDark = '#222';

// 渐变
export const linearGradientBlueLight = '#00a7fa';
export const linearGradientBlueDark = '#0076f5';
export const linearGradientOrangeLight = '#ffa50a';
export const linearGradientOrangeDark = '#ff7700';
export const linearGradientOrangeOrderLight = '#ff9d0a';

export const linearGradientLightBlue = '#19A0F0';
export const linearGradientDarkBlue = '#0B67D1';
export const linearGradientBlueBgLight = '#EDFAFF';
export const linearGradientBlueBgDark = '#E7F3FF';
export const linearGradientXyzBgBlue = '#DFF3FF';

export const linearGradientItineraryCardStop1 = '#E2F1FF';
export const linearGradientItineraryCardStop2 = '#F1F8FF';
export const linearGradientItineraryCardStop3 = '#F3F9FF';

export const linearGradientCancelTipDark = '#EAF5FF';
export const linearGradientCancelTipLight = '#F9FCFF';

export const linearGradientBlueBottom = '#71BDFF';

export const linearGradientBluePercentBg = '#8ACAFF';

// 保险绿
export const greenInsurance = '#45A83A';
export const osdGreenInsurance = '#00b87a';

export const radioBorderColor = '#DCE0E5';
export const radioDisableBackGroundColor = '#cdd6e5';

// horizontal selected of blue color
export const horizontalSelectedColor = '#0186F6';
export const horizontalColor = '#666';

// pickdrop of guide for map component
export const mapTabSelectdColor = '#0086f6';
export const mapTabColor = '#666';
export const mapLocalColor = '#666';
export const btnBorder = '#2698f7';
export const guideStepSequenceColor = '#ccc';
export const hasEndColor = '#999';

export const btnDarkBlueBorder = '#0385F6';

// 表格
export const tableBorderColor = '#dee4ee';
export const tableBackgroundColor = '#f8fafd';

// 无忧租
export const easylifeBg = '#E1C285';
export const easylifeText = '#C9963E';
export const easylifeTextDark = '#654C0A';
export const easylifeTag = '#9F7531';
export const easylifeTagBg = '#FFFDF6';

export const easylifeBorderColor = '#EAD6A1';
export const easylifeText2 = '#8E5E11';
export const easylifeBg2 = '#FFF8E9';
export const easylifeBg3 = '#FFFCF8';
export const easylifeBorderColor2 = 'rgba(159, 117, 49, 0.30)';
export const easylifeGray1 = '#DDE4ED';

export const easylifeBlue = '#006ff6';
export const easylifeTextBlack = '#111111';
export const essylifeTextGray = '#888888';

// 芝麻免押 blue14

export const sesamePrimary = '#00A0E9';
export const sesameFontPrimary = '#222';
export const sesameSuccess = '#09BB07';
export const sesameFail = '#F76260';

export const sesameClose = '#BABABA';

// 猜你喜欢
export const likeGradient1 = '#FF6B61';
export const likeGradient2 = '#F64D41';

export const labelGreenBg = '#e6f8f1';
export const labelGreenText = '#00b87a';
export const labelOrangeBg = '#FFF1E6';
export const labelOrangeText = '#ff7700';
export const labelBlueBg = '#E6F3FE';
export const labelBlueText = '#0086f6';
export const bookbarTipsGreenBg = '#e6f8f1';

export const bookbarTipsOrangeBg = '#FFFAF2';

export const certificateTypeInvaildTip = '#F2C100';
export const certificateTypeInvaildColor = '#ccc';
export const certificateActive = '#F2F8FE';
export const certificateActiveBorder = '#4eaaf8';
// 点评
export const blueReview = '#5678A8';

// 订单详情页
export const blueNps = '#397FF6';
export const blueLightNps = '#E6F3FE';
export const blueVehicleHdBg = '#E9F2FF';

export const selfHelpBg = '#f6f8fa';

export const dayText = '#DADFE6';

export const verifyTitle = '#ff6600';

export const iconCountDownFilled = '#FFA04D';
export const insLableBg = '#eee';
export const insStatusRed = '#E96457';

export const successGreen = '#00ae73';
export const blueShadow = '#99CEFB';
export const diffBg = '#FEF3F2';
export const underlineColor = '#E3E3E3';

export const freeDepositLabelColor = '#E5F8F1';

// 优惠
export const discountRed = '#F85E53';

// 无结果卡片背景色

export const noResultBtnBorder = '#3A91FB';
export const noResultRecommendTip = '#3263A6';

// 支付方式label
export const fontBlackDark = '#455873';
export const fontBlackLight = '#0F294D';
export const orangePayLabel = '#FF6F00';

// dialog
export const dialogTitle = '#030303';

// driver
export const driverError = '#F5190A';
export const driverRadioBorder = '#aaa';

export const driverCertificateTip = '#FF9913';

// 沪牌
export const licenseLabel = '#366AB3';
export const foreignLicenseLabel = 'rgba(34, 34, 34, 0.7)';
// 新版沪牌

// 芝麻label border
export const zhimaBorder = 'rgba(0, 160, 233, 0.5)';

// 城市区域
export const defaultLabel = '#5678A8';
export const secLabel = '#89a0c2';

// 平铺列表
export const lineColor = '#EEEEEE';

export const cityLabel = 'rgb(46, 49, 65)';

export const cityLabelText2 = '#FFEBC9';

// 分割线
export const splitGray = '#ccc';

// Bookbar

export const bookbarBgColor = '#fff';
// filterbutton
export const filterButtonColor = '#f8f8f8';
export const tipsRed = '#F63B2E';

// AddedService
export const addedServiceOrangeTag = '#ff6600';
export const addedServiceRedTag = '#EE3B28';

// switch

// warning

// marketfooter

export const soldOutLabelBgColor = '#EFECEA';
export const soldOutEasyLifeLabelBgColor = '#EFEADA';

export const numberBackgroundColor = '#BBC9DC';
export const numberLineBackgroundColor = '#E5EAF2';

// 端午节颜色

// 88节颜色

// 国庆节颜色

// 保险标签背景色
export const insuranceLabelBg = '#00AE73';

// 变价弹层
export const priceFallBg = '#F2FBF8';
export const priceDiffTableBorder = '#EFF1F6';

// 信用租
export const zhimaLabelBg = '#F0FAFF';
export const preAuthLabelBg = '#1CC677';

// 双11节颜色
export const elevenFestivalLabelBorderColor = 'rgba(225, 33, 255, 0.40)';
export const elevenFestivalLabelColor = '#B809D3';

// 列表页总价说明
export const priceDescIconColor = '#01B87A';

// 一嗨全国连锁标签
export const nationalChainTagColor = '#5378A6';

// 新版首页底部slogan的title颜色

// 双旦主题字体颜色

// 自定义toast背景色
export const toastBgColor = 'rgba(0, 0, 0, 0.7)';

// 增量加载失败按钮颜色

// 区域页分类图标色值
export const areaMetro = '#fd8f3a';
export const areaRegion = '#34C6B6';
export const areaPlane = '#3E89FA';
export const areaTrain = '#5F84FE';
export const areaBus = '#787FFE';
export const shoppingPoint = '#eb7449';

// 租车中心文案颜色

// 优惠券
export const couponItemBg = C_F9F9F9;
export const couponRed = '#F98078';
export const couponLinearStart = '#FF7B5B';
export const couponLinearEnd = '#F94E3A';
export const cornerLinearStart = '#FF8700';
export const cornerLinearEnd = '#FF9908';
export const listCouponLinearStart = '#FF714E';
export const listCouponLinearEnd = '#F53B2E';
export const homeNewCouponLinearStart = 'rgba(255, 216, 213, 0.70)';

export const homeHasPurchaseCouponLinearEnd = 'rgb(255, 198, 198)';
export const bookingCouponLabelBg = '#FF6D4C';
export const newCoupon = '#FF4C1D';
export const listCouponEntryBg = '#FFFAF9';

// 取消订单到店无车颜色

// 特权弹窗

// 修改订单补款确认页-保险加购状态颜色
export const modifyOrderPayTimeShadowColor = 'rgba(0, 0, 0, 0.05)';
// 标签样式
export const labelSoldBg = '#FAFAFA';
export const labelPostfixBg = '#FEF1E6';
export const labelMarketBorder = 'rgba(254, 189, 147, 1)';

// 全国连锁
export const nationalChainTagTextColor = '#93300D';
export const nationalChainTagBackgroundColor = '#FCF2F1';
// 租车中心
export const carRentalCenterBottomLineColor = '#61B7FF';
// 退款进度
export const refundTotalAmount = '#FF6501';
export const refundSuccess = '#01AE73';
export const refundFaile = '#F5180B';
export const refundProcessed = '#007AF5';
// 首页行程卡
export const ItineraryCardTitle = '#5578A8';
export const ItineraryCardSelectedBorder = '#73BCFA';

// VOD调查边框
export const vocBorderColor = '#ffb473';
export const vocSelectedTextColor = '#FE6600';
// 我的凭证 绿色标签
export const signBtnBg = '#1677ff';

export const cansignBtn = '#E7F1FF';
// 催事件进度
export const consultBgColor = 'rgba(255, 255, 255, 0.13)';
export const consultShadow = '#0083FF';
export const consultTip = 'rgba(255, 255, 255, 0.6)';
// 取消订单
export const refundPenaltyHelp = '#B4CBED';

export const refundPenaltyError = '#F51A0B';

export const refundPenaltyGreenDesc = '#01AE73';
export const refundPenaltyRedDesc = '#F51A0B';
export const refundPenaltyStepShadow = '#8dc8ff';
// 新填写页
export const newBookingGrayBg = '#EDF2F8';

// 订详整体优化
export const descItemDot = '#FF7529';
export const sectionTitleGradientBg = '#72BCFA';

export const instructionShadowColor = '#0172D0';

export const homeCouponBgShadow = '#FFDDDD';

export const reviewYellowBg = '#FFF2BF';
export const newBookingTableLine = '#CCD6E5';

// 订详押金

export const depositBg = '#F5F8FA';

// 行程卡 radio 边框颜色
export const itineraryCardRadioBoxBorder = '#BBC9DC';
export const itineraryCardRadioBoxTick = '#DDE4ED';
export const itineraryCardRadioShadow = '#CEE8FF';
export const itineraryCardGapLine = '#979797';

// 权益页面渐变色
export const emptylinearGradientStart = 'rgba(255, 255, 255, 0.21)';

export const memberText = '#FDEBCE';
export const memberLabel = '#784323';

export const memberBg = '#F7F8F9';
export const memberMenuShadow = 'rgba(0, 32, 59, 0.02)';

export const memberGoRent = '#ffeed5';

// 黑钻
export const linearGradientBlackDiamondStart = '#555555';
export const linearGradientBlackDiamondEnd = '#111111';
export const blackDiamondTitleBg = '#FFF4E2';
export const blackDiamondTipBg = '#FFF9EF';
export const blackDiamondTipText = '#B47B43';
export const blackDiamondLine = '#bbb';
export const blackDimamndText = '#FEECCF';
export const linearGradientButtonBlackDiamondStart = '#3E3E3E';
export const linearGradientButtonBlackDiamondEnd = '#111111';
export const linearGradientMenuBlackDiamondStart = '#86898A';
export const linearGradientMenuBlackDiamondEnd = '#494949';
export const blackDiamondNumber = '#787878';
export const blackDiamondBookingTxt = '#AE8032';
export const blackDiamondCouponBg = '#FFF3DF';
export const blackDiamondBookingTip = '#F8E4C3';
// 金钻
export const linearGradientGoldDiamondStart = '#384A8C';
export const linearGradientGoldDiamondEnd = '#192039';
export const goldDiamondTitleBg = '#FFF4E2';
export const goldDiamondTipBg = '#FFF9EF';
export const goldDiamondTipText = '#B47B43';
export const goldDiamondLine = '#AFB7D1';
export const goldDiamondText = '#FEECCF';
export const linearGradientButtonGoldDiamondStart = '#4B5F9D';
export const linearGradientButtonGoldDiamondEnd = '#27396F';
export const linearGradientMenuGoldDiamondStart = '#6376B7';
export const linearGradientMenuGoldDiamondEnd = '#3A4C8B';
export const goldDiamondNumber = '#6275b6';
export const goldDiamondBookingTxt = '#AE8032';
export const goldDiamondCouponBg = '#FFF3DF';
export const goldDiamondBookingTip = '#F8E4C3';
// 钻石
export const linearGradientDiamondStart = '#47559F';
export const linearGradientDiamondEnd = '#383C5F';
export const diamondTitleBg = '#FFF4E2';
export const diamondTipBg = '#FFF9EF';
export const diamondTipText = '#B47B43';
export const diamondLine = '#B5BBD9';
export const diamondText = '#FEECCF';
export const linearGradientButtonDiamondStart = '#626B9D';
export const linearGradientButtonDiamondEnd = '#465290';
export const linearGradientMenuDiamondStart = '#7C8ADC';
export const linearGradientMenuDiamondEnd = '#49579E';
export const diamondNumber = '#7b89db';
export const diamondBookingTxt = '#595cb3';
export const diamondCouponBg = '#e3edfe';
export const diamondBookingTip = '#595cb3';
// 铂金
export const linearGradientPlatinumStart = '#7EA8B5';
export const linearGradientPlatinumEnd = '#36647F';
export const platinumTitleBg = '#D3F5FF';
export const platinumTipBg = '#E8F9FE';
export const platinumTipText = '#5E9BAE';
export const platinumLine = '#CBDCE1';
export const platinumText = '#fff';
export const linearGradientButtonPlatinumStart = '#6FABBF';
export const linearGradientButtonPlatinumEnd = '#6298AA';
export const linearGradientMenuPlatinumStart = '#8ABCCC';
export const linearGradientMenuPlatinumEnd = '#55859A';
export const platinumNumber = '#6293a7';
export const platinumBookingTxt = '#218db1';
export const platinumCouponBg = '#e3f6fc';
export const platinumBookingTip = '#218db1';
// 黄金
export const linearGradientGoldStart = '#DDB57E';
export const linearGradientGoldEnd = '#9D763F';
export const goldTitleBg = '#FFF4E2';
export const goldTipBg = '#FFF9EF';
export const goldTipText = '#B47B43';
export const goldLine = '#F1E1CB';
export const goldText = '#fff';
export const linearGradientButtonGoldStart = '#DBB981';
export const linearGradientButtonGoldEnd = '#D0A357';
export const linearGradientMenuGoldStart = '#E6C489';
export const linearGradientMenuGoldEnd = '#C8A16A';
export const goldNumber = '#debb81';
export const goldBookingTxt = '#AE8032';
export const goldCouponBg = '#FFF3DF';
export const goldBookingTip = '#AE8032';
// 白银
export const linearGradientSilverStart = '#7AA2DD';
export const linearGradientSilverEnd = '#416FB2';
export const silverTitleBg = '#CEE2FF';
export const silverTipBg = '#F0F6FF';
export const silverTipText = '#7AA2DD';
export const silverLine = '#CADAF1';
export const silverText = '#fff';
export const linearGradientButtonSilverStart = '#7AA2DD';
export const linearGradientButtonSilverEnd = '#6090D4';
export const linearGradientMenuSilverStart = '#91B7EE';
export const linearGradientMenuSilverEnd = '#4C7EC6';
export const silverNumber = '#78a0db';
export const silverHomeBlockTitleColor = '#4d86d8';
// 普通
export const linearGradientNormalStart = '#00a7fa';
export const linearGradientNormalEnd = '#0076f5';
export const normalTitleBg = '#D7F1FF';
export const normalTipBg = '#EFF9FF';
export const normalTipText = '#00a7fa';
export const normalLine = '#99DCFD';
export const normalText = '#fff';
export const linearGradientButtonNormalStart = '#46B3FF';
export const linearGradientButtonNormalEnd = '#0092F8';
export const linearGradientMenuNormalStart = '#0FBFFF';
export const linearGradientMenuNormalEnd = '#008AF5';
export const normalNumber = '#46B3FF';
export const normalHomeBlockTitleColor = '#2c9af7';
// 生日福利页面
export const toUseButton = '#F8665B';
// 车损

export const damageFeeDetailHeaderBorder = '#dce4ed';

export const feeProgressGradient1 = '#55B7FF';
export const feeProgressGradient2 = '#49A0FF';
// 领取订按钮
export const linearGradientCouponBookEnd = '#F5180B';

export const activityLinearBg1 = '#FF8678';
export const activityLinearBg2 = '#FFEFEF';

// 车型重构

export const tableSelectedBg = '#f2f9ff';
export const selectedShadowBg = '#4c97ff';

export const serviceTipStart = '#e9f1ff';
export const serviceTipEnd = 'rgba(233, 241, 255, 0)';
export const orangeServiceTipStart = '#FFF1E6';
export const orangeServiceTipEnd = 'rgba(244, 250, 255, 0)';
// 费用明细优惠卷颜色

// 优选好店弹窗按钮颜色

export const optimizeStoreShadowColor = '#5B5B5B';

// 云南云旅价格颜色

// 订详续租背景色

export const cancelTipBtnBorder = '#d6d6d6';
export const cashFailHelp = '#F51909';
export const cashSuccess = '#01AE73';
export const cancelSecondLevelReason = '#0072d1';
// 标签升级
export const marketLinearStartColor = '#FFF6F0';
export const marketLineaEndColor = '#FFEFE5';
export const marketLinearCouponStartColor = '#FFF3F0';
export const marketLineaCouponEndColor = '#FFEAE5';
export const marketCouponPriceColor = '#ff3500';
export const depositLinearBlue = 'rgba(0, 134, 246, 0.08)';

// 优选强化
export const optimizationStrengthenDot = '#5c5c5c';
export const optimizationStrengthenGradientStart = '#FFF0DB';
// 新版限行提示
export const limitObjectTitle = '#222630';

// 车型推荐标签

// 能源说明弹窗
export const fuelTypeModalBg = '#e3f2ff';

// 修改订单修改说明提醒
export const modifyOrderExplain = '#F2200C';

// 无少结果推荐
export const noResultBg = '#F1F5F8';
export const recommendVehicleTipStart = '#edf7ff';
export const recommendProposeBg = '#111111';

// 固话小号

export const virutualNumberBgGradient1 = '#D5E8FF';
export const virutualNumberBgGradient2 = '#F1F7FF';

export const recommendBg = '#FF5500';

export const virtualNumberTitleBg = 'rgba(201,224,254,0.55)';

// LESS可持续旅行项目
export const lessBg = '#E6FAF3';
export const lessMain = '#24B381';

export const cancelPolicyExpire = '#C5C5C5';
export const venderListHeaderGradientBlue = 'rgba(239, 244, 248, 0)';

// 填写页优化
export const rentalDateDurationBg = '#ebeff5';
export const bookingOptimizationBlue = '#006ff6';
export const driverItemSelectedBg = '#ebf3ff';
export const addMoreDriver = '#888888';
export const insuranceTipBg = '#F5F7FA';
export const discountBorder = '#f97b20';
export const bookingOptimizationGrayBg = '#ebeff5';
export const payButtonGradientOrange = '#FF9900';
// 填写页优惠押金合并
export const couponSplit = '#ebeff5';
export const disableCoupon = '#C5C5C5';
export const couponMergeBg = '#F8FAFB';

// 订单卡片
export const storeDetailNewBorder = '#ebeff5';
export const storeDetailBlue = '#006ff6';

// 首页新版搜索框

// 年龄修改提示
export const ageModifyBlue = '#006ff6';

export const browsingBlue = '#1856AE';

export const browsingLinear = '#E0F0FD';
// 盲盒
export const listSecretBoxBg = '#FFF4E1';

// 出境免押
export const depositFreeTableHeaderBg = '#00D19F';
export const depositRadioChecked = '#008FFF';
export const depositLinearGradientStart = '#FCFDFF';
export const depositLinearGradientEnd = '#D9EEFE';
export const depositFreeAuthGradientStart = '#E5F7FE';
export const depositFreeAuthGradientEnd = '#E1F2FF';
// 新版海外保险
export const claimDashColor = '#ced2d9';
export const insuranceTipIconColor = '#26C28D';
export const insuranceTipLinearStart = '#FEB64E';
export const insuranceTipLinearEnd = '#FF5A06';

export const orangeFF8800 = '#FF8800';

export const osdItineraryCardBg = '#F5F8FC';

export const osdItineraryCardBoder = '#EEEFF3';

export const osdItineraryGapLine = '#D5D5D5';

export const osdlinearGradientItinerary1 = '#E6EFFB';

export const C_FFD7CB = '#FFD7CB';
export const C_FFEACC = '#FFEACC';
export const C_646464 = '#646464';
export const C_CBDFFC = '#CBDFFC';
export const C_CACACA = '#CACACA';
export const C_00A66F = '#00A66F';
export const C_B8C6D9 = '#B8C6D9';
export const C_08A66F = '#08A66F';
export const C_F54336 = '#F54336';
export const C_14C800 = '#14C800';
export const C_D0D8E6 = '#D0D8E6';
export const C_00B988 = '#00B988';
export const C_006FF6 = '#006ff6';
export const C_FE5500 = '#FE5500';
export const C_001A65 = '#001A65';
export const C_000A43 = '#000A43';
export const C_0066F6 = '#0066F6';
export const C_DFE9F4 = '#DFE9F4';
export const C_F1F5FC = '#F1F5FC';
export const C_EDF5FF = '#EDF5FF';
export const C_0088F6 = '#0088F6';
export const C_777777 = '#777777';
export const C_FFEDE8 = '#FFEDE8';
export const C_FFF8F5 = '#FFF8F5';
export const C_FFEFEA = '#FFEFEA';

export const C_F9A278 = '#F9A278';
export const C_619EFF = '#619EFF';
export const C_DBDBDB = '#DBDBDB';
export const C_4673BE = '#4673BE';
export const C_4673B2 = '#4673B2';
export const C_41A5F9 = '#41A5F9';
export const deepBlueBase = C_006ff6;
export const C_E4F9F2 = '#E4F9F2';
export const C_EEF4FF = '#EEF4FF';

export const C_00A4F8 = '#00A4F8';
export const C_0079ED = '#0079ED';
export const C_009FFB = '#009FFB';
export const C_B8BDC8 = '#B8BDC8';
export const C_8B91A0 = '#8B91A0';
export const C_0073F5 = '#0073F5';

export const C_156BD5 = '#156BD5';
export const C_49B9FF = '#49B9FF';
export const C_71BBF9 = '#71BBF9';
export const C_308EFF = '#308EFF';
export const C_84BCFF = '#84BCFF';
export const C_E3FFF5 = '#E3FFF5';
export const R_6_111_214_0_6 = 'rgba(6, 111, 214, 0.6)';
export const C_DCF0FF = '#DCF0FF';
export const C_EDF6FF = '#EDF6FF';
export const C_E6F0FE = '#E6F0FE';
export const C_F6F8FB = '#F6F8FB';
export const C_CFD8E6 = '#CFD8E6';
export const C_ACD8FF = '#ACD8FF';
export const C_38A2FF = '#38A2FF';
export const C_FF0404 = '#FF0404';
export const C_FF0B0B = '#FF0B0B';
export const C_F9FBFC = '#F9FBFC';
export const C_E4EEFF = '#E4EEFF';
export const C_e7f8ff = '#e7f8ff';
export const C_DBEAF6 = '#DBEAF6';
export const C_D5E9FE = '#D5E9FE';
export const C_F2FAFD = '#F2FAFD';
export const C_007aff = '#007aff';
export const C_F5594A = '#F5594A';
export const C_ECECEC = '#ECECEC';
export const C_FFF7EB = '#FFF7EB';
export const C_FEEEEC = '#FEEEEC';
export const C_ebf3ff = '#ebf3ff';
export const C_2ABA87 = '#2ABA87';
export const R_244_247_249_0_6 = 'rgba(244,247,249,0.6)';
export const R_76_81_100_0_0_5 = 'rgba(76,81,100,0.05)';
export const C_F0F6FA = '#F0F6FA';
export const C_EFF4FB = '#EFF4FB';
export const C_C4D4E6 = '#C4D4E6';
export const C_D92917 = '#D92917';
export const C_E7EAF4 = '#E7EAF4';
export const C_815418 = '#815418';
export const C_1A1A1A = '#1A1A1A';
export const C_FDFEFF = '#FDFEFF';
export const C_ECF6FF = '#ECF6FF';
export const C_EAF6FF = '#EAF6FF';
export const C_FFF2E3 = '#FFF2E3';
export const C_FFF9F0 = '#FFF9F0';
export const C_FFEFDF = '#FFEFDF';
export const R_187_198_213_0_3 = 'rgba(187, 198, 213, 0.3)';
export const C_F9FBFF = '#F9FBFF';
export const C_FBFCFF = '#FBFCFF';
export const C_FFEFDE = '#FFEFDE';
export const C_FFE4CC = '#FFE4CC';
export const C_3565B9 = '#3565B9';
export const C_F8FAFD = '#F8FAFD';
export const C_F3F5FA = '#F3F5FA';
export const C_FFF4F1 = '#FFF4F1';
export const C_EFEFEF = '#EFEFEF';
export const C_CCE7FF = '#CCE7FF';
export const C_E7E7E7 = '#E7E7E7';
export const C_EEF0F5 = '#EEF0F5';
