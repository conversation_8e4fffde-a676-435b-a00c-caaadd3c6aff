import React from 'react';
import { XView as View } from '@ctrip/xtaro';

import BbkComponentText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import { layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';

import styles from './Styles';

interface DailyPriceItemTypes {
  localCurrencyCode: string;
  quantity: number;
  currentDailyPrice: number;
}

const DailyPriceItem: React.FC<DailyPriceItemTypes> = ({
  localCurrencyCode,
  quantity,
  currentDailyPrice,
}) => (
  <View style={layout.flexRow}>
    <BbkCurrencyFormatter
      currency={localCurrencyCode}
      price={currentDailyPrice}
      currencyStyle={styles.dayAndPrice || {}}
      priceStyle={styles.dayAndPrice || {}}
      wrapperStyle={styles.mr8 || {}}
    />

    <BbkComponentText style={styles.dayAndPrice}>×</BbkComponentText>
    <BbkComponentText style={styles.dayAndPrice}>{quantity}天</BbkComponentText>
  </View>
);

export default DailyPriceItem;
