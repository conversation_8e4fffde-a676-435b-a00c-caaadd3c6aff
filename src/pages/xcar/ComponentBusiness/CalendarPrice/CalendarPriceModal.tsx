import StyleSheet from '@c2x/apis/StyleSheet';
import <PERSON>Handler from '@c2x/apis/BackHandler';
import React, { useCallback, useContext, useMemo, useEffect } from 'react';
import { XView as View, XBoxShadow } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color, tokenType } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import c2xSTYLES from './calendarPriceModalC2xSTYLES.module.scss';
import { UITestID } from '../../Constants/Index';
import BbkHalfPageModal, {
  PAGEMODAL_HEADER_MIN_HEIGHT,
  PAGEMODAL_MAX_HEIGHT,
} from '../HalfPageModal';
import { AutoScrollView } from '../../Components/AutoScrollView/AutoScrollView';
import { CalendarPriceModalContext } from './CalendarPriceContext';
import CalendarPrice from './CalendarPrice';
import { AppContext } from '../../Util/Index';
import { PriceDaily } from './types';
import texts from './Texts';

const { getPixel, fixIOSOffsetBottom, lazySelector } = BbkUtils;
const STYLES = StyleSheet.create({
  container: {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0,
  },
  content: {
    paddingLeft: BbkUtils.getPixel(32),
    paddingRight: BbkUtils.getPixel(32),
    paddingTop: BbkUtils.getPixel(40),
    paddingBottom: BbkUtils.getPixel(40),
  },
  scrollViewContainer: {
    paddingBottom: BbkUtils.getPixel(32),
  },
  footerWrap: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(16),
    paddingBottom: fixIOSOffsetBottom() + getPixel(16),

    backgroundColor: color.white,
  },
  footer: {
    borderRadius: getPixel(12),
  },
  footerText: {
    fontSize: getPixel(34),
  },
});

export interface ModifyOrderCalendarPriceProps {
  origin: {
    sources: PriceDaily[];
    tips?: string;
  };
  current: {
    sources: PriceDaily[];
    tips?: string;
  };
}

const ModifyOrderCalendarPrice: React.FC<ModifyOrderCalendarPriceProps> = ({
  origin,
  current,
}) => (
  <View>
    <View className={c2xSTYLES.calendarPriceGroup}>
      <BbkText className={c2xSTYLES.calendarPriceGroupTitle}>
        {texts.originOrderText}
      </BbkText>
      <CalendarPrice
        sources={origin.sources}
        collapse={false}
        visible={true}
        description=""
        tips={origin.tips || ''}
      />
    </View>
    <View className={c2xSTYLES.calendarPriceGroup}>
      <BbkText className={c2xSTYLES.calendarPriceGroupTitle}>
        {texts.currentOrderText}
      </BbkText>
      <CalendarPrice
        sources={current.sources}
        collapse={false}
        visible={true}
        description=""
        tips={current.tips || ''}
      />
    </View>
  </View>
);

const CalendarPriceModal: React.FC = () => {
  const { visible, payload, setCalendarPriceModal } = useContext(
    CalendarPriceModalContext,
  );
  const requestClose = useCallback(() => {
    setCalendarPriceModal(false, payload);
  }, [payload, setCalendarPriceModal]);

  const handleConfirm = useCallback(() => {
    setCalendarPriceModal(false, payload);
  }, [payload, setCalendarPriceModal]);

  const pageModalProps = useMemo(
    () => ({
      visible,
      onMaskPress: requestClose,
      zIndex: 2001,
    }),
    [visible, requestClose],
  );

  const maxHeight = useMemo(() => {
    const footerHeight = fixIOSOffsetBottom() + getPixel(32) + getPixel(98);
    return PAGEMODAL_MAX_HEIGHT - PAGEMODAL_HEADER_MIN_HEIGHT - footerHeight;
  }, []);

  const modalHeaderProps = useMemo(
    () => ({ title: texts.calendarPriceModalTitle }),
    [],
  );
  const backhandler = useMemo(() => {
    const prePageId = AppContext.PageInstance.getPageId();
    return () => {
      if (visible && prePageId === AppContext.PageInstance.getPageId()) {
        setCalendarPriceModal(false, payload);
        return true;
      }
      return false;
    };
  }, [visible, setCalendarPriceModal, payload]);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', backhandler);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', backhandler);
    };
  }, [backhandler]);
  return (
    <BbkHalfPageModal
      pageModalProps={pageModalProps}
      modalHeaderProps={modalHeaderProps}
      contentStyle={STYLES.container}
      closeModalBtnTestID={UITestID.car_testid_calendarprice_modal_close_mask}
    >
      {lazySelector(payload?.sources, () => (
        <AutoScrollView
          style={STYLES.content}
          maxHeight={maxHeight}
          contentContainerStyle={STYLES.scrollViewContainer}
        >
          {lazySelector(
            payload.modifyOrder,
            () => (
              <ModifyOrderCalendarPrice
                origin={{
                  sources: payload.originalSources,
                  tips: payload.originalTips,
                }}
                current={{ sources: payload.sources, tips: payload.tips }}
              />
            ),

            () => (
              <CalendarPrice
                sources={payload.sources}
                collapse={false}
                description=""
                tips={payload.tips || ''}
              />
            ),
          )}
        </AutoScrollView>
      ))}
      <XBoxShadow
        style={STYLES.footerWrap}
        coordinate={{ x: 0, y: -2 }}
        color="rgba(0, 0, 0, 0.06)"
        opacity={1}
        blurRadius={getPixel(9)}
        elevation={9}
      >
        <BbkButton
          buttonStyle={STYLES.footer}
          textStyle={STYLES.footerText}
          text={texts.confirmText}
          colorType={tokenType.ColorType.Blue}
          onPress={handleConfirm}
          testID={UITestID.car_testid_calendarprice_modal_comfirm}
        />
      </XBoxShadow>
    </BbkHalfPageModal>
  );
};

export default CalendarPriceModal;
