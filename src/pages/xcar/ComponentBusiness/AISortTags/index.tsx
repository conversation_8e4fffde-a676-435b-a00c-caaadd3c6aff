import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Styles from './index.module.scss';
import NormalLabel from './NormalLabel';
import LowestPriceLabel from './LowestPriceLabel';
import { IAISortTagProps, IsdAISortTagType } from './Types';

const AISortTags = memo(
  ({
    aiVehicleTags = [],
    isSoldOut = false,
    isCardUserBrowsed = false,
  }: IAISortTagProps) => {
    return (
      <View className={Styles.labelWrap}>
        {aiVehicleTags?.map((tagItem, index) => {
          if (index >= 2) return null;
          const { type, tag, topNum } = tagItem || {};
          if (type === IsdAISortTagType.Lowest) {
            return <LowestPriceLabel labelName={tag} isSoldOut={isSoldOut} />;
          }
          return (
            <NormalLabel
              labelName={tag}
              type={type}
              topNum={topNum}
              isSoldOut={isSoldOut}
              isCardUserBrowsed={isCardUserBrowsed}
            />
          );
        })}
      </View>
    );
  },
);

export default AISortTags;
