@import '../../Common/src/Tokens/tokens/color.scss';

.lowestPriceLabelWrap {
  background-color: $C_FFF5E5;
  padding-left: 8px;
  padding-right: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
  height: 44px;
  border-radius: 4px;
  flex-direction: row;
  align-self: flex-start;
  align-items: center;
  margin-right: 12px;
}
.lowestPriceLabelText {
  color: $C_C24F00;
  font-size: 22px;
  line-height: 32px;
}
.leftImage {
  width: 26px;
  height: 26px;
}
.soldOutBg {
  opacity: 0.5;
  background-color: $C_f5f6fa;
}
.soldOutText {
  color: $C_333333;
}
.fromVendorListImage {
  width: 28px;
  height: 28px;
}
.fromVendorListText {
  font-size: 24px;
  line-height: 34px;
}
