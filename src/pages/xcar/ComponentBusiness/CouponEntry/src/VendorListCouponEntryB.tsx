import React from 'react';
import { XView as View, XViewExposure } from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './vendorListCouponEntryBC2xStyles.module.scss';
import { Utils, CarLog, AppContext } from '../../../Util/Index';
import { UITestID, LogKey } from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';

import Channel from '../../../Util/Channel';
import { IListCouponEntry } from './Types';
import ListCouponItemB from './ListCouponItemB';
import texts from './Texts';

const FlexType = [];
const FlexType1 = [0, 1];
const FlexType2 = [0];
const FlexType3 = [1];

const getCouponExposureData = vehicleCode => ({
  name: '曝光_租车券包',
  pageId: Channel.getPageId().VendorList.ID,
  info: {
    vehicleCode,
  },
});

const VendorListCouponEntryB: React.FC<IListCouponEntry> = ({
  renderData = [],
  isReceiveAble,
  onPress,
  vehicleCode,
}) => {
  if (!renderData.length || !Utils.isCtripIsd()) return null;
  const handlePressBanner = () => {
    onPress();
    if (isReceiveAble) {
      CarLog.LogCode({
        name: '点击_租车券包_领取按钮',

        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_租车券包_领取按钮',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
          vid: AppContext.MarketInfo.vid,
        },
      });
    } else {
      CarLog.LogCode({
        name: '点击_产品详情页_打开券包弹层',

        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
    }
  };
  let flexIndex = FlexType;
  if (renderData?.length === 2) {
    const maxLength = 41;
    const flen = BbkUtils.getCharLength(renderData[0]);
    const slen = BbkUtils.getCharLength(renderData[1]);
    if (flen + slen > maxLength) {
      if (flen > maxLength / 2 && slen > maxLength / 2) {
        flexIndex = FlexType1;
      } else if (flen > maxLength / 2) {
        flexIndex = FlexType2;
      } else {
        flexIndex = FlexType3;
      }
    }
  }
  return (
    <XViewExposure
      testID={CarLog.LogExposure(getCouponExposureData(vehicleCode))}
    >
      <View className={c2xStyles.gradientWrap}>
        <Touchable
          className={c2xStyles.wrap}
          onPress={handlePressBanner}
          testID={UITestID.car_testid_page_vendorList_show_coupon_modal}
        >
          <View className={c2xStyles.content}>
            {renderData.map((item, index) => (
              <ListCouponItemB
                isFlex={flexIndex.includes(index)}
                isFirst={index === 0}
                key={item}
                name={item}
              />
            ))}
          </View>
          <View className={c2xStyles.rightWrap}>
            <BbkText className={c2xStyles.buttonText}>
              {isReceiveAble ? texts.couponEntryButton : texts.couponSeeMore}
            </BbkText>
            <BbkText className={c2xStyles.rightArrow} type="icon">
              {icon.arrowRight}
            </BbkText>
          </View>
        </Touchable>
      </View>
    </XViewExposure>
  );
};

export default VendorListCouponEntryB;
