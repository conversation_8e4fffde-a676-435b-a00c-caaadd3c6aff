import { CSSProperties } from 'react';

import { PointsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import {
  ProductListType,
  VendorPriceListType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import {
  QueryPackageComparisonRequestType,
  QueryPackageComparisonResponseType,
} from '../../../Types/Dto/QueryPackageComparison';
import {
  PriceDaily,
  QueryPriceCalendarRequestType,
} from '../../CalendarPrice/types';
import { INVOKE_FROM } from '../../../Constants/ServerMapping';
import { PageRole } from '../../../Constants/CommonEnums';
import { PageParamType } from '../../../Types/Dto/QueryVehicleDetailListRequestType';

export interface IFeeDetailType {
  chargesInfos: Array<IItemType>; // 分组费用详情
  chargesSummary: IItemType; // 金额信息
  cashBackInfo: IItemType; // 返现
  points: PointsType; // 积分
  offlineFee: IItemType; // 线下购买项
}

export interface IItemType {
  title: string; // 标题
  subTitle?: string; // 副标题
  description?: string; // 描述
  currencyCode?: string;
  currentTotalPrice: number; // 总价
  currentDailyPrice?: number; // 日价
  originDailyPrice?: number; // 优惠前的日均价
  originTotalPrice?: number; // 优惠前的总价
  code?: string;
  items?: Array<IItemDetailType>;
  notices?: Array<string>;
  hourDesc?: string; // 小时费描述
}

export interface IItemDetailType {
  code?: string;
  title: string;
  subTitle?: string;
  description?: string;
  currentTotalPrice?: number;
  priceDailys?: Array<PriceDaily>; // 日历价
  hourDesc?: string; // 小时费描述
  dPriceDesc?: string; // 日均价描述
  isPromotion?: boolean; // 是否是优惠
  showFree?: boolean; // 是否免费
  isPriceDesc?: boolean; // 是否是支付描述，比如："礼品卡￥600 + 支付宝￥73"这类信息
  size?: string;
  desc?: string;
}

export interface IPriceDetailModal {
  visible: boolean; // 弹层是否展示
  data: IFeeDetailType; // 弹层的数据
  title: string; // 弹层的标题
  footerText?: string; // 弹层底部的文案
  totalPriceName?: string; // 总价的文案
  invokeFrom?: INVOKE_FROM; // 来源页面
  footerChildren?: React.ReactNode | React.ReactNode[]; // 自定义底部组件
  requestQuery?: QueryPriceCalendarRequestType; // 日历价查询请求参数
  tipsTestID?: string; // 日历价提示曝光埋点
  useModal?: boolean;
  isFullScreen?: boolean; // 非弹窗展示时是否全屏
  isSupportPriceModal?: boolean; // 是否支持日历价弹窗
  vendorListPageParam?: PageParamType;
  vehicleIndex?: number;
  priceListLen?: number;
  section?: any;
  onClose?: () => void; // 弹层关闭事件
  onContinue?: (
    data: PageParamType,
    vehicleIndex: number,
    priceListLen: number,
    vehicleList?: ProductListType[],
    type?: string,
    section?: any,
  ) => void; // 弹层底部按钮的回调事件
  onContinueSecretBoxBook?: (
    uniqueCode: string,
    vehicleCode?: string,
    vendorPriceInfo?: any,
  ) => void;
  style?: CSSProperties; // 弹层style
  role?: PageRole;
  pageRef?: any;
  vehicleList?: ProductListType[];
  uniqueCode?: string;
  vehicleCode?: string;
  type?: string;
  vendorPriceInfo?: any;
  isShowEasyLifeCompare?: boolean;
  isEasyLife2024?: boolean;
  soldOutList?: Array<string>;
  vehicleSoldOutList?: Array<string>;
  setOrderModalsVisible?: (data: {
    [key: string]: { [key: string]: boolean };
  }) => void;
  packageCompareRequest?: Partial<QueryPackageComparisonRequestType>;
  packageComparisonData?: {
    status: 'pending' | 'success' | 'error';
    response: QueryPackageComparisonResponseType;
  };
  onRetryPackageComparison?: (
    request?: Partial<QueryPackageComparisonRequestType>,
  ) => void;
  vendorPriceList?: Array<VendorPriceListType>;
  goToBookingEasyLife2024?: (
    data: {
      vendorPriceList: Array<VendorPriceListType>;
    },
    section?: any,
  ) => void;
  logBaseInfo?: any;
}
