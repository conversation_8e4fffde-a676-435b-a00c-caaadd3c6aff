import StyleSheet from '@c2x/apis/StyleSheet';
import React, { PureComponent } from 'react';
import {
  XView as View,
  XBoxShadow,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import memoizeOne from 'memoize-one';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  font,
  layout,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/NumberText';
import BbkButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import CurrencySymbol from '@ctrip/rn_com_car/dist/src/Shark/src/CurrencySymbol';
import { validateIsSoldOut } from '../../../State/List/VehicleListMappers';
import c2xStyles from './priceDetailModalC2xStyles.module.scss';
import BbkHalfPageModal from '../../HalfPageModal';
import { CalendarPriceModalContextProvider } from '../../CalendarPrice/CalendarPriceContext';
import CalendarPriceModal from '../../CalendarPrice/CalendarPriceModal';
import AutoScrollView from '../../../Components/AutoScrollView/AutoScrollView';
import { FEE_CODES } from '../../../Constants/ServerMapping';
import { PageRole } from '../../../Constants/CommonEnums';
import { IntegralModal, DepositSubItem } from '../../FeeDetail/src/FeeDetail';
import ChargesSummary from './ChargesSummary';
import UITestId from '../../../Constants/UITestID';
import PriceGroupItem from './PriceGroupItem';
import EasyLifeCompare, { EasyLifeCompareBg } from './EasyLifeCompare';
import { IPriceDetailModal } from './Types';
import { CarLog, GetAB, Utils } from '../../../Util/Index';
import Enums from '../../Common/src/Enums';

const { getPixel, fixIOSOffsetBottom, vw, isIos, adaptNoaNomalousBottom } =
  BbkUtils;
const LimitHeight = 3000;
const styles = StyleSheet.create({
  cashSubTitle: {
    marginTop: getPixel(24),
    color: color.blackBase,
    ...font.caption1LightPlus6Style,
    marginBottom: getPixel(8),
  },
  cashDesc: {
    color: color.grayBase,
  },
  container: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  containerWidth: {
    width: vw(100),
  },
  scrollViewContainer: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingBottom: getPixel(34),
  },
  easyLife2024Container: {
    paddingLeft: getPixel(0),
    paddingRight: getPixel(0),
  },
  easyLife2024Bg: {
    backgroundColor: color.white,
    borderTopLeftRadius: BbkUtils.getPixel(30, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(30, 'floor'),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(32),
    marginTop: getPixel(28),
  },
  bgTransparent: {
    backgroundColor: color.transparent,
  },
  fullScreenContainer: {
    paddingBottom: 0,
  },
  itemTitleText: {
    color: color.fontPrimary,
    ...font.subTitle1BoldStyle,
  },
  itemTitleTextNew: {
    color: color.fontPrimary,
    ...font.body3BoldStyle,
  },
  totalPriceText: {
    color: color.orangePrice,
    ...font.F_34_10_regular_TripNumberSemiBold,
    marginTop: getPixel(4),
  },
  totalPriceNewText: {
    color: color.deepBlueBase,
    top: getPixel(isIos ? 0 : 3),
    ...font.F_34_10_regular_TripNumberSemiBold,
  },
  totalCurrencyStyle: {
    color: color.orangePrice,
    marginTop: getPixel(12),
    ...font.labelLBoldStyle,
  },
  priceWrap: {
    top: getPixel(-2),
  },
  totalCurrencyNewStyle: {
    color: color.deepBlueBase,
    marginTop: getPixel(8),
    ...font.labelLBoldStyle,
  },
  extraWrap: {
    borderTopColor: color.darkGrayBorder,
    borderTopWidth: StyleSheet.hairlineWidth,
    paddingTop: getPixel(26),
    paddingBottom: getPixel(26),
    marginTop: getPixel(40),
  },
  alignEnd: {
    alignItems: 'flex-end',
  },
  footerWrap: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(16),
    paddingBottom:
      fixIOSOffsetBottom() + getPixel(16) + adaptNoaNomalousBottom(),

    backgroundColor: color.white,
  },
  footer: {
    paddingTop: getPixel(23),
    paddingBottom: getPixel(23),
    borderRadius: getPixel(12),
  },
  footerText: {
    fontSize: getPixel(34),
  },
  mt24: { marginTop: getPixel(24) },
  mt32: { marginTop: getPixel(32) },
  priceColorText: {
    color: color.orangePrice,
  },
});

interface IState {
  integralModalVisible: boolean;
}

const PriceInner = ({
  isShowEasyLifeCompare,
  style,
  children,
}: {
  isShowEasyLifeCompare?: boolean;
  style: any;
  children: React.ReactNode;
}) => {
  return isShowEasyLifeCompare ? (
    <View style={style}>{children}</View>
  ) : (
    <>{children}</>
  );
};

class PriceDetailModal extends PureComponent<IPriceDetailModal, IState> {
  constructor(props) {
    super(props);
    this.state = {
      integralModalVisible: false,
    };
  }

  getMaxHeight = () => {
    const { useModal = true, isShowEasyLifeCompare } = this.props;
    if (isShowEasyLifeCompare) {
      return (
        BbkUtils.vh(80) -
        fixIOSOffsetBottom() -
        getPixel(120) -
        adaptNoaNomalousBottom()
      );
    }
    if (!useModal) {
      return LimitHeight;
    }
    const footerHeight =
      fixIOSOffsetBottom() + getPixel(120) + adaptNoaNomalousBottom(); // 120是底部组件高度
    const headerHeight = getPixel(88); // 弹层头部高度
    return BbkUtils.vh(80) - footerHeight - headerHeight; // UED定义弹窗高度最高占屏幕80%
  };

  onContinue = () => {
    const {
      onContinue = Utils.noop,
      onContinueSecretBoxBook = Utils.noop,
      vendorListPageParam,
      vehicleIndex,
      priceListLen,
      vehicleList,
      type,
      uniqueCode,
      vehicleCode,
      section,
      vendorPriceInfo,
      vendorPriceList,
      isEasyLife2024,
      soldOutList,
      vehicleSoldOutList,
      goToBookingEasyLife2024,
    } = this.props;
    const isSoldOut = validateIsSoldOut(
      soldOutList,
      vehicleSoldOutList,
      section,
    );
    if (isSoldOut) {
      return;
    }
    if (isEasyLife2024) {
      goToBookingEasyLife2024({ vendorPriceList }, section);
      return;
    }
    if (type === Enums.TotalPriceModalType.SecretBoxVendor) {
      onContinueSecretBoxBook(uniqueCode, vehicleCode, vendorPriceInfo);
    } else {
      onContinue(
        vendorListPageParam,
        vehicleIndex,
        priceListLen,
        vehicleList,
        type,
        section,
      );
    }
  };

  getLogKey = () => {
    let logKey;
    const { role } = this.props;
    switch (role) {
      case PageRole.BOOKING:
        logKey = '点击_填写页_费用明细_小时费收费规则';
        break;
      case PageRole.ORDERDETAIL:
        logKey = '点击_订单详情页_小时费收费规则';
        break;
      default:
    }
    return logKey;
  };

  setIntegralModalVisible = visible => {
    this.setState({
      integralModalVisible: visible,
    });
  };

  getGroupDetailTitlePressMap = memoizeOne(setOrderModalsVisible => {
    return {
      [FEE_CODES.PENALTY_EARLY_RETURN]: () =>
        setOrderModalsVisible({
          advanceReturnFeeModal: { visible: true },
        }),
    };
  });

  render() {
    const {
      visible,
      title,
      data,
      onClose = Utils.noop,
      footerText,
      requestQuery,
      tipsTestID,
      invokeFrom,
      footerChildren,
      useModal = true,
      style,
      role,
      isFullScreen = true,
      isSupportPriceModal = true,
      setOrderModalsVisible,
      isShowEasyLifeCompare,
      isEasyLife2024,
      packageCompareRequest,
      packageComparisonData,
      onRetryPackageComparison,
      logBaseInfo,
    } = this.props;
    const { integralModalVisible } = this.state;
    const pageModalProps = {
      visible,
      onMaskPress: () => onClose(),
      zIndex: 2000,
      style,
    };
    const modalHeaderProps = {
      title,
      hasBottomBorder: false,
      style: isShowEasyLifeCompare && styles.bgTransparent,
    };
    const chargesInfos = data?.chargesInfos || [];
    const chargesSummary = data?.chargesSummary;
    const cashBackInfo = data?.cashBackInfo;
    const points = data?.points;
    const offlineFee = data?.offlineFee;
    const Wrapper = useModal ? BbkHalfPageModal : View;
    // 非弹窗调用时，日历价弹窗不能全屏，需要在外层引用弹窗Provider以及日历价弹窗组件
    const Provider = isSupportPriceModal
      ? CalendarPriceModalContextProvider
      : View;
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    const isOrderPolicy = role === PageRole.ORDERDETAIL;
    const itemTitleStyle = isSupportPriceModal
      ? styles.itemTitleText
      : styles.itemTitleTextNew;

    return (
      <Provider tipsLogKey={this.getLogKey()} isOrderPolicy={isOrderPolicy}>
        <Wrapper
          pageModalProps={pageModalProps}
          modalHeaderProps={modalHeaderProps}
          useCustomPageModal={true}
          contentStyle={{
            ...styles.container,
            ...(isShowEasyLifeCompare ? styles.bgTransparent : {}),
          }}
          testID={UITestId.car_testid_comp_price_modal}
          closeModalBtnTestID={UITestId.car_testid_comp_price_modal_close_mask}
          bgDom={isShowEasyLifeCompare && <EasyLifeCompareBg />}
        >
          <AutoScrollView
            // 非模式弹窗时，设置最大高度上限不允许滚动
            maxHeight={this.getMaxHeight()}
            bounces={!isShowEasyLifeCompare}
            // @ts-ignore
            contentContainerStyle={xMergeStyles([
              styles.scrollViewContainer,
              !useModal && isFullScreen && styles.containerWidth,
              !isFullScreen && styles.fullScreenContainer,
              isShowEasyLifeCompare && styles.easyLife2024Container,
            ])}
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_费用明细',

              info: logBaseInfo,
            })}
          >
            {isShowEasyLifeCompare && (
              <EasyLifeCompare
                request={packageCompareRequest}
                data={packageComparisonData}
                onRetry={onRetryPackageComparison}
              />
            )}
            <PriceInner
              isShowEasyLifeCompare={isShowEasyLifeCompare}
              style={styles.easyLife2024Bg}
            >
              {chargesInfos.map((groupItem, index) => (
                <PriceGroupItem
                  key={groupItem.title}
                  groupItem={groupItem}
                  index={index}
                  itemTitleStyle={itemTitleStyle}
                  invokeFrom={invokeFrom}
                  tipsTestID={tipsTestID}
                  requestQuery={requestQuery}
                  isFullScreen={isFullScreen}
                  titlePressFunMap={this.getGroupDetailTitlePressMap(
                    setOrderModalsVisible,
                  )}
                />
              ))}
              {/** 订单总额 */}
              {chargesSummary && (
                <View style={xMergeStyles([styles.extraWrap, styles.alignEnd])}>
                  {/** 总额划价 */}
                  {!!chargesSummary?.originTotalPrice && (
                    <BbkText
                      className={classNames(
                        c2xStyles.priceText,
                        c2xStyles.totalPriceOrigin,
                      )}
                    >
                      {`${CurrencySymbol.RMB}${chargesSummary.originTotalPrice}`}
                    </BbkText>
                  )}
                  {/** 总额详情 */}
                  {chargesSummary?.currentTotalPrice >= 0 && (
                    <View
                      style={layout.flexRow}
                      testID={UITestId.car_testid_comp_price_modal_total_price}
                    >
                      {!!chargesSummary.title && (
                        <BbkText
                          className={c2xStyles.totalPriceTitleText}
                          fontWeight="bold"
                        >
                          {chargesSummary.title}
                        </BbkText>
                      )}
                      <BbkCurrencyFormatter
                        wrapperStyle={isISDInterestPoints && styles.priceWrap}
                        currency={CurrencySymbol.RMB}
                        price={chargesSummary.currentTotalPrice}
                        currencyStyle={
                          isISDInterestPoints && !isEasyLife2024
                            ? styles.totalCurrencyNewStyle
                            : styles.totalCurrencyStyle
                        }
                        priceStyle={
                          isISDInterestPoints && !isEasyLife2024
                            ? styles.totalPriceNewText
                            : styles.totalPriceText
                        }
                        isNew={true}
                      />
                    </View>
                  )}
                  {/** 支付信息 */}
                  {chargesSummary?.items && (
                    <ChargesSummary items={chargesSummary?.items} />
                  )}
                </View>
              )}
              {(cashBackInfo || points) && (
                <View className={c2xStyles.separateLine} />
              )}
              {/** 返现 */}
              {(cashBackInfo || points) && (
                <View className={c2xStyles.otherExtraWrap}>
                  {!!cashBackInfo?.title && (
                    <BbkText style={itemTitleStyle}>
                      {cashBackInfo.title}
                    </BbkText>
                  )}
                  {!!cashBackInfo?.subTitle &&
                    cashBackInfo?.currentTotalPrice && (
                      <View style={layout.betweenHorizontal}>
                        <BbkText
                          style={xMergeStyles([
                            font.caption1LightStyle,
                            styles.cashSubTitle,
                          ])}
                        >
                          {cashBackInfo.subTitle}
                        </BbkText>
                        <BbkText className={c2xStyles.priceColorText}>
                          {`${CurrencySymbol.RMB}${cashBackInfo.currentTotalPrice}`}
                        </BbkText>
                      </View>
                    )}

                  {!!cashBackInfo?.description && (
                    <BbkText
                      style={xMergeStyles([
                        font.caption1LightStyle,
                        styles.cashDesc,
                      ])}
                    >
                      {cashBackInfo.description}
                    </BbkText>
                  )}

                  {cashBackInfo?.notices &&
                    cashBackInfo.notices.map(noticeItem => (
                      <BbkText
                        key={noticeItem}
                        className={c2xStyles.subDescText}
                        fontWeight="bold"
                      >
                        {noticeItem}
                      </BbkText>
                    ))}
                  {/* {积分} */}
                  {points && (
                    <DepositSubItem
                      title={points.title}
                      subTitle={points.subTitle}
                      desc={points.currencyPrice}
                      tip={points.pointsTip}
                      notice={points.pointsNotice}
                      setIntegralModalVisible={this.setIntegralModalVisible}
                      style={cashBackInfo && styles.mt24}
                      subTitleStyle={styles.cashSubTitle}
                      subTextRightStyle={styles.priceColorText}
                    />
                  )}
                </View>
              )}
              {offlineFee && <View className={c2xStyles.separateLine} />}
              {/* 线下购买项 */}
              {offlineFee && (
                <PriceGroupItem
                  groupItem={offlineFee}
                  wrapStyle={styles.mt32}
                  itemTitleStyle={itemTitleStyle}
                  invokeFrom={invokeFrom}
                  tipsTestID={tipsTestID}
                  requestQuery={requestQuery}
                  isFullScreen={isFullScreen}
                />
              )}
            </PriceInner>
          </AutoScrollView>

          {!!footerText && isSupportPriceModal ? (
            <XBoxShadow
              style={styles.footerWrap}
              coordinate={{ x: 0, y: -2 }}
              color="rgba(0, 0, 0, 0.06)"
              opacity={1}
              blurRadius={getPixel(9)}
            >
              <BbkButton
                buttonStyle={styles.footer}
                textStyle={styles.footerText}
                text={footerText}
                colorType={tokenType.ColorType.DeepBlue}
                buttonType={tokenType.ButtonType.Default}
                onPress={this.onContinue}
                testID={UITestId.car_testid_comp_price_modal_next}
                buttonSize="L"
              />
            </XBoxShadow>
          ) : (
            useModal && (
              <XBoxShadow
                className={c2xStyles.shadowCut}
                coordinate={{ x: 0, y: -2 }}
                color="rgba(0, 0, 0, 0.6)"
                opacity={1}
                blurRadius={getPixel(9)}
                elevation={9}
              />
            )
          )}
          {footerChildren}
        </Wrapper>
        {isSupportPriceModal && <CalendarPriceModal />}
        {points?.pointsNotice?.length > 0 && (
          <IntegralModal
            data={points.pointsNotice}
            visible={integralModalVisible}
            onClose={this.setIntegralModalVisible}
          />
        )}
      </Provider>
    );
  }
}

export default PriceDetailModal;
