import { groupBy as lodashGroupBy, maxBy as lodashMaxBy } from 'lodash-es';
/* eslint-disable */

/* bbk-component-business-migrate */
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, memo, CSSProperties } from 'react';
import { XView as View, xMergeStyles, xClassNames, XViewExposure } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  font,
  space,
  tokenType,
  layout,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkComponentLabel, {
  Props as LabelProps,
  BbkSplit,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import BBkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BaseTheme } from '@ctrip/rn_com_car/dist/src/Theming/src/BaseTheme';
import { GetABCache, Utils } from '../../../Util/Index';
import UITestId from '../../../Constants/UITestID';
import c2xStyles from './carVehicleDescribe.module.scss';

const { getPixel, lazySelector } = BbkUtils;

export interface Props {
  items: LabelProps[];
  horizontal?: boolean;
  iconColor?: string;
  wrapStyle?: CSSProperties;
  theme?: BaseTheme;
  isInner?: boolean;
  textColor?: string;
  showSplit?: boolean;
  textStyle?: CSSProperties;
  iconStyle?: CSSProperties;
  labelStyle?: CSSProperties;
  splitStyle?: CSSProperties;
  splitTextStyle?: CSSProperties;
  hasTransformStyle?: boolean;
  hasTransformStyle2?: boolean;
  splitText?: string;
}
const style = StyleSheet.create({
  wrap: {
    alignItems: 'flex-start',
  },
  wrapHorizontal: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  labelRow: {
    marginBottom: space.spaceL,
  },
  innerLabelRow: {
    marginBottom: space.spaceS,
    marginRight: getPixel(20),
  },
  mrBottom: {
    marginBottom: getPixel(6),
  },
});

const formatLabelProps = (
  item = {} as any,
  theme,
  iconColor,
  isInner,
  textColor,
  textStyle = {},
  iconStyle,
) => {
  const { icon, ...passThroughProps } = item;
  theme = theme || {};
  const itemTextStyle = isInner
    ? font.caption1LightStyle
    : font.body3LightStyle;

  if (Utils.isCtripIsd()) {
    return {
      theme: {
        [tokenType.ColorType.Blue]: {
          labelBgColor: color.transparent,
          labelColor: iconColor || theme.iconPrimaryColor || color.blueBase,
        },
      },
      textStyle: {
        ...itemTextStyle,
        color: textColor || theme.fontPrimaryColor || color.fontPrimary,
        ...textStyle,
      },
      labelStyle: {
        paddingLeft: 0,
        paddingRight: 0,
      },
      labelSize: 'XL',
      ...passThroughProps,
    };
  } else {
    return {
      theme: {
        [tokenType.ColorType.Blue]: {
          labelBgColor: color.transparent,
          labelColor: iconColor || theme.iconPrimaryColor || color.blueBase,
        },
      },
      textStyle: {
        ...itemTextStyle,
        color: textColor || theme.fontPrimaryColor || color.fontPrimary,
        ...textStyle,
      },
      labelSize: 'L',
      icon: {
        iconType: 'primary',
        ...icon,
        iconStyle: {
          fontSize: isInner ? getPixel(28) : getPixel(32),
          ...icon?.iconStyle,
          ...iconStyle,
        },
      },
      ...passThroughProps,
    };
  }
};

const renderDescItems = (props: Props) => {
  const {
    items = [],
    horizontal,
    iconColor,
    theme,
    isInner,
    textColor,
    showSplit = true,
    textStyle,
    iconStyle,
    labelStyle,
    splitStyle,
    splitTextStyle,
    hasTransformStyle,
    hasTransformStyle2,
    splitText = '/',
  } = props;
  const len = items.length;
  const layouts = [];

  const [lastItems, setLastItems] = useState(null);

  const calcLayout = (e, index) => {
    layouts.push({ ...e.nativeEvent.layout, index });

    if (layouts.length !== len) return false;

    const layoutMap = lodashGroupBy(layouts, 'y');

    const targetItem = [];

    for (const key in layoutMap) {
      const lastItem = lodashMaxBy(layoutMap[key], 'x');
      if (layoutMap.hasOwnProperty(key) && lastItem) {
        targetItem.push(lastItem.index);
      }
    }
    setLastItems(targetItem);
  };

  const isRemoveSplit = index => lastItems && lastItems.indexOf(index) !== -1;


  return items.map((item, index) => {
    if (!item) return;
    const labelProps = formatLabelProps(
      item,
      theme,
      iconColor,
      isInner,
      textColor,
      textStyle,
      iconStyle,
    );
    return (
      <View
        key={`${item.text}-${index}`}
        onLayout={e => calcLayout(e, index)}
        testID={UITestId.car_testid_comp_vehicle_desc}
        style={xMergeStyles([
          layout.flexRow,
          layout.rowStart,
          labelStyle,
          item.labelStyle,
        ])}
      >
        <BbkComponentLabel
          labelStyle={xMergeStyles([
            isInner && !showSplit ? style.innerLabelRow : style.labelRow,
            { paddingLeft: 0, paddingRight: 0 },
            labelStyle,
          ])}
          {...labelProps}
          key={`${item.text}-${index}`}
        />

        {lazySelector(showSplit && horizontal && index < len - 1, () =>
          Utils.isCtripIsd() ? (
            <View
              className={xClassNames(
                hasTransformStyle && c2xStyles.rotatef15,
                hasTransformStyle2 && c2xStyles.rotate2f15,
              )}
              style={xMergeStyles([
                splitStyle,
                GetABCache.isISDListCard() && {
                  opacity: isRemoveSplit(index) ? 0 : 1,
                },
              ])}
            >
              <BBkText
                style={xMergeStyles([
                  {
                    ...font.caption1LightStyle,
                    color: color.fontGrayBlue,
                  },
                  splitTextStyle,
                ])}
              >
                {splitText}
              </BBkText>
            </View>
          ) : (
            <BbkSplit
              style={xMergeStyles([
                {
                  opacity: isRemoveSplit(index) ? 0 : 1,
                },
                splitStyle,
              ])}
              hasTransformStyle={hasTransformStyle}
              hasTransformStyle2={hasTransformStyle2}
            />
          ),
        )}
      </View>
    );
  });
};

const VehicleDesc = (props: Props) => {
  const { horizontal, wrapStyle } = props;
  const descItems = renderDescItems(props);
  return (
    <XViewExposure
      style={
        horizontal
          ? Utils.getPackageStyle([style.wrapHorizontal, style.mrBottom])
          : Utils.getPackageStyle([style.wrap, wrapStyle])
      }
    >
      {descItems}
    </XViewExposure>
  );
};

const isEqual = (prevProps, nextProps) => {
  if (JSON.stringify(prevProps.items) !== JSON.stringify(nextProps.items)) {
    return false;
  }
  if (prevProps.iconColor !== nextProps.iconColor) {
    return false;
  }
  if (prevProps.textColor !== nextProps.textColor) {
    return false;
  }
  return true;
};

export default memo(withTheme(VehicleDesc), isEqual);
