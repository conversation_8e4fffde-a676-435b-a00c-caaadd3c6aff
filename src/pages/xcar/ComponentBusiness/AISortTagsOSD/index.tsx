import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Styles from './index.module.scss';
import NormalLabel from './NormalLabel';
import { IAISortTagProps, OsdAISortTagType } from './Types';
import BlackDiamondLabel from './BlackDiamondLabel';

const AISortTagsOSD = memo(
  ({
    aiVehicleTags = [],
    isSoldOut = false,
    isCardUserBrowsed = false,
  }: IAISortTagProps) => {
    return (
      <View className={Styles.labelWrap}>
        {aiVehicleTags?.map((tagItem, index) => {
          if (index >= 2) return null;
          const { type, tag, subTag } = tagItem || {};
          if (type === OsdAISortTagType.DiamondPlus) {
            return <BlackDiamondLabel labelName={tag} isSoldOut={isSoldOut} />;
          }
          return (
            <NormalLabel
              labelName={tag}
              type={type}
              isSoldOut={isSoldOut}
              isCardUserBrowsed={isCardUserBrowsed}
              subTag={subTag}
            />
          );
        })}
      </View>
    );
  },
);

export default AISortTagsOSD;
