import React, { memo } from 'react';
import {
  XView as View,
  XImage as Image,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import Styles from './normalLabel.module.scss';
import { INormalLabelProps, OsdAISortTagType } from './Types';
import { ImageUrl } from '../../Constants/Index';
import { BbkUtils } from '../../Common/src/Utils';

const { getPixel, isAndroid } = BbkUtils;

const NormalLabel = memo(
  ({
    labelName,
    isSoldOut,
    type,
    isCardUserBrowsed,
    subTag,
  }: INormalLabelProps) => {
    const hasLeftImage =
      type === OsdAISortTagType.LocalHot ||
      type === OsdAISortTagType.FamilyPreference ||
      type === OsdAISortTagType.Rented;

    const getLeftImage = useMemoizedFn(() => {
      if (
        type === OsdAISortTagType.LocalHot ||
        type === OsdAISortTagType.FamilyPreference
      ) {
        return isSoldOut
          ? `${ImageUrl.DIMG04_PATH}1tg3l12000m13nw1d274D.png`
          : `${ImageUrl.DIMG04_PATH}1tg1f12000m13o0663B55.png`;
      }
      if (type === OsdAISortTagType.Rented) {
        return isSoldOut
          ? `${ImageUrl.DIMG04_PATH}1tg3b12000m13nzyt17C5.png`
          : `${ImageUrl.DIMG04_PATH}1tg0k12000m13nuwl5553.png`;
      }
      return null;
    });

    if (!labelName) {
      return null;
    }

    return (
      <View
        className={classNames(
          Styles.normalWrap,
          isSoldOut && Styles.soldOutBg,
          hasLeftImage && Styles.hasLeftImage,
          isCardUserBrowsed && Styles.userBrowsed,
        )}
        key={`${labelName}_${type}`}
      >
        {hasLeftImage && (
          <Image src={getLeftImage()} className={Styles.leftImage} />
        )}
        <Text
          className={Styles.normalText}
          style={{
            marginTop: isAndroid ? getPixel(-1) : getPixel(0),
          }}
        >
          {labelName}
        </Text>
        {!!subTag && (
          <Text
            className={Styles.topNum}
            style={{
              marginTop: isAndroid ? getPixel(-1) : getPixel(0),
            }}
          >
            {subTag}
          </Text>
        )}
      </View>
    );
  },
);

export default NormalLabel;
