@import '../../Common/src/Tokens/tokens/color.scss';

.normalWrap {
  background-color: $C_f5f6fa;
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
  height: 44px;
  border-radius: 4px;
  flex-direction: row;
  align-self: flex-start;
  align-items: center;
  margin-right: 12px;
}
.normalText {
  color: $C_333333;
  font-size: 22px;
  line-height: 32px;
}
.topNum {
  @extend .normalText;
  margin-left: 4px;
}
.leftImage {
  width: 26px;
  height: 26px;
  margin-right: 2px;
}
.hasLeftImage {
  padding-left: 8px;
}
.soldOutBg {
  opacity: 0.5;
}
.userBrowsed {
  background-color: $C_EDF2F7;
}
