export enum OsdAISortTagType {
  Rented = 1, // 曾租车型
  Diamond = 2, // 钻石用户优选
  FamilyPreference = 3, // 家庭优选
  LocalHot = 4, // 当地热销
  DiamondPlus = 5, // 黑钻/钻石+用户优选
  Viewed = 6, // 浏览过
}

export interface IAISortTagItemOsd {
  type: OsdAISortTagType;
  tag: string;
  subTag?: string;
}

export interface IAISortTagProps {
  aiVehicleTags?: IAISortTagItemOsd[];
  isSoldOut?: boolean;
  isCardUserBrowsed?: boolean;
}


export interface INormalLabelProps {
  labelName?: string;
  isSoldOut?: boolean;
  type?: OsdAISortTagType;
  isCardUserBrowsed?: boolean;
  subTag?: string;
}
