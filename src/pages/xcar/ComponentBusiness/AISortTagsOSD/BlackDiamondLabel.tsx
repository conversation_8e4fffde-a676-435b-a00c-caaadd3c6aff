import React, { memo } from 'react';
import {
  xClassNames as classNames,
  XImage as Image,
  XLinearGradient,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Styles from './blackDiamondLabel.module.scss';
import { ImageUrl } from '../../Constants/Index';
import { BbkUtils } from '../../Common/src/Utils';

const { getPixel, isAndroid } = BbkUtils;

const BlackDiamondLabel = memo(
  ({ labelName, isSoldOut }: { labelName: string; isSoldOut: boolean }) => {
    if (!labelName) {
      return null;
    }
    return (
      <XLinearGradient
        className={classNames(
          Styles.blackDiamondLabelWrap,
          isSoldOut && Styles.soldOutBg,
        )}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={
          isSoldOut
            ? [color.C_F5F6FA, color.C_F5F6FA]
            : [color.C_FFF3D8, color.C_FFE6BC]
        }
        locations={[0, 1]}
      >
        <Image
          src={
            isSoldOut
              ? `${ImageUrl.DIMG04_PATH}1tg4a12000m6c67seA5CB.png`
              : `${ImageUrl.DIMG04_PATH}1tg2b12000m6c6aq8F80A.png`
          }
          className={Styles.leftImage}
        />
        <Text
          className={classNames(
            Styles.blackDiamondLabelText,
            isSoldOut && Styles.soldOutText,
          )}
          style={{
            marginTop: isAndroid ? getPixel(-1) : getPixel(0),
          }}
        >
          {labelName}
        </Text>
      </XLinearGradient>
    );
  },
);

export default BlackDiamondLabel;
