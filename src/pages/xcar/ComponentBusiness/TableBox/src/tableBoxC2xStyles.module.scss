@import '../../../Common/src/Tokens/tokens/color.scss';

.tableNew {
  border-radius: 8px;
  background-color: $tableBackgroundColor;
}
.trLeft {
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  align-self: stretch;
  position: relative;
}
.trLeft2New {
  width: 346px;
}
.trLeftTextSubNew {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $grayBase;
}
.trRightNew {
  flex: 1;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  align-self: stretch;
}
.greatIcon {
  font-size: 28px;
  line-height: 72px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $successGreen;
  margin-left: 11px;
  margin-top: -2px;
}
.formulaWrap {
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  border-width: 1px;
  border-color: $newBookingTableLine;
}
.formulaText {
  font-size: 24px;
  line-height: 34px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.tableItemWrap {
  flex-direction: row;
}
.tableKeyWrap {
  flex: 1;
  border-left-width: 1px;
  border-top-width: 1px;
  border-left-color: $newBookingTableLine;
  border-top-color: $newBookingTableLine;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  flex-direction: row;
  align-items: center;
}
.tableText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.tableValueWrap {
  flex: 1;
  border-left-width: 1px;
  border-top-width: 1px;
  border-right-width: 1px;
  border-left-color: $newBookingTableLine;
  border-top-color: $newBookingTableLine;
  border-right-color: $newBookingTableLine;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
.tableGreatIcon {
  font-size: 32px;
  color: $greenBase;
}
.depositTrLeft {
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  align-self: stretch;
  position: relative;
  width: 192px;
}
.depositText {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.greenTextNew {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $refundSuccess;
  margin-left: 8px;
}
.notice {
  color: $grayBase;
  margin-top: 16px;
}
.trLeftNew {
  flex: 1;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 16px;
  align-self: stretch;
  position: relative;
}
.depositTextWrap {
  align-items: flex-start;
  justify-content: center;
  flex: 1;
}
