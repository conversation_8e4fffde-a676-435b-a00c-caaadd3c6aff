import { PackageDetailListType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import { IInsuranceCode } from '../../../Types/Dto/QueryProductInfoType';

/**
 * 根据保险类型过滤数据的 Mapper 函数
 * @param data 原始数据
 * @param showServiceDetailCode 保险类型代码
 * @param isOnlyShowCurrentIns 是否只显示当前保险
 * @returns 过滤后的数据
 */
const filterDataByInsuranceType = (
  data: PackageDetailListType[],
  showServiceDetailCode?: string,
  isOnlyShowCurrentIns?: boolean,
): PackageDetailListType[] => {
  if (!data?.length || !isOnlyShowCurrentIns) {
    return data;
  }

  // 基础保险是必有的
  const baseInsurance = data.filter(
    item => item.uniqueCode === IInsuranceCode.BAS,
  );

  // 如果没有指定保险类型，返回所有数据
  if (!showServiceDetailCode) {
    return data;
  }

  // 根据选择的保险类型添加对应的保险
  switch (showServiceDetailCode) {
    case IInsuranceCode.BAS: // 基础
      // 只显示基础信息
      return baseInsurance;

    case IInsuranceCode.ADV: {
      // 优享
      // 显示基础 + 优享
      const advInsurance = data.filter(
        item => item.uniqueCode === IInsuranceCode.ADV,
      );
      return [...baseInsurance, ...advInsurance];
    }

    case IInsuranceCode.PRE: {
      // 尊享
      // 显示基础 + 尊享
      const preInsurance = data.filter(
        item => item.uniqueCode === IInsuranceCode.PRE,
      );
      return [...baseInsurance, ...preInsurance];
    }

    default:
      return data;
  }
};

export default filterDataByInsuranceType;
