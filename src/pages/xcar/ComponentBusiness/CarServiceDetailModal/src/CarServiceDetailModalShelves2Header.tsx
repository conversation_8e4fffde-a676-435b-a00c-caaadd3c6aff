import React, { memo, useMemo } from 'react';
import { XView as View, XLinearGradient } from '@ctrip/xtaro';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import StyleSheet from '@c2x/apis/StyleSheet';
import BbkHorizontalNav, {
  BbkHorizontalNavItem,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/HorizontalNav';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { GetAB } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import c2xStyles from './carServiceDetailModalC2xStylesShelves2.module.scss';
import filterDataByInsuranceType from './mapper';

interface IProps {
  selectedId?: string;
  data?: any;
  changeSelectedId?: (selectedId: string, index: number) => void;
  showServiceDetailCode?: string;
  /**
   * 是否只显示当前保险
   */
  isOnlyShowCurrentIns?: boolean;
}

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  navWrap: {
    height: getPixel(88),
    flex: 1,
    borderBottomWidth: 0,
  },
  tab: {
    height: getPixel(88),
    flex: 1,
  },
  textStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontSecondary,
  },
  textSelectedStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontPrimary,
  },
});

// eslint-disable-next-line import/prefer-default-export
export const CarServiceDetailModalShelves2Header = memo((props: IProps) => {
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  const {
    selectedId,
    data,
    changeSelectedId,
    showServiceDetailCode,
    isOnlyShowCurrentIns,
  } = props;

  // 使用 mapper 根据保险类型过滤数据
  const filteredData = useMemo(() => {
    return filterDataByInsuranceType(
      data,
      showServiceDetailCode,
      isOnlyShowCurrentIns,
    );
  }, [data, showServiceDetailCode, isOnlyShowCurrentIns]);

  const isMoreItme = useMemo(() => {
    return filteredData.length >= 2;
  }, [filteredData]);

  return (
    <View
      testID={UITestID.car_testid_comp_booking_ins_detail_tab}
      className={c2xStyles.shadowTop}
    >
      <BbkHorizontalNav
        style={styles.navWrap}
        indicatorWidth={getPixel(120)}
        indicatorHeight={getPixel(4)}
        indicatorColor={isISDInterestPoints ? color.deepBlueBase : ''}
        selectedId={selectedId}
        animateIndicator={isMoreItme}
        myIndicator={
          <XLinearGradient
            start={{ x: 0.0, y: 1 }}
            end={{ x: 1.0, y: 1 }}
            locations={[0, 1]}
            colors={['rgb(65, 165, 249)', 'rgb(0, 111, 246)']}
            style={{
              width: getPixel(120),
              height: getPixel(4),
            }}
          />
        }
      >
        {filteredData?.map((item, index) => (
          <BbkHorizontalNavItem
            key={item.uniqueCode}
            id={item.uniqueCode}
            title={item.name}
            style={styles.tab}
            textStyle={styles.textStyle}
            testID={`${UITestID.car_testid_page_booking_carservicedetail_modal_nav_item}_${item.name}`}
            textSelectedStyle={styles.textSelectedStyle}
            onPress={() => changeSelectedId(item.uniqueCode, index)}
            isShowIndicator={isMoreItme}
          />
        ))}
      </BbkHorizontalNav>
    </View>
  );
});
