import { findLast as lodashFindLast } from 'lodash-es';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet'; /* eslint-disable no-return-assign */
import React, { CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
/* eslint-disable no-return-assign */
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  PackageDetailListType,
  PurchasingNoticeType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import SizeableComponent from '@ctrip/rn_com_car/dist/src/Components/Basic/SizeableComponent';
import CarServiceDetailItem from './CarServiceDetailItem';
import { Utils } from '../../../Util/Index';
import { texts } from './Texts';
import { ProductType } from '../../ValueAddedService';
import BbkHalfPageModal from '../../HalfPageModal';
import { UITestID } from '../../../Constants/Index';
import c2xStyles from './carServiceDetailModalC2xStylesShelves2.module.scss';
import { CarServiceDetailModalShelves2Header } from './CarServiceDetailModalShelves2Header';
import filterDataByInsuranceType from './mapper';

const { vw, getPixel, vh } = BbkUtils;

export const styles = StyleSheet.create({
  navWrap: {
    height: getPixel(88),
    flex: 1,
    borderBottomWidth: 0,
  },
  textStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontSecondary,
  },
  textSelectedStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontPrimary,
  },
  leftIconStyle: {
    color: color.fontSecondary,
  },
  headerWrap: {
    height: getPixel(96),
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    position: 'relative',
    zIndex: 2,
    borderBottomWidth: 2,
    borderBottomColor: color.white,
    overflow: 'visible',
  },
  modalTitle: {
    ...font.title4BoldStyle,
    color: color.fontPrimary,
    textAlign: 'center',
    marginTop: getPixel(4),
    backgroundColor: color.transparent,
  },
  tab: {
    height: getPixel(88),
    alignItems: 'center',
    flex: 1,
  },
  modalWrap: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  modalWrapTopRadius: {
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
  },
  shadowTop: {
    shadowOffset: {
      width: 0,
      height: getPixel(4),
    },
    shadowRadius: getPixel(8),
    shadowColor: color.black,
    shadowOpacity: 0.08,
    elevation: 4,
    backgroundColor: color.white,
    paddingTop: getPixel(8),
    marginTop: -getPixel(8),
    position: 'relative',
    zIndex: 1,
    overflow: 'visible',
  },
});

interface IProps {
  visible: boolean;
  onCancel?: any;
  data?: Array<PackageDetailListType>;
  showIndex?: number;
  dayGap?: number;
  purchasingNotice?: PurchasingNoticeType;
  selectedInsuranceIds?: string[];
  changeSelectInsurance?: (data) => void;
  ptime?: string;
  rtime?: string;
  fromPage?: string;
  haveFooter?: boolean;
  showServiceDetailCode?: string;
  modalBgStyle?: CSSProperties;
  /**
   * 是否只显示当前保险
   */
  isOnlyShowCurrentIns?: boolean;
}
interface IState {
  selectedId: string;
  scrollHeight: Array<number>;
  contentWidth: number;
}

export default class CarServiceDetailModal extends SizeableComponent<
  IProps,
  IState
> {
  scrollViewRef;

  lastIndex = undefined;

  constructor(props) {
    super(props);
    this.state = {
      selectedId: '',
      scrollHeight: [],
      contentWidth: vw(100),
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { data, showServiceDetailCode } = nextProps;
    const { contentWidth } = this.state;
    // 使用 mapper 的过滤逻辑
    const filteredData = filterDataByInsuranceType(data, showServiceDetailCode);

    if (nextProps.visible !== this.props.visible && nextProps.visible) {
      const presentInsurance = lodashFindLast(
        filteredData,
        it => it.type === ProductType.present,
      );
      const selectedId =
        showServiceDetailCode ||
        presentInsurance?.uniqueCode ||
        filteredData?.[0]?.uniqueCode;
      this.setState({
        selectedId,
      });
      const index = filteredData?.findIndex(it => it.uniqueCode === selectedId);
      this.lastIndex = index;
      if (index) {
        setTimeout(() => {
          this.scrollViewRef?.scrollTo({
            x: index * contentWidth,
            animated: false,
          });
        });
      }
    }

    if (!this.lastIndex && filteredData?.length > 0) {
      const index = filteredData?.findIndex(
        it => it.uniqueCode === showServiceDetailCode,
      );
      if (index) {
        setTimeout(() => {
          this.scrollViewRef?.scrollTo({
            x: index * contentWidth,
            animated: false,
          });
        });
      }
    }
  }

  changeSelectedId = (selectedId, index) => {
    const { contentWidth } = this.state;

    this.setState({
      selectedId,
    });
    this.scrollViewRef.scrollTo({ x: index * contentWidth });
  };

  onWindowSizeChanged = () => {
    this.setState({
      contentWidth: vw(100),
    });
  };

  render() {
    const {
      visible,
      onCancel = Utils.noop,
      data,
      purchasingNotice,
      modalBgStyle,
      showServiceDetailCode,
      isOnlyShowCurrentIns,
    } = this.props;
    const { selectedId } = this.state;
    const filteredData = filterDataByInsuranceType(
      data,
      showServiceDetailCode,
      isOnlyShowCurrentIns,
    );
    if (!filteredData?.length) return null;
    const disTitle =
      filteredData?.length === 1
        ? `${filteredData[0].name}${texts.details}`
        : `${texts.serviceDetailTitle}${texts.serviceDetailTitleEnd}`;
    const modalHeaderProps = {
      hasTopBorderRadius: true,
      showRightIcon: false,
      showLeftIcon: true,
      leftIconStyle: styles.leftIconStyle,
      onClose: onCancel,
      style: xMergeStyles([
        styles.headerWrap,
        filteredData?.length === 1 && styles.shadowTop,
      ]),
      titleStyle: styles.modalTitle,
      title: disTitle,
    };
    return (
      <BbkHalfPageModal
        pageModalProps={{
          visible,
          onMaskPress: onCancel,
          modalTouchLayerStyle: modalBgStyle,
          style: styles.modalWrapTopRadius,
        }}
        hasBottomBorder={false} // c2x 新增兼容传参
        modalHeaderProps={modalHeaderProps}
        contentStyle={styles.modalWrap}
        closeModalBtnTestID={
          UITestID.car_testid_carservicedetail_modal_closemask
        }
        testID="car_testid_comp_booking_ins_detail_modal"
      >
        <View className={c2xStyles.wrap} style={{ height: vh(75) }}>
          {filteredData?.length > 1 && (
            <CarServiceDetailModalShelves2Header
              selectedId={selectedId}
              data={data}
              changeSelectedId={this.changeSelectedId}
              showServiceDetailCode={showServiceDetailCode}
              isOnlyShowCurrentIns={isOnlyShowCurrentIns}
            />
          )}
          <ScrollView
            ref={ref => (this.scrollViewRef = ref)}
            showsHorizontalScrollIndicator={false}
            horizontal={true}
            scrollEnabled={false}
            scrollEventThrottle={16}
            style={{ backgroundColor: color.selfHelpBg }}
            testID={
              UITestID.car_testid_page_order_detail_car_service_detail_modal
            }
          >
            {filteredData?.map(item => (
              <CarServiceDetailItem
                key={item.name}
                data={item}
                purchasingNotice={purchasingNotice}
              />
            ))}
          </ScrollView>
        </View>
      </BbkHalfPageModal>
    );
  }
}
