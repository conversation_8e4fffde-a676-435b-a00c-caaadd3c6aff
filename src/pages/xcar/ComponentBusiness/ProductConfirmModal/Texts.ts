export const texts = {
  get vehicleDetail() {
    return '详情';
  },
  get attention() {
    return '预订须知';
  },
  get reviews() {
    return '用户评价';
  },
  get guideWay() {
    return '取还方式';
  },
  get vehicleConfig() {
    return '车辆配置';
  },
  get pickup() {
    return '取';
  },
  get return() {
    return '还';
  },
  get mapDetails() {
    return '地图及指引';
  },
  get showMore() {
    return '查看更多';
  },
  get depositPolice() {
    return '押金政策';
  },
  get morePolice() {
    return '查看更多门店政策';
  },
  get loadingText() {
    return '即将呈现';
  },
  get viewAll() {
    return '查看全部';
  },
  get viewAllReviews() {
    return '查看全部评价';
  },
  get score() {
    return '分';
  },
  get excellent() {
    return '好评';
  },
  get good() {
    return '中评';
  },
  get low() {
    return '差评';
  },
  get bookRightNow() {
    return '立即预订';
  },
  get detail() {
    return '明细';
  },
  get discount() {
    return '已减';
  },
  get freeDetail() {
    return '费用明细';
  },
  get noneCommentScore() {
    return '暂无评分';
  },
  get noneCommentDesc() {
    return '当前点评较少，期待您体验之后的分享';
  },
  get carVideoAndPic() {
    return '门店车辆实拍';
  },
  get storeText() {
    return '问门店';
  },
  get contactVendor() {
    return '咨询门店';
  },
  get vendorOnlineService() {
    return '门店在线客服';
  },
  get recommend() {
    return '推荐';
  },
  get vendorIMDesc() {
    return '门店在线服务，解答车辆、门店、取还车等问题';
  },
  get phone() {
    return '门店电话';
  },
  get China() {
    return '中国';
  },
  get despositSaleOutTip() {
    return '当前车辆已售罄，无法获取到押金政策';
  },
  get despositSaleOutButtonText() {
    return '查看其他报价';
  },
  get new_attention() {
    return '订前必读';
  },
  get new_reviews() {
    return '评价';
  },
  get new_fee() {
    return '费用';
  },
  get fee_shelves2() {
    return '费用明细';
  },
  get carRentalMustRead() {
    return '租车必读';
  },
  get reviewUnit() {
    return '条';
  },
  get new_morePolice() {
    return '更多政策';
  },
  get feeDetail() {
    return '费用明细';
  },
  get bookTotalAmount() {
    return '订单总额';
  },
  get bookTotalAmountSimple() {
    return '总价';
  },
  get selfServicePickUpSlogan() {
    return '快捷取车·无需排队';
  },
  get selfServiceReturnSlogan() {
    return '快捷还车·无需排队';
  },
  get releaseToPicture() {
    return '释放进相册';
  },
  get more() {
    return '查看更多';
  },
  get businessLicense() {
    return '服务方营业执照';
  },
};
