
@import '../../../Common/src/Tokens/tokens/color.scss';
.customerWrapSelect {
  background-color: $C_FFF0DC;
}
.customerWrap {
  background-color: $C_E2EFFF;
  padding: 0 40px;
  border-radius: 12px 0 0 12px;
}

.customerWrapRow {
  background-color: $C_E2EFFF;
  padding: 0 24px 0 35px;
  border-radius: 12px 0 0 12px;
}

.customerButton {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.customerButtonRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.customerTextSlected {
  color: $C_843A00;
}
.customerTextWrap {
  height: 40px;
  top: 2px;
  right: 2px;
}
.customerText {
  font-size: 20px;
  color: $C_006FF6;
}

.customerTextRow {
  font-size: 34px;
  color: $C_006FF6;
  line-height: 40px;
}

.customerIcon {
  width: 44px;
  height: 44px;
  top: 6px;
}

.customerIconRow {
  width: 46px;
  height: 46px;
  right: 8px;
}

.buttonWrap {
  flex-direction: row;
}
