@import '../../../Common/src/Tokens/tokens/color.scss';

.tabNew {
  height: 88px;
  justify-content: flex-end;
  align-items: center;
}
.mr16 {
  margin-right: 16px;
}
.tabTextSelected {
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $storeDetailBlue;
}
.tabText {
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.linear {
  height: 6px;
  width: 50px;
  margin-top: 19px;
}
.showBottomGradientWrap {
  z-index: 1;
}
.shadow {
  z-index: 2;
  background-color: $white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}
.wrapper {
  height: 88px;
  margin: 0 18px;
  display: flex;
  justify-content: space-around;
}
.tabsBottomGradient {
  width: 100%;
  height: 20px;
  position: absolute;
  bottom: -20px;
}
