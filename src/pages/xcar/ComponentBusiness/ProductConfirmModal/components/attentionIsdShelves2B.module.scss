@import '../../../Common/src/Tokens/tokens/color.scss';

.promptInfoWrapperNew {
  margin-bottom: 36px;
}
.promptInfosTitle {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.promptInfoItemNew {
  margin-top: 16px;
}

.limitRuleInfo {
  margin-top: 12px;
  margin-bottom: -4px;
}
.row {
  flex-direction: row;
  align-items: center;
}
.promptInfoTextNew {
  font-size: 26px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blackBase;
}

.limitRuleCont {
  line-height: 48px;
}
.promptInfoTip {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-top: 8px;
  color: $grayBase;
}
.morePolice {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.depositTitle {
  font-size: 30px;
  line-height: 44px;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.depositArrow {
  font-size: 24px;
}
.scrollView {
  flex-direction: row;
}
.policeItemWrap {
  height: 56px;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 12px;
  padding-bottom: 12px;
  margin-top: 16px;
  margin-bottom: 20px;
  margin-right: 12px;
  background-color: $C_F5F8FA;
  border-radius: 8px;
}
.lastPoliceItemWrap {
  margin-right: 0px;
}
.policeItemText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  color: $recommendProposeBg;
}

.lineheight32 {
  line-height: 32px;
}
.secTitleWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.subTitleNew {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $restAssuredTagColor;
}
.starSignTitle {
  font-size: 26px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $modifyOrderExplain;
  align-self: flex-start;
  margin-left: 4px;
}
.subTitleNewPoint {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $restAssuredTagColor;
  margin-left: 8px;
  margin-right: 8px;
}
.descWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 36px;
  margin-right: 36px;
}
.starSignDesc {
  font-size: 26px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $modifyOrderExplain;
  align-self: flex-start;
  position: relative;
  top: 5px;
  margin-right: 2px;
}
.secDesc {
  font-size: 22px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $virtualNumberSplitLine;
}
.easyLifeWrap {
  margin-top: 16px;
}
.easyLifeIconLeftNew {
  width: 78px;
  height: 28px;
  margin-left: 20px;
  margin-right: 20px;
}
.easyLifeArrowTextNew {
  font-size: 22px;
  margin-left: 4px;
  top: 5px;
  color: $restAssuredTagColor;
}
.easyLife2024Wrapper {
  margin-left: 32px;
  margin-top: 16px;
}
.easyLife2024Image {
  height: 76px;
  width: 638px;
}
.wrapper {
  margin-top: 24px;
  margin-left: 24px;
  margin-right: 24px;
  border-radius: 20px;
  background-color: $white;
  padding-left: 32px;
  padding-right: 32px;
  padding-bottom: 16px;
}
.attentionTitle {
  font-size: 34px;
  line-height: 44px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  margin-top: 32px;
  margin-bottom: 24px;
  color: $recommendProposeBg;
}
.cancelRuleInfoWrap {
  margin-bottom: 36px;
}
.cancelRuleTitle {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.cancelRuleNotice {
  font-size: 22px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $grayBase;
  margin-top: 12px;
}
.pickUpMaterialsTitle {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.storeShadow {
  width: 54px;
  height: 96px;
}

.shadowWrap {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: -28px;
  bottom: 12px;
}

.fillBlock {
  width: 24px;
  background-color: $C_FFF;
}

.morePolicyAR {
  position: absolute;
  font-size: 26px;
  color: $C_111111;
}

.busLicenseWrap {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 16px;
}

.busLicenseLeftWrap {
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.blockTitleIcon {
  width: 32px;
  height: 32px;
}

.busLicenseTitle {
  font-size: 30px;
  line-height: 36px;
  color: $recommendProposeBg;
  margin-left: 16px;
  margin-top: 3px;
}
.lookWholeLimit {
  text-decoration: underline;
}

.detailWrap {
  position: absolute;
  right: 0;
  bottom: 6px;
}

.detailInnerWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 106px;
}
.arrowRight {
  font-size: 26px;
  margin-top: 2px;
  color: $C_111111;
}

.depositDescWrap {
  margin-top: 14px;
  margin-left: -18px;
}