@import '../../../Common/src/Tokens/tokens/color.scss';

.wayDot {
  top: 12px;
  width: 12px;
  height: 12px;
  border-radius: 12px;
}
.wayTitle {
  margin-left: 20px;
  margin-right: 10px;
}
.way {
  width: 424px;
  flex-direction: row;
}
.wayText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.wayDesc {
  margin-right: 10px;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.addressWrap {
  width: 494px;
}
.wayAddressDesc {
  margin-left: 66px;
  margin-top: 2px;
  color: #111111;
  font-size: 26px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
}
.distance {
  color: $fontSecondary;
}
.imagesWrapper {
  height: 160px;
  flex-shrink: 1;
  position: relative;
  margin-top: 16px;
  margin-left: 32px;
  margin-right: 32px;
}

.imagesWrapperNew {
  height: 160px;
  flex-shrink: 1;
  position: relative;
  margin-top: 22px;
  margin-left: 0px;
  margin-right: 8px;
}
.imageWrap {
  flex-direction: row;
}
.playIcon {
  position: absolute;
  top: 56px;
  left: 96px;
  width: 48px;
  height: 48px;
}
.imageNumberWrap {
  position: absolute;
  right: 0px;
  bottom: 0px;
  flex-direction: row;
  align-items: center;
}
.imageNumber {
  flex-direction: row;
  align-items: center;
  border-radius: 19px;
  background-color: rgba($black, 0.6);
  padding-left: 12px;
  padding-right: 8px;
  padding-top: 2px;
  padding-bottom: 2px;
  margin-right: 4px;
  margin-bottom: 4px;
}
.carVideoAndPic {
  color: $white;
  font-size: 20px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.arrowRight {
  font-size: 24px;
  color: $white;
}
.wrapper {
  margin-top: 24px;
  margin-left: 24px;
  margin-right: 24px;
  border-radius: 16px;
  background-color: $white;
}
.venderTagWrap {
  margin-top: 16px;
  margin-left: 32px;
  margin-right: 32px;
}
.locationWrapper {
  margin-left: 32px;
  margin-top: 24px;
  padding-bottom: 32px;
}
.guideWayTitle {
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.pickReturnInfoWrap {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
}
.pickReturnMapIcon {
  width: 176px;
  height: 134px;
}
.mapIcon {
  width: 176px;
  height: 134px;
  position: absolute;
  right: 0px;
  bottom: -14px;
}
