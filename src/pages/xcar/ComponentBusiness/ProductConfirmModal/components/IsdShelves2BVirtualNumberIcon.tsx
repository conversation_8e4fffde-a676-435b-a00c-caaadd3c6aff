import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback } from 'react';
import { xRouter, XViewExposure, XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text/src/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import c2xStyles from './isdShelves2BVirtualNumberIconC2xStyles.module.scss';
import { GetAB, CarLog, Utils } from '../../../Util/Index';
import { UITestID, ImageUrl } from '../../../Constants/Index';

const { getPixel, isIos } = BbkUtils;
interface VehicleInfoLogDataType {
  vehicleName?: string;
  vehicleCode?: string;
  vendorName?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
}

type IVirtualNumberIcon = {
  isShowPhone: boolean; // 是否展示虚拟小号
  hasVirtualNumber: boolean; // 是否有虚拟小号
  vendorImUrl: string;
  virtualNumberLog: VehicleInfoLogDataType; // 虚拟小号埋点信息
  showVirtualNumberStoreModal: () => void;
  showPhoneDialog: () => void;
  vehicleDetailModalLogInfo?: any;
  setPriceDetailModalVisible: (data: boolean) => void;
};
const styles = StyleSheet.create({
  phoneIcon: {
    top: getPixel(isIos ? 0 : 2),
  },
});
const IsdShelves2BVirtualNumberIcon: React.FC<IVirtualNumberIcon> = props => {
  const {
    isShowPhone,
    vendorImUrl,
    hasVirtualNumber,
    virtualNumberLog,
    showVirtualNumberStoreModal,
    showPhoneDialog,
    vehicleDetailModalLogInfo,
    setPriceDetailModalVisible,
  } = props;

  // 2022-10-25 IM的展示还需要加上售前供应商IM独立实验的判断
  // 2022-10-25 只有售前供应商IM独立实验号的C版才展示供应商电话咨询入口
  const showIM = Utils.isCtripIsd() && vendorImUrl;

  /**
   * 点击【问门店】icon处理
   * 1、先判断是否im新版，且有im地址。
   *  - 有虚拟号，则打开咨询门店弹层，会同时展示门店电话&门店在线客服
   *  - 没有虚拟号，则直接跳转到im地址
   * 2、再判断是否能展示虚拟小号icon
   *  - 有虚拟号，则打开咨询门店弹层，只会展示门店电话
   *  - 没有虚拟号，则打开’门店电话拨打失败，是否转接携程客服咨询‘的对话确认框
   */
  const handlePress = useCallback(() => {
    if (showIM) {
      // 2022-10-25 C版才展示供应商电话咨询入口
      if (hasVirtualNumber) {
        showVirtualNumberStoreModal();
        setPriceDetailModalVisible(false);
      } else {
        xRouter.navigateTo({ url: vendorImUrl });
      }
    } else if (isShowPhone) {
      if (hasVirtualNumber) {
        showVirtualNumberStoreModal();
        setPriceDetailModalVisible(false);
      } else {
        showPhoneDialog();
      }
    }

    CarLog.LogCode({
      name: '点击_产品详情页_门店弹层_问门店按钮',

      info: {
        ...virtualNumberLog,
        ...vehicleDetailModalLogInfo,
      },
    });
  }, [
    hasVirtualNumber,
    isShowPhone,
    showIM,
    showPhoneDialog,
    showVirtualNumberStoreModal,
    vendorImUrl,
    virtualNumberLog,
  ]);

  // 既不能跳转IM 也不能展示虚拟小号的情况下，不展示icon
  if (!showIM && !isShowPhone) {
    return null;
  }
  const isISDShelves2NewABTest = GetAB.isISDShelves2New();
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_门店信息弹层右上角_问门店按钮',
        info: {
          ...virtualNumberLog,
        },
      })}
      className={
        isISDShelves2NewABTest
          ? c2xStyles.customerWrapRow
          : c2xStyles.customerWrap
      }
    >
      <BbkTouchable
        onPress={handlePress}
        testID={UITestID.car_testid_page_vendorlist_product_modal_header_phone}
        className={
          isISDShelves2NewABTest
            ? c2xStyles.customerButtonRow
            : c2xStyles.customerButton
        }
      >
        <Image
          style={isISDShelves2NewABTest ? styles.phoneIcon : {}}
          className={
            isISDShelves2NewABTest
              ? c2xStyles.customerIconRow
              : c2xStyles.customerIcon
          }
          src={`${ImageUrl.DIMG04_PATH}1tg6s12000j6vx8ooBBD6.png`}
        />
        <View
          className={isISDShelves2NewABTest ? c2xStyles.customerTextWrap : ''}
        >
          <Text
            className={
              isISDShelves2NewABTest
                ? c2xStyles.customerTextRow
                : c2xStyles.customerText
            }
            fontWeight="medium"
          >
            {isISDShelves2NewABTest ? '门店' : '问门店'}
          </Text>
        </View>
      </BbkTouchable>
    </XViewExposure>
  );
};

export default IsdShelves2BVirtualNumberIcon;
