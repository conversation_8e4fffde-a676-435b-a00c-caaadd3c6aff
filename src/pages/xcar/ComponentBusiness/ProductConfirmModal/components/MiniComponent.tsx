import StyleSheet from '@c2x/apis/StyleSheet';
import React, {
  useMemo,
  CSSProperties,
  useState,
  useRef,
  useEffect,
} from 'react';
import {
  XView as View,
  xMergeStyles,
  XImage as Image,
  xClassNames as classNames,
  XViewExposure,
  xCreateAnimation,
  XAnimated,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Formatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import {
  color,
  font,
  icon,
  layout,
  setOpacity,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BookBar from '@ctrip/rn_com_car/dist/src/Components/Basic/BookBar';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './miniComponentC2xStyles.module.scss';
import OptimizationStrengthen from '../../OptimizationStrengthen/src/OptimizationStrengthen';
import { texts } from '../Texts';
import { CarLog, Utils, Channel, AppContext, GetAB } from '../../../Util/Index';
import { ImageUrl, UITestID as UITestId } from '../../../Constants/Index';
import VirtualNumberIcon from './VirtualNumberIcon';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';
import { isIos, useMemoizedFn } from '../../../Common/src/Utils/src/Utils';
import { XAnimateExport } from '../../../Types/Animate';

interface FooterBarDataType {
  price?: number;
  originPrice?: number;
  discountPrice?: number;
  currencyCode?: string;
  unitDesc?: string;
  renderGapPrice?: React.ReactNode;
  renderPrice?: React.ReactNode;
}

interface VehicleInfoLogDataType {
  vehicleName?: string;
  vehicleCode?: string;
  vendorName?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
}

const { getPixel, isAndroid, isHarmony } = BbkUtils;
const styles = StyleSheet.create({
  headerCenter: { paddingLeft: getPixel(88), paddingRight: getPixel(88) },
  header: {
    paddingTop: 0,
  },
  footerMainText: {
    color: color.orangePrice,
    ...font.title4MediumStyle,
  },
  footerMainTextNew: {
    color: color.deepBlueBase,
    ...font.F_36_10_regular_TripNumberSemiBold,
  },
  footerDiscountTextNew: {
    color: color.C_ff6600,
    ...font.caption1LightStyle,
  },
  footerDiscountText: {
    color: color.orangeBase,
    ...font.caption1LightStyle,
  },
  footerLineText: {
    ...font.caption1LightStyle,
    textDecorationLine: 'line-through',
  },
  footerLineTextNew: {
    ...font.caption1LightStyle,
    color: color.C_999,
    textDecorationLine: 'line-through',
  },
  loadingImage: {
    width: getPixel(64),
    height: getPixel(64),
  },
  optimizationStrengthenWrap: {
    width: '100%',
    height: getPixel(isAndroid ? 111 : 112),
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    paddingTop: getPixel(23),
  },
  optimizationStrengthenImg: {
    width: getPixel(184),
    height: getPixel(44),
  },
  optimizationStrengthenImgMid: {
    width: getPixel(184),
    height: getPixel(44),
    marginRight: -getPixel(9),
  },
  optimizationStrengthenImgLong: {
    width: getPixel(184),
    height: getPixel(44),
    marginRight: -getPixel(18),
  },
  optimizationStrengthenDot: {
    width: getPixel(5),
    height: getPixel(5),
    borderRadius: getPixel(5),
    marginLeft: getPixel(10),
    marginRight: getPixel(10),
  },
  optimizationStrengthenVendorName: {
    ...font.title4BoldStyle,
    marginTop: getPixel(isAndroid ? -3 : 2),
  },
  optimizationStrengthenVendorNameMid: {
    ...font.title2BoldStyle,
    marginTop: getPixel(isAndroid ? -3 : 2),
  },
  optimizationStrengthenVendorNameLong: {
    ...font.title3BoldStyle,
    marginTop: getPixel(isAndroid ? -3 : 2),
  },
  buttonStyle: {
    borderRadius: getPixel(16),
  },
  isdShelves2BButtonStyle: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: getPixel(12),
    borderBottomRightRadius: getPixel(12),
    minWidth: getPixel(180),
  },
  bookingOptimizationCurrencyStyle: {
    ...font.body3BoldStyle,
    color: color.recommendBg,
  },
  bookingOptimizationCurrencyStyleNew: {
    ...font.body3BoldStyle,
    color: color.deepBlueBase,
  },
  bookingOptimizationPriceStyle: {
    ...font.vehicleTitleBold,
    color: color.recommendBg,
  },
  bookingOptimizationPriceStyleNew: {
    ...font.F_36_10_regular_TripNumberSemiBold,
    color: color.deepBlueBase,
  },
  bookingOptimizationFormatterWrapperStyle: {
    paddingTop: getPixel(10),
  },
  bookingOptimizationFormatterWrapperStyleNew: {
    paddingTop: !isHarmony ? getPixel(4) : 0,
  },
  bookingOptimizationDiscountPrice: {
    paddingTop: getPixel(1),
    paddingBottom: getPixel(1),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    borderRadius: getPixel(4),
    borderWidth: getPixel(1),
    borderColor: setOpacity(color.discountBorder, 0.25),
  },
  bookingOptimizationDiscountPriceNew: {
    paddingTop: getPixel(1),
    paddingBottom: getPixel(1),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    borderRadius: getPixel(4),
    borderWidth: getPixel(1),
    borderColor: color.C_F9A278,
  },
  bookingOptimizationFooterDiscountText: {
    ...font.labelSLightFlatStyle,
    color: color.orangePrice,
  },
  mt7: {
    marginTop: getPixel(7),
  },
});

const bookingOptimizationGradientColors = [
  color.payButtonGradientOrange,
  color.orangePrice,
];

export const Loading: React.FC = () => {
  const [spinValue, setSpinValue] = useState<XAnimateExport>();
  const spinTimeRef = useRef(1);
  const spin = useMemoizedFn(() => {
    const animation = xCreateAnimation({
      duration: 1000,
      delay: 0,
      timingFunction: 'linear',
    });
    animation.rotate(360 * spinTimeRef.current).step();
    setSpinValue(animation.export());
    spinTimeRef.current += 1;
  });

  useEffect(() => {
    spin();
  }, []);

  return (
    <View className={c2xStyles.loadingWrapper}>
      <XAnimated.View
        style={styles.loadingImage}
        animation={spinValue}
        onTransitionEnd={spin}
      >
        <Image
          src="http://pic.c-ctrip.com/car/osd/mobile/bbk/resource/loading.png"
          style={styles.loadingImage}
        />
      </XAnimated.View>
    </View>
  );
};

type HeaderType = {
  title: string;
  isShowPhone: boolean;
  virtualNumber: string;
  vendorVNumber: string;
  vendorImUrl: string;
  virtualNumberLog: VehicleInfoLogDataType;
  onPressClose: () => void;
  onPressOptimize?: () => void;
  isOptimize?: boolean; // 是否展示优选标签
  nationalChainTagTitle?: string;
  onPressOptimizationStrengthenTitle?: () => void;
  optimizationStrengthenHeaderExposureLogData?: any;
  showVirtualNumberStoreModal: () => void;
  showPhoneDialog: () => void;
};

const NationalChainTag: React.FC<{ tagTitle: string }> = ({ tagTitle }) => (
  <View className={c2xStyles.nationalChainWrap}>
    <Text className={c2xStyles.nationalChainText}>{tagTitle}</Text>
  </View>
);

export const Header: (props: HeaderType) => React.ReactElement = ({
  isOptimize,
  nationalChainTagTitle,
  title,
  onPressClose,
  onPressOptimizationStrengthenTitle,
  optimizationStrengthenHeaderExposureLogData,
  showVirtualNumberStoreModal,
  isShowPhone,
  virtualNumber,
  vendorVNumber,
  vendorImUrl,
  virtualNumberLog,
  showPhoneDialog,
}) => {
  const isInProduct =
    AppContext?.PageInstance?.getPageId() === Channel.getPageId().VendorList.ID;
  const nameLength = useMemo(() => {
    return title ? title.length : 0;
  }, [title]);

  const imgModalUrl = useMemo(() => {
    if (nameLength === 8) {
      return `${ImageUrl.DIMG04_PATH}1tg5t12000cfwenz4E28E.png`;
    }
    if (nameLength > 8) {
      return `${ImageUrl.DIMG04_PATH}1tg2j12000cfwepso4B07.png`;
    }
    return GetAB.isISDInterestPoints()
      ? `${ImageUrl.DIMG04_PATH}1tg1r12000cyui9660F4A.png`
      : `${ImageUrl.BBK_IMAGE_PATH}car_youxuan.png`;
  }, [nameLength]);
  const optimizationvendorNameStyle = useMemo(() => {
    if (nameLength === 8) {
      return styles.optimizationStrengthenVendorNameMid;
    }
    if (nameLength > 8) {
      return styles.optimizationStrengthenVendorNameLong;
    }
    return styles.optimizationStrengthenVendorName;
  }, [nameLength]);
  const optimizationStrengthenImgStyle = useMemo(() => {
    if (nameLength === 8) {
      return styles.optimizationStrengthenImgMid;
    }
    if (nameLength > 8) {
      return styles.optimizationStrengthenImgLong;
    }
    return styles.optimizationStrengthenImg;
  }, [nameLength]);
  return (
    <BbkHeader
      isLeftIconCross={true}
      leftIconTestID={
        UITestId.car_testid_page_vendorlist_product_modal_header_lefticon
      }
      leftIconColor={color.fontSecondary}
      isBottomBorder={false}
      onPressLeft={onPressClose}
      style={styles.header}
      styleInner={{
        minHeight: getPixel(isOptimize ? (isAndroid ? 111 : 112) : 100),
      }}
      leftIconStyle={{ fontSize: getPixel(42) }}
      sideLeftStyle={{ left: getPixel(28) }}
      renderContent={
        isOptimize ? (
          <XViewExposure
            testID={
              optimizationStrengthenHeaderExposureLogData
                ? CarLog.LogExposure({
                    name: '曝光_门店弹层_优选',
                    info: optimizationStrengthenHeaderExposureLogData,
                  })
                : ''
            }
            className={c2xStyles.optimizationStrengthenContainer}
          >
            <OptimizationStrengthen
              vendorName={title}
              isShowRightArrow={false}
              isShowSubTitle={true}
              wrapStyle={styles.optimizationStrengthenWrap}
              imgStyle={optimizationStrengthenImgStyle}
              dotStyle={styles.optimizationStrengthenDot}
              vendorNameStyle={optimizationvendorNameStyle}
              onPress={onPressOptimizationStrengthenTitle}
              imgModalUrl={imgModalUrl}
            />
          </XViewExposure>
        ) : (
          <View
            style={xMergeStyles([
              layout.flexCenter,
              layout.flex1,
              styles.headerCenter,
            ])}
          >
            <View style={layout.flexRow}>
              <Text className={c2xStyles.title} fontWeight="medium">
                {title}
              </Text>
              {!!nationalChainTagTitle && (
                <NationalChainTag tagTitle={nationalChainTagTitle} />
              )}
            </View>
          </View>
        )
      }
      renderRight={
        Utils.isCtripIsd() &&
        isInProduct && (
          <VirtualNumberIcon
            isOptimize={isOptimize}
            isShowPhone={isShowPhone}
            hasVirtualNumber={!!(virtualNumber || vendorVNumber)}
            vendorImUrl={vendorImUrl}
            virtualNumberLog={virtualNumberLog}
            showVirtualNumberStoreModal={showVirtualNumberStoreModal}
            showPhoneDialog={showPhoneDialog}
          />
        )
      }
    />
  );
};
interface IServiceIcon {
  virtualNumber?: string;
  vendorImUrl?: string;
  isShowPhone?: boolean;
  onPressPhone?: () => void;
  virtualNumberLog?: VehicleInfoLogDataType;
  testID?: string;
}
const ServiceIcon: React.FC<IServiceIcon> = ({
  isShowPhone,
  onPressPhone,
  virtualNumberLog,
  testID,
}) => {
  if (Utils.isCtripIsd()) {
    return null;
  }
  if (isShowPhone) {
    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_产品详情页_门店信息弹层_供应商电话',
          info: virtualNumberLog,
        })}
      >
        <Touchable onPress={onPressPhone} testID={testID}>
          <Text type="icon" className={c2xStyles.footerPhone}>
            {icon.phone}
          </Text>
        </Touchable>
      </XViewExposure>
    );
  }
  return null;
};
export const FooterInner: React.FC<{
  data: FooterBarDataType;
  isShowDetailWord?: boolean;
  isShowPhone?: boolean;
  onPressPhone?: () => void;
  virtualNumber?: string;
  vendorImUrl?: string;
  virtualNumberLog?: VehicleInfoLogDataType;
  isShowTotalAmount?: boolean;
  isNewBooking?: boolean;
  testID?: string;
  showPriceDetailModal?: boolean /* 是否展示价格详情弹层 */;
  isISDShelves2B?: boolean; // 是否货架二期版本
}> = ({
  data,
  isShowDetailWord,
  isShowPhone,
  onPressPhone,
  virtualNumber,
  vendorImUrl,
  virtualNumberLog,
  isShowTotalAmount,
  isNewBooking = false,
  showPriceDetailModal = false,
  testID,
  isISDShelves2B,
}) => {
  if (!data) return null;
  const isBookingOptimization = isNewBooking || Utils.isCtripIsd();
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <View
      className={
        isISDShelves2B
          ? c2xStyles.isdShelves2BFooter
          : isBookingOptimization
            ? c2xStyles.footerNew
            : c2xStyles.footer
      }
    >
      <View className={isISDShelves2B ? c2xStyles.mr10 : c2xStyles.flex1}>
        <View style={layout.flexRow}>
          {isShowTotalAmount && (
            <Text className={c2xStyles.totalAmountText} fontWeight="bold">
              {GetAB.isISDShelves2New()
                ? texts.bookTotalAmountSimple
                : texts.bookTotalAmount}
            </Text>
          )}
          {data?.price >= 0 &&
            (isISDInterestPoints ? (
              <Formatter
                currency={data.currencyCode}
                price={data.price}
                testID={UITestId.car_testid_comp_book_bar_total_price}
                currencyStyle={
                  isBookingOptimization
                    ? styles.bookingOptimizationCurrencyStyleNew
                    : styles.footerMainTextNew
                }
                priceStyle={
                  isBookingOptimization
                    ? styles.bookingOptimizationPriceStyleNew
                    : styles.footerMainTextNew
                }
                wrapperStyle={
                  isISDInterestPoints
                    ? styles.bookingOptimizationFormatterWrapperStyleNew
                    : isBookingOptimization &&
                      styles.bookingOptimizationFormatterWrapperStyle
                }
              />
            ) : (
              <Formatter
                currency={data.currencyCode}
                price={data.price}
                testID={UITestId.car_testid_comp_book_bar_total_price}
                currencyStyle={
                  isBookingOptimization
                    ? styles.bookingOptimizationCurrencyStyle
                    : styles.footerMainText
                }
                priceStyle={
                  isBookingOptimization
                    ? styles.bookingOptimizationPriceStyle
                    : styles.footerMainText
                }
                wrapperStyle={
                  isBookingOptimization &&
                  styles.bookingOptimizationFormatterWrapperStyle
                }
              />
            ))}
          {!isISDShelves2B && !!data?.originPrice && (
            <View
              className={
                isBookingOptimization
                  ? c2xStyles.originPriceWrapperNew
                  : c2xStyles.originPriceWrapper
              }
            >
              <Formatter
                currency={data.currencyCode}
                price={data.originPrice}
                currencyStyle={
                  isISDInterestPoints
                    ? styles.footerLineTextNew
                    : styles.footerLineText
                }
                priceStyle={
                  isISDInterestPoints
                    ? styles.footerLineTextNew
                    : styles.footerLineText
                }
              />
            </View>
          )}
          {!!data.unitDesc && (
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,
                isISDInterestPoints
                  ? c2xStyles.unitTextNew
                  : c2xStyles.unitText,
              )}
            >
              {data.unitDesc}
            </Text>
          )}
        </View>
        {!!data?.discountPrice && (
          <View style={layout.flexRow}>
            <View
              style={
                isISDInterestPoints
                  ? styles.bookingOptimizationDiscountPriceNew
                  : isBookingOptimization &&
                    styles.bookingOptimizationDiscountPrice
              }
            >
              <View
                className={classNames(
                  c2xStyles.discountPrice,
                  isBookingOptimization &&
                    c2xStyles.bookingOptimizationDiscountPriceContent,
                )}
              >
                <Text
                  className={classNames(
                    c2xCommonStyles.c2xTextDefaultCss,
                    isISDInterestPoints
                      ? c2xStyles.footerDiscountTextNew
                      : c2xStyles.footerDiscountText,
                    isBookingOptimization &&
                      c2xStyles.bookingOptimizationFooterDiscountText,
                  )}
                >
                  {texts.discount}
                </Text>
                <Formatter
                  currency={data.currencyCode}
                  price={data.discountPrice}
                  currencyStyle={xMergeStyles([
                    isISDInterestPoints
                      ? styles.footerDiscountTextNew
                      : styles.footerDiscountText,
                    isBookingOptimization &&
                      styles.bookingOptimizationFooterDiscountText,
                  ])}
                  priceStyle={xMergeStyles([
                    isISDInterestPoints
                      ? styles.footerDiscountTextNew
                      : styles.footerDiscountText,
                    isBookingOptimization &&
                      styles.bookingOptimizationFooterDiscountText,
                  ])}
                  wrapperStyle={
                    isAndroid &&
                    !isHarmony &&
                    isBookingOptimization &&
                    styles.mt7
                  }
                />
              </View>
            </View>
          </View>
        )}
        {!!data.renderPrice && data.renderPrice}
      </View>
      {isShowDetailWord && (
        <View
          className={classNames(
            isBookingOptimization
              ? c2xStyles.detailWrapNew
              : c2xStyles.detailWrap,
            isISDShelves2B && c2xStyles.detailWrapNewISDShelves2B,
            isAndroid && c2xStyles.mt40,
          )}
        >
          <Text
            className={classNames(
              c2xCommonStyles.c2xTextDefaultCss,
              isBookingOptimization
                ? c2xStyles.bookingOptimizationFooterDetailText
                : c2xStyles.footerDetailText,
              isISDInterestPoints && c2xStyles.deepBule,
              isISDShelves2B && c2xStyles.footerDetailTextISDShelves2B,
            )}
            testID={UITestId.car_testid_comp_bookbar_price}
          >
            {texts.detail}
          </Text>
          {isBookingOptimization && (
            <Text
              type="icon"
              className={classNames(
                c2xCommonStyles.c2xTextDefaultColor,

                isISDInterestPoints
                  ? c2xStyles.feeDetailArrowNew
                  : c2xStyles.feeDetailArrow,
              )}
            >
              {showPriceDetailModal ? icon.arrowDown : icon.arrowUp}
            </Text>
          )}
        </View>
      )}
      <ServiceIcon
        virtualNumber={virtualNumber}
        vendorImUrl={vendorImUrl}
        isShowPhone={isShowPhone}
        onPressPhone={onPressPhone}
        virtualNumberLog={virtualNumberLog}
        testID={testID}
      />
    </View>
  );
};

interface FooterPropType {
  data: FooterBarDataType;
  virtualNumberLog?: VehicleInfoLogDataType;
  isLoading?: boolean;
  disabled?: boolean;
  buttonName?: string;
  isShowPhone?: boolean;
  virtualNumber?: string;
  vendorImUrl?: string;
  onPressBtn?: () => void;
  onPressBar?: () => void;
  onPressPhone?: () => void;
  renderTip?: React.ReactNode;
  renderRightText?: React.ReactNode;
  bookBarStyle?: CSSProperties;
  style?: CSSProperties;
  isShowFreeCancelLabel?: boolean;
  buttonTextStyle?: CSSProperties;
  isShowTotalAmount?: boolean;
  isNewBooking?: boolean;
  buttonTestID?: string;
  payModeTestID?: string;
  barTestID?: string;
  phoneTestID?: string;
  showPriceDetailModal?: boolean; // 是否展示价格详情弹层
  isISDShelves2B?: boolean; // 是否货架二期版本
  virtualNumberEntry?: any; // 联系门店入口
  isShowVirtualNumberEntry?: boolean; // 是否展示问门店入口
}
export const Footer: React.FC<FooterPropType> = ({
  isLoading,
  disabled,
  buttonName,
  data,
  virtualNumberLog,
  onPressBtn = Utils.noop,
  onPressBar = Utils.noop,
  isShowPhone = false,
  virtualNumber = '',
  vendorImUrl = '',
  onPressPhone = Utils.noop,
  renderTip,
  renderRightText,
  bookBarStyle,
  isShowFreeCancelLabel,
  buttonTextStyle,
  isShowTotalAmount,
  isNewBooking = false, // 是否是新版的填写页优化样式，
  buttonTestID,
  payModeTestID,
  barTestID,
  phoneTestID,
  showPriceDetailModal = false,
  style,
  isISDShelves2B,
  virtualNumberEntry,
  isShowVirtualNumberEntry,
}) => {
  const isBookingOptimization = isNewBooking || Utils.isCtripIsd();
  const isBookingPage =
    AppContext.PageInstance.getPageId() === Channel.getPageId().Book.ID;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <View testID={UITestId.car_testid_comp_vendor_modal_book_bar} style={style}>
      <BookBar
        virtualNumberEntry={virtualNumberEntry}
        isISDShelves2B={isISDShelves2B}
        disabled={disabled}
        buttonType={tokenType.ButtonType.Default}
        buttonName={buttonName || texts.bookRightNow}
        isLoading={isLoading}
        onPressBtn={onPressBtn}
        onPressBar={onPressBar}
        renderTip={renderTip}
        renderRightText={renderRightText}
        style={bookBarStyle}
        buttonStyle={
          isISDShelves2B && isShowVirtualNumberEntry
            ? styles.isdShelves2BButtonStyle
            : isBookingOptimization && styles.buttonStyle
        }
        buttonTextStyle={buttonTextStyle}
        buttonGradientColors={
          isBookingOptimization &&
          isBookingPage &&
          !isISDInterestPoints &&
          bookingOptimizationGradientColors
        }
        buttonTestID={buttonTestID}
        payModeTestID={payModeTestID}
        barTestID={barTestID}
        // @ts-ignore
        isShowFreeCancelLabel={isShowFreeCancelLabel}
        renderInner={
          <FooterInner
            isISDShelves2B={isISDShelves2B}
            isNewBooking={isNewBooking}
            isShowDetailWord={!!onPressBar}
            isShowPhone={isShowPhone}
            onPressPhone={onPressPhone}
            virtualNumber={virtualNumber}
            vendorImUrl={vendorImUrl}
            data={data}
            virtualNumberLog={virtualNumberLog}
            isShowTotalAmount={isShowTotalAmount}
            testID={phoneTestID}
            showPriceDetailModal={showPriceDetailModal}
          />
        }
      />
    </View>
  );
};

export default Header;
