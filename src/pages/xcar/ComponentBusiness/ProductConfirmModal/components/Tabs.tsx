import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useState, forwardRef, useImperativeHandle } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { layout, color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './tabsC2xStyles.module.scss';
import { LayoutPartEnum } from '../Type';
import { UITestID } from '../../../Constants/Index';
import { GetAB } from '../../../Util/Index';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  padding32: { paddingLeft: getPixel(32), paddingRight: getPixel(32) },
  padding112: { paddingLeft: getPixel(112), paddingRight: getPixel(112) },
});

const BottomGradient = [
  setOpacity(color.easylifeGray1, 0.3),
  setOpacity(color.white, 0),
];

const BottomGradientNew = [color.C_41A5F9, color.deepBlueBase];

const BottomGradientSelected = [
  color.linearGradientBlueLight,
  color.linearGradientBlueDark,
];

const TabNew = ({ isSelect, text, isLast, onPress, widthStyle }) => (
  <Touchable
    className={classNames(c2xStyles.tabNew, !isLast && c2xStyles.mr16)}
    debounce={true}
    onPress={onPress}
    testID={`${UITestID.car_testid_comp_vendor_modal_tab}_${text}`}
    style={widthStyle}
  >
    <View style={layout.flexCenter}>
      <Text
        className={classNames(
          c2xCommonStyles.c2xTextDefaultCss,
          isSelect ? c2xStyles.tabTextSelected : c2xStyles.tabText,
        )}
      >
        {text}
      </Text>
    </View>
    {isSelect ? (
      <LinearGradient
        start={{ x: 0.5, y: 1.0 }}
        end={{ x: 1.0, y: 1.0 }}
        locations={[0, 1]}
        colors={
          GetAB.isISDInterestPoints()
            ? BottomGradientNew
            : BottomGradientSelected
        }
        className={c2xStyles.linear}
      />
    ) : (
      <View className={c2xStyles.linear} />
    )}
  </Touchable>
);

type TabsProps = {
  selectedId: LayoutPartEnum;
  tab: { type: LayoutPartEnum; title: string; clickKey?: string }[];
  onPressTab: (type: LayoutPartEnum, clickKey?: string) => void;
  renderTop?: React.ReactElement;
  isShowBottomGradient?: boolean;
};

const paddingStyle = {
  2: styles.padding112,
  3: styles.padding32,
};

export const Tabs: React.FC<TabsProps> = memo(
  forwardRef(
    ({ selectedId, tab, onPressTab, renderTop, isShowBottomGradient }, ref) => {
      const [curSelectedId, setSelectedId] = useState(
        selectedId || LayoutPartEnum.VehicleDetail,
      );
      useImperativeHandle(ref, () => ({
        setSelectedId,
      }));
      // 已关闭阴影
      return (
        <XBoxShadow
          coordinate={{ x: 0, y: getPixel(2) }}
          color={setOpacity(color.black, 0)}
          opacity={1}
          blurRadius={12}
          elevation={0}
          className={
            isShowBottomGradient
              ? c2xStyles.showBottomGradientWrap
              : c2xStyles.shadow
          }
        >
          {renderTop}
          {tab?.length > 1 && (
            <View
              className={c2xStyles.wrapper}
              style={xMergeStyles([layout.flexRow, paddingStyle[tab?.length]])}
            >
              {tab.map((v, index) => (
                <TabNew
                  key={v.type}
                  isLast={index === tab.length - 1}
                  isSelect={v.type === curSelectedId}
                  text={v.title}
                  onPress={() => onPressTab(v.type, v.clickKey)}
                  widthStyle={tab?.length >= 4 ? {} : { width: getPixel(200) }}
                />
              ))}
            </View>
          )}
          {isShowBottomGradient && (
            <LinearGradient
              className={c2xStyles.tabsBottomGradient}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 0.0, y: 1.0 }}
              locations={[0, 1]}
              colors={
                GetAB.isISDInterestPoints() ? BottomGradientNew : BottomGradient
              }
            />
          )}
        </XBoxShadow>
      );
    },
  ),
);

export default Tabs;
