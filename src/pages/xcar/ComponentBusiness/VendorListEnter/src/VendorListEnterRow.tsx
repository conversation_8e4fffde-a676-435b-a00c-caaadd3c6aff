import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
  XViewExposure,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, CSSProperties } from 'react';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import {
  color,
  font,
  layout,
  icon,
  space,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import c2xStyles from './vendorListEnterRowC2xStyles.module.scss';
import {
  DayAveragePriceDesc,
  TotalPriceDesc,
} from '../../CarPriceDescribe/index';
import VendorTag from '../../VendorTag';
import { Utils, CarLog, GetABCache } from '../../../Util/Index';
import LicensePlate, {
  PlateBgSize,
} from '../../CarVehicleName/src/LicensePlate';

const { getPixel, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  enterFirstRowSoldOut: { paddingTop: getPixel(18) },
  borderTop: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.grayBorder,
  },
  wrap_padding: { paddingLeft: space.spaceXL, paddingRight: space.spaceL },
  mtf2: { marginTop: -getPixel(2) },
  soldOutText: { color: color.darkGrayBorder },
  priceStyleNew: {
    ...font.F_32_10_regular_TripNumberSemiBold,
    color: color.deepBlueBase,
  },
  totalPriceStyle_B: { fontSize: BbkUtils.getPixel(24) },
  descStyleNew: {
    color: color.deepBlueBase,
    paddingLeft: BbkUtils.getPixel(4),
    paddingTop: BbkUtils.getPixel(BbkUtils.isIos ? 4 : 0),
    marginBottom: BbkUtils.getPixel(0),
  },
  currencyStyleNew: {
    color: color.deepBlueBase,
    fontSize: getPixel(24),
    // @ts-expect-error
    fontWeight: '400',
  },
  originPriceStyle: { ...font.F_24_10_regular, color: color.C_666 },
  originPriceStyleNew: {
    ...font.F_24_10_regular,
    color: color.C_888888,
  },
  originPriceWrapStyle: { paddingTop: getPixel(4), top: getPixel(8) },
  marketTagsLabelStyleNew: {
    marginBottom: 0,
    height: getPixel(34),
    minHeight: getPixel(34),
  },
  enterSecondRowLeft: {
    flexDirection: 'row',
    borderTopLeftRadius: getPixel(4),
    borderBottomLeftRadius: getPixel(4),
  },
  plate: { marginRight: getPixel(10) },
  topDescLineAbs: {
    position: 'absolute',
    left: 0,
    marginTop: isAndroid ? getPixel(18) : getPixel(22),
    width: getPixel(380),
    paddingLeft: space.spaceXL,
  },
  h34: {
    height: getPixel(34),
  },
  mbf3: {
    marginBottom: -getPixel(3),
  },
  licensePlate: {
    bottom: getPixel(4),
    height: getPixel(6),
  },
  userBrowsed: {
    backgroundColor: color.C_F6F8FB,
  },
});

interface IBbkEnterTagProps {
  hasOptimize?: boolean;
  hasEasyLife?: boolean;
  hasSelfService?: boolean;
  tagInfo?: VendorTagType;
  isSoldOut?: boolean;
  theme?: any;
  restAssuredTag?: VendorTagType;
  isCreditAndRestAssured?: boolean;
}

interface IBbkVendorAndTagProps extends IBbkEnterTagProps {
  vendorListDesc?: string;
  style?: CSSProperties;
  showTag?: boolean;
  tagInfo?: VendorTagType;
  isCreditAndRestAssured?: boolean;
}

export interface PriceType {
  price: number;
  originalTotalPrice?: number;
}
interface IVendorListEnterRowProps extends IBbkVendorAndTagProps {
  price: number;
  marketTags?: Array<VendorTagType>;
  descText: string;
  vehicleCode: string;
  prefixDescText?: string;
  showBorder?: boolean;
  priceGroupTitle?: string;
  groupDescList?: Array<string>;
  moreGroupText?: string;
  isSoldOut?: boolean;
  minTotalPrice?: number;
  minTotalPriceDesc?: string;
  minTotalPriceOtherDesc?: string;
  vendorList?: any;
  allVendorPriceList?: any;
  totalPricePress?: () => void;
  onTotalPriceLayOut?: (e: LayoutChangeEvent, offsetY?: number) => void;
  style?: CSSProperties;
  originPrice?: PriceType;
  isRecommend?: boolean;
  pickUpDesc?: string;
  displacement?: string;
  vehicleStyle?: string;
  productRef?: {
    license: string;
    licenseStyle: string;
    licenseTag?: string;
  };
  isSecretBox?: boolean;
  isUserBrowsed?: boolean; // 是否浏览过
  isGroupEnter?: boolean;
}

// 国内列表页车型报价行
const VendorListEnterRow = (props: IVendorListEnterRowProps) => {
  const [isMultipleRows] = useState(false);
  const {
    price,
    descText,
    prefixDescText,
    isSoldOut,
    minTotalPriceDesc,
    minTotalPriceOtherDesc,
    minTotalPrice,
    marketTags = [],
    totalPricePress,
    onTotalPriceLayOut = Utils.noop,
    style,
    originPrice,
    showBorder,
    productRef,
    pickUpDesc,
    vehicleStyle,
    displacement,
    isSecretBox,
    isUserBrowsed,
    isGroupEnter,
  } = props;
  console.log('VendorListEnterRow props===>', props);
  // 是否展示首行 年款｜排量｜牌照｜取车提示
  const isShowTopDescLine = vehicleStyle || !!productRef?.license || pickUpDesc;
  const isShowMarketLabel = marketTags?.length > 0 && !isSoldOut;
  const MarketLabelAndPriceContainer: any = isShowMarketLabel
    ? LinearGradient
    : View;
  const MarketLabelAndPriceProps = isShowMarketLabel
    ? {
        start: { x: 0.0, y: 0.0 },
        end: { x: 1.0, y: 1.0 },
        locations: [0, 1],
        colors: [color.C_FFEDE8, 'rgba(255, 255, 255, 0)'],
      }
    : {};
  return (
    <View
      className={c2xStyles.container}
      style={xMergeStyles([
        showBorder && styles.borderTop,
        styles.wrap_padding,
        isUserBrowsed && isGroupEnter && styles.userBrowsed,
      ])}
    >
      {!!isShowTopDescLine && (
        <View className={c2xStyles.topDescLine} style={styles.topDescLineAbs}>
          {!!vehicleStyle && (
            <>
              <BbkText
                className={classNames(c2xStyles.displaceText, c2xStyles.mr6)}
                style={isSoldOut && styles.soldOutText}
              >
                {vehicleStyle}
              </BbkText>
              {!!displacement && (
                <BbkText
                  className={c2xStyles.displaceText}
                  style={isSoldOut && styles.soldOutText}
                >
                  {displacement}
                </BbkText>
              )}
            </>
          )}

          {/* 牌照展示在年款唯独 */}
          {!!productRef?.license && (
            <LicensePlate
              title={productRef?.license}
              licenseType={productRef.licenseStyle}
              size={PlateBgSize.small}
              style={styles.plate}
              isSecretBox={isSecretBox}
              bottomLinearStyle={styles.licensePlate}
            />
          )}
        </View>
      )}
      <View style={layout.flexRow}>
        <View className={classNames(c2xStyles.leftBox, c2xStyles.leftBox2)}>
          <View
            className={classNames(
              isMultipleRows
                ? c2xStyles.enterFirstMultipleRows
                : c2xStyles.enterFirstRow,
              c2xStyles.mb6,
            )}
            style={xMergeStyles([
              isSoldOut && styles.enterFirstRowSoldOut,
              style,
            ])}
          >
            {/* 首行标签，最多展示三个，后续服务控制数量 */}
            {/* 日均价格区域 */}
            {!isSoldOut && (
              <View>
                <DayAveragePriceDesc
                  price={price}
                  descText={descText}
                  prefixDescText={prefixDescText}
                  priceStyle={styles.priceStyleNew}
                  originPriceStyle={
                    styles.originPriceStyleNew
                  }
                  originPriceWrapStyle={styles.originPriceWrapStyle}
                  descStyle={styles.descStyleNew}
                  currencyStyle={styles.currencyStyleNew}
                  originPrice={originPrice}
                  isNew={false}
                />
              </View>
            )}
          </View>

          <View className={c2xStyles.enterSecondRow} style={layout.rowEnd}>
            <MarketLabelAndPriceContainer
              {...MarketLabelAndPriceProps}
              style={xMergeStyles([styles.enterSecondRowLeft, styles.h34])}
            >
              {/* 营销标签区域 */}
              {/* {marketTags?.length > 0 && !isSoldOut && ( */}
                <XViewExposure
                  testID={CarLog.LogExposure({
                    name: '曝光_列表页_车型_营销标签',

                    info: { labelCode: marketTags?.[0]?.labelCode },
                  })}
                  className={c2xStyles.marketTagWarp}
                >
                  <VendorTag
                    tags={marketTags}
                    labelStyle={styles.marketTagsLabelStyleNew}
                    textStyle={isAndroid && styles.mbf3}
                  />
                </XViewExposure>
              {/* )} */}
              {/* 总价区域 */}
              {!isSoldOut && (
                <View
                  className={c2xStyles.priceWrap}
                  style={isAndroid && styles.mtf2}
                >
                  <TotalPriceDesc
                    minTotalPrice={minTotalPrice}
                    minTotalPriceDesc={minTotalPriceDesc}
                    minTotalPriceOtherDesc={minTotalPriceOtherDesc}
                    totalPriceStyle={styles.totalPriceStyle_B}
                    totalPricePress={totalPricePress}
                    onTotalPriceLayOut={onTotalPriceLayOut}
                  />
                </View>
              )}
              {isSoldOut && (
                <BbkText className={c2xStyles.soldOutTipText}>已售罄</BbkText>
              )}
            </MarketLabelAndPriceContainer>
          </View>
        </View>
        {!isSoldOut && (
          <BbkText type="icon" className={c2xStyles.arrowRightList}>
            {icon.arrowRightList}
          </BbkText>
        )}
      </View>
    </View>
  );
};

export default VendorListEnterRow;
