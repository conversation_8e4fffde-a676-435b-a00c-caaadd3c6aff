## 项目介绍

> 本项目由CRN项目迁移而来，迁移教程请参考[CRN项目迁移教程](https://pages.release.ctripcorp.com/car/crn2xtaro/)

> 本项目为一码多端项目，目前支持的渠道有`携程APP`、`携程小程序`、`去哪儿APP`、`去哪儿小程序` ，未来会支持 `携程H5`、`去哪儿H5`。

## 分支管理

### 主分支

1. 分支定位：线上生产分支，生产环境发布之后合并至此分支。
2. 新增规则：一般不新增，如需新增master分支，需要找@cczhang确认，并更新此文档。

| 分支名称              | 分支描述                                   |
| --------------------- | ------------------------------------------ |
| master-xtaro          | 携程APP主分支                              |
| master-xtaro-mini     | 小程序主分支                               |
| master-xtaro-qunar    | 去哪儿APP主分支                            |
| master-xtaro-h5       | 携程H5主分支，交易快照与携程H5共用一套代码 |
| master-xtaro-h5-qunar | 去哪儿H5主分支                             |

其他分支规则请参考[BRANCH.md](./BRANCH.md)

## 本地开发

1. [xTaro环境搭建](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/environment_setup)
2. 安装依赖

```bash
npm run clean

```

### APP开发

1. 按照[xTaro - CRN](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/using_xtaro_for_crn_development)配置环境
2. 启动APP项目

```bash
npm run dev:crn
```

### 小程序开发

1. 点击[这里](http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********)申请权限微信主版权限申请
2. 按照[xTaro - mini](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/developing_master_program)配置环境，下载微信开发者工具（Stable 1.06.2405020）

3. 启动小程序项目

```bash
npm run dev:mini
```

3. 小程序打登录态

1) 首先

```bash
npm run build:mini -- --accounts
```

2. 然后登陆

3. 最后

```bash
npm run dev:mini
```

4. 注意：在微信开发者工具中打开的目录是`/xxxx/xtaro-car-main/dist/mini/weapp`

### 去哪儿APP开发

1. 确保你拥有QRN环境，配置参考QRN文档[QRN环境搭建](https://ued.qunar.com/qrn/start/start-project-init.html)
2. node版本v18.12.1
3. 安装qrn-cli

```bash
 npm i -g @qnpm/<EMAIL>-for-car.**************
```

4. 安装xtaro转QRN工具

```bash
npm i -g @ctrip/xtaro2qrn@1.0.19

```

5. 启动项目

```bash
npm run dev:qrn
```
